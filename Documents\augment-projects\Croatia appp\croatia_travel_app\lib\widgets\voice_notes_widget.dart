import 'package:flutter/material.dart';
import '../models/voice_note.dart';
import '../services/voice_recording_service.dart';

class VoiceNotesWidget extends StatefulWidget {
  final List<String> voiceNoteIds;
  final Function(List<String>) onVoiceNotesChanged;
  final bool isEditable;

  const VoiceNotesWidget({
    super.key,
    required this.voiceNoteIds,
    required this.onVoiceNotesChanged,
    this.isEditable = true,
  });

  @override
  State<VoiceNotesWidget> createState() => _VoiceNotesWidgetState();
}

class _VoiceNotesWidgetState extends State<VoiceNotesWidget> {
  final VoiceRecordingService _voiceService = VoiceRecordingService();
  List<VoiceNote> _voiceNotes = [];
  bool _isRecording = false;
  bool _isPlaying = false;
  String? _playingNoteId;

  @override
  void initState() {
    super.initState();
    _loadVoiceNotes();
    _setupVoiceServiceListeners();
  }

  void _setupVoiceServiceListeners() {
    _voiceService.eventStream.listen((event) {
      switch (event.type) {
        case VoiceEventType.recordingStarted:
          setState(() {
            _isRecording = true;
          });
          break;
        case VoiceEventType.recordingStopped:
          if (event.voiceNote != null) {
            _addVoiceNote(event.voiceNote!);
          }
          setState(() {
            _isRecording = false;
          });
          break;
        case VoiceEventType.playbackStarted:
          setState(() {
            _isPlaying = true;
            _playingNoteId = event.voiceNote?.id;
          });
          break;
        case VoiceEventType.playbackCompleted:
        case VoiceEventType.playbackStopped:
          setState(() {
            _isPlaying = false;
            _playingNoteId = null;
          });
          break;
        default:
          break;
      }
    });
  }

  Future<void> _loadVoiceNotes() async {
    // Simulace načtení hlasových poznámek z databáze
    // V reálné aplikaci by se načítaly podle IDs
    setState(() {
      _voiceNotes = []; // Načtené poznámky
    });
  }

  void _addVoiceNote(VoiceNote voiceNote) {
    setState(() {
      _voiceNotes.add(voiceNote);
    });
    
    final updatedIds = [...widget.voiceNoteIds, voiceNote.id];
    widget.onVoiceNotesChanged(updatedIds);
  }

  void _removeVoiceNote(VoiceNote voiceNote) {
    setState(() {
      _voiceNotes.removeWhere((note) => note.id == voiceNote.id);
    });
    
    final updatedIds = widget.voiceNoteIds.where((id) => id != voiceNote.id).toList();
    widget.onVoiceNotesChanged(updatedIds);
  }

  Future<void> _startRecording() async {
    await _voiceService.startRecording(
      fileName: 'diary_note_${DateTime.now().millisecondsSinceEpoch}',
    );
  }

  Future<void> _stopRecording() async {
    await _voiceService.stopRecording(
      title: 'Deníková poznámka',
      description: 'Hlasová poznámka k deníkovému záznamu',
    );
  }

  Future<void> _playVoiceNote(VoiceNote voiceNote) async {
    if (_isPlaying && _playingNoteId == voiceNote.id) {
      await _voiceService.stopPlayback();
    } else {
      await _voiceService.playVoiceNote(voiceNote);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.mic, size: 20),
            const SizedBox(width: 8),
            const Text(
              'Hlasové poznámky',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            if (widget.isEditable)
              IconButton(
                onPressed: _isRecording ? _stopRecording : _startRecording,
                icon: Icon(
                  _isRecording ? Icons.stop : Icons.add,
                  color: _isRecording ? Colors.red : Colors.blue,
                ),
                tooltip: _isRecording ? 'Zastavit nahrávání' : 'Přidat hlasovou poznámku',
              ),
          ],
        ),
        
        if (_isRecording) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Nahrávání...',
                  style: TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                StreamBuilder<VoiceRecordingEvent>(
                  stream: _voiceService.eventStream,
                  builder: (context, snapshot) {
                    if (snapshot.hasData && 
                        snapshot.data!.type == VoiceEventType.recordingProgress) {
                      final duration = snapshot.data!.duration ?? Duration.zero;
                      final minutes = duration.inMinutes;
                      final seconds = duration.inSeconds % 60;
                      return Text(
                        '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}',
                        style: const TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.w600,
                        ),
                      );
                    }
                    return const Text(
                      '00:00',
                      style: TextStyle(
                        color: Colors.red,
                        fontWeight: FontWeight.w600,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ],

        if (_voiceNotes.isNotEmpty) ...[
          const SizedBox(height: 12),
          ...(_voiceNotes.map((voiceNote) => _buildVoiceNoteItem(voiceNote))),
        ] else if (!_isRecording) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.mic_none,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Zatím žádné hlasové poznámky',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildVoiceNoteItem(VoiceNote voiceNote) {
    final isCurrentlyPlaying = _isPlaying && _playingNoteId == voiceNote.id;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => _playVoiceNote(voiceNote),
                icon: Icon(
                  isCurrentlyPlaying ? Icons.pause : Icons.play_arrow,
                  color: isCurrentlyPlaying ? Colors.orange : Colors.blue,
                ),
                constraints: const BoxConstraints(
                  minWidth: 32,
                  minHeight: 32,
                ),
                padding: EdgeInsets.zero,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      voiceNote.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 12,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          voiceNote.formattedDuration,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Icon(
                          Icons.storage,
                          size: 12,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          voiceNote.formattedFileSize,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              if (widget.isEditable)
                IconButton(
                  onPressed: () => _showDeleteConfirmation(voiceNote),
                  icon: const Icon(
                    Icons.delete_outline,
                    color: Colors.red,
                    size: 20,
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                  padding: EdgeInsets.zero,
                ),
            ],
          ),
          
          if (voiceNote.description != null && voiceNote.description!.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              voiceNote.description!,
              style: TextStyle(
                color: Colors.grey[700],
                fontSize: 12,
              ),
            ),
          ],
          
          if (voiceNote.transcription != null && voiceNote.transcription!.isNotEmpty) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.text_fields,
                    size: 14,
                    color: Colors.blue[700],
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      voiceNote.transcriptionExcerpt,
                      style: TextStyle(
                        color: Colors.blue[700],
                        fontSize: 11,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _showDeleteConfirmation(VoiceNote voiceNote) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Smazat hlasovou poznámku'),
        content: Text('Opravdu chcete smazat poznámku "${voiceNote.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Zrušit'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _removeVoiceNote(voiceNote);
              _voiceService.deleteVoiceNote(voiceNote);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Smazat'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _voiceService.dispose();
    super.dispose();
  }
}
