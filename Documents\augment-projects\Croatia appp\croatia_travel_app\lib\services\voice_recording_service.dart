import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:audioplayers/audioplayers.dart';
import '../models/voice_note.dart';

class VoiceRecordingService {
  static final VoiceRecordingService _instance =
      VoiceRecordingService._internal();
  factory VoiceRecordingService() => _instance;
  VoiceRecordingService._internal();

  final SpeechToText _speechToText = SpeechToText();
  final FlutterTts _flutterTts = FlutterTts();
  final AudioPlayer _audioPlayer = AudioPlayer();

  StreamController<VoiceRecordingEvent>? _eventController;
  StreamController<String>? _transcriptionController;

  bool _isRecording = false;
  bool _isPlaying = false;
  bool _speechEnabled = false;
  String _currentRecordingPath = '';
  Duration _recordingDuration = Duration.zero;
  Timer? _recordingTimer;

  /// Stream událostí nahrávání
  Stream<VoiceRecordingEvent> get eventStream {
    _eventController ??= StreamController<VoiceRecordingEvent>.broadcast();
    return _eventController!.stream;
  }

  /// Stream přepisu řeči
  Stream<String> get transcriptionStream {
    _transcriptionController ??= StreamController<String>.broadcast();
    return _transcriptionController!.stream;
  }

  /// Inicializace služby
  Future<void> initialize() async {
    await _initializeSpeechToText();
    await _initializeTts();
    await _requestPermissions();
  }

  /// Inicializace speech-to-text
  Future<void> _initializeSpeechToText() async {
    try {
      _speechEnabled = await _speechToText.initialize(
        onStatus: (status) {
          _eventController?.add(
            VoiceRecordingEvent(
              type: VoiceEventType.speechStatusChanged,
              status: status,
            ),
          );
        },
        onError: (error) {
          _eventController?.add(
            VoiceRecordingEvent(
              type: VoiceEventType.error,
              error: error.errorMsg,
            ),
          );
        },
      );
    } catch (e) {
      debugPrint('Chyba při inicializaci speech-to-text: $e');
    }
  }

  /// Inicializace text-to-speech
  Future<void> _initializeTts() async {
    try {
      await _flutterTts.setLanguage('cs-CZ');
      await _flutterTts.setSpeechRate(0.8);
      await _flutterTts.setVolume(1.0);
      await _flutterTts.setPitch(1.0);

      _flutterTts.setStartHandler(() {
        _eventController?.add(
          VoiceRecordingEvent(type: VoiceEventType.ttsStarted),
        );
      });

      _flutterTts.setCompletionHandler(() {
        _eventController?.add(
          VoiceRecordingEvent(type: VoiceEventType.ttsCompleted),
        );
      });

      _flutterTts.setErrorHandler((msg) {
        _eventController?.add(
          VoiceRecordingEvent(type: VoiceEventType.error, error: msg),
        );
      });
    } catch (e) {
      debugPrint('Chyba při inicializaci TTS: $e');
    }
  }

  /// Požádání o povolení
  Future<bool> _requestPermissions() async {
    // Na webu nepoužíváme storage permission
    if (kIsWeb) {
      final microphoneStatus = await Permission.microphone.request();
      return microphoneStatus.isGranted;
    } else {
      final microphoneStatus = await Permission.microphone.request();
      final storageStatus = await Permission.storage.request();
      return microphoneStatus.isGranted && storageStatus.isGranted;
    }
  }

  /// Spuštění nahrávání hlasu
  Future<String?> startRecording({String? fileName}) async {
    if (_isRecording) return null;

    final hasPermission = await _requestPermissions();
    if (!hasPermission) {
      _eventController?.add(
        VoiceRecordingEvent(
          type: VoiceEventType.error,
          error: 'Povolení k mikrofonu bylo zamítnuto',
        ),
      );
      return null;
    }

    try {
      final directory = await getApplicationDocumentsDirectory();
      final recordingsDir = Directory('${directory.path}/voice_recordings');
      if (!await recordingsDir.exists()) {
        await recordingsDir.create(recursive: true);
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final name = fileName ?? 'recording_$timestamp';
      _currentRecordingPath = '${recordingsDir.path}/$name.wav';

      // Simulace nahrávání - v reálné aplikaci by se použil record package
      _isRecording = true;
      _recordingDuration = Duration.zero;

      _startRecordingTimer();

      _eventController?.add(
        VoiceRecordingEvent(
          type: VoiceEventType.recordingStarted,
          filePath: _currentRecordingPath,
        ),
      );

      return _currentRecordingPath;
    } catch (e) {
      _eventController?.add(
        VoiceRecordingEvent(
          type: VoiceEventType.error,
          error: 'Chyba při spuštění nahrávání: $e',
        ),
      );
      return null;
    }
  }

  /// Spuštění časovače nahrávání
  void _startRecordingTimer() {
    _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isRecording) {
        timer.cancel();
        return;
      }

      _recordingDuration += const Duration(seconds: 1);
      _eventController?.add(
        VoiceRecordingEvent(
          type: VoiceEventType.recordingProgress,
          duration: _recordingDuration,
        ),
      );
    });
  }

  /// Zastavení nahrávání
  Future<VoiceNote?> stopRecording({String? title, String? description}) async {
    if (!_isRecording) return null;

    try {
      _isRecording = false;
      _recordingTimer?.cancel();

      // Simulace vytvoření audio souboru
      final file = File(_currentRecordingPath);
      await file.writeAsBytes([0]); // Placeholder pro skutečná audio data

      final voiceNote = VoiceNote(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title ?? 'Hlasová poznámka',
        description: description,
        filePath: _currentRecordingPath,
        duration: _recordingDuration,
        createdAt: DateTime.now(),
        fileSize: await file.length(),
      );

      _eventController?.add(
        VoiceRecordingEvent(
          type: VoiceEventType.recordingStopped,
          voiceNote: voiceNote,
        ),
      );

      return voiceNote;
    } catch (e) {
      _eventController?.add(
        VoiceRecordingEvent(
          type: VoiceEventType.error,
          error: 'Chyba při zastavení nahrávání: $e',
        ),
      );
      return null;
    }
  }

  /// Přehrání hlasové poznámky
  Future<void> playVoiceNote(VoiceNote voiceNote) async {
    if (_isPlaying) {
      await stopPlayback();
    }

    try {
      await _audioPlayer.play(DeviceFileSource(voiceNote.filePath));
      _isPlaying = true;

      _eventController?.add(
        VoiceRecordingEvent(
          type: VoiceEventType.playbackStarted,
          voiceNote: voiceNote,
        ),
      );

      _audioPlayer.onPlayerComplete.listen((_) {
        _isPlaying = false;
        _eventController?.add(
          VoiceRecordingEvent(
            type: VoiceEventType.playbackCompleted,
            voiceNote: voiceNote,
          ),
        );
      });
    } catch (e) {
      _eventController?.add(
        VoiceRecordingEvent(
          type: VoiceEventType.error,
          error: 'Chyba při přehrávání: $e',
        ),
      );
    }
  }

  /// Zastavení přehrávání
  Future<void> stopPlayback() async {
    if (_isPlaying) {
      await _audioPlayer.stop();
      _isPlaying = false;

      _eventController?.add(
        VoiceRecordingEvent(type: VoiceEventType.playbackStopped),
      );
    }
  }

  /// Spuštění speech-to-text
  Future<void> startListening({
    String language = 'cs-CZ',
    Function(String)? onResult,
  }) async {
    if (!_speechEnabled) {
      await _initializeSpeechToText();
    }

    if (_speechEnabled) {
      await _speechToText.listen(
        onResult: (result) {
          final transcription = result.recognizedWords;
          _transcriptionController?.add(transcription);
          onResult?.call(transcription);

          _eventController?.add(
            VoiceRecordingEvent(
              type: VoiceEventType.transcriptionUpdated,
              transcription: transcription,
              isPartial: !result.finalResult,
            ),
          );
        },
        localeId: language,
        listenFor: const Duration(minutes: 5),
        pauseFor: const Duration(seconds: 3),
        listenOptions: SpeechListenOptions(partialResults: true),
      );

      _eventController?.add(
        VoiceRecordingEvent(type: VoiceEventType.listeningStarted),
      );
    }
  }

  /// Zastavení speech-to-text
  Future<void> stopListening() async {
    if (_speechToText.isListening) {
      await _speechToText.stop();

      _eventController?.add(
        VoiceRecordingEvent(type: VoiceEventType.listeningStopped),
      );
    }
  }

  /// Text-to-speech
  Future<void> speak(String text, {String language = 'cs-CZ'}) async {
    try {
      await _flutterTts.setLanguage(language);
      await _flutterTts.speak(text);
    } catch (e) {
      _eventController?.add(
        VoiceRecordingEvent(
          type: VoiceEventType.error,
          error: 'Chyba při syntéze řeči: $e',
        ),
      );
    }
  }

  /// Zastavení text-to-speech
  Future<void> stopSpeaking() async {
    await _flutterTts.stop();
  }

  /// Smazání hlasové poznámky
  Future<bool> deleteVoiceNote(VoiceNote voiceNote) async {
    try {
      final file = File(voiceNote.filePath);
      if (await file.exists()) {
        await file.delete();
      }

      _eventController?.add(
        VoiceRecordingEvent(
          type: VoiceEventType.voiceNoteDeleted,
          voiceNote: voiceNote,
        ),
      );

      return true;
    } catch (e) {
      _eventController?.add(
        VoiceRecordingEvent(
          type: VoiceEventType.error,
          error: 'Chyba při mazání: $e',
        ),
      );
      return false;
    }
  }

  /// Získání dostupných jazyků pro speech-to-text
  Future<List<String>> getAvailableLanguages() async {
    if (_speechEnabled) {
      final locales = await _speechToText.locales();
      return locales.map((locale) => locale.localeId).toList();
    }
    return ['cs-CZ', 'en-US', 'hr-HR'];
  }

  /// Kontrola dostupnosti speech-to-text
  bool get isSpeechEnabled => _speechEnabled;

  /// Kontrola stavu nahrávání
  bool get isRecording => _isRecording;

  /// Kontrola stavu přehrávání
  bool get isPlaying => _isPlaying;

  /// Aktuální doba nahrávání
  Duration get recordingDuration => _recordingDuration;

  void dispose() {
    _recordingTimer?.cancel();
    _audioPlayer.dispose();
    _eventController?.close();
    _transcriptionController?.close();
  }
}
