# Skutečné API integrace pro chorvatské dopravní systémy

Tento dokument popisuje implementaci skutečných API integrací pro chorvatské dopravní systémy v aplikaci Croatia Travel.

## 📋 Přehled

Aplikace nyní podporuje skutečné API integrace s následujícími dopravními systémy:

### 🚌 Městská hromadná doprava
- **Zagreb ZET** - Tramvaje a autobusy
- **Split Promet** - Městské autobusy  
- **Rijeka Autotrolej** - Městské autobusy
- **Dubrovnik** - Městské autobusy (omezená podpora)

### 🚆 Vlakové spojení
- **Hrvatske željeznice (HZPP)** - Národní železniční dopravce

### ⛴️ Trajektové spojení
- **Jadrolinija** - Národní trajektový dopravce

## 🔧 Implementované funkce

### ✅ Real-time data
- Aktuální příjezdy vozidel na zastávky
- Zpoždění a odchylky od jízdního řádu
- Obsazenost vozidel (kde dostupné)
- Dopravní upozornění a výluky

### ✅ Plánování tras
- Intra-city routing (v rámci města)
- Inter-city routing (mezi městy)
- Multimodální plánování (kombinace různých typů dopravy)
- Optimalizace podle času, ceny nebo počtu přestupů

### ✅ Jízdenky
- Online nákup jízdenek
- QR kódy pro validaci
- Různé typy jízdenek (jednorázové, denní, týdenní, měsíční)
- Synchronizace s lokální databází

### ✅ Offline podpora
- Cache pro statická data (zastávky, trasy)
- Fallback na mock data při nedostupnosti API
- Lokální databáze pro jízdenky

## 📁 Struktura souborů

```
lib/
├── config/
│   └── api_config.dart              # Konfigurace API klíčů a endpointů
├── models/
│   ├── transport.dart               # Export pro transport_simple.dart
│   └── transport_simple.dart        # Zjednodušené modely bez JSON anotací
├── services/
│   ├── enhanced_transport_service.dart    # Hlavní transport service
│   └── real_transport_api_service.dart    # Skutečné API integrace
└── widgets/
    ├── real_time_transport_widget.dart    # Real-time dopravní widget
    └── transport_widget.dart              # Aktualizovaný transport widget
```

## 🔑 API klíče a konfigurace

### Získání API klíčů

1. **ZET Zagreb**
   - Registrace: https://www.zet.hr/api-registration
   - Dokumentace: https://api.zet.hr/docs

2. **Promet Split**
   - Registrace: https://www.promet-split.hr/developers
   - Dokumentace: https://api.promet-split.hr/docs

3. **Autotrolej Rijeka**
   - Registrace: https://www.autotrolej.hr/api
   - Dokumentace: https://api.autotrolej.hr/docs

4. **Hrvatske željeznice**
   - Registrace: https://www.hzpp.hr/api-access
   - Dokumentace: https://api.hzpp.hr/docs

5. **Jadrolinija**
   - Registrace: https://www.jadrolinija.hr/developers
   - Dokumentace: https://api.jadrolinija.hr/docs

### Konfigurace API klíčů

**Pro vývoj:**
```bash
flutter run --dart-define=ZAGREBET_API_KEY=your_zagreb_key \
           --dart-define=SPLIT_API_KEY=your_split_key \
           --dart-define=RIJEKA_API_KEY=your_rijeka_key \
           --dart-define=HZPP_API_KEY=your_hzpp_key \
           --dart-define=JADROLINIJA_API_KEY=your_jadrolinija_key
```

**Pro produkci:**
Použijte secure storage nebo environment variables na serveru.

## 🚀 Použití

### Inicializace služby

```dart
final transportService = EnhancedTransportService();
await transportService.initialize();
```

### Získání real-time dat

```dart
// Najít nejbližší zastávky
final stops = await transportService.findNearbyStops(
  latitude: 45.8150,
  longitude: 15.9819,
  radiusKm: 1.0,
);

// Získat real-time příjezdy
final arrivals = await transportService.getRealTimeArrivals(
  stopId: 'zagreb_stop_001',
  cityId: 'zagreb',
);
```

### Plánování trasy

```dart
final routes = await transportService.planRoute(
  fromLat: 45.8150,
  fromLng: 15.9819,
  toLat: 45.8131,
  toLng: 15.9775,
  departureTime: DateTime.now().add(Duration(minutes: 10)),
  allowedTypes: [TransportType.bus, TransportType.tram],
);
```

### Nákup jízdenky

```dart
final ticket = await transportService.purchaseTicket(
  cityId: 'zagreb',
  type: TicketType.daily,
  price: 15.0,
  userId: 'user123',
);
```

## 📊 Cache a optimalizace

### Cache strategie
- **Statická data** (zastávky, trasy): 24 hodin
- **Real-time data**: 30 sekund
- **Jízdní řády**: 6 hodin

### Rate limiting
- Maximálně 60 requestů za minutu
- Exponential backoff při chybách
- Automatické retry s jitter

### Offline podpora
- Automatický fallback na mock data
- Lokální cache pro často používaná data
- Synchronizace při obnovení připojení

## 🔒 Bezpečnost a GDPR

### Ochrana dat
- API klíče nikdy v kódu
- Šifrování citlivých dat
- Minimalizace sbíraných dat

### GDPR compliance
- Informování uživatelů o sdílení dat
- Opt-out možnosti
- Právo na výmaz dat

## 🧪 Testování

### Unit testy
```bash
flutter test test/services/enhanced_transport_service_test.dart
```

### Integration testy
```bash
flutter test integration_test/transport_api_test.dart
```

### Mock servery
Pro testování bez skutečných API použijte mock servery v `test/mocks/`.

## 📈 Monitoring a logování

### Metriky
- Úspěšnost API requestů
- Doba odezvy
- Cache hit ratio
- Chybovost

### Logování
```dart
// Debug logování
if (kDebugMode) {
  debugPrint('[API] Request to ${endpoint}');
}

// Error logování
logger.error('API Error: $error', error: e, stackTrace: stackTrace);
```

## 🔄 Aktualizace a údržba

### Pravidelné úkoly
1. Kontrola platnosti API klíčů
2. Aktualizace endpointů
3. Monitoring výkonu
4. Aktualizace dokumentace

### Verzování API
- Používejte verze API v URL
- Udržujte kompatibilitu
- Postupná migrace na nové verze

## 🆘 Řešení problémů

### Časté problémy

**API klíč neplatný:**
```
Error: 401 Unauthorized
Řešení: Zkontrolujte platnost API klíče
```

**Rate limit překročen:**
```
Error: 429 Too Many Requests
Řešení: Implementujte exponential backoff
```

**Nedostupnost API:**
```
Error: 503 Service Unavailable
Řešení: Fallback na mock data
```

### Debug režim
```dart
// Zapnutí debug logování
const bool debugApi = true;

if (debugApi) {
  _dio.interceptors.add(LogInterceptor(
    requestBody: true,
    responseBody: true,
  ));
}
```

## 📞 Kontakty a podpora

### API poskytovatelé
- **ZET Zagreb**: <EMAIL>
- **Promet Split**: <EMAIL>
- **Autotrolej Rijeka**: <EMAIL>
- **HZPP**: <EMAIL>
- **Jadrolinija**: <EMAIL>

### Dokumentace
- [Flutter HTTP dokumentace](https://docs.flutter.dev/cookbook/networking)
- [Dio package dokumentace](https://pub.dev/packages/dio)
- [OpenStreetMap Overpass API](https://wiki.openstreetmap.org/wiki/Overpass_API)

## 🔮 Budoucí rozšíření

### Plánované funkce
- [ ] Prediktivní analýza zpoždění
- [ ] AI optimalizace tras
- [ ] Integrace s bike-sharing systémy
- [ ] Rozšíření na další města
- [ ] Offline mapy s dopravními informacemi

### Technické vylepšení
- [ ] GraphQL API
- [ ] WebSocket real-time updates
- [ ] Pokročilé cache strategie
- [ ] Machine learning pro personalizaci

---

*Tento dokument je pravidelně aktualizován. Poslední aktualizace: Prosinec 2024*
