import 'package:flutter/material.dart';
import '../models/cuisine.dart';

class RecipeDetailWidget extends StatelessWidget {
  final CuisineItem cuisineItem;

  const RecipeDetailWidget({super.key, required this.cuisineItem});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: _getGradientColors(cuisineItem.type),
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        cuisineItem.name,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Recept z ${_getRegionLabel(cuisineItem.region)}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: const BoxDecoration(
                    color: Colors.white24,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _getCuisineIcon(cuisineItem.type),
                    color: Colors.white,
                    size: 32,
                  ),
                ),
              ],
            ),
          ),

          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Základní informace
                  Row(
                    children: [
                      if (cuisineItem.rating != null) ...[
                        Icon(Icons.star, color: Colors.amber[600], size: 20),
                        const SizedBox(width: 4),
                        Text(
                          cuisineItem.rating!.toStringAsFixed(1),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(width: 16),
                      ],
                      if (cuisineItem.averagePrice != null) ...[
                        Icon(Icons.euro, color: Colors.green[600], size: 20),
                        const SizedBox(width: 4),
                        Text(
                          '${cuisineItem.averagePrice!.toStringAsFixed(0)}€',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Dietní označení
                  if (cuisineItem.isVegetarian ||
                      cuisineItem.isVegan ||
                      cuisineItem.isGlutenFree)
                    Wrap(
                      spacing: 8,
                      children: [
                        if (cuisineItem.isVegan)
                          _buildDietBadge('Veganské', Colors.green),
                        if (cuisineItem.isVegetarian && !cuisineItem.isVegan)
                          _buildDietBadge('Vegetariánské', Colors.lightGreen),
                        if (cuisineItem.isGlutenFree)
                          _buildDietBadge('Bezlepkové', Colors.orange),
                      ],
                    ),

                  const SizedBox(height: 16),

                  // Popis
                  Text(
                    cuisineItem.description,
                    style: const TextStyle(fontSize: 16, height: 1.5),
                  ),

                  const SizedBox(height: 24),

                  // Ingredience
                  if (cuisineItem.ingredients.isNotEmpty) ...[
                    const Text(
                      'Ingredience',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ...cuisineItem.ingredients.asMap().entries.map((entry) {
                      final index = entry.key;
                      final ingredient = entry.value;
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: Row(
                          children: [
                            Container(
                              width: 24,
                              height: 24,
                              decoration: BoxDecoration(
                                color: _getTypeColor(cuisineItem.type),
                                shape: BoxShape.circle,
                              ),
                              child: Center(
                                child: Text(
                                  '${index + 1}',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                ingredient,
                                style: const TextStyle(fontSize: 16),
                              ),
                            ),
                          ],
                        ),
                      );
                    }),
                    const SizedBox(height: 24),
                  ],

                  // Recept
                  if (cuisineItem.recipe != null) ...[
                    const Text(
                      'Postup přípravy',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Text(
                        cuisineItem.recipe!,
                        style: const TextStyle(fontSize: 16, height: 1.6),
                      ),
                    ),
                    const SizedBox(height: 24),
                  ],

                  // Alergeny
                  if (cuisineItem.allergens.isNotEmpty) ...[
                    const Text(
                      'Alergeny',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.warning,
                                color: Colors.red.shade600,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Obsahuje:',
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color: Colors.red.shade700,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            cuisineItem.allergens.join(', '),
                            style: TextStyle(color: Colors.red.shade700),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),
                  ],

                  // Restaurace
                  if (cuisineItem.restaurants.isNotEmpty) ...[
                    const Text(
                      'Kde ochutnat',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Tento pokrm můžete ochutnat v ${cuisineItem.restaurants.length} doporučených restauracích.',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton.icon(
                      onPressed: () {
                        // Zobrazit restaurace na mapě
                      },
                      icon: const Icon(Icons.map),
                      label: const Text('Zobrazit na mapě'),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDietBadge(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  List<Color> _getGradientColors(CuisineType type) {
    switch (type) {
      case CuisineType.mainDish:
        return [Colors.red.shade400, Colors.red.shade600];
      case CuisineType.appetizer:
        return [Colors.green.shade400, Colors.green.shade600];
      case CuisineType.dessert:
        return [Colors.pink.shade400, Colors.pink.shade600];
      case CuisineType.drink:
        return [Colors.blue.shade400, Colors.blue.shade600];
      case CuisineType.snack:
        return [Colors.orange.shade400, Colors.orange.shade600];
      case CuisineType.soup:
        return [Colors.amber.shade400, Colors.amber.shade600];
      case CuisineType.seafood:
        return [Colors.cyan.shade400, Colors.cyan.shade600];
      case CuisineType.meat:
        return [Colors.brown.shade400, Colors.brown.shade600];
      case CuisineType.pasta:
        return [Colors.yellow.shade400, Colors.yellow.shade600];
      case CuisineType.bread:
        return [Colors.deepOrange.shade400, Colors.deepOrange.shade600];
    }
  }

  IconData _getCuisineIcon(CuisineType type) {
    switch (type) {
      case CuisineType.mainDish:
        return Icons.dinner_dining;
      case CuisineType.appetizer:
        return Icons.tapas;
      case CuisineType.dessert:
        return Icons.cake;
      case CuisineType.drink:
        return Icons.local_drink;
      case CuisineType.snack:
        return Icons.fastfood;
      case CuisineType.soup:
        return Icons.soup_kitchen;
      case CuisineType.seafood:
        return Icons.set_meal;
      case CuisineType.meat:
        return Icons.kebab_dining;
      case CuisineType.pasta:
        return Icons.ramen_dining;
      case CuisineType.bread:
        return Icons.bakery_dining;
    }
  }

  Color _getTypeColor(CuisineType type) {
    switch (type) {
      case CuisineType.mainDish:
        return Colors.red;
      case CuisineType.appetizer:
        return Colors.green;
      case CuisineType.dessert:
        return Colors.pink;
      case CuisineType.drink:
        return Colors.blue;
      case CuisineType.snack:
        return Colors.orange;
      case CuisineType.soup:
        return Colors.amber;
      case CuisineType.seafood:
        return Colors.cyan;
      case CuisineType.meat:
        return Colors.brown;
      case CuisineType.pasta:
        return Colors.yellow;
      case CuisineType.bread:
        return Colors.deepOrange;
    }
  }

  String _getRegionLabel(String region) {
    switch (region) {
      case 'istria':
        return 'Istrie';
      case 'dalmatia':
        return 'Dalmácie';
      case 'slavonia':
        return 'Slavonie';
      case 'lika':
        return 'Lika';
      case 'zagreb':
        return 'Zagreb';
      case 'all':
        return 'celého Chorvatska';
      default:
        return region;
    }
  }
}
