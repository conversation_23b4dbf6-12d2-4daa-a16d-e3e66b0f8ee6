name: croatia_travel_app
description: "Chorvatská cestovatelská aplikace s offline funkcemi, mapami, AR a mnoho da<PERSON>."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI a navigace
  cupertino_icons: ^1.0.8
  go_router: ^15.1.3
  flutter_bloc: ^9.1.1
  google_fonts: ^6.2.1

  # Mapy a lokace
  google_maps_flutter: ^2.9.0
  google_maps_cluster_manager: ^3.0.0+1
  geolocator: ^14.0.1
  geocoding: ^4.0.0

  # Offline funkcionalita
  sqflite: ^2.4.1
  connectivity_plus: ^6.0.5
  path_provider: ^2.1.4
  dio: ^5.7.0

  # Camera & AR funkce
  camera: ^0.11.1
  qr_code_scanner: ^1.0.1
  image_picker: ^1.0.7
  image: ^4.1.7

  # Backend & Cloud
  firebase_core: ^3.13.1
  firebase_auth: ^5.5.4
  cloud_firestore: ^5.6.8
  firebase_storage: ^12.4.6
  firebase_messaging: ^15.2.6

  # Personalizace & ML
  flutter_local_notifications: ^19.2.1
  shared_preferences: ^2.3.2

  # UI/UX vylepšení
  flutter_animate: ^4.5.0
  lottie: ^3.1.2
  shimmer: ^3.0.0
  flutter_staggered_animations: ^1.1.1

  # Accessibility & Internationalization
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2

  # Performance & Analytics
  firebase_analytics: ^11.4.6
  firebase_crashlytics: ^4.3.6
  firebase_performance: ^0.10.1+6

  # Advanced UI
  flutter_svg: ^2.0.10+1
  auto_size_text: ^3.0.0
  flutter_screenutil: ^5.9.3

  # Databáze a storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Síť a API
  http: ^1.2.2

  # HTML parsing (pro AI scraping)
  html: ^0.15.4

  # Archive (pro GTFS soubory)
  archive: ^4.0.7

  # Secure storage
  flutter_secure_storage: ^9.0.0

  # Audio a hlasové funkce
  audioplayers: ^6.1.0
  speech_to_text: ^7.0.0
  flutter_tts: ^4.1.0
  record: ^6.0.0

  # Obrázky a média
  cached_network_image: ^3.4.1
  photo_view: ^0.15.0

  # Sdílení a export
  share_plus: ^11.0.0
  pdf: ^3.11.1
  printing: ^5.13.2
  url_launcher: ^6.3.1

  # JSON serialization
  json_annotation: ^4.9.0

  # Utility
  uuid: ^4.5.1
  permission_handler: ^12.0.0+1
  device_info_plus: ^11.4.0
  battery_plus: ^6.0.3
  path: ^1.9.0

  # Charts a grafy
  fl_chart: ^1.0.0

  # 💳 STRIPE & PAYPAL PAYMENT INTEGRATION
  flutter_stripe: ^11.5.0
  flutter_paypal_payment: ^1.0.8
  flutter_inappwebview: ^6.1.5

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0

  # Code generation
  build_runner: ^2.4.13
  json_serializable: ^6.8.0
  hive_generator: ^2.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/maps/
    - assets/audio/
    - assets/data/
    - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # Fonty budou načteny přes Google Fonts package
