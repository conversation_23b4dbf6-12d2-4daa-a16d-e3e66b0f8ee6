import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import '../models/diary_entry.dart';
import '../models/place.dart';
import '../models/route_plan.dart';

class PrintService {
  static final PrintService _instance = PrintService._internal();
  factory PrintService() => _instance;
  PrintService._internal();

  final Dio _dio = Dio();
  static const String _printApiUrl = 'https://api.croatia-print.com';

  StreamController<PrintOrderEvent>? _orderController;

  /// Stream událostí objednávek
  Stream<PrintOrderEvent> get orderStream {
    _orderController ??= StreamController<PrintOrderEvent>.broadcast();
    return _orderController!.stream;
  }

  /// Vytvoření fotoknihy z deníku
  Future<PrintOrder?> createPhotoBook({
    required List<DiaryEntry> entries,
    required PhotoBookTemplate template,
    required PhotoBookSize size,
    required String title,
    String? subtitle,
    bool includeMap = true,
    bool includeStatistics = true,
    CoverType coverType = CoverType.hardcover,
  }) async {
    try {
      _orderController?.add(
        PrintOrderEvent(
          type: PrintEventType.processingStarted,
          message: 'Vytváření fotoknihy...',
        ),
      );

      // Příprava dat pro fotoknihu
      final bookData = await _preparePhotoBookData(
        entries: entries,
        template: template,
        size: size,
        title: title,
        subtitle: subtitle,
        includeMap: includeMap,
        includeStatistics: includeStatistics,
        coverType: coverType,
      );

      // Odeslání na tiskový server
      final response = await _dio.post(
        '$_printApiUrl/photobook/create',
        data: bookData,
        options: Options(headers: {'Content-Type': 'application/json'}),
      );

      if (response.statusCode == 200) {
        final order = PrintOrder.fromJson(response.data);

        _orderController?.add(
          PrintOrderEvent(
            type: PrintEventType.orderCreated,
            order: order,
            message: 'Fotokniha byla vytvořena',
          ),
        );

        return order;
      }
    } catch (e) {
      _orderController?.add(
        PrintOrderEvent(
          type: PrintEventType.error,
          error: 'Chyba při vytváření fotoknihy: $e',
        ),
      );
    }
    return null;
  }

  /// Vytvoření kalendáře
  Future<PrintOrder?> createCalendar({
    required List<DiaryEntry> entries,
    required int year,
    required CalendarType type,
    required CalendarSize size,
    bool includeHolidays = true,
    bool includeMoonPhases = false,
  }) async {
    try {
      _orderController?.add(
        PrintOrderEvent(
          type: PrintEventType.processingStarted,
          message: 'Vytváření kalendáře...',
        ),
      );

      final calendarData = await _prepareCalendarData(
        entries: entries,
        year: year,
        type: type,
        size: size,
        includeHolidays: includeHolidays,
        includeMoonPhases: includeMoonPhases,
      );

      final response = await _dio.post(
        '$_printApiUrl/calendar/create',
        data: calendarData,
      );

      if (response.statusCode == 200) {
        final order = PrintOrder.fromJson(response.data);

        _orderController?.add(
          PrintOrderEvent(
            type: PrintEventType.orderCreated,
            order: order,
            message: 'Kalendář byl vytvořen',
          ),
        );

        return order;
      }
    } catch (e) {
      _orderController?.add(
        PrintOrderEvent(
          type: PrintEventType.error,
          error: 'Chyba při vytváření kalendáře: $e',
        ),
      );
    }
    return null;
  }

  /// Tisk jednotlivých fotografií
  Future<PrintOrder?> printPhotos({
    required List<String> photoPaths,
    required PhotoSize size,
    required PhotoPaper paper,
    int quantity = 1,
    bool borderless = true,
  }) async {
    try {
      _orderController?.add(
        PrintOrderEvent(
          type: PrintEventType.processingStarted,
          message: 'Příprava fotografií k tisku...',
        ),
      );

      final photoData = await _preparePhotoData(
        photoPaths: photoPaths,
        size: size,
        paper: paper,
        quantity: quantity,
        borderless: borderless,
      );

      final response = await _dio.post(
        '$_printApiUrl/photos/print',
        data: photoData,
      );

      if (response.statusCode == 200) {
        final order = PrintOrder.fromJson(response.data);

        _orderController?.add(
          PrintOrderEvent(
            type: PrintEventType.orderCreated,
            order: order,
            message: 'Fotografie byly připraveny k tisku',
          ),
        );

        return order;
      }
    } catch (e) {
      _orderController?.add(
        PrintOrderEvent(
          type: PrintEventType.error,
          error: 'Chyba při přípravě fotografií: $e',
        ),
      );
    }
    return null;
  }

  /// Vytvoření mapy trasy
  Future<PrintOrder?> createRouteMap({
    required RoutePlan route,
    required List<Place> places,
    required MapSize size,
    required MapStyle style,
    bool includeElevation = false,
    bool includePhotos = true,
  }) async {
    try {
      _orderController?.add(
        PrintOrderEvent(
          type: PrintEventType.processingStarted,
          message: 'Vytváření mapy trasy...',
        ),
      );

      final mapData = await _prepareRouteMapData(
        route: route,
        places: places,
        size: size,
        style: style,
        includeElevation: includeElevation,
        includePhotos: includePhotos,
      );

      final response = await _dio.post(
        '$_printApiUrl/map/create',
        data: mapData,
      );

      if (response.statusCode == 200) {
        final order = PrintOrder.fromJson(response.data);

        _orderController?.add(
          PrintOrderEvent(
            type: PrintEventType.orderCreated,
            order: order,
            message: 'Mapa trasy byla vytvořena',
          ),
        );

        return order;
      }
    } catch (e) {
      _orderController?.add(
        PrintOrderEvent(
          type: PrintEventType.error,
          error: 'Chyba při vytváření mapy: $e',
        ),
      );
    }
    return null;
  }

  /// Získání stavu objednávky
  Future<PrintOrder?> getOrderStatus(String orderId) async {
    try {
      final response = await _dio.get('$_printApiUrl/order/$orderId/status');

      if (response.statusCode == 200) {
        return PrintOrder.fromJson(response.data);
      }
    } catch (e) {
      debugPrint('Chyba při získávání stavu objednávky: $e');
    }
    return null;
  }

  /// Získání historie objednávek
  Future<List<PrintOrder>> getOrderHistory() async {
    try {
      final response = await _dio.get('$_printApiUrl/orders/history');

      if (response.statusCode == 200) {
        final List<dynamic> ordersData = response.data['orders'];
        return ordersData.map((data) => PrintOrder.fromJson(data)).toList();
      }
    } catch (e) {
      debugPrint('Chyba při získávání historie: $e');
    }
    return [];
  }

  /// Zrušení objednávky
  Future<bool> cancelOrder(String orderId) async {
    try {
      final response = await _dio.post('$_printApiUrl/order/$orderId/cancel');

      if (response.statusCode == 200) {
        _orderController?.add(
          PrintOrderEvent(
            type: PrintEventType.orderCancelled,
            message: 'Objednávka byla zrušena',
          ),
        );
        return true;
      }
    } catch (e) {
      _orderController?.add(
        PrintOrderEvent(
          type: PrintEventType.error,
          error: 'Chyba při rušení objednávky: $e',
        ),
      );
    }
    return false;
  }

  /// Příprava dat pro fotoknihu
  Future<Map<String, dynamic>> _preparePhotoBookData({
    required List<DiaryEntry> entries,
    required PhotoBookTemplate template,
    required PhotoBookSize size,
    required String title,
    String? subtitle,
    required bool includeMap,
    required bool includeStatistics,
    required CoverType coverType,
  }) async {
    // Příprava stránek fotoknihy
    final pages = <Map<String, dynamic>>[];

    // Titulní stránka
    pages.add({
      'type': 'cover',
      'title': title,
      'subtitle': subtitle,
      'background_image': await _selectCoverImage(entries),
    });

    // Stránky s deníkovými záznamy
    for (final entry in entries) {
      pages.add({
        'type': 'diary_entry',
        'title': entry.title,
        'content': entry.content,
        'date': entry.date.toIso8601String(),
        'location': entry.location,
        'photos': entry.photos,
        'mood': entry.mood?.name,
        'rating': entry.rating,
      });
    }

    // Mapa tras (pokud je požadována)
    if (includeMap) {
      pages.add({
        'type': 'map',
        'title': 'Mapa cest',
        'locations': entries
            .where((e) => e.hasCoordinates)
            .map(
              (e) => {
                'lat': e.latitude,
                'lng': e.longitude,
                'name': e.location,
              },
            )
            .toList(),
      });
    }

    // Statistiky (pokud jsou požadovány)
    if (includeStatistics) {
      pages.add({
        'type': 'statistics',
        'title': 'Statistiky cesty',
        'total_entries': entries.length,
        'total_photos': entries.fold<int>(0, (sum, e) => sum + e.photos.length),
        'countries_visited': ['Chorvatsko'], // Můžeme rozšířit
        'favorite_places': entries
            .where((e) => e.rating != null && e.rating! >= 4.5)
            .map((e) => e.location)
            .toList(),
      });
    }

    return {
      'template': template.name,
      'size': size.name,
      'cover_type': coverType.name,
      'pages': pages,
      'created_at': DateTime.now().toIso8601String(),
    };
  }

  /// Příprava dat pro kalendář
  Future<Map<String, dynamic>> _prepareCalendarData({
    required List<DiaryEntry> entries,
    required int year,
    required CalendarType type,
    required CalendarSize size,
    required bool includeHolidays,
    required bool includeMoonPhases,
  }) async {
    // Seskupení záznamů podle měsíců
    final entriesByMonth = <int, List<DiaryEntry>>{};
    for (final entry in entries) {
      if (entry.date.year == year) {
        final month = entry.date.month;
        entriesByMonth[month] ??= [];
        entriesByMonth[month]!.add(entry);
      }
    }

    return {
      'year': year,
      'type': type.name,
      'size': size.name,
      'include_holidays': includeHolidays,
      'include_moon_phases': includeMoonPhases,
      'entries_by_month': entriesByMonth.map(
        (month, entries) => MapEntry(
          month.toString(),
          entries
              .map(
                (e) => {
                  'date': e.date.day,
                  'title': e.title,
                  'photo': e.photos.isNotEmpty ? e.photos.first : null,
                },
              )
              .toList(),
        ),
      ),
      'created_at': DateTime.now().toIso8601String(),
    };
  }

  /// Příprava dat pro fotografie
  Future<Map<String, dynamic>> _preparePhotoData({
    required List<String> photoPaths,
    required PhotoSize size,
    required PhotoPaper paper,
    required int quantity,
    required bool borderless,
  }) async {
    return {
      'photos': photoPaths,
      'size': size.name,
      'paper': paper.name,
      'quantity': quantity,
      'borderless': borderless,
      'created_at': DateTime.now().toIso8601String(),
    };
  }

  /// Příprava dat pro mapu trasy
  Future<Map<String, dynamic>> _prepareRouteMapData({
    required RoutePlan route,
    required List<Place> places,
    required MapSize size,
    required MapStyle style,
    required bool includeElevation,
    required bool includePhotos,
  }) async {
    return {
      'route': {
        'name': route.name,
        'points': route.points
            .map((p) => {'lat': p.latitude, 'lng': p.longitude, 'name': p.name})
            .toList(),
      },
      'places': places
          .map(
            (p) => {
              'lat': p.latitude,
              'lng': p.longitude,
              'name': p.name,
              'type': p.type.name,
              'photos': includePhotos ? p.images : [],
            },
          )
          .toList(),
      'size': size.name,
      'style': style.name,
      'include_elevation': includeElevation,
      'include_photos': includePhotos,
      'created_at': DateTime.now().toIso8601String(),
    };
  }

  /// Výběr obrázku pro obálku
  Future<String?> _selectCoverImage(List<DiaryEntry> entries) async {
    // Najít nejlepší fotografii pro obálku
    for (final entry in entries) {
      if (entry.photos.isNotEmpty &&
          entry.rating != null &&
          entry.rating! >= 4.0) {
        return entry.photos.first;
      }
    }

    // Fallback na první dostupnou fotografii
    for (final entry in entries) {
      if (entry.photos.isNotEmpty) {
        return entry.photos.first;
      }
    }

    return null;
  }

  void dispose() {
    _orderController?.close();
  }
}

// Modely pro tiskové služby
class PrintOrder {
  final String id;
  final PrintOrderType type;
  final PrintOrderStatus status;
  final double price;
  final String currency;
  final DateTime createdAt;
  final DateTime? estimatedDelivery;
  final String? trackingNumber;
  final Map<String, dynamic> details;

  PrintOrder({
    required this.id,
    required this.type,
    required this.status,
    required this.price,
    this.currency = 'EUR',
    required this.createdAt,
    this.estimatedDelivery,
    this.trackingNumber,
    this.details = const {},
  });

  factory PrintOrder.fromJson(Map<String, dynamic> json) {
    return PrintOrder(
      id: json['id'],
      type: PrintOrderType.values.firstWhere((t) => t.name == json['type']),
      status: PrintOrderStatus.values.firstWhere(
        (s) => s.name == json['status'],
      ),
      price: json['price'].toDouble(),
      currency: json['currency'] ?? 'EUR',
      createdAt: DateTime.parse(json['created_at']),
      estimatedDelivery: json['estimated_delivery'] != null
          ? DateTime.parse(json['estimated_delivery'])
          : null,
      trackingNumber: json['tracking_number'],
      details: json['details'] ?? {},
    );
  }
}

class PrintOrderEvent {
  final PrintEventType type;
  final PrintOrder? order;
  final String? message;
  final String? error;

  PrintOrderEvent({required this.type, this.order, this.message, this.error});
}

// Enums
enum PrintOrderType { photoBook, calendar, photos, map }

enum PrintOrderStatus {
  pending,
  processing,
  printing,
  shipped,
  delivered,
  cancelled,
}

enum PrintEventType { processingStarted, orderCreated, orderCancelled, error }

enum PhotoBookTemplate { classic, modern, vintage, travel }

enum PhotoBookSize { small, medium, large, extraLarge }

enum CoverType { softcover, hardcover, premium }

enum CalendarType { wall, desk, pocket }

enum CalendarSize { a4, a3, a5 }

enum PhotoSize { small, medium, large, extraLarge }

enum PhotoPaper { matte, glossy, pearl }

enum MapSize { a4, a3, a2, a1 }

enum MapStyle { classic, satellite, terrain, artistic }
