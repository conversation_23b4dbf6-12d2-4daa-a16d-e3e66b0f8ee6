import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import '../models/city_services.dart';
import '../services/city_services_service.dart';

class IssueReportingWidget extends StatefulWidget {
  const IssueReportingWidget({super.key});

  @override
  State<IssueReportingWidget> createState() => _IssueReportingWidgetState();
}

class _IssueReportingWidgetState extends State<IssueReportingWidget> {
  final CityServicesService _cityService = CityServicesService();

  List<IssueReport> _nearbyIssues = [];
  List<IssueReport> _myReports = [];
  bool _isLoading = false;
  Position? _currentPosition;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      _currentPosition = await Geolocator.getCurrentPosition();

      // Načtení problé<PERSON>ů v okolí
      final nearbyIssues = await _cityService.getIssuesInArea(
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
      );

      setState(() {
        _nearbyIssues = nearbyIssues;
        // Simulace vlastních hlášení
        _myReports = nearbyIssues.take(2).toList();
      });
    } catch (e) {
      _showError('Chyba při načítání dat: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildQuickActions(),
            const SizedBox(height: 24),
            if (_myReports.isNotEmpty) ...[
              _buildMyReports(),
              const SizedBox(height: 24),
            ],
            _buildNearbyIssues(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Rychlé akce',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _reportNewIssue,
                    icon: const Icon(Icons.add_circle),
                    label: const Text('Nahlásit problém'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _viewIssueMap,
                    icon: const Icon(Icons.map),
                    label: const Text('Mapa problémů'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMyReports() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Moje hlášení',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _myReports.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final issue = _myReports[index];
                return _buildIssueTile(issue, isMyReport: true);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNearbyIssues() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Problémy v okolí',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else if (_nearbyIssues.isEmpty)
              const Text('Žádné problémy v okolí')
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _nearbyIssues.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final issue = _nearbyIssues[index];
                  return _buildIssueTile(issue);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildIssueTile(IssueReport issue, {bool isMyReport = false}) {
    return ListTile(
      leading: Icon(
        _getIssueCategoryIcon(issue.category),
        color: _getIssuePriorityColor(issue.priority),
      ),
      title: Text(issue.title),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(issue.description, maxLines: 2, overflow: TextOverflow.ellipsis),
          const SizedBox(height: 4),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _getIssueStatusColor(issue.status),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getIssueStatusText(issue.status),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                _getIssuePriorityText(issue.priority),
                style: TextStyle(
                  color: _getIssuePriorityColor(issue.priority),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          Text(
            'Nahlášeno: ${_formatDate(issue.reportedAt)}',
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
      trailing: isMyReport
          ? PopupMenuButton<String>(
              onSelected: (value) => _handleMyReportAction(issue, value),
              itemBuilder: (context) => [
                const PopupMenuItem(value: 'view', child: Text('Zobrazit')),
                const PopupMenuItem(
                  value: 'update',
                  child: Text('Aktualizovat'),
                ),
              ],
            )
          : const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () => _showIssueDetails(issue),
    );
  }

  IconData _getIssueCategoryIcon(IssueCategory category) {
    switch (category) {
      case IssueCategory.roads:
        return Icons.add_road;
      case IssueCategory.lighting:
        return Icons.lightbulb;
      case IssueCategory.waste:
        return Icons.delete;
      case IssueCategory.water:
        return Icons.water_drop;
      case IssueCategory.parks:
        return Icons.park;
      case IssueCategory.noise:
        return Icons.volume_up;
      case IssueCategory.safety:
        return Icons.security;
      case IssueCategory.other:
        return Icons.report_problem;
    }
  }

  Color _getIssuePriorityColor(IssuePriority priority) {
    switch (priority) {
      case IssuePriority.low:
        return Colors.green;
      case IssuePriority.medium:
        return Colors.orange;
      case IssuePriority.high:
        return Colors.red;
      case IssuePriority.urgent:
        return Colors.purple;
    }
  }

  String _getIssuePriorityText(IssuePriority priority) {
    switch (priority) {
      case IssuePriority.low:
        return 'Nízká';
      case IssuePriority.medium:
        return 'Střední';
      case IssuePriority.high:
        return 'Vysoká';
      case IssuePriority.urgent:
        return 'Urgentní';
    }
  }

  Color _getIssueStatusColor(IssueStatus status) {
    switch (status) {
      case IssueStatus.reported:
        return Colors.blue;
      case IssueStatus.assigned:
        return Colors.orange;
      case IssueStatus.inProgress:
        return Colors.purple;
      case IssueStatus.resolved:
        return Colors.green;
      case IssueStatus.closed:
        return Colors.grey;
    }
  }

  String _getIssueStatusText(IssueStatus status) {
    switch (status) {
      case IssueStatus.reported:
        return 'Nahlášeno';
      case IssueStatus.assigned:
        return 'Přiřazeno';
      case IssueStatus.inProgress:
        return 'Řeší se';
      case IssueStatus.resolved:
        return 'Vyřešeno';
      case IssueStatus.closed:
        return 'Uzavřeno';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}.${date.month}.${date.year}';
  }

  void _reportNewIssue() {
    showDialog(
      context: context,
      builder: (context) => _ReportIssueDialog(
        onSubmit: (issue) async {
          final scaffoldMessenger = ScaffoldMessenger.of(context);

          final issueReport = IssueReport(
            id: 'issue_${DateTime.now().millisecondsSinceEpoch}',
            title: issue['title'],
            description: issue['description'],
            category: issue['category'],
            priority: issue['priority'],
            latitude: _currentPosition!.latitude,
            longitude: _currentPosition!.longitude,
            address: issue['address'],
            reporterName: issue['reporterName'],
            reporterEmail: issue['reporterEmail'],
            reporterPhone: issue['reporterPhone'],
            reportedAt: DateTime.now(),
          );

          final result = await _cityService.reportIssue(issueReport);

          if (mounted && result.isNotEmpty) {
            scaffoldMessenger.showSnackBar(
              const SnackBar(content: Text('Problém byl úspěšně nahlášen')),
            );
            _loadData(); // Obnovení seznamu
          } else if (mounted) {
            _showError('Chyba při hlášení problému');
          }
        },
      ),
    );
  }

  void _viewIssueMap() {
    // Implementace mapy problémů
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Otevírám mapu problémů...')));
  }

  void _handleMyReportAction(IssueReport issue, String action) {
    switch (action) {
      case 'view':
        _showIssueDetails(issue);
        break;
      case 'update':
        _updateIssue(issue);
        break;
    }
  }

  void _updateIssue(IssueReport issue) {
    // Implementace aktualizace problému
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Aktualizuji problém: ${issue.title}')),
    );
  }

  void _showIssueDetails(IssueReport issue) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(issue.title),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(issue.description),
              const SizedBox(height: 8),
              Text('Kategorie: ${_getIssueCategoryText(issue.category)}'),
              Text('Priorita: ${_getIssuePriorityText(issue.priority)}'),
              Text('Stav: ${_getIssueStatusText(issue.status)}'),
              Text('Adresa: ${issue.address}'),
              Text('Nahlášeno: ${_formatDate(issue.reportedAt)}'),
              if (issue.resolvedAt != null)
                Text('Vyřešeno: ${_formatDate(issue.resolvedAt!)}'),
              if (issue.reporterName != null)
                Text('Nahlásil: ${issue.reporterName}'),
              if (issue.updates.isNotEmpty) ...[
                const SizedBox(height: 8),
                const Text(
                  'Aktualizace:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                ...issue.updates.map(
                  (update) => Padding(
                    padding: const EdgeInsets.only(left: 8, top: 4),
                    child: Text(
                      '${_formatDate(update.timestamp)}: ${update.message}',
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zavřít'),
          ),
        ],
      ),
    );
  }

  String _getIssueCategoryText(IssueCategory category) {
    switch (category) {
      case IssueCategory.roads:
        return 'Silnice';
      case IssueCategory.lighting:
        return 'Osvětlení';
      case IssueCategory.waste:
        return 'Odpad';
      case IssueCategory.water:
        return 'Voda';
      case IssueCategory.parks:
        return 'Parky';
      case IssueCategory.noise:
        return 'Hluk';
      case IssueCategory.safety:
        return 'Bezpečnost';
      case IssueCategory.other:
        return 'Ostatní';
    }
  }
}

class _ReportIssueDialog extends StatefulWidget {
  final Function(Map<String, dynamic>) onSubmit;

  const _ReportIssueDialog({required this.onSubmit});

  @override
  State<_ReportIssueDialog> createState() => _ReportIssueDialogState();
}

class _ReportIssueDialogState extends State<_ReportIssueDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _addressController = TextEditingController();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();

  IssueCategory _selectedCategory = IssueCategory.other;
  IssuePriority _selectedPriority = IssuePriority.medium;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Nahlásit problém'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(labelText: 'Název problému'),
                validator: (value) =>
                    value?.isEmpty == true ? 'Povinné pole' : null,
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(labelText: 'Popis'),
                maxLines: 3,
                validator: (value) =>
                    value?.isEmpty == true ? 'Povinné pole' : null,
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<IssueCategory>(
                value: _selectedCategory,
                decoration: const InputDecoration(labelText: 'Kategorie'),
                items: IssueCategory.values
                    .map(
                      (category) => DropdownMenuItem(
                        value: category,
                        child: Text(_getCategoryText(category)),
                      ),
                    )
                    .toList(),
                onChanged: (value) =>
                    setState(() => _selectedCategory = value!),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<IssuePriority>(
                value: _selectedPriority,
                decoration: const InputDecoration(labelText: 'Priorita'),
                items: IssuePriority.values
                    .map(
                      (priority) => DropdownMenuItem(
                        value: priority,
                        child: Text(_getPriorityText(priority)),
                      ),
                    )
                    .toList(),
                onChanged: (value) =>
                    setState(() => _selectedPriority = value!),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _addressController,
                decoration: const InputDecoration(labelText: 'Adresa'),
                validator: (value) =>
                    value?.isEmpty == true ? 'Povinné pole' : null,
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Vaše jméno (volitelné)',
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(
                  labelText: 'E-mail (volitelné)',
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'Telefon (volitelné)',
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Zrušit'),
        ),
        ElevatedButton(onPressed: _submitForm, child: const Text('Nahlásit')),
      ],
    );
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      widget.onSubmit({
        'title': _titleController.text,
        'description': _descriptionController.text,
        'category': _selectedCategory,
        'priority': _selectedPriority,
        'address': _addressController.text,
        'reporterName': _nameController.text.isNotEmpty
            ? _nameController.text
            : null,
        'reporterEmail': _emailController.text.isNotEmpty
            ? _emailController.text
            : null,
        'reporterPhone': _phoneController.text.isNotEmpty
            ? _phoneController.text
            : null,
      });
      Navigator.of(context).pop();
    }
  }

  String _getCategoryText(IssueCategory category) {
    switch (category) {
      case IssueCategory.roads:
        return 'Silnice';
      case IssueCategory.lighting:
        return 'Osvětlení';
      case IssueCategory.waste:
        return 'Odpad';
      case IssueCategory.water:
        return 'Voda';
      case IssueCategory.parks:
        return 'Parky';
      case IssueCategory.noise:
        return 'Hluk';
      case IssueCategory.safety:
        return 'Bezpečnost';
      case IssueCategory.other:
        return 'Ostatní';
    }
  }

  String _getPriorityText(IssuePriority priority) {
    switch (priority) {
      case IssuePriority.low:
        return 'Nízká';
      case IssuePriority.medium:
        return 'Střední';
      case IssuePriority.high:
        return 'Vysoká';
      case IssuePriority.urgent:
        return 'Urgentní';
    }
  }
}
