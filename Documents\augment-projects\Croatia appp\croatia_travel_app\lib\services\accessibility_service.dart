import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Typy barvosleposti
enum ColorBlindMode {
  none,
  protanopia, // Červeno-zelená barvoslepost (chybí červené)
  deuteranopia, // Červeno-zelená barvoslepost (chybí zelené)
  tritanopia, // Modro-žlutá barvoslepost
}

/// Služba pro accessibility a usnadnění přístupu
class AccessibilityService extends ChangeNotifier {
  static final AccessibilityService _instance =
      AccessibilityService._internal();
  factory AccessibilityService() => _instance;
  AccessibilityService._internal();

  static const String _highContrastKey = 'high_contrast';
  static const String _largeTextKey = 'large_text';
  static const String _reduceMotionKey = 'reduce_motion';
  static const String _screenReaderKey = 'screen_reader_enabled';
  static const String _voiceGuidanceKey = 'voice_guidance';
  static const String _hapticFeedbackKey = 'haptic_feedback';
  static const String _colorBlindModeKey = 'color_blind_mode';

  bool _highContrastMode = false;
  bool _largeTextMode = false;
  bool _reduceMotionMode = false;
  bool _screenReaderEnabled = false;
  bool _voiceGuidanceEnabled = false;
  bool _hapticFeedbackEnabled = true;
  ColorBlindMode _colorBlindMode = ColorBlindMode.none;
  bool _isInitialized = false;

  // Gettery
  bool get highContrastMode => _highContrastMode;
  bool get largeTextMode => _largeTextMode;
  bool get reduceMotionMode => _reduceMotionMode;
  bool get screenReaderEnabled => _screenReaderEnabled;
  bool get voiceGuidanceEnabled => _voiceGuidanceEnabled;
  bool get hapticFeedbackEnabled => _hapticFeedbackEnabled;
  ColorBlindMode get colorBlindMode => _colorBlindMode;
  bool get isInitialized => _isInitialized;

  /// Inicializuje accessibility službu
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      final prefs = await SharedPreferences.getInstance();

      // Načte uložené nastavení
      _highContrastMode = prefs.getBool(_highContrastKey) ?? false;
      _largeTextMode = prefs.getBool(_largeTextKey) ?? false;
      _reduceMotionMode = prefs.getBool(_reduceMotionKey) ?? false;
      _screenReaderEnabled = prefs.getBool(_screenReaderKey) ?? false;
      _voiceGuidanceEnabled = prefs.getBool(_voiceGuidanceKey) ?? false;
      _hapticFeedbackEnabled = prefs.getBool(_hapticFeedbackKey) ?? true;

      final colorBlindIndex = prefs.getInt(_colorBlindModeKey) ?? 0;
      _colorBlindMode = ColorBlindMode.values[colorBlindIndex];

      _isInitialized = true;
      notifyListeners();

      debugPrint('Accessibility služba inicializována');
    } catch (e) {
      debugPrint('Chyba při inicializaci accessibility služby: $e');
      _isInitialized = true;
    }
  }

  /// Zapne/vypne vysoký kontrast
  Future<void> setHighContrastMode(bool enabled) async {
    if (_highContrastMode == enabled) return;

    try {
      _highContrastMode = enabled;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_highContrastKey, enabled);

      notifyListeners();
      debugPrint('Vysoký kontrast ${enabled ? 'zapnut' : 'vypnut'}');
    } catch (e) {
      debugPrint('Chyba při změně vysokého kontrastu: $e');
    }
  }

  /// Zapne/vypne velký text
  Future<void> setLargeTextMode(bool enabled) async {
    if (_largeTextMode == enabled) return;

    try {
      _largeTextMode = enabled;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_largeTextKey, enabled);

      notifyListeners();
      debugPrint('Velký text ${enabled ? 'zapnut' : 'vypnut'}');
    } catch (e) {
      debugPrint('Chyba při změně velkého textu: $e');
    }
  }

  /// Zapne/vypne redukci pohybu
  Future<void> setReduceMotionMode(bool enabled) async {
    if (_reduceMotionMode == enabled) return;

    try {
      _reduceMotionMode = enabled;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_reduceMotionKey, enabled);

      notifyListeners();
      debugPrint('Redukce pohybu ${enabled ? 'zapnuta' : 'vypnuta'}');
    } catch (e) {
      debugPrint('Chyba při změně redukce pohybu: $e');
    }
  }

  /// Zapne/vypne screen reader
  Future<void> setScreenReaderEnabled(bool enabled) async {
    if (_screenReaderEnabled == enabled) return;

    try {
      _screenReaderEnabled = enabled;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_screenReaderKey, enabled);

      notifyListeners();
      debugPrint('Screen reader ${enabled ? 'zapnut' : 'vypnut'}');
    } catch (e) {
      debugPrint('Chyba při změně screen reader: $e');
    }
  }

  /// Zapne/vypne hlasové vedení
  Future<void> setVoiceGuidanceEnabled(bool enabled) async {
    if (_voiceGuidanceEnabled == enabled) return;

    try {
      _voiceGuidanceEnabled = enabled;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_voiceGuidanceKey, enabled);

      notifyListeners();
      debugPrint('Hlasové vedení ${enabled ? 'zapnuto' : 'vypnuto'}');
    } catch (e) {
      debugPrint('Chyba při změně hlasového vedení: $e');
    }
  }

  /// Zapne/vypne haptickou zpětnou vazbu
  Future<void> setHapticFeedbackEnabled(bool enabled) async {
    if (_hapticFeedbackEnabled == enabled) return;

    try {
      _hapticFeedbackEnabled = enabled;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_hapticFeedbackKey, enabled);

      notifyListeners();
      debugPrint('Haptická zpětná vazba ${enabled ? 'zapnuta' : 'vypnuta'}');
    } catch (e) {
      debugPrint('Chyba při změně haptické zpětné vazby: $e');
    }
  }

  /// Nastaví režim barvosleposti
  Future<void> setColorBlindMode(ColorBlindMode mode) async {
    if (_colorBlindMode == mode) return;

    try {
      _colorBlindMode = mode;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_colorBlindModeKey, mode.index);

      notifyListeners();
      debugPrint('Režim barvosleposti nastaven na: ${mode.name}');
    } catch (e) {
      debugPrint('Chyba při změně režimu barvosleposti: $e');
    }
  }

  /// Provede haptickou zpětnou vazbu
  Future<void> performHapticFeedback([String type = 'light']) async {
    if (!_hapticFeedbackEnabled) return;

    try {
      switch (type) {
        case 'light':
          await HapticFeedback.lightImpact();
          break;
        case 'medium':
          await HapticFeedback.mediumImpact();
          break;
        case 'heavy':
          await HapticFeedback.heavyImpact();
          break;
        case 'selection':
          await HapticFeedback.selectionClick();
          break;
        case 'vibrate':
          await HapticFeedback.vibrate();
          break;
        default:
          await HapticFeedback.lightImpact();
      }
    } catch (e) {
      debugPrint('Chyba při haptické zpětné vazbě: $e');
    }
  }

  /// Upraví barvu pro barvoslepé uživatele
  Color adjustColorForColorBlind(Color originalColor) {
    if (_colorBlindMode == ColorBlindMode.none) {
      return originalColor;
    }

    final hsl = HSLColor.fromColor(originalColor);

    switch (_colorBlindMode) {
      case ColorBlindMode.protanopia:
        // Protanopia - chybí červené fotoreceptory
        return _adjustForProtanopia(hsl).toColor();
      case ColorBlindMode.deuteranopia:
        // Deuteranopia - chybí zelené fotoreceptory
        return _adjustForDeuteranopia(hsl).toColor();
      case ColorBlindMode.tritanopia:
        // Tritanopia - chybí modré fotoreceptory
        return _adjustForTritanopia(hsl).toColor();
      case ColorBlindMode.none:
        return originalColor;
    }
  }

  /// Upraví barvu pro protanopii
  HSLColor _adjustForProtanopia(HSLColor hsl) {
    // Převede červené odstíny na žluté/oranžové
    if (hsl.hue >= 0 && hsl.hue <= 60) {
      return hsl.withHue((hsl.hue + 30) % 360);
    }
    return hsl;
  }

  /// Upraví barvu pro deuteranopii
  HSLColor _adjustForDeuteranopia(HSLColor hsl) {
    // Převede zelené odstíny na žluté/modré
    if (hsl.hue >= 60 && hsl.hue <= 180) {
      return hsl.withHue(hsl.hue < 120 ? hsl.hue - 30 : hsl.hue + 30);
    }
    return hsl;
  }

  /// Upraví barvu pro tritanopii
  HSLColor _adjustForTritanopia(HSLColor hsl) {
    // Převede modré odstíny na zelené/červené
    if (hsl.hue >= 180 && hsl.hue <= 300) {
      return hsl.withHue((hsl.hue + 60) % 360);
    }
    return hsl;
  }

  /// Získá text scale factor pro velký text
  double getTextScaleFactor() {
    return _largeTextMode ? 1.3 : 1.0;
  }

  /// Získá duration pro animace (redukované pokud je zapnuta redukce pohybu)
  Duration getAnimationDuration(Duration originalDuration) {
    return _reduceMotionMode
        ? Duration(
            milliseconds: (originalDuration.inMilliseconds * 0.3).round(),
          )
        : originalDuration;
  }

  /// Zkontroluje, zda jsou animace povolené
  bool areAnimationsEnabled() {
    return !_reduceMotionMode;
  }

  /// Získá accessibility label pro screen reader
  String getAccessibilityLabel(String originalText, {String? context}) {
    if (!_screenReaderEnabled) return originalText;

    if (context != null) {
      return '$context: $originalText';
    }
    return originalText;
  }

  /// Získá semantic label pro tlačítka
  String getButtonSemanticLabel(String buttonText, {String? action}) {
    if (!_screenReaderEnabled) return buttonText;

    final actionText = action ?? 'stisknout';
    return '$buttonText, $actionText';
  }

  /// Získá název režimu barvosleposti v češtině
  String getColorBlindModeDisplayName() {
    switch (_colorBlindMode) {
      case ColorBlindMode.none:
        return 'Žádný';
      case ColorBlindMode.protanopia:
        return 'Protanopie (červeno-zelená)';
      case ColorBlindMode.deuteranopia:
        return 'Deuteranopie (červeno-zelená)';
      case ColorBlindMode.tritanopia:
        return 'Tritanopie (modro-žlutá)';
    }
  }

  /// Resetuje na výchozí nastavení
  Future<void> resetToDefaults() async {
    try {
      await setHighContrastMode(false);
      await setLargeTextMode(false);
      await setReduceMotionMode(false);
      await setScreenReaderEnabled(false);
      await setVoiceGuidanceEnabled(false);
      await setHapticFeedbackEnabled(true);
      await setColorBlindMode(ColorBlindMode.none);

      debugPrint('Accessibility nastavení resetováno na výchozí');
    } catch (e) {
      debugPrint('Chyba při resetování accessibility: $e');
    }
  }

  /// Získá doporučené nastavení na základě systémových preferencí
  Future<void> applySystemAccessibilitySettings() async {
    try {
      // TODO: Implementovat detekci systémových accessibility nastavení
      // Na Android a iOS lze detekovat některá nastavení přes platform channels

      debugPrint('Systémová accessibility nastavení aplikována');
    } catch (e) {
      debugPrint('Chyba při aplikování systémových nastavení: $e');
    }
  }
}
