import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/contact_info.dart';
import '../data/croatia_contacts_database.dart';

/// 📞 OBRAZOVKA ADRESÁŘE KONTAKTŮ
class ContactsDirectoryScreen extends StatefulWidget {
  final ContactCategory? initialCategory;
  final String? initialCity;

  const ContactsDirectoryScreen({
    super.key,
    this.initialCategory,
    this.initialCity,
  });

  @override
  State<ContactsDirectoryScreen> createState() => _ContactsDirectoryScreenState();
}

class _ContactsDirectoryScreenState extends State<ContactsDirectoryScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  List<ContactInfo> _allContacts = [];
  List<ContactInfo> _filteredContacts = [];
  String _searchQuery = '';
  ContactCategory? _selectedCategory;
  String? _selectedCity;

  final List<ContactCategory> _categories = [
    ContactCategory.emergency,
    ContactCategory.healthcare,
    ContactCategory.government,
    ContactCategory.culture,
  ];

  final List<String> _cities = [
    'Zagreb',
    'Split',
    'Dubrovnik',
    'Rijeka',
    'Zadar',
    'Pula',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _categories.length, vsync: this);
    _selectedCategory = widget.initialCategory ?? ContactCategory.emergency;
    _selectedCity = widget.initialCity;
    _loadContacts();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadContacts() {
    setState(() {
      _allContacts = CroatiaContactsDatabase.getAllContacts();
      _filterContacts();
    });
  }

  void _filterContacts() {
    List<ContactInfo> filtered = _allContacts;

    // Filtr podle kategorie
    if (_selectedCategory != null) {
      filtered = filtered.where((contact) => 
        contact.category == _selectedCategory
      ).toList();
    }

    // Filtr podle města
    if (_selectedCity != null && _selectedCity!.isNotEmpty) {
      filtered = filtered.where((contact) => 
        contact.city.toLowerCase().contains(_selectedCity!.toLowerCase())
      ).toList();
    }

    // Vyhledávání
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((contact) =>
        contact.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
        contact.services.any((service) => 
          service.toLowerCase().contains(_searchQuery.toLowerCase())
        )
      ).toList();
    }

    setState(() {
      _filteredContacts = filtered;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          '📞 Kontakty',
          style: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF006994),
                const Color(0xFF2E8B8B),
                const Color(0xFF9C27B0),
              ],
            ),
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          onTap: (index) {
            setState(() {
              _selectedCategory = _categories[index];
              _filterContacts();
            });
          },
          tabs: _categories.map((category) => Tab(
            icon: Text(category.icon, style: const TextStyle(fontSize: 20)),
            text: category.displayNameHr,
          )).toList(),
        ),
      ),
      body: Column(
        children: [
          // Vyhledávání a filtry
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey.shade50,
            child: Column(
              children: [
                // Vyhledávací pole
                TextField(
                  decoration: InputDecoration(
                    hintText: 'Pretraži kontakte...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                      _filterContacts();
                    });
                  },
                ),
                
                const SizedBox(height: 12),
                
                // Filtr podle města
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildCityChip('Svi gradovi', null),
                      ..._cities.map((city) => _buildCityChip(city, city)),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Seznam kontaktů
          Expanded(
            child: _filteredContacts.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _filteredContacts.length,
                    itemBuilder: (context, index) {
                      return _buildContactCard(_filteredContacts[index]);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildCityChip(String label, String? value) {
    final isSelected = _selectedCity == value;
    
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedCity = selected ? value : null;
            _filterContacts();
          });
        },
        backgroundColor: Colors.white,
        selectedColor: const Color(0xFF006994).withValues(alpha: 0.2),
        checkmarkColor: const Color(0xFF006994),
      ),
    );
  }

  Widget _buildContactCard(ContactInfo contact) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header s ikonou a názvem
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Color(contact.category.colorValue).withValues(alpha: 0.1),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Color(contact.category.colorValue),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: Text(
                      contact.category.icon,
                      style: const TextStyle(fontSize: 24),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        contact.name,
                        style: GoogleFonts.playfairDisplay(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF2C2C2C),
                        ),
                      ),
                      if (contact.nameEn != null)
                        Text(
                          contact.nameEn!,
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: const Color(0xFF666666),
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      Text(
                        '${contact.city}, ${contact.region}',
                        style: GoogleFonts.inter(
                          fontSize: 12,
                          color: Color(contact.category.colorValue),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                if (contact.isEmergency)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'HITNO',
                      style: GoogleFonts.inter(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // Kontaktní informace
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Telefon
                _buildContactRow(
                  Icons.phone,
                  'Telefon',
                  contact.phone,
                  () => _makePhoneCall(contact.phone),
                ),
                
                if (contact.emergencyPhone != null)
                  _buildContactRow(
                    Icons.emergency,
                    'Hitna linija',
                    contact.emergencyPhone!,
                    () => _makePhoneCall(contact.emergencyPhone!),
                    isEmergency: true,
                  ),

                if (contact.email != null)
                  _buildContactRow(
                    Icons.email,
                    'Email',
                    contact.email!,
                    () => _sendEmail(contact.email!),
                  ),

                if (contact.website != null)
                  _buildContactRow(
                    Icons.web,
                    'Web',
                    contact.website!,
                    () => _openWebsite(contact.website!),
                  ),

                _buildContactRow(
                  Icons.location_on,
                  'Adresa',
                  contact.address,
                  () => _openMaps(contact.latitude, contact.longitude),
                ),

                _buildContactRow(
                  Icons.access_time,
                  'Otevírací doba',
                  contact.openingHours,
                  null,
                ),

                // Služby
                if (contact.services.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Text(
                    'Služby:',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF2C2C2C),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 4,
                    children: contact.services.map((service) => Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Color(contact.category.colorValue).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Color(contact.category.colorValue).withValues(alpha: 0.3),
                        ),
                      ),
                      child: Text(
                        service,
                        style: GoogleFonts.inter(
                          fontSize: 12,
                          color: Color(contact.category.colorValue),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    )).toList(),
                  ),
                ],

                // Jazyky
                if (contact.languages.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Icon(
                        Icons.language,
                        size: 16,
                        color: const Color(0xFF666666),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Jazyky: ${contact.languages.join(', ')}',
                        style: GoogleFonts.inter(
                          fontSize: 12,
                          color: const Color(0xFF666666),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactRow(
    IconData icon,
    String label,
    String value,
    VoidCallback? onTap, {
    bool isEmergency = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: isEmergency 
                    ? Colors.red 
                    : const Color(0xFF666666),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label,
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        color: const Color(0xFF666666),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      value,
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        color: isEmergency 
                            ? Colors.red 
                            : const Color(0xFF2C2C2C),
                        fontWeight: isEmergency 
                            ? FontWeight.bold 
                            : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
              if (onTap != null)
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: const Color(0xFF666666),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '📞',
            style: const TextStyle(fontSize: 64),
          ),
          const SizedBox(height: 16),
          Text(
            'Žádné kontakty nenalezeny',
            style: GoogleFonts.playfairDisplay(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF666666),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Zkuste změnit filtry nebo vyhledávání',
            style: GoogleFonts.inter(
              fontSize: 14,
              color: const Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }

  // Akce pro kontakty
  void _makePhoneCall(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    }
  }

  void _sendEmail(String email) async {
    final Uri emailUri = Uri(scheme: 'mailto', path: email);
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    }
  }

  void _openWebsite(String website) async {
    final Uri webUri = Uri.parse(website);
    if (await canLaunchUrl(webUri)) {
      await launchUrl(webUri, mode: LaunchMode.externalApplication);
    }
  }

  void _openMaps(double latitude, double longitude) async {
    final Uri mapsUri = Uri.parse(
      'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude'
    );
    if (await canLaunchUrl(mapsUri)) {
      await launchUrl(mapsUri, mode: LaunchMode.externalApplication);
    }
  }
}
