// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cuisine.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CuisineItem _$CuisineItemFromJson(Map<String, dynamic> json) => CuisineItem(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      region: json['region'] as String,
      type: $enumDecode(_$CuisineTypeEnumMap, json['type']),
      ingredients: (json['ingredients'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      recipe: json['recipe'] as String?,
      images: (json['images'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      restaurants: (json['restaurants'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      isVegetarian: json['isVegetarian'] as bool? ?? false,
      isVegan: json['isVegan'] as bool? ?? false,
      isGlutenFree: json['isGlutenFree'] as bool? ?? false,
      allergens: (json['allergens'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      averagePrice: (json['averagePrice'] as num?)?.toDouble(),
      rating: (json['rating'] as num?)?.toDouble(),
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
    );

Map<String, dynamic> _$CuisineItemToJson(CuisineItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'region': instance.region,
      'type': _$CuisineTypeEnumMap[instance.type]!,
      'ingredients': instance.ingredients,
      'recipe': instance.recipe,
      'images': instance.images,
      'restaurants': instance.restaurants,
      'isVegetarian': instance.isVegetarian,
      'isVegan': instance.isVegan,
      'isGlutenFree': instance.isGlutenFree,
      'allergens': instance.allergens,
      'averagePrice': instance.averagePrice,
      'rating': instance.rating,
      'tags': instance.tags,
    };

const _$CuisineTypeEnumMap = {
  CuisineType.mainDish: 'main_dish',
  CuisineType.appetizer: 'appetizer',
  CuisineType.dessert: 'dessert',
  CuisineType.drink: 'drink',
  CuisineType.snack: 'snack',
  CuisineType.soup: 'soup',
  CuisineType.seafood: 'seafood',
  CuisineType.meat: 'meat',
  CuisineType.pasta: 'pasta',
  CuisineType.bread: 'bread',
};
