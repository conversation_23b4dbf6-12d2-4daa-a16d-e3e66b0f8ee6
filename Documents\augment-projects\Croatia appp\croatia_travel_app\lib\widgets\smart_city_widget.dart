import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import '../services/parking_service.dart';
import '../services/shared_mobility_service.dart';
import '../services/smart_city_services.dart';
import '../services/issue_reporting_service.dart';
import '../models/smart_city_models.dart';

/// Hlavní widget pro chytré město funkce
class SmartCityWidget extends StatefulWidget {
  const SmartCityWidget({super.key});

  @override
  State<SmartCityWidget> createState() => _SmartCityWidgetState();
}

class _SmartCityWidgetState extends State<SmartCityWidget>
    with TickerProviderStateMixin {
  late TabController _tabController;

  final ParkingService _parkingService = ParkingService();
  final SharedMobilityService _mobilityService = SharedMobilityService();
  final SmartCityServices _cityService = SmartCityServices();
  final IssueReportingService _issueService = IssueReportingService();

  Position? _currentPosition;
  bool _isLoading = true;
  String _selectedCity = 'zagreb';

  // Data pro jednotlivé taby
  List<ParkingSpot> _nearbyParkingSpots = [];
  List<SharedVehicle> _nearbyVehicles = [];
  List<CityService> _cityServices = [];
  List<IssueReport> _nearbyIssues = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeServices();
    _getCurrentLocation();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeServices() async {
    await Future.wait([
      _parkingService.initialize(),
      _mobilityService.initialize(),
      _cityService.initialize(),
      _issueService.initialize(),
    ]);
  }

  Future<void> _getCurrentLocation() async {
    try {
      final permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        await Geolocator.requestPermission();
      }

      final position = await Geolocator.getCurrentPosition();
      setState(() {
        _currentPosition = position;
        _isLoading = false;
      });

      _loadAllData();
    } catch (e) {
      setState(() {
        _currentPosition = Position(
          latitude: 45.815,
          longitude: 15.982,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          heading: 0,
          speed: 0,
          speedAccuracy: 0,
          altitudeAccuracy: 0,
          headingAccuracy: 0,
        );
        _isLoading = false;
      });
      _loadAllData();
    }
  }

  Future<void> _loadAllData() async {
    if (_currentPosition == null) return;

    await Future.wait([
      _loadParkingData(),
      _loadMobilityData(),
      _loadCityServicesData(),
      _loadIssuesData(),
    ]);
  }

  Future<void> _loadParkingData() async {
    try {
      final spots = await _parkingService.getNearbyParkingSpots(
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
        radiusKm: 2.0,
      );
      setState(() => _nearbyParkingSpots = spots);
    } catch (e) {
      debugPrint('Chyba při načítání parkování: $e');
    }
  }

  Future<void> _loadMobilityData() async {
    try {
      final vehicles = await _mobilityService.getNearbyVehicles(
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
        radiusKm: 1.0,
      );
      setState(() => _nearbyVehicles = vehicles);
    } catch (e) {
      debugPrint('Chyba při načítání sdílených vozidel: $e');
    }
  }

  Future<void> _loadCityServicesData() async {
    try {
      final services = await _cityService.getCityServices(
        cityId: _selectedCity,
      );
      setState(() => _cityServices = services);
    } catch (e) {
      debugPrint('Chyba při načítání městských služeb: $e');
    }
  }

  Future<void> _loadIssuesData() async {
    try {
      final issues = await _issueService.getIssuesInArea(
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
        radiusKm: 2.0,
      );
      setState(() => _nearbyIssues = issues);
    } catch (e) {
      debugPrint('Chyba při načítání problémů: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chytré město'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.local_parking), text: 'Parkování'),
            Tab(icon: Icon(Icons.directions_bike), text: 'Sdílení'),
            Tab(icon: Icon(Icons.business), text: 'Služby'),
            Tab(icon: Icon(Icons.report_problem), text: 'Problémy'),
          ],
        ),
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.location_city),
            onSelected: (city) {
              setState(() => _selectedCity = city);
              _loadAllData();
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'zagreb', child: Text('Zagreb')),
              const PopupMenuItem(value: 'split', child: Text('Split')),
              const PopupMenuItem(value: 'rijeka', child: Text('Rijeka')),
              const PopupMenuItem(value: 'dubrovnik', child: Text('Dubrovnik')),
            ],
          ),
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadAllData),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildParkingTab(),
                _buildMobilityTab(),
                _buildServicesTab(),
                _buildIssuesTab(),
              ],
            ),
    );
  }

  Widget _buildParkingTab() {
    return RefreshIndicator(
      onRefresh: _loadParkingData,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSectionHeader(
            'Parkování v okolí',
            '${_nearbyParkingSpots.length} míst',
            Icons.local_parking,
            Colors.blue,
          ),
          const SizedBox(height: 16),

          if (_nearbyParkingSpots.isEmpty)
            _buildEmptyState('Žádná parkovací místa v okolí')
          else
            ..._nearbyParkingSpots.map((spot) => _buildParkingCard(spot)),
        ],
      ),
    );
  }

  Widget _buildMobilityTab() {
    return RefreshIndicator(
      onRefresh: _loadMobilityData,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSectionHeader(
            'Sdílená vozidla',
            '${_nearbyVehicles.length} dostupných',
            Icons.directions_bike,
            Colors.green,
          ),
          const SizedBox(height: 16),

          if (_nearbyVehicles.isEmpty)
            _buildEmptyState('Žádná sdílená vozidla v okolí')
          else
            ..._nearbyVehicles.map((vehicle) => _buildVehicleCard(vehicle)),
        ],
      ),
    );
  }

  Widget _buildServicesTab() {
    return RefreshIndicator(
      onRefresh: _loadCityServicesData,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSectionHeader(
            'Městské služby',
            '${_cityServices.length} služeb',
            Icons.business,
            Colors.orange,
          ),
          const SizedBox(height: 16),

          if (_cityServices.isEmpty)
            _buildEmptyState('Žádné městské služby')
          else
            ..._cityServices.map((service) => _buildServiceCard(service)),
        ],
      ),
    );
  }

  Widget _buildIssuesTab() {
    return RefreshIndicator(
      onRefresh: _loadIssuesData,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSectionHeader(
            'Nahlášené problémy',
            '${_nearbyIssues.length} v okolí',
            Icons.report_problem,
            Colors.red,
          ),
          const SizedBox(height: 16),

          // Tlačítko pro nahlášení nového problému
          Card(
            color: Colors.red[50],
            child: ListTile(
              leading: Icon(Icons.add_circle, color: Colors.red[700], size: 32),
              title: const Text(
                'Nahlásit nový problém',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: const Text('Pomozte zlepšit město'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _showReportIssueDialog,
            ),
          ),

          const SizedBox(height: 16),

          if (_nearbyIssues.isEmpty)
            _buildEmptyState('Žádné problémy v okolí')
          else
            ..._nearbyIssues.map((issue) => _buildIssueCard(issue)),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(
    String title,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                subtitle,
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(String message) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(Icons.info_outline, size: 48, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              message,
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildParkingCard(ParkingSpot spot) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: spot.hasAvailableSpaces
                ? Colors.green[100]
                : Colors.red[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.local_parking,
            color: spot.hasAvailableSpaces
                ? Colors.green[700]
                : Colors.red[700],
          ),
        ),
        title: Text(spot.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(spot.address),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.directions_car, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text('${spot.availableSpaces}/${spot.totalSpaces} volných'),
                const SizedBox(width: 16),
                Icon(Icons.euro, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text('${spot.hourlyRate} ${spot.currency}/h'),
              ],
            ),
          ],
        ),
        trailing: spot.hasAvailableSpaces
            ? ElevatedButton(
                onPressed: () => _reserveParking(spot),
                child: const Text('Rezervovat'),
              )
            : const Text('Obsazeno'),
        isThreeLine: true,
      ),
    );
  }

  Widget _buildVehicleCard(SharedVehicle vehicle) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _getVehicleTypeColor(vehicle.type).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getVehicleTypeIcon(vehicle.type),
            color: _getVehicleTypeColor(vehicle.type),
          ),
        ),
        title: Text('${vehicle.brand} ${vehicle.model}'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(vehicle.licensePlate),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.battery_charging_full,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  '${vehicle.type == VehicleType.electric ? vehicle.batteryLevel : vehicle.fuelLevel}%',
                ),
                const SizedBox(width: 16),
                Icon(Icons.euro, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text('${vehicle.pricePerMinute} ${vehicle.currency}/min'),
              ],
            ),
          ],
        ),
        trailing: vehicle.isAvailable
            ? ElevatedButton(
                onPressed: () => _reserveVehicle(vehicle),
                child: const Text('Rezervovat'),
              )
            : const Text('Nedostupné'),
        isThreeLine: true,
      ),
    );
  }

  Widget _buildServiceCard(CityService service) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _getServiceCategoryColor(
              service.category,
            ).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getServiceCategoryIcon(service.category),
            color: _getServiceCategoryColor(service.category),
          ),
        ),
        title: Text(service.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(service.description),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  service.isOpenNow
                      ? Icons.access_time
                      : Icons.access_time_filled,
                  size: 16,
                  color: service.isOpenNow ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 4),
                Text(
                  service.isOpenNow ? 'Otevřeno' : 'Zavřeno',
                  style: TextStyle(
                    color: service.isOpenNow ? Colors.green : Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 16),
                Icon(Icons.star, size: 16, color: Colors.amber),
                const SizedBox(width: 4),
                Text(
                  '${service.rating.toStringAsFixed(1)} (${service.reviewCount})',
                ),
              ],
            ),
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: () => _showServiceDetails(service),
        isThreeLine: true,
      ),
    );
  }

  Widget _buildIssueCard(IssueReport issue) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _getIssuePriorityColor(
              issue.priority,
            ).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getIssueCategoryIcon(issue.category),
            color: _getIssuePriorityColor(issue.priority),
          ),
        ),
        title: Text(issue.title),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              issue.description,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(_formatTimeAgo(issue.timeSinceReported)),
                const SizedBox(width: 16),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: _getIssueStatusColor(
                      issue.status,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getIssueStatusText(issue.status),
                    style: TextStyle(
                      fontSize: 12,
                      color: _getIssueStatusColor(issue.status),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: () => _showIssueDetails(issue),
        isThreeLine: true,
      ),
    );
  }

  // Pomocné metody pro barvy a ikony
  Color _getVehicleTypeColor(VehicleType type) {
    switch (type) {
      case VehicleType.bike:
        return Colors.green;
      case VehicleType.scooter:
        return Colors.blue;
      case VehicleType.car:
        return Colors.orange;
      case VehicleType.electric:
        return Colors.purple;
      case VehicleType.hybrid:
        return Colors.teal;
    }
  }

  IconData _getVehicleTypeIcon(VehicleType type) {
    switch (type) {
      case VehicleType.bike:
        return Icons.directions_bike;
      case VehicleType.scooter:
        return Icons.electric_scooter;
      case VehicleType.car:
        return Icons.directions_car;
      case VehicleType.electric:
        return Icons.electric_car;
      case VehicleType.hybrid:
        return Icons.directions_car;
    }
  }

  Color _getServiceCategoryColor(ServiceCategory category) {
    switch (category) {
      case ServiceCategory.administration:
        return Colors.blue;
      case ServiceCategory.health:
        return Colors.red;
      case ServiceCategory.education:
        return Colors.green;
      case ServiceCategory.social:
        return Colors.purple;
      case ServiceCategory.transport:
        return Colors.orange;
      case ServiceCategory.environment:
        return Colors.teal;
      case ServiceCategory.culture:
        return Colors.pink;
      case ServiceCategory.sports:
        return Colors.indigo;
      case ServiceCategory.emergency:
        return Colors.red;
      case ServiceCategory.other:
        return Colors.grey;
    }
  }

  IconData _getServiceCategoryIcon(ServiceCategory category) {
    switch (category) {
      case ServiceCategory.administration:
        return Icons.business;
      case ServiceCategory.health:
        return Icons.local_hospital;
      case ServiceCategory.education:
        return Icons.school;
      case ServiceCategory.social:
        return Icons.people;
      case ServiceCategory.transport:
        return Icons.directions_bus;
      case ServiceCategory.environment:
        return Icons.eco;
      case ServiceCategory.culture:
        return Icons.museum;
      case ServiceCategory.sports:
        return Icons.sports;
      case ServiceCategory.emergency:
        return Icons.emergency;
      case ServiceCategory.other:
        return Icons.more_horiz;
    }
  }

  Color _getIssuePriorityColor(IssuePriority priority) {
    switch (priority) {
      case IssuePriority.low:
        return Colors.green;
      case IssuePriority.medium:
        return Colors.orange;
      case IssuePriority.high:
        return Colors.red;
      case IssuePriority.urgent:
        return Colors.red[900]!;
    }
  }

  IconData _getIssueCategoryIcon(IssueCategory category) {
    switch (category) {
      case IssueCategory.roads:
        return Icons.construction;
      case IssueCategory.lighting:
        return Icons.lightbulb;
      case IssueCategory.waste:
        return Icons.delete;
      case IssueCategory.water:
        return Icons.water_drop;
      case IssueCategory.parks:
        return Icons.park;
      case IssueCategory.noise:
        return Icons.volume_up;
      case IssueCategory.safety:
        return Icons.security;
      case IssueCategory.transport:
        return Icons.directions_bus;
      case IssueCategory.other:
        return Icons.report_problem;
    }
  }

  Color _getIssueStatusColor(IssueStatus status) {
    switch (status) {
      case IssueStatus.reported:
        return Colors.blue;
      case IssueStatus.inProgress:
        return Colors.orange;
      case IssueStatus.resolved:
        return Colors.green;
      case IssueStatus.rejected:
        return Colors.red;
      case IssueStatus.duplicate:
        return Colors.grey;
    }
  }

  String _getIssueStatusText(IssueStatus status) {
    switch (status) {
      case IssueStatus.reported:
        return 'Nahlášeno';
      case IssueStatus.inProgress:
        return 'Řeší se';
      case IssueStatus.resolved:
        return 'Vyřešeno';
      case IssueStatus.rejected:
        return 'Zamítnuto';
      case IssueStatus.duplicate:
        return 'Duplikát';
    }
  }

  String _formatTimeAgo(Duration duration) {
    if (duration.inDays > 0) {
      return 'před ${duration.inDays} dny';
    } else if (duration.inHours > 0) {
      return 'před ${duration.inHours} h';
    } else if (duration.inMinutes > 0) {
      return 'před ${duration.inMinutes} min';
    } else {
      return 'právě teď';
    }
  }

  // Akční metody
  void _reserveParking(ParkingSpot spot) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Rezervovat ${spot.name}'),
        content: Text(
          'Chcete rezervovat parkovací místo za ${spot.hourlyRate} ${spot.currency}/h?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Zrušit'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showSuccessMessage('Parkování rezervováno!');
            },
            child: const Text('Rezervovat'),
          ),
        ],
      ),
    );
  }

  void _reserveVehicle(SharedVehicle vehicle) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Rezervovat ${vehicle.brand} ${vehicle.model}'),
        content: Text(
          'Chcete rezervovat vozidlo za ${vehicle.pricePerMinute} ${vehicle.currency}/min?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Zrušit'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showSuccessMessage('Vozidlo rezervováno!');
            },
            child: const Text('Rezervovat'),
          ),
        ],
      ),
    );
  }

  void _showServiceDetails(CityService service) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(service.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(service.description),
            const SizedBox(height: 16),
            Text('📍 ${service.address}'),
            Text('📞 ${service.phone}'),
            if (service.email.isNotEmpty) Text('📧 ${service.email}'),
            const SizedBox(height: 16),
            Text('Služby:', style: Theme.of(context).textTheme.titleSmall),
            ...service.services.map((s) => Text('• $s')),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Zavřít'),
          ),
          if (service.requiresAppointment)
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _showSuccessMessage('Termín rezervován!');
              },
              child: const Text('Rezervovat termín'),
            ),
        ],
      ),
    );
  }

  void _showIssueDetails(IssueReport issue) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(issue.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(issue.description),
            const SizedBox(height: 16),
            Text('📍 ${issue.address}'),
            Text('📅 ${_formatTimeAgo(issue.timeSinceReported)}'),
            Text('🏷️ ${_getIssueStatusText(issue.status)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Zavřít'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showSuccessMessage('Problém podporován!');
            },
            child: const Text('Podpořit'),
          ),
        ],
      ),
    );
  }

  void _showReportIssueDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Nahlásit problém'),
        content: const Text(
          'Funkce pro nahlášení problému bude implementována v další verzi.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Zavřít'),
          ),
        ],
      ),
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }
}
