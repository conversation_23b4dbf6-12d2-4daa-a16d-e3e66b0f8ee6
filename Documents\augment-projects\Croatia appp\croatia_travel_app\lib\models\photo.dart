class TravelPhoto {
  final String id;
  final String originalPath;
  final String optimizedPath;
  final String thumbnailPath;
  final String? albumId;
  final List<String> tags;
  final String? location;
  final DateTime takenAt;
  final DateTime createdAt;
  final PhotoMetadata metadata;
  final bool isProcessed;
  final double? rating;
  final bool isFavorite;

  const TravelPhoto({
    required this.id,
    required this.originalPath,
    required this.optimizedPath,
    required this.thumbnailPath,
    this.albumId,
    required this.tags,
    this.location,
    required this.takenAt,
    required this.createdAt,
    required this.metadata,
    required this.isProcessed,
    this.rating,
    this.isFavorite = false,
  });

  TravelPhoto copyWith({
    String? id,
    String? originalPath,
    String? optimizedPath,
    String? thumbnailPath,
    String? albumId,
    List<String>? tags,
    String? location,
    DateTime? takenAt,
    DateTime? createdAt,
    PhotoMetadata? metadata,
    bool? isProcessed,
    double? rating,
    bool? isFavorite,
  }) {
    return TravelPhoto(
      id: id ?? this.id,
      originalPath: originalPath ?? this.originalPath,
      optimizedPath: optimizedPath ?? this.optimizedPath,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      albumId: albumId ?? this.albumId,
      tags: tags ?? this.tags,
      location: location ?? this.location,
      takenAt: takenAt ?? this.takenAt,
      createdAt: createdAt ?? this.createdAt,
      metadata: metadata ?? this.metadata,
      isProcessed: isProcessed ?? this.isProcessed,
      rating: rating ?? this.rating,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }
}

class PhotoMetadata {
  final int width;
  final int height;
  final int fileSize;
  final String format;
  final String colorSpace;
  final PhotoOrientation orientation;
  final bool hasExif;
  final GPSCoordinates? gpsCoordinates;
  final CameraInfo? cameraInfo;

  const PhotoMetadata({
    required this.width,
    required this.height,
    required this.fileSize,
    required this.format,
    required this.colorSpace,
    required this.orientation,
    required this.hasExif,
    this.gpsCoordinates,
    this.cameraInfo,
  });

  Map<String, dynamic> toJson() {
    return {
      'width': width,
      'height': height,
      'fileSize': fileSize,
      'format': format,
      'colorSpace': colorSpace,
      'orientation': orientation.name,
      'hasExif': hasExif,
      'gpsCoordinates': gpsCoordinates?.toJson(),
      'cameraInfo': cameraInfo?.toJson(),
    };
  }

  factory PhotoMetadata.fromJson(Map<String, dynamic> json) {
    return PhotoMetadata(
      width: json['width'],
      height: json['height'],
      fileSize: json['fileSize'],
      format: json['format'],
      colorSpace: json['colorSpace'],
      orientation: PhotoOrientation.values.firstWhere((e) => e.name == json['orientation']),
      hasExif: json['hasExif'],
      gpsCoordinates: json['gpsCoordinates'] != null 
          ? GPSCoordinates.fromJson(json['gpsCoordinates'])
          : null,
      cameraInfo: json['cameraInfo'] != null 
          ? CameraInfo.fromJson(json['cameraInfo'])
          : null,
    );
  }
}

class GPSCoordinates {
  final double latitude;
  final double longitude;
  final double? altitude;
  final double? accuracy;

  const GPSCoordinates({
    required this.latitude,
    required this.longitude,
    this.altitude,
    this.accuracy,
  });

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'altitude': altitude,
      'accuracy': accuracy,
    };
  }

  factory GPSCoordinates.fromJson(Map<String, dynamic> json) {
    return GPSCoordinates(
      latitude: json['latitude'],
      longitude: json['longitude'],
      altitude: json['altitude'],
      accuracy: json['accuracy'],
    );
  }
}

class CameraInfo {
  final String? make;
  final String? model;
  final String? lens;
  final double? focalLength;
  final double? aperture;
  final String? shutterSpeed;
  final int? iso;
  final bool? flash;

  const CameraInfo({
    this.make,
    this.model,
    this.lens,
    this.focalLength,
    this.aperture,
    this.shutterSpeed,
    this.iso,
    this.flash,
  });

  Map<String, dynamic> toJson() {
    return {
      'make': make,
      'model': model,
      'lens': lens,
      'focalLength': focalLength,
      'aperture': aperture,
      'shutterSpeed': shutterSpeed,
      'iso': iso,
      'flash': flash,
    };
  }

  factory CameraInfo.fromJson(Map<String, dynamic> json) {
    return CameraInfo(
      make: json['make'],
      model: json['model'],
      lens: json['lens'],
      focalLength: json['focalLength'],
      aperture: json['aperture'],
      shutterSpeed: json['shutterSpeed'],
      iso: json['iso'],
      flash: json['flash'],
    );
  }
}

class PhotoAlbum {
  final String id;
  final String name;
  final String description;
  final String? coverPhotoId;
  final List<String> photoIds;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isPublic;
  final List<String> tags;

  const PhotoAlbum({
    required this.id,
    required this.name,
    required this.description,
    this.coverPhotoId,
    required this.photoIds,
    required this.createdAt,
    required this.updatedAt,
    required this.isPublic,
    required this.tags,
  });

  PhotoAlbum copyWith({
    String? id,
    String? name,
    String? description,
    String? coverPhotoId,
    List<String>? photoIds,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isPublic,
    List<String>? tags,
  }) {
    return PhotoAlbum(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      coverPhotoId: coverPhotoId ?? this.coverPhotoId,
      photoIds: photoIds ?? this.photoIds,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isPublic: isPublic ?? this.isPublic,
      tags: tags ?? this.tags,
    );
  }
}

class PhotoStatistics {
  final int totalPhotos;
  final int totalAlbums;
  final int totalSize;
  final List<MapEntry<String, int>> mostUsedTags;
  final Map<String, int> photosByLocation;
  final Map<String, int> photosByMonth;

  const PhotoStatistics({
    required this.totalPhotos,
    required this.totalAlbums,
    required this.totalSize,
    required this.mostUsedTags,
    required this.photosByLocation,
    required this.photosByMonth,
  });
}

class StorageOptimizationResult {
  final int savedSpace;
  final int processedFiles;
  final int totalFiles;

  const StorageOptimizationResult({
    required this.savedSpace,
    required this.processedFiles,
    required this.totalFiles,
  });

  double get compressionRatio => processedFiles / totalFiles;
  String get savedSpaceFormatted => _formatBytes(savedSpace);

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}

// Enums
enum PhotoOrientation {
  landscape,
  portrait,
  square,
}

enum BackupStatus {
  pending,
  inProgress,
  completed,
  failed,
}

enum PhotoSortBy {
  dateAsc,
  dateDesc,
  nameAsc,
  nameDesc,
  sizeAsc,
  sizeDesc,
  ratingAsc,
  ratingDesc,
}

// Extensions
extension PhotoOrientationExtension on PhotoOrientation {
  String get displayName {
    switch (this) {
      case PhotoOrientation.landscape:
        return 'Na šířku';
      case PhotoOrientation.portrait:
        return 'Na výšku';
      case PhotoOrientation.square:
        return 'Čtvercová';
    }
  }

  String get icon {
    switch (this) {
      case PhotoOrientation.landscape:
        return '📱';
      case PhotoOrientation.portrait:
        return '📲';
      case PhotoOrientation.square:
        return '⬜';
    }
  }
}
