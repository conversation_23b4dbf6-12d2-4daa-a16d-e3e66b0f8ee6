import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:image_picker/image_picker.dart';
import '../models/video_diary.dart';

class VideoDiaryService {
  static final VideoDiaryService _instance = VideoDiaryService._internal();
  factory VideoDiaryService() => _instance;
  VideoDiaryService._internal();

  final ImagePicker _imagePicker = ImagePicker();

  StreamController<VideoProcessingEvent>? _processingController;
  StreamController<VideoRecordingEvent>? _recordingController;

  bool _isRecording = false;
  bool _isProcessing = false;
  String _currentRecordingPath = '';
  Duration _recordingDuration = Duration.zero;
  Timer? _recordingTimer;

  /// Stream událostí zpracování videa
  Stream<VideoProcessingEvent> get processingStream {
    _processingController ??=
        StreamController<VideoProcessingEvent>.broadcast();
    return _processingController!.stream;
  }

  /// Stream událostí nahrávání videa
  Stream<VideoRecordingEvent> get recordingStream {
    _recordingController ??= StreamController<VideoRecordingEvent>.broadcast();
    return _recordingController!.stream;
  }

  /// Inicializace služby
  Future<void> initialize() async {
    // Na webu neinicializujeme permissions automaticky
    if (!kIsWeb) {
      await _requestPermissions();
    }
  }

  /// Požádání o povolení
  Future<bool> _requestPermissions() async {
    // Na webu dočasně vracíme false, aby se nevolaly permissions
    if (kIsWeb) {
      return false;
    } else {
      final cameraStatus = await Permission.camera.request();
      final microphoneStatus = await Permission.microphone.request();
      final storageStatus = await Permission.storage.request();
      return cameraStatus.isGranted &&
          microphoneStatus.isGranted &&
          storageStatus.isGranted;
    }
  }

  /// Spuštění nahrávání videa
  Future<String?> startVideoRecording({
    VideoQuality quality = VideoQuality.medium,
    bool enableAudio = true,
  }) async {
    if (_isRecording) return null;

    final hasPermission = await _requestPermissions();
    if (!hasPermission) {
      _recordingController?.add(
        VideoRecordingEvent(
          type: VideoRecordingEventType.error,
          error: 'Povolení k fotoaparátu bylo zamítnuto',
        ),
      );
      return null;
    }

    try {
      final directory = await getApplicationDocumentsDirectory();
      final videosDir = Directory('${directory.path}/video_diaries');
      if (!await videosDir.exists()) {
        await videosDir.create(recursive: true);
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      _currentRecordingPath = '${videosDir.path}/video_$timestamp.mp4';

      // Simulace nahrávání - v reálné aplikaci by se použil camera package
      _isRecording = true;
      _recordingDuration = Duration.zero;

      _startRecordingTimer();

      _recordingController?.add(
        VideoRecordingEvent(
          type: VideoRecordingEventType.recordingStarted,
          filePath: _currentRecordingPath,
        ),
      );

      return _currentRecordingPath;
    } catch (e) {
      _recordingController?.add(
        VideoRecordingEvent(
          type: VideoRecordingEventType.error,
          error: 'Chyba při spuštění nahrávání: $e',
        ),
      );
      return null;
    }
  }

  /// Spuštění časovače nahrávání
  void _startRecordingTimer() {
    _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isRecording) {
        timer.cancel();
        return;
      }

      _recordingDuration += const Duration(seconds: 1);
      _recordingController?.add(
        VideoRecordingEvent(
          type: VideoRecordingEventType.recordingProgress,
          duration: _recordingDuration,
        ),
      );
    });
  }

  /// Zastavení nahrávání videa
  Future<VideoDiary?> stopVideoRecording({
    String? title,
    String? description,
  }) async {
    if (!_isRecording) return null;

    try {
      _isRecording = false;
      _recordingTimer?.cancel();

      // Simulace vytvoření video souboru
      final file = File(_currentRecordingPath);
      await file.writeAsBytes([0]); // Placeholder pro skutečná video data

      // Vytvoření thumbnail
      final thumbnailPath = await _generateThumbnail(_currentRecordingPath);

      final videoDiary = VideoDiary(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title ?? 'Video deník',
        description: description,
        filePath: _currentRecordingPath,
        thumbnailPath: thumbnailPath,
        duration: _recordingDuration,
        createdAt: DateTime.now(),
        fileSize: await file.length(),
        width: 1920,
        height: 1080,
        quality: VideoQuality.medium,
        orientation: VideoOrientation.landscape,
      );

      _recordingController?.add(
        VideoRecordingEvent(
          type: VideoRecordingEventType.recordingStopped,
          videoDiary: videoDiary,
        ),
      );

      return videoDiary;
    } catch (e) {
      _recordingController?.add(
        VideoRecordingEvent(
          type: VideoRecordingEventType.error,
          error: 'Chyba při zastavení nahrávání: $e',
        ),
      );
      return null;
    }
  }

  /// Výběr videa z galerie
  Future<VideoDiary?> pickVideoFromGallery({
    String? title,
    String? description,
  }) async {
    try {
      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(minutes: 10),
      );

      if (video != null) {
        final file = File(video.path);
        final fileSize = await file.length();

        // Kopírování do app directory
        final directory = await getApplicationDocumentsDirectory();
        final videosDir = Directory('${directory.path}/video_diaries');
        if (!await videosDir.exists()) {
          await videosDir.create(recursive: true);
        }

        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final newPath = '${videosDir.path}/imported_$timestamp.mp4';
        await file.copy(newPath);

        // Vytvoření thumbnail
        final thumbnailPath = await _generateThumbnail(newPath);

        // Získání metadata videa (simulace)
        final metadata = await _getVideoMetadata(newPath);

        final videoDiary = VideoDiary(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          title: title ?? 'Importované video',
          description: description,
          filePath: newPath,
          thumbnailPath: thumbnailPath,
          duration: metadata['duration'] ?? const Duration(seconds: 30),
          createdAt: DateTime.now(),
          fileSize: fileSize,
          width: metadata['width'] ?? 1920,
          height: metadata['height'] ?? 1080,
          quality: VideoQuality.medium,
          orientation: _getOrientation(
            metadata['width'] ?? 1920,
            metadata['height'] ?? 1080,
          ),
        );

        return videoDiary;
      }
    } catch (e) {
      debugPrint('Chyba při výběru videa: $e');
    }
    return null;
  }

  /// Generování thumbnail z videa
  Future<String?> _generateThumbnail(String videoPath) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final thumbnailsDir = Directory('${directory.path}/thumbnails');
      if (!await thumbnailsDir.exists()) {
        await thumbnailsDir.create(recursive: true);
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final thumbnailPath = '${thumbnailsDir.path}/thumb_$timestamp.jpg';

      // Simulace generování thumbnail
      // V reálné aplikaci by se použila knihovna jako video_thumbnail
      final thumbnailFile = File(thumbnailPath);
      await thumbnailFile.writeAsBytes([0]); // Placeholder

      return thumbnailPath;
    } catch (e) {
      debugPrint('Chyba při generování thumbnail: $e');
      return null;
    }
  }

  /// Získání metadata videa
  Future<Map<String, dynamic>> _getVideoMetadata(String videoPath) async {
    // Simulace získání metadata
    // V reálné aplikaci by se použila knihovna jako video_player nebo ffmpeg
    return {
      'duration': const Duration(seconds: 30),
      'width': 1920,
      'height': 1080,
      'frameRate': 30.0,
      'hasAudio': true,
    };
  }

  /// Určení orientace videa
  VideoOrientation _getOrientation(int width, int height) {
    if (width > height) {
      return VideoOrientation.landscape;
    } else if (height > width) {
      return VideoOrientation.portrait;
    } else {
      return VideoOrientation.square;
    }
  }

  /// Aplikace filtru na video
  Future<VideoDiary?> applyFilter(VideoDiary video, VideoFilter filter) async {
    if (_isProcessing) return null;

    try {
      _isProcessing = true;

      _processingController?.add(
        VideoProcessingEvent(
          type: VideoProcessingEventType.filteringStarted,
          videoId: video.id,
          progress: 0.0,
        ),
      );

      // Simulace aplikace filtru
      await _simulateProcessing(video.id);

      final updatedFilters = List<VideoFilter>.from(video.filters);
      updatedFilters.add(filter);

      final updatedVideo = video.copyWith(
        filters: updatedFilters,
        isProcessed: true,
        updatedAt: DateTime.now(),
      );

      _processingController?.add(
        VideoProcessingEvent(
          type: VideoProcessingEventType.filteringCompleted,
          videoId: video.id,
          progress: 1.0,
        ),
      );

      _isProcessing = false;
      return updatedVideo;
    } catch (e) {
      _isProcessing = false;
      _processingController?.add(
        VideoProcessingEvent(
          type: VideoProcessingEventType.error,
          videoId: video.id,
          error: 'Chyba při aplikaci filtru: $e',
        ),
      );
      return null;
    }
  }

  /// Simulace zpracování videa
  Future<void> _simulateProcessing(String videoId) async {
    for (int i = 0; i <= 100; i += 10) {
      await Future.delayed(const Duration(milliseconds: 200));
      _processingController?.add(
        VideoProcessingEvent(
          type: VideoProcessingEventType.processing,
          videoId: videoId,
          progress: i / 100.0,
        ),
      );
    }
  }

  /// Kontrola stavu nahrávání
  bool get isRecording => _isRecording;

  /// Kontrola stavu zpracování
  bool get isProcessing => _isProcessing;

  /// Aktuální doba nahrávání
  Duration get recordingDuration => _recordingDuration;

  void dispose() {
    _recordingTimer?.cancel();
    _processingController?.close();
    _recordingController?.close();
  }
}

// Event třídy
class VideoProcessingEvent {
  final VideoProcessingEventType type;
  final String videoId;
  final double progress;
  final String? error;

  VideoProcessingEvent({
    required this.type,
    required this.videoId,
    this.progress = 0.0,
    this.error,
  });
}

class VideoRecordingEvent {
  final VideoRecordingEventType type;
  final String? filePath;
  final Duration? duration;
  final VideoDiary? videoDiary;
  final String? error;

  VideoRecordingEvent({
    required this.type,
    this.filePath,
    this.duration,
    this.videoDiary,
    this.error,
  });
}

enum VideoProcessingEventType {
  filteringStarted,
  processing,
  filteringCompleted,
  error,
}

enum VideoRecordingEventType {
  recordingStarted,
  recordingProgress,
  recordingStopped,
  error,
}
