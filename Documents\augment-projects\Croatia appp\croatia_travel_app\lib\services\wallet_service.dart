import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/wallet.dart';

/// 💳 WALLET SERVICE - Integrovaná peněženka
class WalletService {
  static final WalletService _instance = WalletService._internal();
  factory WalletService() => _instance;
  WalletService._internal();

  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  bool _isInitialized = false;

  // Aktuální stav peněženky
  Wallet? _currentWallet;
  final List<Transaction> _transactions = [];
  final List<LoyaltyCard> _loyaltyCards = [];
  final List<Reward> _availableRewards = [];

  /// Inicializace wallet služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('💳 Inicializuji Wallet Service...');

      await _loadWalletData();
      await _loadTransactions();
      await _loadLoyaltyCards();
      await _loadRewards();

      _isInitialized = true;
      debugPrint('✅ Wallet Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Wallet: $e');
      await _createDefaultWallet();
      _isInitialized = true;
    }
  }

  /// Přidání prostředků do peněženky
  Future<bool> addFunds(double amount, PaymentMethod method) async {
    try {
      if (_currentWallet == null) return false;

      // Simulace platební brány
      await Future.delayed(const Duration(seconds: 2));

      final transaction = Transaction(
        id: 'txn_${DateTime.now().millisecondsSinceEpoch}',
        type: TransactionType.deposit,
        amount: amount,
        currency: 'HRK',
        description: 'Dobití peněženky',
        timestamp: DateTime.now(),
        status: TransactionStatus.completed,
        paymentMethod: method,
      );

      _currentWallet = _currentWallet!.copyWith(
        balance: _currentWallet!.balance + amount,
      );

      _transactions.insert(0, transaction);
      await _saveWalletData();
      await _saveTransactions();

      debugPrint('💰 Přidáno ${amount} HRK do peněženky');
      return true;
    } catch (e) {
      debugPrint('❌ Chyba při přidávání prostředků: $e');
      return false;
    }
  }

  /// Platba z peněženky
  Future<bool> makePayment(
    double amount,
    String description,
    String merchantId,
  ) async {
    try {
      if (_currentWallet == null || _currentWallet!.balance < amount) {
        return false;
      }

      final transaction = Transaction(
        id: 'txn_${DateTime.now().millisecondsSinceEpoch}',
        type: TransactionType.payment,
        amount: amount,
        currency: 'HRK',
        description: description,
        timestamp: DateTime.now(),
        status: TransactionStatus.completed,
        merchantId: merchantId,
      );

      _currentWallet = _currentWallet!.copyWith(
        balance: _currentWallet!.balance - amount,
      );

      _transactions.insert(0, transaction);

      // Přidání loyalty bodů (1 bod za každých 10 HRK)
      final loyaltyPoints = (amount / 10).floor();
      if (loyaltyPoints > 0) {
        await _addLoyaltyPoints(loyaltyPoints, 'Platba u $description');
      }

      await _saveWalletData();
      await _saveTransactions();

      debugPrint('💸 Platba $amount HRK provedena');
      return true;
    } catch (e) {
      debugPrint('❌ Chyba při platbě: $e');
      return false;
    }
  }

  /// Vytvoření virtuální karty
  Future<VirtualCard?> createVirtualCard() async {
    try {
      if (_currentWallet == null) return null;

      final card = VirtualCard(
        id: 'card_${DateTime.now().millisecondsSinceEpoch}',
        walletId: _currentWallet!.id,
        cardNumber: _generateCardNumber(),
        expiryDate: DateTime.now().add(const Duration(days: 1095)), // 3 roky
        cvv: _generateCVV(),
        cardholderName: _currentWallet!.ownerName,
        isActive: true,
        createdAt: DateTime.now(),
      );

      _currentWallet = _currentWallet!.copyWith(
        virtualCards: [..._currentWallet!.virtualCards, card],
      );

      await _saveWalletData();
      debugPrint('💳 Virtuální karta vytvořena');
      return card;
    } catch (e) {
      debugPrint('❌ Chyba při vytváření karty: $e');
      return null;
    }
  }

  /// Získání historie transakcí
  Future<List<Transaction>> getTransactionHistory({
    int limit = 50,
    TransactionType? type,
  }) async {
    var transactions = List<Transaction>.from(_transactions);

    if (type != null) {
      transactions = transactions.where((t) => t.type == type).toList();
    }

    return transactions.take(limit).toList();
  }

  /// Přidání loyalty bodů
  Future<void> _addLoyaltyPoints(int points, String reason) async {
    try {
      // Najdeme hlavní loyalty kartu
      var mainCard = _loyaltyCards.firstWhere(
        (card) => card.type == LoyaltyCardType.main,
        orElse: () => _createMainLoyaltyCard(),
      );

      mainCard = mainCard.copyWith(points: mainCard.points + points);

      final index = _loyaltyCards.indexWhere((c) => c.id == mainCard.id);
      if (index >= 0) {
        _loyaltyCards[index] = mainCard;
      } else {
        _loyaltyCards.add(mainCard);
      }

      // Přidáme transakci pro body
      final transaction = Transaction(
        id: 'loyalty_${DateTime.now().millisecondsSinceEpoch}',
        type: TransactionType.loyalty,
        amount: points.toDouble(),
        currency: 'BODY',
        description: reason,
        timestamp: DateTime.now(),
        status: TransactionStatus.completed,
      );

      _transactions.insert(0, transaction);
      await _saveLoyaltyCards();
      await _saveTransactions();

      debugPrint('⭐ Přidáno $points loyalty bodů');
    } catch (e) {
      debugPrint('❌ Chyba při přidávání bodů: $e');
    }
  }

  /// Výměna bodů za odměnu
  Future<bool> redeemReward(String rewardId) async {
    try {
      final reward = _availableRewards.firstWhere((r) => r.id == rewardId);
      final mainCard = _loyaltyCards.firstWhere(
        (c) => c.type == LoyaltyCardType.main,
      );

      if (mainCard.points < reward.pointsCost) {
        return false;
      }

      // Odečteme body
      final updatedCard = mainCard.copyWith(
        points: mainCard.points - reward.pointsCost,
      );

      final index = _loyaltyCards.indexWhere((c) => c.id == mainCard.id);
      _loyaltyCards[index] = updatedCard;

      // Přidáme transakci
      final transaction = Transaction(
        id: 'reward_${DateTime.now().millisecondsSinceEpoch}',
        type: TransactionType.reward,
        amount: reward.pointsCost.toDouble(),
        currency: 'BODY',
        description: 'Výměna za: ${reward.title}',
        timestamp: DateTime.now(),
        status: TransactionStatus.completed,
      );

      _transactions.insert(0, transaction);
      await _saveLoyaltyCards();
      await _saveTransactions();

      debugPrint('🎁 Odměna ${reward.title} vyměněna');
      return true;
    } catch (e) {
      debugPrint('❌ Chyba při výměně odměny: $e');
      return false;
    }
  }

  /// Nastavení cashback programu
  Future<void> setupCashback(CashbackSettings settings) async {
    try {
      _currentWallet = _currentWallet!.copyWith(cashbackSettings: settings);

      await _saveWalletData();
      debugPrint('💰 Cashback program nastaven');
    } catch (e) {
      debugPrint('❌ Chyba při nastavení cashback: $e');
    }
  }

  /// Výpočet cashback
  double calculateCashback(double amount, String category) {
    if (_currentWallet?.cashbackSettings == null) return 0.0;

    final settings = _currentWallet!.cashbackSettings!;
    final rate = settings.categoryRates[category] ?? settings.defaultRate;

    return amount * (rate / 100);
  }

  /// Načtení dat peněženky
  Future<void> _loadWalletData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final walletJson = prefs.getString('wallet_data');

      if (walletJson != null) {
        final walletData = jsonDecode(walletJson);
        _currentWallet = Wallet.fromJson(walletData);
      }
    } catch (e) {
      debugPrint('❌ Chyba při načítání wallet dat: $e');
    }
  }

  /// Uložení dat peněženky
  Future<void> _saveWalletData() async {
    try {
      if (_currentWallet == null) return;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'wallet_data',
        jsonEncode(_currentWallet!.toJson()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání wallet dat: $e');
    }
  }

  /// Načtení transakcí
  Future<void> _loadTransactions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final transactionsJson = prefs.getString('wallet_transactions');

      if (transactionsJson != null) {
        final List<dynamic> data = jsonDecode(transactionsJson);
        _transactions.clear();
        _transactions.addAll(
          data.map((json) => Transaction.fromJson(json)).toList(),
        );
      }
    } catch (e) {
      debugPrint('❌ Chyba při načítání transakcí: $e');
    }
  }

  /// Uložení transakcí
  Future<void> _saveTransactions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'wallet_transactions',
        jsonEncode(_transactions.map((t) => t.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání transakcí: $e');
    }
  }

  /// Načtení loyalty karet
  Future<void> _loadLoyaltyCards() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cardsJson = prefs.getString('loyalty_cards');

      if (cardsJson != null) {
        final List<dynamic> data = jsonDecode(cardsJson);
        _loyaltyCards.clear();
        _loyaltyCards.addAll(
          data.map((json) => LoyaltyCard.fromJson(json)).toList(),
        );
      }
    } catch (e) {
      debugPrint('❌ Chyba při načítání loyalty karet: $e');
    }
  }

  /// Uložení loyalty karet
  Future<void> _saveLoyaltyCards() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'loyalty_cards',
        jsonEncode(_loyaltyCards.map((c) => c.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání loyalty karet: $e');
    }
  }

  /// Načtení odměn
  Future<void> _loadRewards() async {
    // Simulace načtení dostupných odměn
    _availableRewards.addAll([
      Reward(
        id: 'reward_1',
        title: '10% sleva na dopravu',
        description: 'Sleva na všechny druhy veřejné dopravy',
        pointsCost: 100,
        category: 'transport',
        validUntil: DateTime.now().add(const Duration(days: 30)),
      ),
      Reward(
        id: 'reward_2',
        title: 'Zdarma káva',
        description: 'Káva zdarma v partnerských kavárnách',
        pointsCost: 50,
        category: 'food',
        validUntil: DateTime.now().add(const Duration(days: 30)),
      ),
    ]);
  }

  /// Vytvoření výchozí peněženky
  Future<void> _createDefaultWallet() async {
    _currentWallet = Wallet(
      id: 'wallet_${DateTime.now().millisecondsSinceEpoch}',
      ownerName: 'Uživatel',
      balance: 0.0,
      currency: 'HRK',
      createdAt: DateTime.now(),
      isActive: true,
      virtualCards: [],
    );

    await _saveWalletData();
  }

  /// Vytvoření hlavní loyalty karty
  LoyaltyCard _createMainLoyaltyCard() {
    return LoyaltyCard(
      id: 'loyalty_main',
      name: 'Croatia Travel Card',
      type: LoyaltyCardType.main,
      points: 0,
      tier: LoyaltyTier.bronze,
      issuedAt: DateTime.now(),
    );
  }

  /// Generování čísla karty
  String _generateCardNumber() {
    // Simulace generování čísla karty (ne skutečné!)
    return '4532 1234 5678 ${DateTime.now().millisecondsSinceEpoch.toString().substring(7)}';
  }

  /// Generování CVV
  String _generateCVV() {
    return (100 + (DateTime.now().millisecondsSinceEpoch % 900)).toString();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  Wallet? get currentWallet => _currentWallet;
  List<Transaction> get recentTransactions => _transactions.take(10).toList();
  List<LoyaltyCard> get loyaltyCards => List.unmodifiable(_loyaltyCards);
  List<Reward> get availableRewards => List.unmodifiable(_availableRewards);
}
