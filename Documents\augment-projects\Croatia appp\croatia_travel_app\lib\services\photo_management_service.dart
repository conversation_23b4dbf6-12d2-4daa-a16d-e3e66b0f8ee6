import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import '../models/photo.dart';
import '../data/local_database.dart';

class PhotoManagementService {
  static final PhotoManagementService _instance =
      PhotoManagementService._internal();
  factory PhotoManagementService() => _instance;
  PhotoManagementService._internal();

  final LocalDatabase _localDb = LocalDatabase();

  final Map<String, PhotoAlbum> _albums = {};
  final Map<String, List<String>> _photoTags = {};

  /// Inicializace služby
  Future<void> initialize() async {
    await _loadAlbums();
    await _loadPhotoTags();
  }

  /// Načtení alb z databáze
  Future<void> _loadAlbums() async {
    // Simulace načtení alb
    _albums.addAll({
      'croatia_trip_2024': PhotoAlbum(
        id: 'croatia_trip_2024',
        name: 'Chorvatsko 2024',
        description: 'Letní dovolená v Chorvatsku',
        coverPhotoId: 'photo_001',
        photoIds: ['photo_001', 'photo_002', 'photo_003'],
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
        isPublic: false,
        tags: ['dovolená', 'chorvatsko', 'léto'],
      ),
    });
  }

  /// Načtení tagů fotografií
  Future<void> _loadPhotoTags() async {
    // Simulace načtení tagů
    _photoTags.addAll({
      'photo_001': ['dubrovník', 'hradby', 'západ slunce'],
      'photo_002': ['plitvická jezera', 'vodopády', 'příroda'],
      'photo_003': ['split', 'palác', 'architektura'],
    });
  }

  /// Zpracování nové fotografie
  Future<TravelPhoto> processPhoto({
    required File imageFile,
    String? albumId,
    List<String>? manualTags,
    String? location,
    DateTime? takenAt,
  }) async {
    try {
      // Načtení a analýza obrázku
      final imageBytes = await imageFile.readAsBytes();
      final image = img.decodeImage(imageBytes);

      if (image == null) {
        throw Exception('Nepodařilo se načíst obrázek');
      }

      // Optimalizace obrázku
      final optimizedImage = await _optimizeImage(image);
      final optimizedBytes = img.encodeJpg(optimizedImage, quality: 85);

      // Vytvoření náhledu
      final thumbnail = await _createThumbnail(image);
      final thumbnailBytes = img.encodeJpg(thumbnail, quality: 70);

      // Uložení optimalizovaných verzí
      final photoId = DateTime.now().millisecondsSinceEpoch.toString();
      final optimizedPath = await _saveOptimizedPhoto(photoId, optimizedBytes);
      final thumbnailPath = await _saveThumbnail(photoId, thumbnailBytes);

      // Extrakce metadat
      final metadata = await _extractMetadata(imageFile, image);

      // Automatické tagování
      final autoTags = await _generateAutoTags(optimizedImage, location);
      final allTags = [...(manualTags ?? []), ...autoTags];

      // Vytvoření objektu fotografie
      final photo = TravelPhoto(
        id: photoId,
        originalPath: imageFile.path,
        optimizedPath: optimizedPath,
        thumbnailPath: thumbnailPath,
        albumId: albumId,
        tags: allTags,
        location: location,
        takenAt: takenAt ?? DateTime.now(),
        createdAt: DateTime.now(),
        metadata: metadata,
        isProcessed: true,
      );

      // Uložení do databáze
      // Konverze TravelPhoto na Photo pro databázi
      final dbPhoto = Photo(
        id: photo.id,
        path: photo.originalPath,
        takenAt: photo.takenAt,
        location: photo.location,
        tags: photo.tags,
      );
      await _localDb.savePhoto(dbPhoto);

      // Přidání do alba
      if (albumId != null) {
        await _addPhotoToAlbum(albumId, photoId);
      }

      return photo;
    } catch (e) {
      throw Exception('Chyba při zpracování fotografie: $e');
    }
  }

  /// Optimalizace obrázku
  Future<img.Image> _optimizeImage(img.Image original) async {
    // Změna velikosti pokud je příliš velký
    const maxWidth = 1920;
    const maxHeight = 1080;

    if (original.width > maxWidth || original.height > maxHeight) {
      final aspectRatio = original.width / original.height;
      int newWidth, newHeight;

      if (aspectRatio > 1) {
        // Landscape
        newWidth = maxWidth;
        newHeight = (maxWidth / aspectRatio).round();
      } else {
        // Portrait
        newHeight = maxHeight;
        newWidth = (maxHeight * aspectRatio).round();
      }

      return img.copyResize(original, width: newWidth, height: newHeight);
    }

    return original;
  }

  /// Vytvoření náhledu
  Future<img.Image> _createThumbnail(img.Image original) async {
    const thumbnailSize = 300;

    // Vytvoření čtvercového náhledu
    final size = original.width < original.height
        ? original.width
        : original.height;
    final x = (original.width - size) ~/ 2;
    final y = (original.height - size) ~/ 2;

    final cropped = img.copyCrop(
      original,
      x: x,
      y: y,
      width: size,
      height: size,
    );
    return img.copyResize(cropped, width: thumbnailSize, height: thumbnailSize);
  }

  /// Uložení optimalizované fotografie
  Future<String> _saveOptimizedPhoto(String photoId, Uint8List bytes) async {
    final path = 'photos/optimized/$photoId.jpg';
    // Simulace uložení
    await Future.delayed(const Duration(milliseconds: 100));
    return path;
  }

  /// Uložení náhledu
  Future<String> _saveThumbnail(String photoId, Uint8List bytes) async {
    final path = 'photos/thumbnails/$photoId.jpg';
    // Simulace uložení
    await Future.delayed(const Duration(milliseconds: 50));
    return path;
  }

  /// Extrakce metadat
  Future<PhotoMetadata> _extractMetadata(File file, img.Image image) async {
    final stats = await file.stat();

    return PhotoMetadata(
      width: image.width,
      height: image.height,
      fileSize: stats.size,
      format: 'JPEG',
      colorSpace: 'sRGB',
      orientation: _getOrientation(image),
      hasExif: false, // Simulace
      gpsCoordinates: null, // Simulace
      cameraInfo: null, // Simulace
    );
  }

  /// Určení orientace obrázku
  PhotoOrientation _getOrientation(img.Image image) {
    if (image.width > image.height) {
      return PhotoOrientation.landscape;
    } else if (image.height > image.width) {
      return PhotoOrientation.portrait;
    } else {
      return PhotoOrientation.square;
    }
  }

  /// Automatické generování tagů
  Future<List<String>> _generateAutoTags(
    img.Image image,
    String? location,
  ) async {
    final tags = <String>[];

    // Analýza barev
    final colorTags = _analyzeColors(image);
    tags.addAll(colorTags);

    // Analýza kompozice
    final compositionTags = _analyzeComposition(image);
    tags.addAll(compositionTags);

    // Tagy podle lokace
    if (location != null) {
      final locationTags = _getLocationTags(location);
      tags.addAll(locationTags);
    }

    // Časové tagy
    final timeTags = _getTimeTags();
    tags.addAll(timeTags);

    return tags.toSet().toList(); // Odstranění duplikátů
  }

  /// Analýza barev pro tagy
  List<String> _analyzeColors(img.Image image) {
    final tags = <String>[];

    // Zjednodušená analýza dominantních barev
    final pixels = image.data;
    int redSum = 0, greenSum = 0, blueSum = 0;

    if (pixels != null) {
      for (final pixel in pixels) {
        redSum += pixel.r.toInt();
        greenSum += pixel.g.toInt();
        blueSum += pixel.b.toInt();
      }
    }

    final pixelCount = pixels?.length ?? 0;
    if (pixelCount == 0) return ['#000000'];

    final avgRed = redSum / pixelCount;
    final avgGreen = greenSum / pixelCount;
    final avgBlue = blueSum / pixelCount;

    // Určení dominantní barvy
    if (avgBlue > avgRed && avgBlue > avgGreen) {
      tags.add('modrá');
      if (avgBlue > 150) tags.add('jasná');
    } else if (avgGreen > avgRed && avgGreen > avgBlue) {
      tags.add('zelená');
      tags.add('příroda');
    } else if (avgRed > avgGreen && avgRed > avgBlue) {
      tags.add('červená');
      tags.add('teplá');
    }

    // Analýza jasu
    final brightness = (avgRed + avgGreen + avgBlue) / 3;
    if (brightness > 200) {
      tags.add('světlá');
    } else if (brightness < 100) {
      tags.add('tmavá');
    }

    return tags;
  }

  /// Analýza kompozice
  List<String> _analyzeComposition(img.Image image) {
    final tags = <String>[];

    // Analýza poměru stran
    final aspectRatio = image.width / image.height;
    if (aspectRatio > 1.5) {
      tags.add('panorama');
    } else if (aspectRatio < 0.8) {
      tags.add('portrét');
    }

    // Simulace detekce objektů
    if (DateTime.now().millisecond % 3 == 0) {
      tags.add('architektura');
    }
    if (DateTime.now().millisecond % 4 == 0) {
      tags.add('krajina');
    }
    if (DateTime.now().millisecond % 5 == 0) {
      tags.add('lidé');
    }

    return tags;
  }

  /// Tagy podle lokace
  List<String> _getLocationTags(String location) {
    final tags = <String>[];
    final lowerLocation = location.toLowerCase();

    if (lowerLocation.contains('dubrovník')) {
      tags.addAll(['dubrovník', 'dalmácie', 'hradby', 'unesco']);
    } else if (lowerLocation.contains('split')) {
      tags.addAll(['split', 'dalmácie', 'palác', 'diokleciánus']);
    } else if (lowerLocation.contains('plitvice')) {
      tags.addAll(['plitvická jezera', 'národní park', 'vodopády', 'příroda']);
    } else if (lowerLocation.contains('zagreb')) {
      tags.addAll(['zagreb', 'hlavní město', 'horní město']);
    } else if (lowerLocation.contains('rovinj')) {
      tags.addAll(['rovinj', 'istrie', 'romantické město']);
    }

    return tags;
  }

  /// Časové tagy
  List<String> _getTimeTags() {
    final now = DateTime.now();
    final tags = <String>[];

    // Roční období
    final month = now.month;
    if (month >= 3 && month <= 5) {
      tags.add('jaro');
    } else if (month >= 6 && month <= 8) {
      tags.add('léto');
    } else if (month >= 9 && month <= 11) {
      tags.add('podzim');
    } else {
      tags.add('zima');
    }

    // Denní doba
    final hour = now.hour;
    if (hour >= 5 && hour < 12) {
      tags.add('ráno');
    } else if (hour >= 12 && hour < 17) {
      tags.add('odpoledne');
    } else if (hour >= 17 && hour < 21) {
      tags.add('večer');
    } else {
      tags.add('noc');
    }

    return tags;
  }

  /// Přidání fotografie do alba
  Future<void> _addPhotoToAlbum(String albumId, String photoId) async {
    final album = _albums[albumId];
    if (album != null) {
      final updatedPhotoIds = [...album.photoIds, photoId];
      final updatedAlbum = PhotoAlbum(
        id: album.id,
        name: album.name,
        description: album.description,
        coverPhotoId: album.coverPhotoId,
        photoIds: updatedPhotoIds,
        createdAt: album.createdAt,
        updatedAt: DateTime.now(),
        isPublic: album.isPublic,
        tags: album.tags,
      );

      _albums[albumId] = updatedAlbum;
      await _localDb.saveAlbum({
        'id': updatedAlbum.id,
        'name': updatedAlbum.name,
        'description': updatedAlbum.description,
        'coverPhotoId': updatedAlbum.coverPhotoId,
        'photoIds': updatedAlbum.photoIds,
        'createdAt': updatedAlbum.createdAt.toIso8601String(),
        'updatedAt': updatedAlbum.updatedAt.toIso8601String(),
      });
    }
  }

  /// Vytvoření nového alba
  Future<PhotoAlbum> createAlbum({
    required String name,
    String? description,
    List<String>? tags,
    bool isPublic = false,
  }) async {
    final albumId = DateTime.now().millisecondsSinceEpoch.toString();

    final album = PhotoAlbum(
      id: albumId,
      name: name,
      description: description ?? '',
      coverPhotoId: null,
      photoIds: [],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isPublic: isPublic,
      tags: tags ?? [],
    );

    _albums[albumId] = album;
    await _localDb.saveAlbum({
      'id': album.id,
      'name': album.name,
      'description': album.description,
      'coverPhotoId': album.coverPhotoId,
      'photoIds': album.photoIds,
      'createdAt': album.createdAt.toIso8601String(),
      'updatedAt': album.updatedAt.toIso8601String(),
    });

    return album;
  }

  /// Vyhledání fotografií podle tagů
  Future<List<TravelPhoto>> searchPhotosByTags(List<String> tags) async {
    final allPhotos = await _localDb.getAllPhotos();

    return allPhotos
        .where((photo) {
          return tags.any(
            (tag) => photo.tags.any(
              (photoTag) => photoTag.toLowerCase().contains(tag.toLowerCase()),
            ),
          );
        })
        .map(
          (photo) => TravelPhoto(
            id: photo.id,
            originalPath: photo.path,
            optimizedPath: photo.path,
            thumbnailPath: photo.path,
            albumId: null,
            tags: photo.tags,
            location: photo.location,
            takenAt: photo.takenAt ?? DateTime.now(),
            createdAt: DateTime.now(),
            metadata: PhotoMetadata(
              fileSize: 1000000,
              width: 1920,
              height: 1080,
              format: 'jpg',
              colorSpace: 'sRGB',
              orientation: PhotoOrientation.landscape,
              hasExif: false,
            ),
            isProcessed: false,
          ),
        )
        .toList();
  }

  /// Vyhledání fotografií podle lokace
  Future<List<TravelPhoto>> searchPhotosByLocation(String location) async {
    final allPhotos = await _localDb.getAllPhotos();

    return allPhotos
        .where((photo) {
          return photo.location?.toLowerCase().contains(
                location.toLowerCase(),
              ) ??
              false;
        })
        .map(
          (photo) => TravelPhoto(
            id: photo.id,
            originalPath: photo.path,
            optimizedPath: photo.path,
            thumbnailPath: photo.path,
            albumId: null,
            tags: photo.tags,
            location: photo.location,
            takenAt: photo.takenAt ?? DateTime.now(),
            createdAt: DateTime.now(),
            metadata: PhotoMetadata(
              fileSize: 1000000,
              width: 1920,
              height: 1080,
              format: 'jpg',
              colorSpace: 'sRGB',
              orientation: PhotoOrientation.landscape,
              hasExif: false,
            ),
            isProcessed: false,
          ),
        )
        .toList();
  }

  /// Vyhledání fotografií podle data
  Future<List<TravelPhoto>> searchPhotosByDate(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final allPhotos = await _localDb.getAllPhotos();

    return allPhotos
        .where((photo) {
          return photo.takenAt?.isAfter(startDate) == true &&
              photo.takenAt?.isBefore(endDate) == true;
        })
        .map(
          (photo) => TravelPhoto(
            id: photo.id,
            originalPath: photo.path,
            optimizedPath: photo.path,
            thumbnailPath: photo.path,
            albumId: null,
            tags: photo.tags,
            location: photo.location,
            takenAt: photo.takenAt ?? DateTime.now(),
            createdAt: DateTime.now(),
            metadata: PhotoMetadata(
              fileSize: 1000000,
              width: 1920,
              height: 1080,
              format: 'jpg',
              colorSpace: 'sRGB',
              orientation: PhotoOrientation.landscape,
              hasExif: false,
            ),
            isProcessed: false,
          ),
        )
        .toList();
  }

  /// Získání statistik fotografií
  Future<PhotoStatistics> getPhotoStatistics() async {
    final allPhotos = await _localDb.getAllPhotos();
    final allAlbums = _albums.values.toList();

    // Analýza tagů
    final tagCounts = <String, int>{};
    for (final photo in allPhotos) {
      for (final tag in photo.tags) {
        tagCounts[tag] = (tagCounts[tag] ?? 0) + 1;
      }
    }

    // Analýza lokací
    final locationCounts = <String, int>{};
    for (final photo in allPhotos) {
      if (photo.location != null) {
        locationCounts[photo.location!] =
            (locationCounts[photo.location!] ?? 0) + 1;
      }
    }

    // Celková velikost
    int totalSize = allPhotos.length * 1000000; // Simulace velikosti souboru

    return PhotoStatistics(
      totalPhotos: allPhotos.length,
      totalAlbums: allAlbums.length,
      totalSize: totalSize,
      mostUsedTags: tagCounts.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value)),
      photosByLocation: locationCounts,
      photosByMonth: _groupPhotosByMonth(
        allPhotos
            .map(
              (photo) => TravelPhoto(
                id: photo.id,
                originalPath: photo.path,
                optimizedPath: photo.path,
                thumbnailPath: photo.path,
                albumId: null,
                tags: photo.tags,
                location: photo.location,
                takenAt: photo.takenAt ?? DateTime.now(),
                createdAt: DateTime.now(),
                metadata: PhotoMetadata(
                  fileSize: 1000000,
                  width: 1920,
                  height: 1080,
                  format: 'jpg',
                  colorSpace: 'sRGB',
                  orientation: PhotoOrientation.landscape,
                  hasExif: false,
                ),
                isProcessed: false,
              ),
            )
            .toList(),
      ),
    );
  }

  /// Seskupení fotografií podle měsíce
  Map<String, int> _groupPhotosByMonth(List<TravelPhoto> photos) {
    final monthCounts = <String, int>{};

    for (final photo in photos) {
      final monthKey =
          '${photo.takenAt.year}-${photo.takenAt.month.toString().padLeft(2, '0')}';
      monthCounts[monthKey] = (monthCounts[monthKey] ?? 0) + 1;
    }

    return monthCounts;
  }

  /// Optimalizace úložiště
  Future<StorageOptimizationResult> optimizeStorage() async {
    int savedSpace = 0;
    int processedFiles = 0;

    final allPhotos = await _localDb.getAllPhotos();

    // Simulace optimalizace všech fotografií
    savedSpace = allPhotos.length * 500000; // 500KB průměrně na foto
    processedFiles = allPhotos.length;

    return StorageOptimizationResult(
      savedSpace: savedSpace,
      processedFiles: processedFiles,
      totalFiles: allPhotos.length,
    );
  }

  /// Získání všech alb
  List<PhotoAlbum> getAllAlbums() {
    return _albums.values.toList();
  }

  /// Získání alba podle ID
  PhotoAlbum? getAlbum(String albumId) {
    return _albums[albumId];
  }
}
