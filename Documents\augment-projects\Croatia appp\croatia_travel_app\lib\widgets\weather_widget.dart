import 'package:flutter/material.dart';

class WeatherWidget extends StatefulWidget {
  const WeatherWidget({super.key});

  @override
  State<WeatherWidget> createState() => _WeatherWidgetState();
}

class _WeatherWidgetState extends State<WeatherWidget> {
  final String _location = 'Zagreb';
  final double _temperature = 22.0;
  final String _condition = 'Slunečno';
  final IconData _weatherIcon = Icons.wb_sunny;
  List<WeatherForecast> _forecast = [];

  @override
  void initState() {
    super.initState();
    _loadWeatherData();
  }

  Future<void> _loadWeatherData() async {
    // Simulace načtení dat o počasí
    // V reálné aplikaci by se použilo API jako OpenWeatherMap

    setState(() {
      _forecast = [
        WeatherForecast('Dnes', 22, 18, Icons.wb_sunny, 'Slunečno'),
        WeatherForecast('Zítra', 24, 19, Icons.wb_sunny, '<PERSON>lu<PERSON><PERSON><PERSON>'),
        WeatherForecast('Po', 20, 15, Icons.cloud, '<PERSON><PERSON>č<PERSON>'),
        WeatherForecast('Út', 18, 12, Icons.grain, '<PERSON><PERSON><PERSON><PERSON>'),
        WeatherForecast('St', 21, 16, Icons.wb_sunny_outlined, 'Polojasno'),
      ];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  _location,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: _loadWeatherData,
                  icon: const Icon(Icons.refresh),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Aktuální počasí
            Row(
              children: [
                Icon(_weatherIcon, size: 48, color: Colors.orange),
                const SizedBox(width: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${_temperature.round()}°C',
                      style: const TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      _condition,
                      style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Předpověď na další dny
            const Text(
              'Předpověď',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: 8),

            SizedBox(
              height: 80,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _forecast.length,
                itemBuilder: (context, index) {
                  final forecast = _forecast[index];
                  return Container(
                    width: 80,
                    margin: const EdgeInsets.only(right: 12),
                    child: Column(
                      children: [
                        Text(
                          forecast.day,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Icon(forecast.icon, size: 24, color: Colors.orange),
                        const SizedBox(height: 4),
                        Text(
                          '${forecast.maxTemp}°',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '${forecast.minTemp}°',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class WeatherForecast {
  final String day;
  final int maxTemp;
  final int minTemp;
  final IconData icon;
  final String condition;

  WeatherForecast(
    this.day,
    this.maxTemp,
    this.minTemp,
    this.icon,
    this.condition,
  );
}
