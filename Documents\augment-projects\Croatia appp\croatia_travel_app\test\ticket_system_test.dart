import 'package:flutter_test/flutter_test.dart';
import 'package:croatia_travel_app/models/ticket.dart';
import 'package:croatia_travel_app/services/ticket_service.dart';

void main() {
  group('Ticket System Tests', () {
    late TicketService ticketService;

    setUp(() {
      ticketService = TicketService();
    });

    test('TicketService initialization', () {
      expect(ticketService, isNotNull);
      expect(ticketService.tickets, isEmpty);
      expect(ticketService.favoriteTickets, isEmpty);
      expect(ticketService.isLoading, isFalse);
    });

    test('Load tickets successfully', () async {
      await ticketService.initialize();
      
      expect(ticketService.tickets, isNotEmpty);
      expect(ticketService.tickets.length, greaterThan(5));
      expect(ticketService.isLoading, isFalse);
    });

    test('Ticket model validation', () async {
      await ticketService.initialize();
      final tickets = ticketService.tickets;
      
      for (final ticket in tickets) {
        expect(ticket.id, isNotEmpty);
        expect(ticket.title, isNotEmpty);
        expect(ticket.description, isNotEmpty);
        expect(ticket.venueName, isNotEmpty);
        expect(ticket.location, isNotEmpty);
        expect(ticket.region, isNotEmpty);
        expect(ticket.pricing.categories, isNotEmpty);
        expect(ticket.availability, isNotEmpty);
        expect(ticket.provider.name, isNotEmpty);
        expect(ticket.provider.officialWebsite, isNotEmpty);
        expect(ticket.provider.isVerified, isTrue);
        expect(ticket.isActive, isTrue);
      }
    });

    test('Ticket pricing validation', () async {
      await ticketService.initialize();
      final tickets = ticketService.tickets;
      
      for (final ticket in tickets) {
        expect(ticket.pricing.categories, isNotEmpty);
        expect(ticket.pricing.currency, equals('EUR'));
        expect(ticket.pricing.includesVAT, isTrue);
        
        // Ověř, že má alespoň dospělou kategorii
        final hasAdultCategory = ticket.pricing.categories
            .any((cat) => cat.category == TicketCategory.adult);
        expect(hasAdultCategory, isTrue);
        
        // Ověř, že ceny jsou rozumné
        for (final category in ticket.pricing.categories) {
          expect(category.price, greaterThanOrEqualTo(0));
          expect(category.price, lessThan(100)); // Rozumná horní hranice
          expect(category.description, isNotEmpty);
        }
      }
    });

    test('Group discounts validation', () async {
      await ticketService.initialize();
      final tickets = ticketService.tickets;
      
      final ticketsWithGroupDiscounts = tickets
          .where((ticket) => ticket.hasGroupDiscounts)
          .toList();
      
      expect(ticketsWithGroupDiscounts, isNotEmpty);
      
      for (final ticket in ticketsWithGroupDiscounts) {
        for (final discount in ticket.pricing.groupDiscounts) {
          expect(discount.minPeople, greaterThan(1));
          expect(discount.discountPercentage, greaterThan(0));
          expect(discount.discountPercentage, lessThanOrEqualTo(50));
          expect(discount.description, isNotEmpty);
        }
      }
    });

    test('Seasonal discounts validation', () async {
      await ticketService.initialize();
      final tickets = ticketService.tickets;
      
      final ticketsWithSeasonalDiscounts = tickets
          .where((ticket) => ticket.hasSeasonalDiscounts)
          .toList();
      
      expect(ticketsWithSeasonalDiscounts, isNotEmpty);
      
      for (final ticket in ticketsWithSeasonalDiscounts) {
        for (final discount in ticket.pricing.seasonalDiscounts) {
          expect(discount.startDate.isBefore(discount.endDate), isTrue);
          expect(discount.discountPercentage, greaterThan(0));
          expect(discount.discountPercentage, lessThanOrEqualTo(100));
          expect(discount.description, isNotEmpty);
        }
      }
    });

    test('Ticket availability validation', () async {
      await ticketService.initialize();
      final tickets = ticketService.tickets;
      
      for (final ticket in tickets) {
        expect(ticket.availability, isNotEmpty);
        
        for (final availability in ticket.availability) {
          expect(availability.startDate.isBefore(availability.endDate), isTrue);
          expect(availability.availableDays, isNotEmpty);
          expect(availability.availableDays.every((day) => day >= 1 && day <= 7), isTrue);
          
          if (availability.openingHours != null) {
            expect(availability.openingHours, isNotEmpty);
          }
        }
      }
    });

    test('Ticket provider validation', () async {
      await ticketService.initialize();
      final tickets = ticketService.tickets;
      
      for (final ticket in tickets) {
        final provider = ticket.provider;
        
        expect(provider.name, isNotEmpty);
        expect(provider.officialWebsite, isNotEmpty);
        expect(provider.officialWebsite, startsWith('https://'));
        expect(provider.type, isIn([ProviderType.official, ProviderType.authorized, ProviderType.partner]));
        expect(provider.isVerified, isTrue);
        expect(provider.rating, greaterThanOrEqualTo(0));
        expect(provider.rating, lessThanOrEqualTo(5));
        
        if (provider.bookingUrl != null) {
          expect(provider.bookingUrl, startsWith('https://'));
        }
      }
    });

    test('Search tickets functionality', () async {
      await ticketService.initialize();
      
      // Test vyhledávání podle názvu
      final dubrovnikTickets = ticketService.searchTickets(query: 'Dubrovník');
      expect(dubrovnikTickets, isNotEmpty);
      expect(dubrovnikTickets.every((t) => 
        t.title.toLowerCase().contains('dubrovník') ||
        t.location.toLowerCase().contains('dubrovník')
      ), isTrue);
      
      // Test filtrování podle typu
      final museumTickets = ticketService.searchTickets(type: TicketType.museum);
      expect(museumTickets, isNotEmpty);
      expect(museumTickets.every((t) => t.type == TicketType.museum), isTrue);
      
      // Test filtrování podle regionu
      final dubrovnikRegionTickets = ticketService.searchTickets(region: 'Dubrovnicko-neretvanská');
      expect(dubrovnikRegionTickets, isNotEmpty);
      expect(dubrovnikRegionTickets.every((t) => t.region == 'Dubrovnicko-neretvanská'), isTrue);
      
      // Test filtrování podle maximální ceny
      final cheapTickets = ticketService.searchTickets(maxPrice: 15.0);
      expect(cheapTickets, isNotEmpty);
      
      // Test filtrování podle skupinových slev
      final groupDiscountTickets = ticketService.searchTickets(hasGroupDiscounts: true);
      expect(groupDiscountTickets, isNotEmpty);
      expect(groupDiscountTickets.every((t) => t.hasGroupDiscounts), isTrue);
    });

    test('Favorites functionality', () async {
      await ticketService.initialize();
      final tickets = ticketService.tickets;
      expect(tickets, isNotEmpty);
      
      final testTicket = tickets.first;
      
      // Přidej do oblíbených
      expect(ticketService.isFavorite(testTicket.id), isFalse);
      await ticketService.addToFavorites(testTicket);
      expect(ticketService.isFavorite(testTicket.id), isTrue);
      expect(ticketService.favoriteTickets, contains(testTicket));
      
      // Odeber z oblíbených
      await ticketService.removeFromFavorites(testTicket.id);
      expect(ticketService.isFavorite(testTicket.id), isFalse);
      expect(ticketService.favoriteTickets, isNot(contains(testTicket)));
    });

    test('Get tickets by type', () async {
      await ticketService.initialize();
      
      final museumTickets = ticketService.getTicketsByType(TicketType.museum);
      expect(museumTickets.every((t) => t.type == TicketType.museum), isTrue);
      
      final monumentTickets = ticketService.getTicketsByType(TicketType.monument);
      expect(monumentTickets.every((t) => t.type == TicketType.monument), isTrue);
      
      final parkTickets = ticketService.getTicketsByType(TicketType.park);
      expect(parkTickets.every((t) => t.type == TicketType.park), isTrue);
    });

    test('Get tickets by region', () async {
      await ticketService.initialize();
      
      final dubrovnikTickets = ticketService.getTicketsByRegion('Dubrovnicko-neretvanská');
      expect(dubrovnikTickets.every((t) => t.region == 'Dubrovnicko-neretvanská'), isTrue);
      
      final splitTickets = ticketService.getTicketsByRegion('Splitsko-dalmatinská');
      expect(splitTickets.every((t) => t.region == 'Splitsko-dalmatinská'), isTrue);
      
      final zagrebTickets = ticketService.getTicketsByRegion('Záhřebská');
      expect(zagrebTickets.every((t) => t.region == 'Záhřebská'), isTrue);
    });

    test('Ticket price calculation for groups', () async {
      await ticketService.initialize();
      final tickets = ticketService.tickets;
      
      final ticketsWithGroupDiscounts = tickets
          .where((ticket) => ticket.hasGroupDiscounts)
          .toList();
      
      expect(ticketsWithGroupDiscounts, isNotEmpty);
      
      for (final ticket in ticketsWithGroupDiscounts) {
        final basePrice = ticket.getPriceForGroup(TicketCategory.adult, 1);
        expect(basePrice, isNotNull);
        expect(basePrice!, greaterThan(0));
        
        // Test skupinové slevy
        final groupPrice = ticket.getPriceForGroup(TicketCategory.adult, 15);
        expect(groupPrice, isNotNull);
        expect(groupPrice!, lessThan(basePrice * 15)); // Měla by být sleva
      }
    });

    test('Ticket availability check', () async {
      await ticketService.initialize();
      final tickets = ticketService.tickets;
      
      final today = DateTime.now();
      final tomorrow = today.add(const Duration(days: 1));
      
      for (final ticket in tickets) {
        // Většina vstupenek by měla být dostupná dnes nebo zítra
        final availableToday = ticket.isAvailableForDate(today);
        final availableTomorrow = ticket.isAvailableForDate(tomorrow);
        
        // Alespoň jeden z dnů by měl být dostupný
        expect(availableToday || availableTomorrow, isTrue);
      }
    });

    test('Specific ticket validation - Dubrovnik Walls', () async {
      await ticketService.initialize();
      final tickets = ticketService.tickets;
      
      final dubrovnikWalls = tickets
          .where((t) => t.id == 'dubrovnik_walls')
          .firstOrNull;
      
      expect(dubrovnikWalls, isNotNull);
      expect(dubrovnikWalls!.title, equals('Dubrovnické hradby'));
      expect(dubrovnikWalls.type, equals(TicketType.monument));
      expect(dubrovnikWalls.hasGroupDiscounts, isTrue);
      expect(dubrovnikWalls.hasSeasonalDiscounts, isTrue);
      expect(dubrovnikWalls.features.hasQRCode, isTrue);
      expect(dubrovnikWalls.features.isMobileTicket, isTrue);
    });

    test('Specific ticket validation - Plitvice Lakes', () async {
      await ticketService.initialize();
      final tickets = ticketService.tickets;
      
      final plitvice = tickets
          .where((t) => t.id == 'plitvice_lakes')
          .firstOrNull;
      
      expect(plitvice, isNotNull);
      expect(plitvice!.title, equals('Plitvická jezera - Národní park'));
      expect(plitvice.type, equals(TicketType.park));
      expect(plitvice.hasSeasonalDiscounts, isTrue);
      expect(plitvice.features.allowsCancellation, isTrue);
      expect(plitvice.features.allowsRescheduling, isTrue);
    });
  });
}
