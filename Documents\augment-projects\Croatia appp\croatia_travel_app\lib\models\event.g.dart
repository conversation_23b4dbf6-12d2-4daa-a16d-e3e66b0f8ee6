// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'event.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Event _$EventFromJson(Map<String, dynamic> json) => Event(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      location: json['location'] as String,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      region: json['region'] as String,
      type: $enumDecode(_$EventTypeEnumMap, json['type']),
      images: (json['images'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      website: json['website'] as String?,
      ticketInfo: json['ticketInfo'] as String?,
      price: (json['price'] as num?)?.toDouble(),
      isFree: json['isFree'] as bool? ?? false,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      isBookmarked: json['isBookmarked'] as bool? ?? false,
    );

Map<String, dynamic> _$EventToJson(Event instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'location': instance.location,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'region': instance.region,
      'type': _$EventTypeEnumMap[instance.type]!,
      'images': instance.images,
      'website': instance.website,
      'ticketInfo': instance.ticketInfo,
      'price': instance.price,
      'isFree': instance.isFree,
      'tags': instance.tags,
      'isBookmarked': instance.isBookmarked,
    };

const _$EventTypeEnumMap = {
  EventType.festival: 'festival',
  EventType.concert: 'concert',
  EventType.exhibition: 'exhibition',
  EventType.cultural: 'cultural',
  EventType.sports: 'sports',
  EventType.food: 'food',
  EventType.religious: 'religious',
  EventType.traditional: 'traditional',
  EventType.other: 'other',
};
