// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'camera_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RecognizedMonument _$RecognizedMonumentFromJson(Map<String, dynamic> json) =>
    RecognizedMonument(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: $enumDecode(_$MonumentTypeEnumMap, json['type']),
      confidence: (json['confidence'] as num).toDouble(),
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      imageUrl: json['imageUrl'] as String?,
      audioGuideUrl: json['audioGuideUrl'] as String?,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      additionalInfo:
          json['additionalInfo'] as Map<String, dynamic>? ?? const {},
      recognizedAt: DateTime.parse(json['recognizedAt'] as String),
    );

Map<String, dynamic> _$RecognizedMonumentToJson(RecognizedMonument instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': _$MonumentTypeEnumMap[instance.type]!,
      'confidence': instance.confidence,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'imageUrl': instance.imageUrl,
      'audioGuideUrl': instance.audioGuideUrl,
      'tags': instance.tags,
      'additionalInfo': instance.additionalInfo,
      'recognizedAt': instance.recognizedAt.toIso8601String(),
    };

const _$MonumentTypeEnumMap = {
  MonumentType.church: 'church',
  MonumentType.castle: 'castle',
  MonumentType.museum: 'museum',
  MonumentType.bridge: 'bridge',
  MonumentType.square: 'square',
  MonumentType.building: 'building',
  MonumentType.statue: 'statue',
  MonumentType.park: 'park',
  MonumentType.beach: 'beach',
  MonumentType.other: 'other',
};

ScannedQRCode _$ScannedQRCodeFromJson(Map<String, dynamic> json) =>
    ScannedQRCode(
      id: json['id'] as String,
      content: json['content'] as String,
      type: $enumDecode(_$QRCodeTypeEnumMap, json['type']),
      title: json['title'] as String?,
      description: json['description'] as String?,
      parsedData: json['parsedData'] as Map<String, dynamic>? ?? const {},
      scannedAt: DateTime.parse(json['scannedAt'] as String),
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$ScannedQRCodeToJson(ScannedQRCode instance) =>
    <String, dynamic>{
      'id': instance.id,
      'content': instance.content,
      'type': _$QRCodeTypeEnumMap[instance.type]!,
      'title': instance.title,
      'description': instance.description,
      'parsedData': instance.parsedData,
      'scannedAt': instance.scannedAt.toIso8601String(),
      'latitude': instance.latitude,
      'longitude': instance.longitude,
    };

const _$QRCodeTypeEnumMap = {
  QRCodeType.ticket: 'ticket',
  QRCodeType.menu: 'menu',
  QRCodeType.info: 'info',
  QRCodeType.website: 'website',
  QRCodeType.contact: 'contact',
  QRCodeType.wifi: 'wifi',
  QRCodeType.location: 'location',
  QRCodeType.other: 'other',
};

TravelPhoto _$TravelPhotoFromJson(Map<String, dynamic> json) => TravelPhoto(
      id: json['id'] as String,
      filePath: json['filePath'] as String,
      cloudUrl: json['cloudUrl'] as String?,
      title: json['title'] as String?,
      description: json['description'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      locationName: json['locationName'] as String?,
      takenAt: DateTime.parse(json['takenAt'] as String),
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      isUploaded: json['isUploaded'] as bool? ?? false,
      isFavorite: json['isFavorite'] as bool? ?? false,
    );

Map<String, dynamic> _$TravelPhotoToJson(TravelPhoto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'filePath': instance.filePath,
      'cloudUrl': instance.cloudUrl,
      'title': instance.title,
      'description': instance.description,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'locationName': instance.locationName,
      'takenAt': instance.takenAt.toIso8601String(),
      'tags': instance.tags,
      'metadata': instance.metadata,
      'isUploaded': instance.isUploaded,
      'isFavorite': instance.isFavorite,
    };

AROverlayInfo _$AROverlayInfoFromJson(Map<String, dynamic> json) =>
    AROverlayInfo(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      screenX: (json['screenX'] as num).toDouble(),
      screenY: (json['screenY'] as num).toDouble(),
      distance: (json['distance'] as num).toDouble(),
      type: $enumDecode(_$MonumentTypeEnumMap, json['type']),
      imageUrl: json['imageUrl'] as String?,
      isVisible: json['isVisible'] as bool? ?? true,
    );

Map<String, dynamic> _$AROverlayInfoToJson(AROverlayInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'screenX': instance.screenX,
      'screenY': instance.screenY,
      'distance': instance.distance,
      'type': _$MonumentTypeEnumMap[instance.type]!,
      'imageUrl': instance.imageUrl,
      'isVisible': instance.isVisible,
    };

CameraSettings _$CameraSettingsFromJson(Map<String, dynamic> json) =>
    CameraSettings(
      isAREnabled: json['isAREnabled'] as bool? ?? true,
      isQRScanEnabled: json['isQRScanEnabled'] as bool? ?? true,
      isGeotaggingEnabled: json['isGeotaggingEnabled'] as bool? ?? true,
      isAutoUploadEnabled: json['isAutoUploadEnabled'] as bool? ?? false,
      arSensitivity: (json['arSensitivity'] as num?)?.toDouble() ?? 0.7,
      photoQuality: (json['photoQuality'] as num?)?.toInt() ?? 85,
      useFlash: json['useFlash'] as bool? ?? false,
      showGrid: json['showGrid'] as bool? ?? false,
    );

Map<String, dynamic> _$CameraSettingsToJson(CameraSettings instance) =>
    <String, dynamic>{
      'isAREnabled': instance.isAREnabled,
      'isQRScanEnabled': instance.isQRScanEnabled,
      'isGeotaggingEnabled': instance.isGeotaggingEnabled,
      'isAutoUploadEnabled': instance.isAutoUploadEnabled,
      'arSensitivity': instance.arSensitivity,
      'photoQuality': instance.photoQuality,
      'useFlash': instance.useFlash,
      'showGrid': instance.showGrid,
    };
