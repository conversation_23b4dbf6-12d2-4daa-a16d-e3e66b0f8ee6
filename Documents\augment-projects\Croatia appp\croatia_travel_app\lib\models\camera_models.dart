import 'package:json_annotation/json_annotation.dart';

part 'camera_models.g.dart';

/// Typ rozpoznan<PERSON> památky
enum MonumentType {
  church,
  castle,
  museum,
  bridge,
  square,
  building,
  statue,
  park,
  beach,
  other,
}

/// Typ QR kódu
enum QRCodeType {
  ticket,
  menu,
  info,
  website,
  contact,
  wifi,
  location,
  other,
}

/// Model pro rozpoznanou památku pomocí AR
@JsonSerializable()
class RecognizedMonument {
  final String id;
  final String name;
  final String description;
  final MonumentType type;
  final double confidence;
  final double latitude;
  final double longitude;
  final String? imageUrl;
  final String? audioGuideUrl;
  final List<String> tags;
  final Map<String, dynamic> additionalInfo;
  final DateTime recognizedAt;

  const RecognizedMonument({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.confidence,
    required this.latitude,
    required this.longitude,
    this.imageUrl,
    this.audioGuideUrl,
    this.tags = const [],
    this.additionalInfo = const {},
    required this.recognizedAt,
  });

  /// Název typu v češtině
  String get typeName {
    switch (type) {
      case MonumentType.church:
        return 'Kostel';
      case MonumentType.castle:
        return 'Hrad/Zámek';
      case MonumentType.museum:
        return 'Muzeum';
      case MonumentType.bridge:
        return 'Most';
      case MonumentType.square:
        return 'Náměstí';
      case MonumentType.building:
        return 'Budova';
      case MonumentType.statue:
        return 'Socha';
      case MonumentType.park:
        return 'Park';
      case MonumentType.beach:
        return 'Pláž';
      case MonumentType.other:
        return 'Ostatní';
    }
  }

  /// Zkontroluje, zda je rozpoznání spolehlivé
  bool get isReliable => confidence >= 0.7;

  factory RecognizedMonument.fromJson(Map<String, dynamic> json) =>
      _$RecognizedMonumentFromJson(json);

  Map<String, dynamic> toJson() => _$RecognizedMonumentToJson(this);
}

/// Model pro naskenovaný QR kód
@JsonSerializable()
class ScannedQRCode {
  final String id;
  final String content;
  final QRCodeType type;
  final String? title;
  final String? description;
  final Map<String, dynamic> parsedData;
  final DateTime scannedAt;
  final double? latitude;
  final double? longitude;

  const ScannedQRCode({
    required this.id,
    required this.content,
    required this.type,
    this.title,
    this.description,
    this.parsedData = const {},
    required this.scannedAt,
    this.latitude,
    this.longitude,
  });

  /// Název typu v češtině
  String get typeName {
    switch (type) {
      case QRCodeType.ticket:
        return 'Vstupenka';
      case QRCodeType.menu:
        return 'Menu';
      case QRCodeType.info:
        return 'Informace';
      case QRCodeType.website:
        return 'Webová stránka';
      case QRCodeType.contact:
        return 'Kontakt';
      case QRCodeType.wifi:
        return 'WiFi';
      case QRCodeType.location:
        return 'Lokace';
      case QRCodeType.other:
        return 'Ostatní';
    }
  }

  factory ScannedQRCode.fromJson(Map<String, dynamic> json) =>
      _$ScannedQRCodeFromJson(json);

  Map<String, dynamic> toJson() => _$ScannedQRCodeToJson(this);
}

/// Model pro fotografii v cestovním deníku
@JsonSerializable()
class TravelPhoto {
  final String id;
  final String filePath;
  final String? cloudUrl;
  final String? title;
  final String? description;
  final double? latitude;
  final double? longitude;
  final String? locationName;
  final DateTime takenAt;
  final List<String> tags;
  final Map<String, dynamic> metadata;
  final bool isUploaded;
  final bool isFavorite;

  const TravelPhoto({
    required this.id,
    required this.filePath,
    this.cloudUrl,
    this.title,
    this.description,
    this.latitude,
    this.longitude,
    this.locationName,
    required this.takenAt,
    this.tags = const [],
    this.metadata = const {},
    this.isUploaded = false,
    this.isFavorite = false,
  });

  /// Zkontroluje, zda má foto GPS souřadnice
  bool get hasLocation => latitude != null && longitude != null;

  /// Zkontroluje, zda je foto z Chorvatska
  bool get isFromCroatia {
    if (!hasLocation) return false;
    // Chorvatsko: 42.4°N - 46.5°N, 13.5°E - 19.4°E
    return latitude! >= 42.4 && 
           latitude! <= 46.5 && 
           longitude! >= 13.5 && 
           longitude! <= 19.4;
  }

  factory TravelPhoto.fromJson(Map<String, dynamic> json) =>
      _$TravelPhotoFromJson(json);

  Map<String, dynamic> toJson() => _$TravelPhotoToJson(this);

  TravelPhoto copyWith({
    String? id,
    String? filePath,
    String? cloudUrl,
    String? title,
    String? description,
    double? latitude,
    double? longitude,
    String? locationName,
    DateTime? takenAt,
    List<String>? tags,
    Map<String, dynamic>? metadata,
    bool? isUploaded,
    bool? isFavorite,
  }) {
    return TravelPhoto(
      id: id ?? this.id,
      filePath: filePath ?? this.filePath,
      cloudUrl: cloudUrl ?? this.cloudUrl,
      title: title ?? this.title,
      description: description ?? this.description,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      locationName: locationName ?? this.locationName,
      takenAt: takenAt ?? this.takenAt,
      tags: tags ?? this.tags,
      metadata: metadata ?? this.metadata,
      isUploaded: isUploaded ?? this.isUploaded,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TravelPhoto && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Model pro AR overlay informace
@JsonSerializable()
class AROverlayInfo {
  final String id;
  final String title;
  final String description;
  final double screenX;
  final double screenY;
  final double distance;
  final MonumentType type;
  final String? imageUrl;
  final bool isVisible;

  const AROverlayInfo({
    required this.id,
    required this.title,
    required this.description,
    required this.screenX,
    required this.screenY,
    required this.distance,
    required this.type,
    this.imageUrl,
    this.isVisible = true,
  });

  /// Formátovaná vzdálenost
  String get formattedDistance {
    if (distance < 1000) {
      return '${distance.round()} m';
    } else {
      return '${(distance / 1000).toStringAsFixed(1)} km';
    }
  }

  factory AROverlayInfo.fromJson(Map<String, dynamic> json) =>
      _$AROverlayInfoFromJson(json);

  Map<String, dynamic> toJson() => _$AROverlayInfoToJson(this);
}

/// Model pro camera nastavení
@JsonSerializable()
class CameraSettings {
  final bool isAREnabled;
  final bool isQRScanEnabled;
  final bool isGeotaggingEnabled;
  final bool isAutoUploadEnabled;
  final double arSensitivity;
  final int photoQuality;
  final bool useFlash;
  final bool showGrid;

  const CameraSettings({
    this.isAREnabled = true,
    this.isQRScanEnabled = true,
    this.isGeotaggingEnabled = true,
    this.isAutoUploadEnabled = false,
    this.arSensitivity = 0.7,
    this.photoQuality = 85,
    this.useFlash = false,
    this.showGrid = false,
  });

  factory CameraSettings.fromJson(Map<String, dynamic> json) =>
      _$CameraSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$CameraSettingsToJson(this);

  CameraSettings copyWith({
    bool? isAREnabled,
    bool? isQRScanEnabled,
    bool? isGeotaggingEnabled,
    bool? isAutoUploadEnabled,
    double? arSensitivity,
    int? photoQuality,
    bool? useFlash,
    bool? showGrid,
  }) {
    return CameraSettings(
      isAREnabled: isAREnabled ?? this.isAREnabled,
      isQRScanEnabled: isQRScanEnabled ?? this.isQRScanEnabled,
      isGeotaggingEnabled: isGeotaggingEnabled ?? this.isGeotaggingEnabled,
      isAutoUploadEnabled: isAutoUploadEnabled ?? this.isAutoUploadEnabled,
      arSensitivity: arSensitivity ?? this.arSensitivity,
      photoQuality: photoQuality ?? this.photoQuality,
      useFlash: useFlash ?? this.useFlash,
      showGrid: showGrid ?? this.showGrid,
    );
  }
}
