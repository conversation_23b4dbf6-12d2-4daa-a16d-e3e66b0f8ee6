class BudgetEntry {
  final String id;
  final String title;
  final String? description;
  final double amount;
  final BudgetCategory category;
  final DateTime date;
  final String? location;
  final String? notes;
  final String? receiptPath;
  final bool isPlanned;
  final String currency;

  const BudgetEntry({
    required this.id,
    required this.title,
    this.description,
    required this.amount,
    required this.category,
    required this.date,
    this.location,
    this.notes,
    this.receiptPath,
    this.isPlanned = false,
    this.currency = 'EUR',
  });

  BudgetEntry copyWith({
    String? id,
    String? title,
    String? description,
    double? amount,
    BudgetCategory? category,
    DateTime? date,
    String? location,
    String? notes,
    String? receiptPath,
    bool? isPlanned,
    String? currency,
  }) {
    return BudgetEntry(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      category: category ?? this.category,
      date: date ?? this.date,
      location: location ?? this.location,
      notes: notes ?? this.notes,
      receiptPath: receiptPath ?? this.receiptPath,
      isPlanned: isPlanned ?? this.isPlanned,
      currency: currency ?? this.currency,
    );
  }
}

class Budget {
  final String id;
  final String name;
  final String? description;
  final double totalBudget;
  final String currency;
  final DateTime startDate;
  final DateTime endDate;
  final List<BudgetEntry> entries;
  final Map<BudgetCategory, double> categoryLimits;

  const Budget({
    required this.id,
    required this.name,
    this.description,
    required this.totalBudget,
    this.currency = 'EUR',
    required this.startDate,
    required this.endDate,
    this.entries = const [],
    this.categoryLimits = const {},
  });

  double get totalSpent =>
      entries.fold(0.0, (sum, entry) => sum + entry.amount);

  double get remainingBudget => totalBudget - totalSpent;

  double get spentPercentage => totalBudget > 0 ? (totalSpent / totalBudget) * 100 : 0;

  bool get isOverBudget => totalSpent > totalBudget;

  Map<BudgetCategory, double> get spentByCategory {
    final Map<BudgetCategory, double> result = {};
    for (final entry in entries) {
      result[entry.category] = (result[entry.category] ?? 0) + entry.amount;
    }
    return result;
  }

  List<BudgetEntry> get plannedEntries =>
      entries.where((entry) => entry.isPlanned).toList();

  List<BudgetEntry> get actualEntries =>
      entries.where((entry) => !entry.isPlanned).toList();

  double get plannedAmount =>
      plannedEntries.fold(0.0, (sum, entry) => sum + entry.amount);

  double get actualAmount =>
      actualEntries.fold(0.0, (sum, entry) => sum + entry.amount);

  Budget copyWith({
    String? id,
    String? name,
    String? description,
    double? totalBudget,
    String? currency,
    DateTime? startDate,
    DateTime? endDate,
    List<BudgetEntry>? entries,
    Map<BudgetCategory, double>? categoryLimits,
  }) {
    return Budget(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      totalBudget: totalBudget ?? this.totalBudget,
      currency: currency ?? this.currency,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      entries: entries ?? this.entries,
      categoryLimits: categoryLimits ?? this.categoryLimits,
    );
  }
}

enum BudgetCategory {
  accommodation,
  food,
  transport,
  entertainment,
  shopping,
  activities,
  emergency,
  other,
}

extension BudgetCategoryExtension on BudgetCategory {
  String get displayName {
    switch (this) {
      case BudgetCategory.accommodation:
        return 'Ubytování';
      case BudgetCategory.food:
        return 'Jídlo a pití';
      case BudgetCategory.transport:
        return 'Doprava';
      case BudgetCategory.entertainment:
        return 'Zábava';
      case BudgetCategory.shopping:
        return 'Nákupy';
      case BudgetCategory.activities:
        return 'Aktivity';
      case BudgetCategory.emergency:
        return 'Nouzové výdaje';
      case BudgetCategory.other:
        return 'Ostatní';
    }
  }

  String get icon {
    switch (this) {
      case BudgetCategory.accommodation:
        return '🏨';
      case BudgetCategory.food:
        return '🍽️';
      case BudgetCategory.transport:
        return '🚗';
      case BudgetCategory.entertainment:
        return '🎭';
      case BudgetCategory.shopping:
        return '🛍️';
      case BudgetCategory.activities:
        return '🎯';
      case BudgetCategory.emergency:
        return '🚨';
      case BudgetCategory.other:
        return '📦';
    }
  }
}

// Předdefinované šablony rozpočtů
class BudgetTemplates {
  static const List<Map<String, dynamic>> templates = [
    {
      'name': 'Víkendový výlet',
      'description': 'Rozpočet na 2-3 denní výlet',
      'totalBudget': 300.0,
      'categoryLimits': {
        BudgetCategory.accommodation: 120.0,
        BudgetCategory.food: 80.0,
        BudgetCategory.transport: 60.0,
        BudgetCategory.activities: 30.0,
        BudgetCategory.other: 10.0,
      },
    },
    {
      'name': 'Týdenní dovolená',
      'description': 'Rozpočet na týdenní dovolenou',
      'totalBudget': 800.0,
      'categoryLimits': {
        BudgetCategory.accommodation: 350.0,
        BudgetCategory.food: 200.0,
        BudgetCategory.transport: 150.0,
        BudgetCategory.activities: 80.0,
        BudgetCategory.shopping: 20.0,
      },
    },
    {
      'name': 'Měsíční cestování',
      'description': 'Rozpočet na měsíční cestování',
      'totalBudget': 2000.0,
      'categoryLimits': {
        BudgetCategory.accommodation: 800.0,
        BudgetCategory.food: 500.0,
        BudgetCategory.transport: 400.0,
        BudgetCategory.activities: 200.0,
        BudgetCategory.shopping: 50.0,
        BudgetCategory.emergency: 50.0,
      },
    },
  ];
}

// Statistiky rozpočtu
class BudgetStatistics {
  final double totalSpent;
  final double averageDailySpending;
  final BudgetCategory topCategory;
  final double topCategoryAmount;
  final int totalEntries;
  final Map<String, double> spendingByDay;

  const BudgetStatistics({
    required this.totalSpent,
    required this.averageDailySpending,
    required this.topCategory,
    required this.topCategoryAmount,
    required this.totalEntries,
    required this.spendingByDay,
  });
}
