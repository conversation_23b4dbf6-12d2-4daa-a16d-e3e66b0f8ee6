import 'diary_entry.dart';

/// 👨‍👩‍👧‍👦 FAMILY SHARING MODELS - Modely pro rod<PERSON><PERSON> sd<PERSON>

/// <PERSON><PERSON><PERSON> kruh
class FamilyCircle {
  final String id;
  final String name;
  final String description;
  final String? coverImageUrl;
  final String ownerId;
  final List<FamilyMember> members;
  final FamilyCirclePrivacy privacy;
  final DateTime createdAt;
  final DateTime lastActivity;
  final FamilyCircleSettings settings;

  const FamilyCircle({
    required this.id,
    required this.name,
    required this.description,
    this.coverImageUrl,
    required this.ownerId,
    required this.members,
    required this.privacy,
    required this.createdAt,
    required this.lastActivity,
    required this.settings,
  });

  FamilyCircle copyWith({
    String? id,
    String? name,
    String? description,
    String? coverImageUrl,
    String? ownerId,
    List<FamilyMember>? members,
    FamilyCirclePrivacy? privacy,
    DateTime? createdAt,
    DateTime? lastActivity,
    FamilyCircleSettings? settings,
  }) {
    return FamilyCircle(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      coverImageUrl: coverImageUrl ?? this.coverImageUrl,
      ownerId: ownerId ?? this.ownerId,
      members: members ?? this.members,
      privacy: privacy ?? this.privacy,
      createdAt: createdAt ?? this.createdAt,
      lastActivity: lastActivity ?? this.lastActivity,
      settings: settings ?? this.settings,
    );
  }

  int get memberCount => members.length;
  int get activeMemberCount => members.where((m) => m.isActive).length;

  FamilyMember? get owner => members.firstWhere((m) => m.userId == ownerId);

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'coverImageUrl': coverImageUrl,
      'ownerId': ownerId,
      'members': members.map((m) => m.toJson()).toList(),
      'privacy': privacy.name,
      'createdAt': createdAt.toIso8601String(),
      'lastActivity': lastActivity.toIso8601String(),
      'settings': settings.toJson(),
    };
  }

  factory FamilyCircle.fromJson(Map<String, dynamic> json) {
    return FamilyCircle(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      coverImageUrl: json['coverImageUrl'] as String?,
      ownerId: json['ownerId'] as String,
      members: (json['members'] as List<dynamic>)
          .map((m) => FamilyMember.fromJson(m as Map<String, dynamic>))
          .toList(),
      privacy: FamilyCirclePrivacy.values.firstWhere(
        (p) => p.name == json['privacy'],
      ),
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastActivity: DateTime.parse(json['lastActivity'] as String),
      settings: FamilyCircleSettings.fromJson(
        json['settings'] as Map<String, dynamic>,
      ),
    );
  }
}

/// Člen rodinného kruhu
class FamilyMember {
  final String userId;
  final String displayName;
  final String? avatarUrl;
  final FamilyRole role;
  final DateTime joinedAt;
  final bool isActive;
  final DateTime? lastSeen;

  const FamilyMember({
    required this.userId,
    required this.displayName,
    this.avatarUrl,
    required this.role,
    required this.joinedAt,
    required this.isActive,
    this.lastSeen,
  });

  FamilyMember copyWith({
    String? userId,
    String? displayName,
    String? avatarUrl,
    FamilyRole? role,
    DateTime? joinedAt,
    bool? isActive,
    DateTime? lastSeen,
  }) {
    return FamilyMember(
      userId: userId ?? this.userId,
      displayName: displayName ?? this.displayName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      role: role ?? this.role,
      joinedAt: joinedAt ?? this.joinedAt,
      isActive: isActive ?? this.isActive,
      lastSeen: lastSeen ?? this.lastSeen,
    );
  }

  bool get canInviteMembers =>
      role == FamilyRole.owner || role == FamilyRole.admin;
  bool get canShareMemories => isActive;
  bool get canModerateContent =>
      role == FamilyRole.owner || role == FamilyRole.admin;
  bool get canChangeSettings => role == FamilyRole.owner;

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'displayName': displayName,
      'avatarUrl': avatarUrl,
      'role': role.name,
      'joinedAt': joinedAt.toIso8601String(),
      'isActive': isActive,
      'lastSeen': lastSeen?.toIso8601String(),
    };
  }

  factory FamilyMember.fromJson(Map<String, dynamic> json) {
    return FamilyMember(
      userId: json['userId'] as String,
      displayName: json['displayName'] as String,
      avatarUrl: json['avatarUrl'] as String?,
      role: FamilyRole.values.firstWhere((r) => r.name == json['role']),
      joinedAt: DateTime.parse(json['joinedAt'] as String),
      isActive: json['isActive'] as bool,
      lastSeen: json['lastSeen'] != null
          ? DateTime.parse(json['lastSeen'] as String)
          : null,
    );
  }
}

/// Role v rodinném kruhu
enum FamilyRole {
  owner, // Vlastník
  admin, // Administrátor
  member, // Člen
  viewer, // Pouze prohlížení
}

extension FamilyRoleExtension on FamilyRole {
  String get displayName {
    switch (this) {
      case FamilyRole.owner:
        return 'Vlastník';
      case FamilyRole.admin:
        return 'Administrátor';
      case FamilyRole.member:
        return 'Člen';
      case FamilyRole.viewer:
        return 'Pozorovatel';
    }
  }

  String get description {
    switch (this) {
      case FamilyRole.owner:
        return 'Může vše včetně smazání kruhu';
      case FamilyRole.admin:
        return 'Může spravovat členy a obsah';
      case FamilyRole.member:
        return 'Může sdílet a komentovat';
      case FamilyRole.viewer:
        return 'Může pouze prohlížet';
    }
  }
}

/// Soukromí rodinného kruhu
enum FamilyCirclePrivacy {
  private, // Soukromý
  protected, // Chráněný
  public, // Veřejný
}

/// Nastavení rodinného kruhu
class FamilyCircleSettings {
  final bool allowMemberInvites;
  final bool requireApprovalForSharing;
  final bool allowComments;
  final bool allowReactions;
  final bool notifyOnNewMemories;
  final bool notifyOnComments;
  final bool notifyOnReactions;
  final int maxPhotosPerMemory;
  final List<String> blockedWords;

  const FamilyCircleSettings({
    required this.allowMemberInvites,
    required this.requireApprovalForSharing,
    required this.allowComments,
    required this.allowReactions,
    required this.notifyOnNewMemories,
    required this.notifyOnComments,
    required this.notifyOnReactions,
    required this.maxPhotosPerMemory,
    this.blockedWords = const [],
  });

  factory FamilyCircleSettings.defaultSettings() {
    return const FamilyCircleSettings(
      allowMemberInvites: true,
      requireApprovalForSharing: false,
      allowComments: true,
      allowReactions: true,
      notifyOnNewMemories: true,
      notifyOnComments: true,
      notifyOnReactions: false,
      maxPhotosPerMemory: 10,
      blockedWords: [],
    );
  }

  FamilyCircleSettings copyWith({
    bool? allowMemberInvites,
    bool? requireApprovalForSharing,
    bool? allowComments,
    bool? allowReactions,
    bool? notifyOnNewMemories,
    bool? notifyOnComments,
    bool? notifyOnReactions,
    int? maxPhotosPerMemory,
    List<String>? blockedWords,
  }) {
    return FamilyCircleSettings(
      allowMemberInvites: allowMemberInvites ?? this.allowMemberInvites,
      requireApprovalForSharing:
          requireApprovalForSharing ?? this.requireApprovalForSharing,
      allowComments: allowComments ?? this.allowComments,
      allowReactions: allowReactions ?? this.allowReactions,
      notifyOnNewMemories: notifyOnNewMemories ?? this.notifyOnNewMemories,
      notifyOnComments: notifyOnComments ?? this.notifyOnComments,
      notifyOnReactions: notifyOnReactions ?? this.notifyOnReactions,
      maxPhotosPerMemory: maxPhotosPerMemory ?? this.maxPhotosPerMemory,
      blockedWords: blockedWords ?? this.blockedWords,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'allowMemberInvites': allowMemberInvites,
      'requireApprovalForSharing': requireApprovalForSharing,
      'allowComments': allowComments,
      'allowReactions': allowReactions,
      'notifyOnNewMemories': notifyOnNewMemories,
      'notifyOnComments': notifyOnComments,
      'notifyOnReactions': notifyOnReactions,
      'maxPhotosPerMemory': maxPhotosPerMemory,
      'blockedWords': blockedWords,
    };
  }

  factory FamilyCircleSettings.fromJson(Map<String, dynamic> json) {
    return FamilyCircleSettings(
      allowMemberInvites: json['allowMemberInvites'] as bool,
      requireApprovalForSharing: json['requireApprovalForSharing'] as bool,
      allowComments: json['allowComments'] as bool,
      allowReactions: json['allowReactions'] as bool,
      notifyOnNewMemories: json['notifyOnNewMemories'] as bool,
      notifyOnComments: json['notifyOnComments'] as bool,
      notifyOnReactions: json['notifyOnReactions'] as bool,
      maxPhotosPerMemory: json['maxPhotosPerMemory'] as int,
      blockedWords: (json['blockedWords'] as List<dynamic>).cast<String>(),
    );
  }
}

/// Sdílená vzpomínka
class SharedMemory {
  final String id;
  final String circleId;
  final String originalEntryId;
  final String sharedByUserId;
  final String sharedByName;
  final String title;
  final String content;
  final String? location;
  final DateTime date;
  final DiaryMood? mood;
  final String? weather;
  final List<String> photos;
  final String? personalNote;
  final bool allowComments;
  final bool allowReactions;
  final DateTime sharedAt;
  final List<MemoryReaction> reactions;
  final List<MemoryComment> comments;
  final int viewCount;

  const SharedMemory({
    required this.id,
    required this.circleId,
    required this.originalEntryId,
    required this.sharedByUserId,
    required this.sharedByName,
    required this.title,
    required this.content,
    this.location,
    required this.date,
    this.mood,
    this.weather,
    this.photos = const [],
    this.personalNote,
    required this.allowComments,
    required this.allowReactions,
    required this.sharedAt,
    this.reactions = const [],
    this.comments = const [],
    this.viewCount = 0,
  });

  SharedMemory copyWith({
    String? id,
    String? circleId,
    String? originalEntryId,
    String? sharedByUserId,
    String? sharedByName,
    String? title,
    String? content,
    String? location,
    DateTime? date,
    DiaryMood? mood,
    String? weather,
    List<String>? photos,
    String? personalNote,
    bool? allowComments,
    bool? allowReactions,
    DateTime? sharedAt,
    List<MemoryReaction>? reactions,
    List<MemoryComment>? comments,
    int? viewCount,
  }) {
    return SharedMemory(
      id: id ?? this.id,
      circleId: circleId ?? this.circleId,
      originalEntryId: originalEntryId ?? this.originalEntryId,
      sharedByUserId: sharedByUserId ?? this.sharedByUserId,
      sharedByName: sharedByName ?? this.sharedByName,
      title: title ?? this.title,
      content: content ?? this.content,
      location: location ?? this.location,
      date: date ?? this.date,
      mood: mood ?? this.mood,
      weather: weather ?? this.weather,
      photos: photos ?? this.photos,
      personalNote: personalNote ?? this.personalNote,
      allowComments: allowComments ?? this.allowComments,
      allowReactions: allowReactions ?? this.allowReactions,
      sharedAt: sharedAt ?? this.sharedAt,
      reactions: reactions ?? this.reactions,
      comments: comments ?? this.comments,
      viewCount: viewCount ?? this.viewCount,
    );
  }

  int get reactionCount => reactions.length;
  int get commentCount => comments.length;
  bool get hasPhotos => photos.isNotEmpty;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'circleId': circleId,
      'originalEntryId': originalEntryId,
      'sharedByUserId': sharedByUserId,
      'sharedByName': sharedByName,
      'title': title,
      'content': content,
      'location': location,
      'date': date.toIso8601String(),
      'mood': mood?.name,
      'weather': weather,
      'photos': photos,
      'personalNote': personalNote,
      'allowComments': allowComments,
      'allowReactions': allowReactions,
      'sharedAt': sharedAt.toIso8601String(),
      'reactions': reactions.map((r) => r.toJson()).toList(),
      'comments': comments.map((c) => c.toJson()).toList(),
      'viewCount': viewCount,
    };
  }

  factory SharedMemory.fromJson(Map<String, dynamic> json) {
    return SharedMemory(
      id: json['id'] as String,
      circleId: json['circleId'] as String,
      originalEntryId: json['originalEntryId'] as String,
      sharedByUserId: json['sharedByUserId'] as String,
      sharedByName: json['sharedByName'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      location: json['location'] as String?,
      date: DateTime.parse(json['date'] as String),
      mood: json['mood'] != null
          ? DiaryMood.values.firstWhere((m) => m.name == json['mood'])
          : null,
      weather: json['weather'] as String?,
      photos: (json['photos'] as List<dynamic>).cast<String>(),
      personalNote: json['personalNote'] as String?,
      allowComments: json['allowComments'] as bool,
      allowReactions: json['allowReactions'] as bool,
      sharedAt: DateTime.parse(json['sharedAt'] as String),
      reactions: (json['reactions'] as List<dynamic>)
          .map((r) => MemoryReaction.fromJson(r as Map<String, dynamic>))
          .toList(),
      comments: (json['comments'] as List<dynamic>)
          .map((c) => MemoryComment.fromJson(c as Map<String, dynamic>))
          .toList(),
      viewCount: json['viewCount'] as int? ?? 0,
    );
  }
}

/// Reakce na vzpomínku
class MemoryReaction {
  final String userId;
  final String userName;
  final ReactionType reaction;
  final DateTime timestamp;

  const MemoryReaction({
    required this.userId,
    required this.userName,
    required this.reaction,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'userName': userName,
      'reaction': reaction.name,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory MemoryReaction.fromJson(Map<String, dynamic> json) {
    return MemoryReaction(
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      reaction: ReactionType.values.firstWhere(
        (r) => r.name == json['reaction'],
      ),
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }
}

/// Typ reakce
enum ReactionType {
  love, // ❤️
  like, // 👍
  laugh, // 😂
  wow, // 😮
  sad, // 😢
  angry, // 😠
}

extension ReactionTypeExtension on ReactionType {
  String get emoji {
    switch (this) {
      case ReactionType.love:
        return '❤️';
      case ReactionType.like:
        return '👍';
      case ReactionType.laugh:
        return '😂';
      case ReactionType.wow:
        return '😮';
      case ReactionType.sad:
        return '😢';
      case ReactionType.angry:
        return '😠';
    }
  }

  String get displayName {
    switch (this) {
      case ReactionType.love:
        return 'Láska';
      case ReactionType.like:
        return 'Líbí se';
      case ReactionType.laugh:
        return 'Smích';
      case ReactionType.wow:
        return 'Úžas';
      case ReactionType.sad:
        return 'Smutek';
      case ReactionType.angry:
        return 'Zlost';
    }
  }
}

/// Komentář k vzpomínce
class MemoryComment {
  final String id;
  final String userId;
  final String userName;
  final String text;
  final DateTime timestamp;
  final String? replyToCommentId;

  const MemoryComment({
    required this.id,
    required this.userId,
    required this.userName,
    required this.text,
    required this.timestamp,
    this.replyToCommentId,
  });

  bool get isReply => replyToCommentId != null;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'text': text,
      'timestamp': timestamp.toIso8601String(),
      'replyToCommentId': replyToCommentId,
    };
  }

  factory MemoryComment.fromJson(Map<String, dynamic> json) {
    return MemoryComment(
      id: json['id'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      text: json['text'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      replyToCommentId: json['replyToCommentId'] as String?,
    );
  }
}

/// Pozvánka do rodinného kruhu
class FamilyInvitation {
  final String id;
  final String circleId;
  final String circleName;
  final String inviterUserId;
  final String inviterName;
  final String inviteeEmail;
  final FamilyRole role;
  final String? personalMessage;
  final InvitationStatus status;
  final DateTime createdAt;
  final DateTime expiresAt;

  const FamilyInvitation({
    required this.id,
    required this.circleId,
    required this.circleName,
    required this.inviterUserId,
    required this.inviterName,
    required this.inviteeEmail,
    required this.role,
    this.personalMessage,
    required this.status,
    required this.createdAt,
    required this.expiresAt,
  });

  FamilyInvitation copyWith({
    String? id,
    String? circleId,
    String? circleName,
    String? inviterUserId,
    String? inviterName,
    String? inviteeEmail,
    FamilyRole? role,
    String? personalMessage,
    InvitationStatus? status,
    DateTime? createdAt,
    DateTime? expiresAt,
  }) {
    return FamilyInvitation(
      id: id ?? this.id,
      circleId: circleId ?? this.circleId,
      circleName: circleName ?? this.circleName,
      inviterUserId: inviterUserId ?? this.inviterUserId,
      inviterName: inviterName ?? this.inviterName,
      inviteeEmail: inviteeEmail ?? this.inviteeEmail,
      role: role ?? this.role,
      personalMessage: personalMessage ?? this.personalMessage,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  bool get isPending => status == InvitationStatus.pending && !isExpired;

  Duration get timeUntilExpiry => expiresAt.difference(DateTime.now());

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'circleId': circleId,
      'circleName': circleName,
      'inviterUserId': inviterUserId,
      'inviterName': inviterName,
      'inviteeEmail': inviteeEmail,
      'role': role.name,
      'personalMessage': personalMessage,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'expiresAt': expiresAt.toIso8601String(),
    };
  }

  factory FamilyInvitation.fromJson(Map<String, dynamic> json) {
    return FamilyInvitation(
      id: json['id'] as String,
      circleId: json['circleId'] as String,
      circleName: json['circleName'] as String,
      inviterUserId: json['inviterUserId'] as String,
      inviterName: json['inviterName'] as String,
      inviteeEmail: json['inviteeEmail'] as String,
      role: FamilyRole.values.firstWhere((r) => r.name == json['role']),
      personalMessage: json['personalMessage'] as String?,
      status: InvitationStatus.values.firstWhere(
        (s) => s.name == json['status'],
      ),
      createdAt: DateTime.parse(json['createdAt'] as String),
      expiresAt: DateTime.parse(json['expiresAt'] as String),
    );
  }
}

/// Stav pozvánky
enum InvitationStatus {
  pending, // Čekající
  accepted, // Přijatá
  declined, // Odmítnutá
  expired, // Vypršelá
}

/// Statistiky rodinného kruhu
class FamilyCircleStats {
  final String circleId;
  final int totalMembers;
  final int activeMembers;
  final int totalMemories;
  final int totalReactions;
  final int totalComments;
  final int totalViews;
  final String? mostActiveMemberId;
  final DateTime createdAt;
  final DateTime lastActivity;

  const FamilyCircleStats({
    required this.circleId,
    required this.totalMembers,
    required this.activeMembers,
    required this.totalMemories,
    required this.totalReactions,
    required this.totalComments,
    required this.totalViews,
    this.mostActiveMemberId,
    required this.createdAt,
    required this.lastActivity,
  });

  double get averageReactionsPerMemory =>
      totalMemories > 0 ? totalReactions / totalMemories : 0.0;

  double get averageCommentsPerMemory =>
      totalMemories > 0 ? totalComments / totalMemories : 0.0;

  double get averageViewsPerMemory =>
      totalMemories > 0 ? totalViews / totalMemories : 0.0;

  int get daysSinceCreated => DateTime.now().difference(createdAt).inDays;
  int get daysSinceLastActivity =>
      DateTime.now().difference(lastActivity).inDays;

  String get activityLevel {
    if (daysSinceLastActivity <= 1) return 'Velmi aktivní';
    if (daysSinceLastActivity <= 7) return 'Aktivní';
    if (daysSinceLastActivity <= 30) return 'Mírně aktivní';
    return 'Neaktivní';
  }

  Map<String, dynamic> toJson() {
    return {
      'circleId': circleId,
      'totalMembers': totalMembers,
      'activeMembers': activeMembers,
      'totalMemories': totalMemories,
      'totalReactions': totalReactions,
      'totalComments': totalComments,
      'totalViews': totalViews,
      'mostActiveMemberId': mostActiveMemberId,
      'createdAt': createdAt.toIso8601String(),
      'lastActivity': lastActivity.toIso8601String(),
    };
  }
}
