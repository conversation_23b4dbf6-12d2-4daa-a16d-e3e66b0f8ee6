import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/diary_entry.dart';
import '../models/search_filters.dart';

/// 🔍 ADVANCED SEARCH SERVICE - Pokročilé vyhledávání v deníku
class AdvancedSearchService {
  static final AdvancedSearchService _instance = AdvancedSearchService._internal();
  factory AdvancedSearchService() => _instance;
  AdvancedSearchService._internal();

  bool _isInitialized = false;
  final List<DiaryEntry> _allEntries = [];
  final Map<String, List<String>> _searchIndex = {};
  final Map<String, List<DiaryEntry>> _cache = {};

  /// Inicializace služby
  Future<void> initialize(List<DiaryEntry> entries) async {
    if (_isInitialized) return;

    try {
      debugPrint('🔍 Inicializuji Advanced Search Service...');
      
      _allEntries.clear();
      _allEntries.addAll(entries);
      
      await _buildSearchIndex();
      
      _isInitialized = true;
      debugPrint('✅ Advanced Search Service inicializován s ${_allEntries.length} záznamy');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Advanced Search: $e');
    }
  }

  /// Pokročilé vyhledávání s filtry
  Future<SearchResults> search({
    String? query,
    List<DiaryMood>? moods,
    DateTime? fromDate,
    DateTime? toDate,
    List<String>? weatherConditions,
    List<String>? locations,
    List<String>? companions,
    int? minRating,
    bool? hasPhotos,
    bool? hasVideos,
    String? season,
    int limit = 50,
  }) async {
    try {
      final stopwatch = Stopwatch()..start();
      
      // Vytvoření cache klíče
      final cacheKey = _createCacheKey(
        query, moods, fromDate, toDate, weatherConditions, 
        locations, companions, minRating, hasPhotos, hasVideos, season
      );
      
      // Kontrola cache
      if (_cache.containsKey(cacheKey)) {
        final cachedResults = _cache[cacheKey]!;
        return SearchResults(
          entries: cachedResults.take(limit).toList(),
          totalCount: cachedResults.length,
          searchTime: stopwatch.elapsedMilliseconds,
          suggestions: _generateSuggestions(query, cachedResults),
          filters: _generateActiveFilters(moods, weatherConditions, locations, companions),
        );
      }

      var results = List<DiaryEntry>.from(_allEntries);

      // Aplikace filtrů
      results = _applyFilters(
        results,
        query: query,
        moods: moods,
        fromDate: fromDate,
        toDate: toDate,
        weatherConditions: weatherConditions,
        locations: locations,
        companions: companions,
        minRating: minRating,
        hasPhotos: hasPhotos,
        hasVideos: hasVideos,
        season: season,
      );

      // Řazení podle relevance
      if (query != null && query.isNotEmpty) {
        results = _sortByRelevance(results, query);
      } else {
        results.sort((a, b) => b.date.compareTo(a.date)); // Nejnovější první
      }

      // Uložení do cache
      _cache[cacheKey] = results;

      stopwatch.stop();

      return SearchResults(
        entries: results.take(limit).toList(),
        totalCount: results.length,
        searchTime: stopwatch.elapsedMilliseconds,
        suggestions: _generateSuggestions(query, results),
        filters: _generateActiveFilters(moods, weatherConditions, locations, companions),
      );
    } catch (e) {
      debugPrint('❌ Chyba při vyhledávání: $e');
      return SearchResults.empty();
    }
  }

  /// Vyhledávání podle emocí
  Future<List<DiaryEntry>> searchByMood(DiaryMood mood, {int limit = 20}) async {
    return _allEntries
        .where((entry) => entry.mood == mood)
        .take(limit)
        .toList();
  }

  /// "Zobraz mi všechny pláže z července"
  Future<List<DiaryEntry>> searchBeachesFromMonth(int month, {int? year}) async {
    return _allEntries.where((entry) {
      // Kontrola měsíce
      if (entry.date.month != month) return false;
      
      // Kontrola roku (pokud je zadán)
      if (year != null && entry.date.year != year) return false;
      
      // Kontrola, zda obsahuje slova související s plážemi
      final beachKeywords = ['pláž', 'beach', 'moře', 'sea', 'koupání', 'swimming', 'slunce', 'sun'];
      final content = '${entry.title} ${entry.content} ${entry.location ?? ''}'.toLowerCase();
      
      return beachKeywords.any((keyword) => content.contains(keyword));
    }).toList();
  }

  /// "Co jsem dělal loni dnes?"
  Future<List<DiaryEntry>> searchSameDayLastYear(DateTime date) async {
    final lastYear = DateTime(date.year - 1, date.month, date.day);
    final dayBefore = lastYear.subtract(const Duration(days: 1));
    final dayAfter = lastYear.add(const Duration(days: 1));
    
    return _allEntries.where((entry) {
      return entry.date.isAfter(dayBefore) && entry.date.isBefore(dayAfter);
    }).toList();
  }

  /// Vyhledání podobných zážitků
  Future<List<DiaryEntry>> findSimilarExperiences(DiaryEntry referenceEntry, {int limit = 10}) async {
    final similarities = <DiaryEntry, double>{};
    
    for (final entry in _allEntries) {
      if (entry.id == referenceEntry.id) continue;
      
      final similarity = _calculateSimilarity(referenceEntry, entry);
      if (similarity > 0.3) { // Práh podobnosti
        similarities[entry] = similarity;
      }
    }
    
    // Seřazení podle podobnosti
    final sortedEntries = similarities.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedEntries.take(limit).map((e) => e.key).toList();
  }

  /// Aplikace všech filtrů
  List<DiaryEntry> _applyFilters(
    List<DiaryEntry> entries, {
    String? query,
    List<DiaryMood>? moods,
    DateTime? fromDate,
    DateTime? toDate,
    List<String>? weatherConditions,
    List<String>? locations,
    List<String>? companions,
    int? minRating,
    bool? hasPhotos,
    bool? hasVideos,
    String? season,
  }) {
    var filtered = entries;

    // Textové vyhledávání
    if (query != null && query.isNotEmpty) {
      filtered = _filterByText(filtered, query);
    }

    // Filtr podle nálady
    if (moods != null && moods.isNotEmpty) {
      filtered = filtered.where((entry) => 
        entry.mood != null && moods.contains(entry.mood)
      ).toList();
    }

    // Filtr podle data
    if (fromDate != null) {
      filtered = filtered.where((entry) => 
        entry.date.isAfter(fromDate) || entry.date.isAtSameMomentAs(fromDate)
      ).toList();
    }
    
    if (toDate != null) {
      filtered = filtered.where((entry) => 
        entry.date.isBefore(toDate) || entry.date.isAtSameMomentAs(toDate)
      ).toList();
    }

    // Filtr podle počasí
    if (weatherConditions != null && weatherConditions.isNotEmpty) {
      filtered = filtered.where((entry) => 
        entry.weather != null && weatherConditions.contains(entry.weather)
      ).toList();
    }

    // Filtr podle lokace
    if (locations != null && locations.isNotEmpty) {
      filtered = filtered.where((entry) => 
        entry.location != null && 
        locations.any((loc) => entry.location!.toLowerCase().contains(loc.toLowerCase()))
      ).toList();
    }

    // Filtr podle společníků
    if (companions != null && companions.isNotEmpty) {
      filtered = filtered.where((entry) => 
        entry.tags.any((tag) => companions.contains(tag))
      ).toList();
    }

    // Filtr podle hodnocení
    if (minRating != null) {
      filtered = filtered.where((entry) => 
        entry.rating != null && entry.rating! >= minRating
      ).toList();
    }

    // Filtr podle fotek
    if (hasPhotos == true) {
      filtered = filtered.where((entry) => entry.hasPhotos).toList();
    } else if (hasPhotos == false) {
      filtered = filtered.where((entry) => !entry.hasPhotos).toList();
    }

    // Filtr podle videí
    if (hasVideos == true) {
      filtered = filtered.where((entry) => entry.hasVideos).toList();
    } else if (hasVideos == false) {
      filtered = filtered.where((entry) => !entry.hasVideos).toList();
    }

    // Filtr podle sezóny
    if (season != null) {
      filtered = _filterBySeason(filtered, season);
    }

    return filtered;
  }

  /// Textové vyhledávání s indexem
  List<DiaryEntry> _filterByText(List<DiaryEntry> entries, String query) {
    final queryWords = query.toLowerCase().split(' ').where((w) => w.isNotEmpty).toList();
    
    return entries.where((entry) {
      final searchText = '${entry.title} ${entry.content} ${entry.location ?? ''} ${entry.tags.join(' ')}'.toLowerCase();
      
      return queryWords.every((word) => searchText.contains(word));
    }).toList();
  }

  /// Filtrování podle sezóny
  List<DiaryEntry> _filterBySeason(List<DiaryEntry> entries, String season) {
    final seasonMonths = {
      'jaro': [3, 4, 5],
      'léto': [6, 7, 8],
      'podzim': [9, 10, 11],
      'zima': [12, 1, 2],
      'spring': [3, 4, 5],
      'summer': [6, 7, 8],
      'autumn': [9, 10, 11],
      'winter': [12, 1, 2],
    };

    final months = seasonMonths[season.toLowerCase()] ?? [];
    if (months.isEmpty) return entries;

    return entries.where((entry) => months.contains(entry.date.month)).toList();
  }

  /// Řazení podle relevance
  List<DiaryEntry> _sortByRelevance(List<DiaryEntry> entries, String query) {
    final queryWords = query.toLowerCase().split(' ').where((w) => w.isNotEmpty).toList();
    
    final scored = entries.map((entry) {
      double score = 0.0;
      final title = entry.title.toLowerCase();
      final content = entry.content.toLowerCase();
      
      for (final word in queryWords) {
        // Vyšší skóre pro shodu v titulku
        if (title.contains(word)) score += 3.0;
        
        // Střední skóre pro shodu v obsahu
        if (content.contains(word)) score += 1.0;
        
        // Bonus za přesnou shodu
        if (title == word || content.split(' ').contains(word)) score += 2.0;
      }
      
      // Bonus za novější záznamy
      final daysSinceCreated = DateTime.now().difference(entry.date).inDays;
      score += max(0, 30 - daysSinceCreated) * 0.1;
      
      return MapEntry(entry, score);
    }).toList();
    
    scored.sort((a, b) => b.value.compareTo(a.value));
    return scored.map((e) => e.key).toList();
  }

  /// Výpočet podobnosti mezi záznamy
  double _calculateSimilarity(DiaryEntry entry1, DiaryEntry entry2) {
    double similarity = 0.0;
    
    // Podobnost nálady
    if (entry1.mood == entry2.mood && entry1.mood != null) {
      similarity += 0.3;
    }
    
    // Podobnost lokace
    if (entry1.location != null && entry2.location != null) {
      if (entry1.location!.toLowerCase() == entry2.location!.toLowerCase()) {
        similarity += 0.4;
      }
    }
    
    // Podobnost počasí
    if (entry1.weather == entry2.weather && entry1.weather != null) {
      similarity += 0.2;
    }
    
    // Podobnost tagů
    final commonTags = entry1.tags.toSet().intersection(entry2.tags.toSet());
    if (commonTags.isNotEmpty) {
      similarity += commonTags.length * 0.1;
    }
    
    // Podobnost sezóny
    if (entry1.date.month == entry2.date.month) {
      similarity += 0.1;
    }
    
    return min(similarity, 1.0);
  }

  /// Vytvoření search indexu
  Future<void> _buildSearchIndex() async {
    _searchIndex.clear();
    
    for (final entry in _allEntries) {
      final words = '${entry.title} ${entry.content} ${entry.location ?? ''} ${entry.tags.join(' ')}'
          .toLowerCase()
          .split(RegExp(r'\W+'))
          .where((w) => w.length > 2)
          .toSet();
      
      for (final word in words) {
        _searchIndex.putIfAbsent(word, () => []).add(entry.id);
      }
    }
    
    debugPrint('🔍 Search index vytvořen s ${_searchIndex.length} slovy');
  }

  /// Generování návrhů
  List<String> _generateSuggestions(String? query, List<DiaryEntry> results) {
    if (query == null || query.isEmpty) return [];
    
    final suggestions = <String>[];
    
    // Návrhy na základě častých slov v výsledcích
    final wordFreq = <String, int>{};
    for (final entry in results.take(10)) {
      final words = '${entry.title} ${entry.content}'.toLowerCase().split(RegExp(r'\W+'));
      for (final word in words) {
        if (word.length > 3 && !query.toLowerCase().contains(word)) {
          wordFreq[word] = (wordFreq[word] ?? 0) + 1;
        }
      }
    }
    
    final sortedWords = wordFreq.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    suggestions.addAll(
      sortedWords.take(3).map((e) => '$query ${e.key}')
    );
    
    return suggestions;
  }

  /// Generování aktivních filtrů
  Map<String, dynamic> _generateActiveFilters(
    List<DiaryMood>? moods,
    List<String>? weatherConditions,
    List<String>? locations,
    List<String>? companions,
  ) {
    return {
      'moods': moods?.map((m) => m.displayName).toList() ?? [],
      'weather': weatherConditions ?? [],
      'locations': locations ?? [],
      'companions': companions ?? [],
    };
  }

  /// Vytvoření cache klíče
  String _createCacheKey(
    String? query,
    List<DiaryMood>? moods,
    DateTime? fromDate,
    DateTime? toDate,
    List<String>? weatherConditions,
    List<String>? locations,
    List<String>? companions,
    int? minRating,
    bool? hasPhotos,
    bool? hasVideos,
    String? season,
  ) {
    return [
      query ?? '',
      moods?.map((m) => m.name).join(',') ?? '',
      fromDate?.toIso8601String() ?? '',
      toDate?.toIso8601String() ?? '',
      weatherConditions?.join(',') ?? '',
      locations?.join(',') ?? '',
      companions?.join(',') ?? '',
      minRating?.toString() ?? '',
      hasPhotos?.toString() ?? '',
      hasVideos?.toString() ?? '',
      season ?? '',
    ].join('|');
  }

  /// Vyčištění cache
  void clearCache() {
    _cache.clear();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  int get totalEntries => _allEntries.length;
  int get indexSize => _searchIndex.length;
}
