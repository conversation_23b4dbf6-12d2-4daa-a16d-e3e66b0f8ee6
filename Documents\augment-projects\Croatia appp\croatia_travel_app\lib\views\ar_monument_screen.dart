import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../services/monument_recognition_service.dart';
import '../models/monument.dart';
import '../widgets/monument_info_card.dart';
import '../widgets/ar_overlay_widget.dart';

class ARMonumentScreen extends StatefulWidget {
  const ARMonumentScreen({super.key});

  @override
  State<ARMonumentScreen> createState() => _ARMonumentScreenState();
}

class _ARMonumentScreenState extends State<ARMonumentScreen> {
  final MonumentRecognitionService _recognitionService =
      MonumentRecognitionService();
  final ImagePicker _imagePicker = ImagePicker();

  bool _isRecognizing = false;
  MonumentRecognitionResult? _lastResult;
  File? _capturedImage;
  bool _showAROverlay = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Rozpoznávání památek'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _showHelp,
            icon: const Icon(Icons.help_outline),
          ),
        ],
      ),
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // Simulace kamery/AR pohledu
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Colors.blue.shade200, Colors.blue.shade400],
              ),
            ),
            child: _capturedImage != null
                ? Image.file(_capturedImage!, fit: BoxFit.cover)
                : const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.camera_alt, size: 64, color: Colors.white70),
                        SizedBox(height: 16),
                        Text(
                          'Namiřte kameru na památku',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Stiskněte tlačítko pro rozpoznání',
                          style: TextStyle(fontSize: 14, color: Colors.white70),
                        ),
                      ],
                    ),
                  ),
          ),

          // AR Overlay
          if (_showAROverlay && _lastResult?.monument != null)
            AROverlayWidget(monument: _lastResult!.monument!),

          // Rozpoznávání indikátor
          if (_isRecognizing)
            Container(
              color: Colors.black54,
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Rozpoznávám památku...',
                      style: TextStyle(color: Colors.white, fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),

          // Výsledek rozpoznání
          if (_lastResult != null && !_isRecognizing)
            Positioned(
              bottom: 100,
              left: 16,
              right: 16,
              child: _buildResultCard(),
            ),

          // Ovládací tlačítka
          Positioned(
            bottom: 20,
            left: 0,
            right: 0,
            child: _buildControlButtons(),
          ),

          // Informační overlay
          Positioned(top: 100, left: 16, right: 16, child: _buildInfoOverlay()),
        ],
      ),
    );
  }

  Widget _buildResultCard() {
    if (_lastResult!.isRecognized && _lastResult!.monument != null) {
      return MonumentInfoCard(
        monument: _lastResult!.monument!,
        confidence: _lastResult!.confidence,
        onShowDetails: () => _showMonumentDetails(_lastResult!.monument!),
        onShowAR: () => setState(() => _showAROverlay = !_showAROverlay),
      );
    } else {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              const Icon(Icons.search_off, size: 48, color: Colors.grey),
              const SizedBox(height: 8),
              const Text(
                'Památka nebyla rozpoznána',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              const Text(
                'Zkuste jiný úhel nebo se přibližte',
                style: TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  TextButton.icon(
                    onPressed: _searchManually,
                    icon: const Icon(Icons.search),
                    label: const Text('Hledat ručně'),
                  ),
                  TextButton.icon(
                    onPressed: _capturePhoto,
                    icon: const Icon(Icons.camera_alt),
                    label: const Text('Zkusit znovu'),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildControlButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        FloatingActionButton(
          heroTag: 'gallery',
          onPressed: _pickFromGallery,
          backgroundColor: Colors.white,
          child: const Icon(Icons.photo_library, color: Colors.blue),
        ),
        FloatingActionButton.large(
          heroTag: 'capture',
          onPressed: _isRecognizing ? null : _capturePhoto,
          backgroundColor: Colors.white,
          child: Icon(
            _isRecognizing ? Icons.hourglass_empty : Icons.camera_alt,
            color: Colors.blue,
            size: 32,
          ),
        ),
        FloatingActionButton(
          heroTag: 'switch',
          onPressed: _switchCamera,
          backgroundColor: Colors.white,
          child: const Icon(Icons.flip_camera_ios, color: Colors.blue),
        ),
      ],
    );
  }

  Widget _buildInfoOverlay() {
    return Card(
      color: Colors.black87,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            const Icon(Icons.info_outline, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                _getInfoText(),
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
            ),
            if (_lastResult?.confidence != null)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getConfidenceColor(),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${(_lastResult!.confidence * 100).round()}%',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  String _getInfoText() {
    if (_isRecognizing) {
      return 'Analyzuji obrázek...';
    } else if (_lastResult?.isRecognized == true) {
      return 'Památka rozpoznána! Klepněte pro více informací.';
    } else if (_lastResult != null) {
      return 'Památka nebyla rozpoznána. Zkuste jiný úhel.';
    } else {
      return 'Namiřte kameru na památku a stiskněte tlačítko.';
    }
  }

  Color _getConfidenceColor() {
    if (_lastResult?.confidence == null) return Colors.grey;

    final confidence = _lastResult!.confidence;
    if (confidence > 0.8) return Colors.green;
    if (confidence > 0.6) return Colors.orange;
    return Colors.red;
  }

  Future<void> _capturePhoto() async {
    try {
      final XFile? photo = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (photo != null) {
        setState(() {
          _capturedImage = File(photo.path);
          _isRecognizing = true;
          _lastResult = null;
          _showAROverlay = false;
        });

        final result = await _recognitionService.recognizeMonument(
          _capturedImage!,
        );

        setState(() {
          _lastResult = result;
          _isRecognizing = false;
        });

        if (result.isRecognized) {
          _showSuccessAnimation();
        }
      }
    } catch (e) {
      setState(() {
        _isRecognizing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Chyba při pořizování fotky: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _pickFromGallery() async {
    try {
      final XFile? photo = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (photo != null) {
        setState(() {
          _capturedImage = File(photo.path);
          _isRecognizing = true;
          _lastResult = null;
          _showAROverlay = false;
        });

        final result = await _recognitionService.recognizeMonument(
          _capturedImage!,
        );

        setState(() {
          _lastResult = result;
          _isRecognizing = false;
        });
      }
    } catch (e) {
      setState(() {
        _isRecognizing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Chyba při výběru obrázku: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _switchCamera() {
    // Simulace přepnutí kamery
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Přepnutí kamery bude dostupné v příští verzi'),
      ),
    );
  }

  void _showSuccessAnimation() {
    // Animace úspěšného rozpoznání
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.check_circle, color: Colors.green, size: 64),
            const SizedBox(height: 16),
            Text(
              'Památka rozpoznána!',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _lastResult?.monument?.name ?? '',
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );

    // Automatické zavření po 2 sekundách
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        Navigator.of(context, rootNavigator: true).pop();
      }
    });
  }

  void _showMonumentDetails(MonumentInfo monument) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MonumentDetailScreen(monument: monument),
      ),
    );
  }

  void _searchManually() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Vyhledat památku',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                labelText: 'Název památky',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.search),
              ),
              onSubmitted: (query) async {
                await _recognitionService.searchMonuments(query);
                // Zobrazit výsledky vyhledávání
              },
            ),
            const SizedBox(height: 16),
            const Text('Nebo vyberte z oblíbených:'),
            const SizedBox(height: 8),
            Expanded(
              child: ListView(
                children: [
                  ListTile(
                    leading: const Text('🏰'),
                    title: const Text('Hradby Dubrovníku'),
                    onTap: () => Navigator.pop(context),
                  ),
                  ListTile(
                    leading: const Text('🏛️'),
                    title: const Text('Diokleciánův palác'),
                    onTap: () => Navigator.pop(context),
                  ),
                  ListTile(
                    leading: const Text('🏞️'),
                    title: const Text('Plitvická jezera'),
                    onTap: () => Navigator.pop(context),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Jak používat rozpoznávání'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('📸 Pořízení fotky:'),
              Text('• Namiřte kameru na památku'),
              Text('• Ujistěte se, že je památka dobře viditelná'),
              Text('• Stiskněte velké tlačítko pro pořízení fotky'),
              SizedBox(height: 12),
              Text('🔍 Rozpoznávání:'),
              Text('• Aplikace automaticky analyzuje obrázek'),
              Text('• Výsledek se zobrazí během několika sekund'),
              Text('• Zelené skóre = vysoká přesnost'),
              SizedBox(height: 12),
              Text('📱 AR režim:'),
              Text('• Po rozpoznání můžete zapnout AR'),
              Text('• Zobrazí se dodatečné informace'),
              Text('• Namiřte telefon zpět na památku'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Rozumím'),
          ),
        ],
      ),
    );
  }
}

// Placeholder pro detail obrazovku
class MonumentDetailScreen extends StatelessWidget {
  final MonumentInfo monument;

  const MonumentDetailScreen({super.key, required this.monument});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(monument.name)),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              monument.name,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(monument.description),
            const SizedBox(height: 16),
            const Text(
              'Historické informace:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(monument.historicalInfo),
          ],
        ),
      ),
    );
  }
}
