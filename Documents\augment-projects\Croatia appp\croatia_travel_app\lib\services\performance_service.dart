import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';

/// Služba pro monitoring výkonu a optimalizace
class PerformanceService extends ChangeNotifier {
  static final PerformanceService _instance = PerformanceService._internal();
  factory PerformanceService() => _instance;
  PerformanceService._internal();

  final Map<String, DateTime> _screenLoadTimes = {};
  final Map<String, Duration> _operationDurations = {};
  final List<PerformanceMetric> _metrics = [];
  Timer? _memoryMonitorTimer;
  bool _isInitialized = false;
  bool _isMonitoring = false;

  // Gettery
  bool get isInitialized => _isInitialized;
  bool get isMonitoring => _isMonitoring;
  List<PerformanceMetric> get metrics => List.unmodifiable(_metrics);

  /// Inicializuje performance službu
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _isInitialized = true;
      await startMonitoring();

      debugPrint('Performance služba inicializována');
    } catch (e) {
      debugPrint('Chyba při inicializaci performance služby: $e');
    }
  }

  /// Spustí monitoring výkonu
  Future<void> startMonitoring() async {
    if (_isMonitoring) return;

    try {
      _isMonitoring = true;

      // Spustí periodické měření paměti
      _memoryMonitorTimer = Timer.periodic(
        const Duration(seconds: 30),
        (_) => _measureMemoryUsage(),
      );

      debugPrint('Performance monitoring spuštěn');
    } catch (e) {
      debugPrint('Chyba při spuštění monitoringu: $e');
    }
  }

  /// Zastaví monitoring výkonu
  Future<void> stopMonitoring() async {
    if (!_isMonitoring) return;

    try {
      _isMonitoring = false;
      _memoryMonitorTimer?.cancel();
      _memoryMonitorTimer = null;

      debugPrint('Performance monitoring zastaven');
    } catch (e) {
      debugPrint('Chyba při zastavení monitoringu: $e');
    }
  }

  /// Začne měření načítání obrazovky
  void startScreenLoad(String screenName) {
    _screenLoadTimes[screenName] = DateTime.now();
    debugPrint('Začátek načítání obrazovky: $screenName');
  }

  /// Ukončí měření načítání obrazovky
  Duration? endScreenLoad(String screenName) {
    final startTime = _screenLoadTimes.remove(screenName);
    if (startTime == null) return null;

    final duration = DateTime.now().difference(startTime);

    _addMetric(
      PerformanceMetric(
        type: MetricType.screenLoad,
        name: screenName,
        value: duration.inMilliseconds.toDouble(),
        unit: 'ms',
        timestamp: DateTime.now(),
      ),
    );

    debugPrint('Obrazovka $screenName načtena za ${duration.inMilliseconds}ms');
    return duration;
  }

  /// Začne měření operace
  PerformanceTimer startOperation(String operationName) {
    return PerformanceTimer._(operationName, this);
  }

  /// Ukončí měření operace
  void _endOperation(String operationName, Duration duration) {
    _operationDurations[operationName] = duration;

    _addMetric(
      PerformanceMetric(
        type: MetricType.operation,
        name: operationName,
        value: duration.inMilliseconds.toDouble(),
        unit: 'ms',
        timestamp: DateTime.now(),
      ),
    );

    debugPrint(
      'Operace $operationName dokončena za ${duration.inMilliseconds}ms',
    );
  }

  /// Změří použití paměti
  Future<void> _measureMemoryUsage() async {
    try {
      // Na mobilních platformách lze získat informace o paměti
      if (Platform.isAndroid || Platform.isIOS) {
        // TODO: Implementovat platform-specific měření paměti
        // Pro teď použijeme mock hodnoty
        final memoryUsage = _getMockMemoryUsage();

        _addMetric(
          PerformanceMetric(
            type: MetricType.memory,
            name: 'memory_usage',
            value: memoryUsage,
            unit: 'MB',
            timestamp: DateTime.now(),
          ),
        );
      }
    } catch (e) {
      debugPrint('Chyba při měření paměti: $e');
    }
  }

  /// Mock hodnoty pro použití paměti
  double _getMockMemoryUsage() {
    // Simuluje použití paměti mezi 50-200 MB
    return 50 + (DateTime.now().millisecondsSinceEpoch % 150).toDouble();
  }

  /// Změří FPS (frames per second)
  void measureFPS(double fps) {
    _addMetric(
      PerformanceMetric(
        type: MetricType.fps,
        name: 'fps',
        value: fps,
        unit: 'fps',
        timestamp: DateTime.now(),
      ),
    );
  }

  /// Změří velikost cache
  void measureCacheSize(String cacheName, int sizeBytes) {
    _addMetric(
      PerformanceMetric(
        type: MetricType.cache,
        name: cacheName,
        value: (sizeBytes / (1024 * 1024)).toDouble(), // Převede na MB
        unit: 'MB',
        timestamp: DateTime.now(),
      ),
    );
  }

  /// Změří síťový požadavek
  void measureNetworkRequest({
    required String url,
    required Duration duration,
    required int responseSize,
    required bool success,
  }) {
    _addMetric(
      PerformanceMetric(
        type: MetricType.network,
        name: 'network_request',
        value: duration.inMilliseconds.toDouble(),
        unit: 'ms',
        timestamp: DateTime.now(),
        metadata: {
          'url': url,
          'response_size': responseSize,
          'success': success,
        },
      ),
    );
  }

  /// Změří databázovou operaci
  void measureDatabaseOperation({
    required String operation,
    required Duration duration,
    required int recordCount,
  }) {
    _addMetric(
      PerformanceMetric(
        type: MetricType.database,
        name: operation,
        value: duration.inMilliseconds.toDouble(),
        unit: 'ms',
        timestamp: DateTime.now(),
        metadata: {'record_count': recordCount},
      ),
    );
  }

  /// Přidá metriku
  void _addMetric(PerformanceMetric metric) {
    _metrics.add(metric);

    // Udržuje pouze posledních 1000 metrik
    if (_metrics.length > 1000) {
      _metrics.removeRange(0, _metrics.length - 1000);
    }

    notifyListeners();
  }

  /// Získá průměrnou dobu načítání obrazovek
  double getAverageScreenLoadTime() {
    final screenLoadMetrics = _metrics
        .where((m) => m.type == MetricType.screenLoad)
        .toList();

    if (screenLoadMetrics.isEmpty) return 0;

    final total = screenLoadMetrics.map((m) => m.value).reduce((a, b) => a + b);

    return total / screenLoadMetrics.length;
  }

  /// Získá průměrné použití paměti
  double getAverageMemoryUsage() {
    final memoryMetrics = _metrics
        .where((m) => m.type == MetricType.memory)
        .toList();

    if (memoryMetrics.isEmpty) return 0;

    final total = memoryMetrics.map((m) => m.value).reduce((a, b) => a + b);

    return total / memoryMetrics.length;
  }

  /// Získá průměrné FPS
  double getAverageFPS() {
    final fpsMetrics = _metrics.where((m) => m.type == MetricType.fps).toList();

    if (fpsMetrics.isEmpty) return 0;

    final total = fpsMetrics.map((m) => m.value).reduce((a, b) => a + b);

    return total / fpsMetrics.length;
  }

  /// Získá nejpomalejší operace
  List<PerformanceMetric> getSlowestOperations({int limit = 10}) {
    final operationMetrics = _metrics
        .where((m) => m.type == MetricType.operation)
        .toList();

    operationMetrics.sort((a, b) => b.value.compareTo(a.value));

    return operationMetrics.take(limit).toList();
  }

  /// Získá metriky podle typu
  List<PerformanceMetric> getMetricsByType(MetricType type) {
    return _metrics.where((m) => m.type == type).toList();
  }

  /// Získá metriky za posledních N minut
  List<PerformanceMetric> getRecentMetrics(int minutes) {
    final cutoff = DateTime.now().subtract(Duration(minutes: minutes));
    return _metrics.where((m) => m.timestamp.isAfter(cutoff)).toList();
  }

  /// Vymaže všechny metriky
  void clearMetrics() {
    _metrics.clear();
    notifyListeners();
    debugPrint('Performance metriky vymazány');
  }

  /// Získá performance report
  PerformanceReport generateReport() {
    return PerformanceReport(
      averageScreenLoadTime: getAverageScreenLoadTime(),
      averageMemoryUsage: getAverageMemoryUsage(),
      averageFPS: getAverageFPS(),
      totalMetrics: _metrics.length,
      slowestOperations: getSlowestOperations(limit: 5),
      generatedAt: DateTime.now(),
    );
  }

  @override
  void dispose() {
    stopMonitoring();
    super.dispose();
  }
}

/// Timer pro měření výkonu operací
class PerformanceTimer {
  final String _operationName;
  final PerformanceService _service;
  final DateTime _startTime;

  PerformanceTimer._(this._operationName, this._service)
    : _startTime = DateTime.now();

  /// Ukončí měření
  Duration stop() {
    final duration = DateTime.now().difference(_startTime);
    _service._endOperation(_operationName, duration);
    return duration;
  }
}

/// Typ performance metriky
enum MetricType { screenLoad, operation, memory, fps, cache, network, database }

/// Performance metrika
class PerformanceMetric {
  final MetricType type;
  final String name;
  final double value;
  final String unit;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  const PerformanceMetric({
    required this.type,
    required this.name,
    required this.value,
    required this.unit,
    required this.timestamp,
    this.metadata = const {},
  });

  @override
  String toString() {
    return 'PerformanceMetric(type: $type, name: $name, value: $value$unit)';
  }
}

/// Performance report
class PerformanceReport {
  final double averageScreenLoadTime;
  final double averageMemoryUsage;
  final double averageFPS;
  final int totalMetrics;
  final List<PerformanceMetric> slowestOperations;
  final DateTime generatedAt;

  const PerformanceReport({
    required this.averageScreenLoadTime,
    required this.averageMemoryUsage,
    required this.averageFPS,
    required this.totalMetrics,
    required this.slowestOperations,
    required this.generatedAt,
  });

  @override
  String toString() {
    return '''
Performance Report (${generatedAt.toIso8601String()}):
- Průměrná doba načítání: ${averageScreenLoadTime.toStringAsFixed(1)}ms
- Průměrné použití paměti: ${averageMemoryUsage.toStringAsFixed(1)}MB
- Průměrné FPS: ${averageFPS.toStringAsFixed(1)}
- Celkem metrik: $totalMetrics
- Nejpomalejších operací: ${slowestOperations.length}
''';
  }
}
