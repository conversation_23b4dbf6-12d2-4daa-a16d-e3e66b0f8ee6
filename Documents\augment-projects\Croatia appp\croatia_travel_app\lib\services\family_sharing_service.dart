import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/family_sharing.dart';
import '../models/diary_entry.dart';

/// 👨‍👩‍👧‍👦 FAMILY SHARING SERVICE - <PERSON><PERSON><PERSON> s<PERSON>
class FamilySharingService {
  static final FamilySharingService _instance =
      FamilySharingService._internal();
  factory FamilySharingService() => _instance;
  FamilySharingService._internal();

  bool _isInitialized = false;
  final List<FamilyCircle> _familyCircles = [];
  final List<SharedMemory> _sharedMemories = [];
  final List<FamilyInvitation> _invitations = [];
  String? _currentUserId;

  /// Inicializace služby
  Future<void> initialize(String userId) async {
    if (_isInitialized) return;

    try {
      debugPrint('👨‍👩‍👧‍👦 Inicializuji Family Sharing Service...');

      _currentUserId = userId;
      await _loadFamilyCircles();
      await _loadSharedMemories();
      await _loadInvitations();

      _isInitialized = true;
      debugPrint('✅ Family Sharing Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Family Sharing: $e');
      _isInitialized = true;
    }
  }

  /// Vytvoření rodinného kruhu
  Future<FamilyCircle?> createFamilyCircle({
    required String name,
    required String description,
    String? coverImageUrl,
    FamilyCirclePrivacy privacy = FamilyCirclePrivacy.private,
  }) async {
    try {
      if (_currentUserId == null) return null;

      final circle = FamilyCircle(
        id: 'circle_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        description: description,
        coverImageUrl: coverImageUrl,
        ownerId: _currentUserId!,
        members: [
          FamilyMember(
            userId: _currentUserId!,
            displayName: 'Já', // V produkci by se načetlo ze user profilu
            role: FamilyRole.owner,
            joinedAt: DateTime.now(),
            isActive: true,
          ),
        ],
        privacy: privacy,
        createdAt: DateTime.now(),
        lastActivity: DateTime.now(),
        settings: FamilyCircleSettings.defaultSettings(),
      );

      _familyCircles.add(circle);
      await _saveFamilyCircles();

      debugPrint('👨‍👩‍👧‍👦 Vytvořen rodinný kruh: ${circle.name}');
      return circle;
    } catch (e) {
      debugPrint('❌ Chyba při vytváření rodinného kruhu: $e');
      return null;
    }
  }

  /// Pozvání člena do rodinného kruhu
  Future<FamilyInvitation?> inviteMember({
    required String circleId,
    required String email,
    String? personalMessage,
    FamilyRole role = FamilyRole.member,
  }) async {
    try {
      final circle = _familyCircles.firstWhere((c) => c.id == circleId);

      // Kontrola oprávnění
      final currentMember = circle.members.firstWhere(
        (m) => m.userId == _currentUserId,
      );
      if (!currentMember.canInviteMembers) {
        throw Exception('Nemáte oprávnění pozvat nové členy');
      }

      final invitation = FamilyInvitation(
        id: 'invite_${DateTime.now().millisecondsSinceEpoch}',
        circleId: circleId,
        circleName: circle.name,
        inviterUserId: _currentUserId!,
        inviterName: currentMember.displayName,
        inviteeEmail: email,
        role: role,
        personalMessage: personalMessage,
        status: InvitationStatus.pending,
        createdAt: DateTime.now(),
        expiresAt: DateTime.now().add(const Duration(days: 7)),
      );

      _invitations.add(invitation);
      await _saveInvitations();

      // V produkci by zde bylo odeslání emailu
      debugPrint('📧 Pozvánka odeslána na: $email');
      return invitation;
    } catch (e) {
      debugPrint('❌ Chyba při odesílání pozvánky: $e');
      return null;
    }
  }

  /// Přijetí pozvánky
  Future<bool> acceptInvitation(
    String invitationId,
    String userId,
    String displayName,
  ) async {
    try {
      final invitationIndex = _invitations.indexWhere(
        (i) => i.id == invitationId,
      );
      if (invitationIndex == -1) return false;

      final invitation = _invitations[invitationIndex];
      if (invitation.status != InvitationStatus.pending ||
          invitation.isExpired) {
        return false;
      }

      // Přidání člena do kruhu
      final circleIndex = _familyCircles.indexWhere(
        (c) => c.id == invitation.circleId,
      );
      if (circleIndex == -1) return false;

      final circle = _familyCircles[circleIndex];
      final newMember = FamilyMember(
        userId: userId,
        displayName: displayName,
        role: invitation.role,
        joinedAt: DateTime.now(),
        isActive: true,
      );

      final updatedCircle = circle.copyWith(
        members: [...circle.members, newMember],
        lastActivity: DateTime.now(),
      );

      _familyCircles[circleIndex] = updatedCircle;

      // Aktualizace pozvánky
      _invitations[invitationIndex] = invitation.copyWith(
        status: InvitationStatus.accepted,
      );

      await _saveFamilyCircles();
      await _saveInvitations();

      debugPrint('✅ Pozvánka přijata: ${invitation.circleName}');
      return true;
    } catch (e) {
      debugPrint('❌ Chyba při přijímání pozvánky: $e');
      return false;
    }
  }

  /// Sdílení vzpomínky s rodinným kruhem
  Future<SharedMemory?> shareMemory({
    required String circleId,
    required DiaryEntry entry,
    String? personalNote,
    List<String>? selectedPhotos,
    bool allowComments = true,
    bool allowReactions = true,
  }) async {
    try {
      final circle = _familyCircles.firstWhere((c) => c.id == circleId);

      // Kontrola členství
      final member = circle.members.firstWhere(
        (m) => m.userId == _currentUserId,
      );
      if (!member.canShareMemories) {
        throw Exception('Nemáte oprávnění sdílet vzpomínky');
      }

      final sharedMemory = SharedMemory(
        id: 'memory_${DateTime.now().millisecondsSinceEpoch}',
        circleId: circleId,
        originalEntryId: entry.id,
        sharedByUserId: _currentUserId!,
        sharedByName: member.displayName,
        title: entry.title,
        content: entry.content,
        location: entry.location,
        date: entry.date,
        mood: entry.mood,
        weather: entry.weather,
        photos: selectedPhotos ?? entry.photos,
        personalNote: personalNote,
        allowComments: allowComments,
        allowReactions: allowReactions,
        sharedAt: DateTime.now(),
        reactions: [],
        comments: [],
        viewCount: 0,
      );

      _sharedMemories.add(sharedMemory);
      await _saveSharedMemories();

      // Aktualizace aktivity kruhu
      final circleIndex = _familyCircles.indexWhere((c) => c.id == circleId);
      _familyCircles[circleIndex] = circle.copyWith(
        lastActivity: DateTime.now(),
      );
      await _saveFamilyCircles();

      debugPrint('📤 Vzpomínka sdílena: ${entry.title}');
      return sharedMemory;
    } catch (e) {
      debugPrint('❌ Chyba při sdílení vzpomínky: $e');
      return null;
    }
  }

  /// Přidání reakce na sdílenou vzpomínku
  Future<bool> addReaction({
    required String memoryId,
    required ReactionType reaction,
  }) async {
    try {
      final memoryIndex = _sharedMemories.indexWhere((m) => m.id == memoryId);
      if (memoryIndex == -1) return false;

      final memory = _sharedMemories[memoryIndex];
      if (!memory.allowReactions) return false;

      // Odstranění předchozí reakce od stejného uživatele
      final filteredReactions = memory.reactions
          .where((r) => r.userId != _currentUserId)
          .toList();

      // Přidání nové reakce
      final newReaction = MemoryReaction(
        userId: _currentUserId!,
        userName: 'Já', // V produkci by se načetlo
        reaction: reaction,
        timestamp: DateTime.now(),
      );

      final updatedMemory = memory.copyWith(
        reactions: [...filteredReactions, newReaction],
      );

      _sharedMemories[memoryIndex] = updatedMemory;
      await _saveSharedMemories();

      debugPrint('👍 Reakce přidána: ${reaction.emoji}');
      return true;
    } catch (e) {
      debugPrint('❌ Chyba při přidávání reakce: $e');
      return false;
    }
  }

  /// Přidání komentáře
  Future<bool> addComment({
    required String memoryId,
    required String text,
  }) async {
    try {
      final memoryIndex = _sharedMemories.indexWhere((m) => m.id == memoryId);
      if (memoryIndex == -1) return false;

      final memory = _sharedMemories[memoryIndex];
      if (!memory.allowComments) return false;

      final comment = MemoryComment(
        id: 'comment_${DateTime.now().millisecondsSinceEpoch}',
        userId: _currentUserId!,
        userName: 'Já', // V produkci by se načetlo
        text: text,
        timestamp: DateTime.now(),
      );

      final updatedMemory = memory.copyWith(
        comments: [...memory.comments, comment],
      );

      _sharedMemories[memoryIndex] = updatedMemory;
      await _saveSharedMemories();

      debugPrint('💬 Komentář přidán');
      return true;
    } catch (e) {
      debugPrint('❌ Chyba při přidávání komentáře: $e');
      return false;
    }
  }

  /// Získání sdílených vzpomínek pro kruh
  Future<List<SharedMemory>> getSharedMemories(
    String circleId, {
    int limit = 20,
  }) async {
    return _sharedMemories
        .where((memory) => memory.circleId == circleId)
        .toList()
      ..sort((a, b) => b.sharedAt.compareTo(a.sharedAt))
      ..take(limit);
  }

  /// Získání rodinných kruhů uživatele
  List<FamilyCircle> getUserFamilyCircles() {
    if (_currentUserId == null) return [];

    return _familyCircles
        .where(
          (circle) => circle.members.any((m) => m.userId == _currentUserId),
        )
        .toList();
  }

  /// Získání čekajících pozvánek
  List<FamilyInvitation> getPendingInvitations() {
    return _invitations
        .where(
          (inv) => inv.status == InvitationStatus.pending && !inv.isExpired,
        )
        .toList();
  }

  /// Opuštění rodinného kruhu
  Future<bool> leaveFamilyCircle(String circleId) async {
    try {
      final circleIndex = _familyCircles.indexWhere((c) => c.id == circleId);
      if (circleIndex == -1) return false;

      final circle = _familyCircles[circleIndex];

      // Vlastník nemůže opustit kruh (musí ho smazat)
      if (circle.ownerId == _currentUserId) {
        throw Exception('Vlastník nemůže opustit kruh. Musíte ho smazat.');
      }

      // Odstranění člena
      final updatedMembers = circle.members
          .where((m) => m.userId != _currentUserId)
          .toList();

      final updatedCircle = circle.copyWith(
        members: updatedMembers,
        lastActivity: DateTime.now(),
      );

      _familyCircles[circleIndex] = updatedCircle;
      await _saveFamilyCircles();

      debugPrint('👋 Opustil jste rodinný kruh: ${circle.name}');
      return true;
    } catch (e) {
      debugPrint('❌ Chyba při opouštění kruhu: $e');
      return false;
    }
  }

  /// Smazání rodinného kruhu (pouze vlastník)
  Future<bool> deleteFamilyCircle(String circleId) async {
    try {
      final circle = _familyCircles.firstWhere((c) => c.id == circleId);

      if (circle.ownerId != _currentUserId) {
        throw Exception('Pouze vlastník může smazat rodinný kruh');
      }

      // Odstranění kruhu
      _familyCircles.removeWhere((c) => c.id == circleId);

      // Odstranění souvisejících vzpomínek
      _sharedMemories.removeWhere((m) => m.circleId == circleId);

      // Odstranění souvisejících pozvánek
      _invitations.removeWhere((i) => i.circleId == circleId);

      await _saveFamilyCircles();
      await _saveSharedMemories();
      await _saveInvitations();

      debugPrint('🗑️ Rodinný kruh smazán: ${circle.name}');
      return true;
    } catch (e) {
      debugPrint('❌ Chyba při mazání kruhu: $e');
      return false;
    }
  }

  /// Generování statistik pro rodinný kruh
  FamilyCircleStats generateCircleStats(String circleId) {
    final circle = _familyCircles.firstWhere((c) => c.id == circleId);
    final memories = _sharedMemories
        .where((m) => m.circleId == circleId)
        .toList();

    final totalReactions = memories.fold<int>(
      0,
      (sum, m) => sum + m.reactions.length,
    );
    final totalComments = memories.fold<int>(
      0,
      (sum, m) => sum + m.comments.length,
    );
    final totalViews = memories.fold<int>(0, (sum, m) => sum + m.viewCount);

    final activeMembersCount = circle.members.where((m) => m.isActive).length;

    // Nejaktivnější člen
    final memberActivity = <String, int>{};
    for (final memory in memories) {
      memberActivity[memory.sharedByUserId] =
          (memberActivity[memory.sharedByUserId] ?? 0) + 1;
    }

    final mostActiveMemberId = memberActivity.isNotEmpty
        ? memberActivity.entries.reduce((a, b) => a.value > b.value ? a : b).key
        : null;

    return FamilyCircleStats(
      circleId: circleId,
      totalMembers: circle.members.length,
      activeMembers: activeMembersCount,
      totalMemories: memories.length,
      totalReactions: totalReactions,
      totalComments: totalComments,
      totalViews: totalViews,
      mostActiveMemberId: mostActiveMemberId,
      createdAt: circle.createdAt,
      lastActivity: circle.lastActivity,
    );
  }

  /// Načítání a ukládání dat
  Future<void> _loadFamilyCircles() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final circlesJson = prefs.getString('family_circles');

      if (circlesJson != null) {
        final List<dynamic> data = jsonDecode(circlesJson);
        _familyCircles.clear();
        _familyCircles.addAll(
          data.map((json) => FamilyCircle.fromJson(json)).toList(),
        );
      }
    } catch (e) {
      debugPrint('❌ Chyba při načítání rodinných kruhů: $e');
    }
  }

  Future<void> _saveFamilyCircles() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'family_circles',
        jsonEncode(_familyCircles.map((c) => c.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání rodinných kruhů: $e');
    }
  }

  Future<void> _loadSharedMemories() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final memoriesJson = prefs.getString('shared_memories');

      if (memoriesJson != null) {
        final List<dynamic> data = jsonDecode(memoriesJson);
        _sharedMemories.clear();
        _sharedMemories.addAll(
          data.map((json) => SharedMemory.fromJson(json)).toList(),
        );
      }
    } catch (e) {
      debugPrint('❌ Chyba při načítání sdílených vzpomínek: $e');
    }
  }

  Future<void> _saveSharedMemories() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'shared_memories',
        jsonEncode(_sharedMemories.map((m) => m.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání sdílených vzpomínek: $e');
    }
  }

  Future<void> _loadInvitations() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final invitationsJson = prefs.getString('family_invitations');

      if (invitationsJson != null) {
        final List<dynamic> data = jsonDecode(invitationsJson);
        _invitations.clear();
        _invitations.addAll(
          data.map((json) => FamilyInvitation.fromJson(json)).toList(),
        );
      }
    } catch (e) {
      debugPrint('❌ Chyba při načítání pozvánek: $e');
    }
  }

  Future<void> _saveInvitations() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'family_invitations',
        jsonEncode(_invitations.map((i) => i.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání pozvánek: $e');
    }
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  String? get currentUserId => _currentUserId;
  List<FamilyCircle> get allFamilyCircles => List.unmodifiable(_familyCircles);
  List<SharedMemory> get allSharedMemories =>
      List.unmodifiable(_sharedMemories);
  List<FamilyInvitation> get allInvitations => List.unmodifiable(_invitations);
}
