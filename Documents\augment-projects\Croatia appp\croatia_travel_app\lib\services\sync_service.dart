import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../data/local_database.dart';
import '../models/place.dart';
import '../models/event.dart';
import '../models/cuisine.dart';

class SyncService {
  static final SyncService _instance = SyncService._internal();
  factory SyncService() => _instance;
  SyncService._internal();

  final Dio _dio = Dio();
  final LocalDatabase _localDb = LocalDatabase();

  static const String _baseUrl = 'https://api.croatia-travel.com';
  static const String _lastSyncKey = 'last_sync_timestamp';

  /// Synchronizace všech dat po připojení k internetu
  Future<void> syncAllData() async {
    final connectivity = await Connectivity().checkConnectivity();
    if (connectivity.contains(ConnectivityResult.none)) {
      throw Exception('<PERSON><PERSON><PERSON> k dispozici internetové připojení');
    }

    try {
      final prefs = await SharedPreferences.getInstance();
      final lastSync = prefs.getString(_lastSyncKey);

      // Synchronizace míst
      await _syncPlaces(lastSync);

      // Synchronizace událostí
      await _syncEvents(lastSync);

      // Synchronizace kuchyně
      await _syncCuisine(lastSync);

      // Synchronizace uživatelských dat na server
      await _uploadUserData();

      // Uložení času poslední synchronizace
      await prefs.setString(_lastSyncKey, DateTime.now().toIso8601String());
    } catch (e) {
      throw Exception('Chyba při synchronizaci: $e');
    }
  }

  /// Synchronizace míst
  Future<void> _syncPlaces(String? lastSync) async {
    try {
      final response = await _dio.get(
        '$_baseUrl/places',
        queryParameters: lastSync != null ? {'since': lastSync} : null,
      );

      if (response.statusCode == 200) {
        final List<dynamic> placesData = response.data['places'];
        final places = placesData.map((data) => Place.fromJson(data)).toList();

        for (final place in places) {
          await _localDb.insertOrUpdatePlace(place);
        }
      }
    } catch (e) {
      debugPrint('Chyba při synchronizaci míst: $e');
    }
  }

  /// Synchronizace událostí
  Future<void> _syncEvents(String? lastSync) async {
    try {
      final response = await _dio.get(
        '$_baseUrl/events',
        queryParameters: lastSync != null ? {'since': lastSync} : null,
      );

      if (response.statusCode == 200) {
        final List<dynamic> eventsData = response.data['events'];
        final events = eventsData.map((data) => Event.fromJson(data)).toList();

        for (final event in events) {
          await _localDb.insertOrUpdateEvent(event);
        }
      }
    } catch (e) {
      debugPrint('Chyba při synchronizaci událostí: $e');
    }
  }

  /// Synchronizace kuchyně
  Future<void> _syncCuisine(String? lastSync) async {
    try {
      final response = await _dio.get(
        '$_baseUrl/cuisine',
        queryParameters: lastSync != null ? {'since': lastSync} : null,
      );

      if (response.statusCode == 200) {
        final List<dynamic> cuisineData = response.data['cuisine'];
        final cuisineItems = cuisineData
            .map((data) => CuisineItem.fromJson(data))
            .toList();

        for (final item in cuisineItems) {
          await _localDb.insertOrUpdateCuisineItem(item);
        }
      }
    } catch (e) {
      debugPrint('Chyba při synchronizaci kuchyně: $e');
    }
  }

  /// Nahrání uživatelských dat na server
  Future<void> _uploadUserData() async {
    try {
      // Získání nesynchronizovaných dat z lokální databáze
      final unsyncedData = await _localDb.getUnsyncedUserData();

      if (unsyncedData.isNotEmpty) {
        final response = await _dio.post(
          '$_baseUrl/user-data',
          data: {'data': unsyncedData},
        );

        if (response.statusCode == 200) {
          // Označení dat jako synchronizovaných
          // Označení jednotlivých položek jako synchronizovaných
          for (final item in unsyncedData) {
            await _localDb.markDataAsSynced(item['type'], item['data']['id']);
          }
        }
      }
    } catch (e) {
      debugPrint('Chyba při nahrávání uživatelských dat: $e');
    }
  }

  /// Stažení offline map pro konkrétní region
  Future<void> downloadOfflineMaps(String region) async {
    try {
      final response = await _dio.get(
        '$_baseUrl/maps/$region',
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200) {
        await _localDb.saveOfflineMap({
          'region': region,
          'data': response.data,
        });
      }
    } catch (e) {
      throw Exception('Chyba při stahování offline map: $e');
    }
  }

  /// Kontrola dostupnosti aktualizací
  Future<bool> checkForUpdates() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastSync = prefs.getString(_lastSyncKey);

      final response = await _dio.get(
        '$_baseUrl/updates',
        queryParameters: lastSync != null ? {'since': lastSync} : null,
      );

      return response.statusCode == 200 && response.data['has_updates'] == true;
    } catch (e) {
      return false;
    }
  }

  /// Automatická synchronizace na pozadí
  Future<void> backgroundSync() async {
    final connectivity = await Connectivity().checkConnectivity();
    if (connectivity.contains(ConnectivityResult.none)) return;

    try {
      // Pouze kritické aktualizace v úsporném režimu
      await _syncCriticalData();
    } catch (e) {
      debugPrint('Chyba při synchronizaci na pozadí: $e');
    }
  }

  /// Synchronizace pouze kritických dat (pro úsporný režim)
  Future<void> _syncCriticalData() async {
    try {
      // Pouze aktuální události a důležitá upozornění
      final response = await _dio.get('$_baseUrl/critical-updates');

      if (response.statusCode == 200) {
        final data = response.data;

        // Zpracování kritických aktualizací
        if (data['emergency_alerts'] != null) {
          await _localDb.saveEmergencyAlerts(data['emergency_alerts']);
        }

        if (data['current_events'] != null) {
          final events = (data['current_events'] as List)
              .map((e) => Event.fromJson(e))
              .toList();

          for (final event in events) {
            await _localDb.insertOrUpdateEvent(event);
          }
        }
      }
    } catch (e) {
      debugPrint('Chyba při synchronizaci kritických dat: $e');
    }
  }
}
