import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/cultural_site.dart';
import '../services/cultural_discovery_service.dart';

class CulturalDiscoveryScreen extends StatefulWidget {
  const CulturalDiscoveryScreen({super.key});

  @override
  State<CulturalDiscoveryScreen> createState() =>
      _CulturalDiscoveryScreenState();
}

class _CulturalDiscoveryScreenState extends State<CulturalDiscoveryScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Cultural Discovery stav
  String _selectedRegion = 'all';
  CulturalSiteType? _selectedSiteType;
  HistoricalPeriod? _selectedHistoricalPeriod;
  String _searchQuery = '';
  bool _isLoading = false;

  // Data
  List<CulturalSite> _allCulturalSites = [];
  List<CulturalSite> _filteredCulturalSites = [];
  List<CulturalEvent> _allCulturalEvents = [];

  // Statistiky
  int _totalSites = 0;
  int _unescoSites = 0;
  int _topRated = 0;
  int _familyFriendly = 0;
  int _historicallySignificant = 0;

  final CulturalDiscoveryService _culturalService = CulturalDiscoveryService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _loadCulturalData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadCulturalData() async {
    setState(() => _isLoading = true);

    try {
      _allCulturalSites = await _culturalService.getAllCulturalSites();
      _allCulturalEvents = await _culturalService.getAllCulturalEvents();
      _applyFilters();
      _updateStatistics();
    } catch (e) {
      debugPrint('Chyba při načítání kulturních míst: $e');
    }

    setState(() => _isLoading = false);
  }

  void _applyFilters() {
    _filteredCulturalSites = _allCulturalSites.where((site) {
      // Region filter
      if (_selectedRegion != 'all' && site.region != _selectedRegion) {
        return false;
      }

      // Site type filter
      if (_selectedSiteType != null && site.siteType != _selectedSiteType) {
        return false;
      }

      // Historical period filter
      if (_selectedHistoricalPeriod != null &&
          site.historicalPeriod != _selectedHistoricalPeriod) {
        return false;
      }

      // Search query
      if (_searchQuery.isNotEmpty) {
        return site.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            site.description.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            site.address.toLowerCase().contains(_searchQuery.toLowerCase());
      }

      return true;
    }).toList();
  }

  void _updateStatistics() {
    _totalSites = _allCulturalSites.length;
    _unescoSites = _allCulturalSites.where((s) => s.isUnescoSite).length;
    _topRated = _allCulturalSites.where((s) => s.rating >= 4.5).length;
    _familyFriendly = _allCulturalSites.where((s) => s.isFamilyFriendly).length;
    _historicallySignificant = _allCulturalSites
        .where((s) => s.isHistoricallySignificant)
        .length;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Chorvatská kultura',
          style: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(
                  0xFFB8860B,
                ).withValues(alpha: 0.9), // Dark Goldenrod
                const Color(0xFF8B4513).withValues(alpha: 0.8), // Saddle Brown
                const Color(0xFF654321).withValues(alpha: 0.7), // Dark Brown
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: CustomPaint(
            painter: WatercolorCulturalHeaderPainter(),
            size: Size.infinite,
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: _loadCulturalData,
              icon: const Icon(Icons.refresh),
              tooltip: 'Aktualizovat kulturní místa',
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          indicator: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Colors.white.withValues(alpha: 0.3),
          ),
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Přehled'),
            Tab(icon: Icon(Icons.account_balance), text: 'Všechny'),
            Tab(icon: Icon(Icons.emoji_events), text: 'UNESCO'),
            Tab(icon: Icon(Icons.star), text: 'Top hodnocené'),
            Tab(icon: Icon(Icons.family_restroom), text: 'Rodinné'),
            Tab(icon: Icon(Icons.map), text: 'Mapa'),
          ],
        ),
      ),
      body: Container(
        child: CustomPaint(
          painter: WatercolorCulturalBackgroundPainter(),
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(),
              _buildAllCulturalSitesTab(),
              _buildUnescoTab(),
              _buildTopRatedTab(),
              _buildFamilyFriendlyTab(),
              _buildMapTab(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Hlavní karta
          Container(
            child: CustomPaint(
              painter: WatercolorCulturalMainCardPainter(
                const Color(0xFFB8860B),
              ),
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Column(
                  children: [
                    Text(
                      'Objevte chorvatskou kulturu',
                      style: GoogleFonts.playfairDisplay(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFFB8860B),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Prozkoumejte bohaté kulturní dědictví a historické památky',
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        color: const Color(0xFF666666),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Vyhledávání
          _buildSearchSection(),

          const SizedBox(height: 20),

          // Statistiky
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            children: [
              _buildStatCard(
                'Celkem',
                '$_totalSites',
                'kulturních míst',
                Icons.account_balance,
                const Color(0xFFB8860B),
              ),
              _buildStatCard(
                'UNESCO',
                '$_unescoSites',
                'světových památek',
                Icons.emoji_events,
                const Color(0xFF8B4513),
              ),
              _buildStatCard(
                'Top hodnocené',
                '$_topRated',
                '4.5+ hvězdiček',
                Icons.star,
                const Color(0xFF654321),
              ),
              _buildStatCard(
                'Rodinné',
                '$_familyFriendly',
                'pro rodiny',
                Icons.family_restroom,
                const Color(0xFFCD853F),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // UNESCO památky přehled
          _buildUnescoOverview(),

          const SizedBox(height: 20),

          // Kulturní události
          _buildCulturalEventsOverview(),
        ],
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      child: CustomPaint(
        painter: WatercolorCulturalSearchPainter(const Color(0xFF8B4513)),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Vyhledávání kulturních míst',
                style: GoogleFonts.playfairDisplay(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF8B4513),
                ),
              ),
              const SizedBox(height: 16),

              // Search field
              TextField(
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                    _applyFilters();
                  });
                },
                decoration: InputDecoration(
                  hintText: 'Hledat hrady, kostely, muzea...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                  fillColor: Colors.grey[50],
                ),
              ),

              const SizedBox(height: 16),

              // Filters
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildFilterChip(
                    'Region',
                    _selectedRegion,
                    [
                      'all',
                      'dalmatia',
                      'istria',
                      'kvarner',
                      'dubrovnik',
                      'zagreb',
                    ],
                    (value) => setState(() {
                      _selectedRegion = value;
                      _applyFilters();
                    }),
                  ),
                  _buildFilterChip(
                    'Typ',
                    _selectedSiteType?.toString().split('.').last ?? 'all',
                    [
                      'all',
                      'castle',
                      'church',
                      'museum',
                      'palace',
                      'fortress',
                      'monastery',
                    ],
                    (value) => setState(() {
                      if (value == 'all') {
                        _selectedSiteType = null;
                      } else {
                        _selectedSiteType = CulturalSiteType.values.firstWhere(
                          (type) => type.toString().split('.').last == value,
                        );
                      }
                      _applyFilters();
                    }),
                  ),
                  _buildFilterChip(
                    'Období',
                    _selectedHistoricalPeriod?.toString().split('.').last ??
                        'all',
                    [
                      'all',
                      'roman',
                      'medieval',
                      'renaissance',
                      'baroque',
                      'modern',
                    ],
                    (value) => setState(() {
                      if (value == 'all') {
                        _selectedHistoricalPeriod = null;
                      } else {
                        _selectedHistoricalPeriod = HistoricalPeriod.values
                            .firstWhere(
                              (period) =>
                                  period.toString().split('.').last == value,
                            );
                      }
                      _applyFilters();
                    }),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Container(
      child: CustomPaint(
        painter: WatercolorCulturalStatCardPainter(color),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                child: CustomPaint(
                  painter: WatercolorCulturalIconPainter(color),
                  child: Icon(icon, size: 32, color: color),
                ),
              ),
              const SizedBox(height: 12),
              Text(
                value,
                style: GoogleFonts.playfairDisplay(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: GoogleFonts.inter(
                  fontSize: 12,
                  color: const Color(0xFF666666),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF2C2C2C),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterChip(
    String label,
    String currentValue,
    List<String> options,
    Function(String) onChanged,
  ) {
    return Container(
      child: CustomPaint(
        painter: WatercolorCulturalFilterChipPainter(const Color(0xFFB8860B)),
        child: PopupMenuButton<String>(
          onSelected: onChanged,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: const Color(0xFFB8860B).withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '$label: ${_getFilterLabel(currentValue)}',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFFB8860B),
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  Icons.arrow_drop_down,
                  size: 16,
                  color: const Color(0xFFB8860B),
                ),
              ],
            ),
          ),
          itemBuilder: (context) => options
              .map(
                (option) => PopupMenuItem(
                  value: option,
                  child: Text(_getFilterLabel(option)),
                ),
              )
              .toList(),
        ),
      ),
    );
  }

  String _getFilterLabel(String value) {
    switch (value) {
      case 'all':
        return 'Vše';
      case 'dalmatia':
        return 'Dalmácie';
      case 'istria':
        return 'Istrie';
      case 'kvarner':
        return 'Kvarner';
      case 'dubrovnik':
        return 'Dubrovník';
      case 'zagreb':
        return 'Zagreb';
      case 'castle':
        return 'Hrad';
      case 'church':
        return 'Kostel';
      case 'museum':
        return 'Muzeum';
      case 'palace':
        return 'Palác';
      case 'fortress':
        return 'Pevnost';
      case 'monastery':
        return 'Klášter';
      case 'roman':
        return 'Římské';
      case 'medieval':
        return 'Středověk';
      case 'renaissance':
        return 'Renesance';
      case 'baroque':
        return 'Baroko';
      case 'modern':
        return 'Moderní';
      default:
        return value;
    }
  }

  Widget _buildUnescoOverview() {
    return Container(
      child: CustomPaint(
        painter: WatercolorCulturalUnescoOverviewPainter(
          const Color(0xFF8B4513),
        ),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    child: CustomPaint(
                      painter: WatercolorCulturalIconPainter(
                        const Color(0xFF8B4513),
                      ),
                      child: Icon(
                        Icons.emoji_events,
                        size: 32,
                        color: const Color(0xFF8B4513),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'UNESCO světové dědictví',
                          style: GoogleFonts.playfairDisplay(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF8B4513),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '$_unescoSites památek světového významu',
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: const Color(0xFF666666),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: const Color(0xFF8B4513),
                    size: 16,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                'Objevte nejcennější kulturní poklady Chorvatska uznané UNESCO',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: const Color(0xFF666666),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCulturalEventsOverview() {
    return Container(
      child: CustomPaint(
        painter: WatercolorCulturalEventsOverviewPainter(
          const Color(0xFF654321),
        ),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    child: CustomPaint(
                      painter: WatercolorCulturalIconPainter(
                        const Color(0xFF654321),
                      ),
                      child: Icon(
                        Icons.event,
                        size: 32,
                        color: const Color(0xFF654321),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Kulturní události',
                          style: GoogleFonts.playfairDisplay(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF654321),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${_allCulturalEvents.length} festivaly a akce',
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: const Color(0xFF666666),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: const Color(0xFF654321),
                    size: 16,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                'Festivaly, koncerty a kulturní akce po celém Chorvatsku',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: const Color(0xFF666666),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAllCulturalSitesTab() {
    return Column(
      children: [
        // Search bar
        Container(
          padding: const EdgeInsets.all(16),
          child: TextField(
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
                _applyFilters();
              });
            },
            decoration: InputDecoration(
              hintText: 'Hledat kulturní místa...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
          ),
        ),

        // Results
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _filteredCulturalSites.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.account_balance,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Žádná kulturní místa nenalezena',
                        style: GoogleFonts.inter(
                          fontSize: 18,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _filteredCulturalSites.length,
                  itemBuilder: (context, index) {
                    return _buildCulturalSiteCard(
                      _filteredCulturalSites[index],
                    );
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildUnescoTab() {
    final unescoSites = _allCulturalSites.where((s) => s.isUnescoSite).toList();
    unescoSites.sort((a, b) => b.rating.compareTo(a.rating));

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: unescoSites.length,
      itemBuilder: (context, index) {
        return _buildCulturalSiteCard(
          unescoSites[index],
          showUnescoBadge: true,
        );
      },
    );
  }

  Widget _buildTopRatedTab() {
    final topRated = _allCulturalSites.where((s) => s.rating >= 4.5).toList();
    topRated.sort((a, b) => b.rating.compareTo(a.rating));

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: topRated.length,
      itemBuilder: (context, index) {
        return _buildCulturalSiteCard(topRated[index], showRank: index + 1);
      },
    );
  }

  Widget _buildFamilyFriendlyTab() {
    final familyFriendly = _allCulturalSites
        .where((s) => s.isFamilyFriendly)
        .toList();
    familyFriendly.sort((a, b) => b.rating.compareTo(a.rating));

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: familyFriendly.length,
      itemBuilder: (context, index) {
        return _buildCulturalSiteCard(
          familyFriendly[index],
          showFamilyBadge: true,
        );
      },
    );
  }

  Widget _buildMapTab() {
    return Container(
      child: CustomPaint(
        painter: WatercolorCulturalMapPainter(),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.map, size: 64, color: const Color(0xFFB8860B)),
              const SizedBox(height: 16),
              Text(
                'Mapa kulturních míst',
                style: GoogleFonts.playfairDisplay(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFFB8860B),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Připravujeme interaktivní mapu\ns watercolor designem',
                style: GoogleFonts.inter(
                  fontSize: 16,
                  color: const Color(0xFF666666),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCulturalSiteCard(
    CulturalSite site, {
    bool isCompact = false,
    int? showRank,
    bool showUnescoBadge = false,
    bool showFamilyBadge = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: CustomPaint(
        painter: WatercolorCulturalSiteCardPainter(
          _getCulturalSiteTypeColor(site.siteType),
        ),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  if (showRank != null) ...[
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: const Color(0xFFB8860B),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          '$showRank',
                          style: GoogleFonts.inter(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                  ],

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                site.name,
                                style: GoogleFonts.playfairDisplay(
                                  fontSize: isCompact ? 16 : 18,
                                  fontWeight: FontWeight.bold,
                                  color: const Color(0xFF2C2C2C),
                                ),
                              ),
                            ),
                            if (showUnescoBadge)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(
                                    0xFFB8860B,
                                  ).withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'UNESCO',
                                  style: GoogleFonts.inter(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFFB8860B),
                                  ),
                                ),
                              ),
                            if (showFamilyBadge)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(
                                    0xFF4CAF50,
                                  ).withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'Rodinné',
                                  style: GoogleFonts.inter(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFF4CAF50),
                                  ),
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(Icons.star, size: 16, color: Colors.amber),
                            const SizedBox(width: 4),
                            Text(
                              site.rating.toStringAsFixed(1),
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF2C2C2C),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '(${site.reviewCount} recenzí)',
                              style: GoogleFonts.inter(
                                fontSize: 12,
                                color: const Color(0xFF666666),
                              ),
                            ),
                            const Spacer(),
                            Text(
                              _getHistoricalPeriodLabel(site.historicalPeriod),
                              style: GoogleFonts.inter(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: _getHistoricalPeriodColor(
                                  site.historicalPeriod,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  Icon(
                    _getCulturalSiteTypeIcon(site.siteType),
                    color: _getCulturalSiteTypeColor(site.siteType),
                    size: 24,
                  ),
                ],
              ),

              if (!isCompact) ...[
                const SizedBox(height: 12),

                // Description
                Text(
                  site.description,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    color: const Color(0xFF666666),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 12),

                // Features
                Row(
                  children: [
                    if (site.hasGuidedTours)
                      _buildFeatureIcon(Icons.tour, 'Prohlídky'),
                    if (site.hasAudioGuide)
                      _buildFeatureIcon(Icons.headphones, 'Audio průvodce'),
                    if (site.isAccessible)
                      _buildFeatureIcon(Icons.accessible, 'Bezbariérové'),
                    if (site.hasParking)
                      _buildFeatureIcon(Icons.local_parking, 'Parkování'),
                  ],
                ),

                const SizedBox(height: 8),

                // Address and info
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 16,
                      color: const Color(0xFF666666),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        site.address,
                        style: GoogleFonts.inter(
                          fontSize: 12,
                          color: const Color(0xFF666666),
                        ),
                      ),
                    ),
                    if (site.ticketPrice != null)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFFB8860B).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          site.ticketPrice!,
                          style: GoogleFonts.inter(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: const Color(0xFFB8860B),
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureIcon(IconData icon, String tooltip) {
    return Padding(
      padding: const EdgeInsets.only(right: 12),
      child: Tooltip(
        message: tooltip,
        child: Icon(icon, size: 16, color: const Color(0xFF4CAF50)),
      ),
    );
  }

  Color _getCulturalSiteTypeColor(CulturalSiteType siteType) {
    switch (siteType) {
      case CulturalSiteType.castle:
        return const Color(0xFF8B4513);
      case CulturalSiteType.fortress:
        return const Color(0xFF654321);
      case CulturalSiteType.church:
        return const Color(0xFFB8860B);
      case CulturalSiteType.monastery:
        return const Color(0xFFCD853F);
      case CulturalSiteType.museum:
        return const Color(0xFF2196F3);
      case CulturalSiteType.gallery:
        return const Color(0xFF9C27B0);
      case CulturalSiteType.palace:
        return const Color(0xFFE91E63);
      case CulturalSiteType.ruins:
        return const Color(0xFF795548);
      case CulturalSiteType.monument:
        return const Color(0xFF607D8B);
      case CulturalSiteType.archaeologicalSite:
        return const Color(0xFF5D4037);
      case CulturalSiteType.historicTown:
        return const Color(0xFFFF9800);
      case CulturalSiteType.culturalCenter:
        return const Color(0xFF4CAF50);
    }
  }

  IconData _getCulturalSiteTypeIcon(CulturalSiteType siteType) {
    switch (siteType) {
      case CulturalSiteType.castle:
        return Icons.castle;
      case CulturalSiteType.fortress:
        return Icons.security;
      case CulturalSiteType.church:
        return Icons.church;
      case CulturalSiteType.monastery:
        return Icons.temple_buddhist;
      case CulturalSiteType.museum:
        return Icons.museum;
      case CulturalSiteType.gallery:
        return Icons.palette;
      case CulturalSiteType.palace:
        return Icons.account_balance;
      case CulturalSiteType.ruins:
        return Icons.broken_image;
      case CulturalSiteType.monument:
        return Icons.place;
      case CulturalSiteType.archaeologicalSite:
        return Icons.terrain;
      case CulturalSiteType.historicTown:
        return Icons.location_city;
      case CulturalSiteType.culturalCenter:
        return Icons.theater_comedy;
    }
  }

  String _getHistoricalPeriodLabel(HistoricalPeriod period) {
    switch (period) {
      case HistoricalPeriod.prehistoric:
        return 'Pravěk';
      case HistoricalPeriod.roman:
        return 'Římské';
      case HistoricalPeriod.byzantine:
        return 'Byzantské';
      case HistoricalPeriod.medieval:
        return 'Středověk';
      case HistoricalPeriod.renaissance:
        return 'Renesance';
      case HistoricalPeriod.baroque:
        return 'Baroko';
      case HistoricalPeriod.modern:
        return 'Moderní';
      case HistoricalPeriod.contemporary:
        return 'Současnost';
    }
  }

  Color _getHistoricalPeriodColor(HistoricalPeriod period) {
    switch (period) {
      case HistoricalPeriod.prehistoric:
        return const Color(0xFF795548);
      case HistoricalPeriod.roman:
        return const Color(0xFFE91E63);
      case HistoricalPeriod.byzantine:
        return const Color(0xFF9C27B0);
      case HistoricalPeriod.medieval:
        return const Color(0xFF8B4513);
      case HistoricalPeriod.renaissance:
        return const Color(0xFFB8860B);
      case HistoricalPeriod.baroque:
        return const Color(0xFFCD853F);
      case HistoricalPeriod.modern:
        return const Color(0xFF2196F3);
      case HistoricalPeriod.contemporary:
        return const Color(0xFF4CAF50);
    }
  }
}

// Watercolor painters pro Cultural Discovery Screen
class WatercolorCulturalHeaderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor efekt pro Cultural header - evokuje historické budovy
    final path1 = Path();
    path1.moveTo(0, size.height * 0.25);
    path1.quadraticBezierTo(
      size.width * 0.12,
      size.height * 0.08,
      size.width * 0.3,
      size.height * 0.35,
    );
    path1.quadraticBezierTo(
      size.width * 0.48,
      size.height * 0.62,
      size.width * 0.7,
      size.height * 0.15,
    );
    path1.quadraticBezierTo(
      size.width * 0.88,
      size.height * 0.02,
      size.width,
      size.height * 0.3,
    );
    path1.lineTo(size.width, size.height);
    path1.lineTo(0, size.height);
    path1.close();

    paint.color = const Color(0xFFB8860B).withValues(alpha: 0.2);
    canvas.drawPath(path1, paint);

    // Druhá vrstva - gotické oblouky
    final path2 = Path();
    path2.moveTo(0, size.height * 0.45);
    path2.quadraticBezierTo(
      size.width * 0.18,
      size.height * 0.22,
      size.width * 0.4,
      size.height * 0.55,
    );
    path2.quadraticBezierTo(
      size.width * 0.62,
      size.height * 0.8,
      size.width,
      size.height * 0.4,
    );
    path2.lineTo(size.width, size.height);
    path2.lineTo(0, size.height);
    path2.close();

    paint.color = const Color(0xFF8B4513).withValues(alpha: 0.15);
    canvas.drawPath(path2, paint);

    // Třetí vrstva - věže
    final path3 = Path();
    path3.moveTo(0, size.height * 0.7);
    path3.quadraticBezierTo(
      size.width * 0.22,
      size.height * 0.5,
      size.width * 0.55,
      size.height * 0.75,
    );
    path3.quadraticBezierTo(
      size.width * 0.78,
      size.height * 0.85,
      size.width,
      size.height * 0.6,
    );
    path3.lineTo(size.width, size.height);
    path3.lineTo(0, size.height);
    path3.close();

    paint.color = const Color(0xFF654321).withValues(alpha: 0.1);
    canvas.drawPath(path3, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorCulturalBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Jemný watercolor efekt pro pozadí cultural discovery
    final path = Path();
    path.moveTo(size.width * 0.03, size.height * 0.01);
    path.quadraticBezierTo(
      size.width * 0.32,
      size.height * 0.005,
      size.width * 0.72,
      size.height * 0.04,
    );
    path.quadraticBezierTo(
      size.width * 0.97,
      size.height * 0.07,
      size.width * 0.99,
      size.height * 0.99,
    );
    path.quadraticBezierTo(
      size.width * 0.68,
      size.height * 0.995,
      size.width * 0.28,
      size.height * 0.96,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.93,
      size.width * 0.03,
      size.height * 0.01,
    );
    path.close();

    paint.color = const Color(0xFFB8860B).withValues(alpha: 0.02);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorCulturalMainCardPainter extends CustomPainter {
  final Color color;

  WatercolorCulturalMainCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro hlavní cultural kartu - evokuje historické dokumenty
    final path = Path();
    path.moveTo(size.width * 0.01, size.height * 0.05);
    path.quadraticBezierTo(
      size.width * 0.2,
      size.height * 0.005,
      size.width * 0.5,
      size.height * 0.03,
    );
    path.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.055,
      size.width * 0.99,
      size.height * 0.15,
    );
    path.quadraticBezierTo(
      size.width * 0.995,
      size.height * 0.6,
      size.width * 0.95,
      size.height * 0.95,
    );
    path.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.995,
      size.width * 0.5,
      size.height * 0.97,
    );
    path.quadraticBezierTo(
      size.width * 0.2,
      size.height * 0.945,
      size.width * 0.005,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.005,
      size.height * 0.4,
      size.width * 0.01,
      size.height * 0.05,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);

    // Druhá vrstva pro hloubku
    final path2 = Path();
    path2.moveTo(size.width * 0.07, size.height * 0.1);
    path2.quadraticBezierTo(
      size.width * 0.35,
      size.height * 0.015,
      size.width * 0.7,
      size.height * 0.08,
    );
    path2.quadraticBezierTo(
      size.width * 0.93,
      size.height * 0.18,
      size.width * 0.9,
      size.height * 0.9,
    );
    path2.quadraticBezierTo(
      size.width * 0.65,
      size.height * 0.985,
      size.width * 0.3,
      size.height * 0.92,
    );
    path2.quadraticBezierTo(
      size.width * 0.07,
      size.height * 0.82,
      size.width * 0.07,
      size.height * 0.1,
    );
    path2.close();

    paint.color = color.withValues(alpha: 0.04);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorCulturalSearchPainter extends CustomPainter {
  final Color color;

  WatercolorCulturalSearchPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro search sekci - evokuje hledání v archivech
    final path = Path();
    path.moveTo(size.width * 0.005, size.height * 0.08);
    path.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.005,
      size.width * 0.6,
      size.height * 0.04,
    );
    path.quadraticBezierTo(
      size.width * 0.995,
      size.height * 0.09,
      size.width * 0.98,
      size.height * 0.92,
    );
    path.quadraticBezierTo(
      size.width * 0.75,
      size.height * 0.995,
      size.width * 0.4,
      size.height * 0.96,
    );
    path.quadraticBezierTo(
      size.width * 0.005,
      size.height * 0.91,
      size.width * 0.005,
      size.height * 0.08,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.06);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorCulturalStatCardPainter extends CustomPainter {
  final Color color;

  WatercolorCulturalStatCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro stat karty - evokuje historické štítky
    final path = Path();
    path.moveTo(size.width * 0.04, size.height * 0.08);
    path.quadraticBezierTo(
      size.width * 0.28,
      size.height * 0.015,
      size.width * 0.68,
      size.height * 0.06,
    );
    path.quadraticBezierTo(
      size.width * 0.96,
      size.height * 0.12,
      size.width * 0.93,
      size.height * 0.92,
    );
    path.quadraticBezierTo(
      size.width * 0.72,
      size.height * 0.985,
      size.width * 0.32,
      size.height * 0.94,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.88,
      size.width * 0.04,
      size.height * 0.08,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.1);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorCulturalIconPainter extends CustomPainter {
  final Color color;

  WatercolorCulturalIconPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor kruh kolem cultural ikony - evokuje pečetě
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2.4;

    final path = Path();
    for (int i = 0; i < 360; i += 10) {
      final angle = i * pi / 180;
      final variation = 0.8 + (sin(i * pi / 24) * 0.2);
      final x = center.dx + (radius * variation) * cos(angle);
      final y = center.dy + (radius * variation) * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    paint.color = color.withValues(alpha: 0.15);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorCulturalFilterChipPainter extends CustomPainter {
  final Color color;

  WatercolorCulturalFilterChipPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro filter chipy - evokuje historické štítky
    final path = Path();
    path.moveTo(size.width * 0.05, size.height * 0.12);
    path.quadraticBezierTo(
      size.width * 0.32,
      size.height * 0.02,
      size.width * 0.72,
      size.height * 0.08,
    );
    path.quadraticBezierTo(
      size.width * 0.98,
      size.height * 0.16,
      size.width * 0.95,
      size.height * 0.88,
    );
    path.quadraticBezierTo(
      size.width * 0.68,
      size.height * 0.98,
      size.width * 0.28,
      size.height * 0.92,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.84,
      size.width * 0.05,
      size.height * 0.12,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorCulturalUnescoOverviewPainter extends CustomPainter {
  final Color color;

  WatercolorCulturalUnescoOverviewPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro UNESCO sekci - evokuje světové dědictví
    final path = Path();
    path.moveTo(size.width * 0.01, size.height * 0.06);
    path.quadraticBezierTo(
      size.width * 0.22,
      size.height * 0.005,
      size.width * 0.55,
      size.height * 0.03,
    );
    path.quadraticBezierTo(
      size.width * 0.99,
      size.height * 0.08,
      size.width * 0.97,
      size.height * 0.94,
    );
    path.quadraticBezierTo(
      size.width * 0.78,
      size.height * 0.995,
      size.width * 0.45,
      size.height * 0.97,
    );
    path.quadraticBezierTo(
      size.width * 0.005,
      size.height * 0.92,
      size.width * 0.01,
      size.height * 0.06,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.06);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorCulturalEventsOverviewPainter extends CustomPainter {
  final Color color;

  WatercolorCulturalEventsOverviewPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro events sekci - evokuje festivaly
    final path = Path();
    path.moveTo(size.width * 0.01, size.height * 0.1);
    path.quadraticBezierTo(
      size.width * 0.27,
      size.height * 0.02,
      size.width * 0.65,
      size.height * 0.07,
    );
    path.quadraticBezierTo(
      size.width * 0.99,
      size.height * 0.14,
      size.width * 0.96,
      size.height * 0.9,
    );
    path.quadraticBezierTo(
      size.width * 0.73,
      size.height * 0.98,
      size.width * 0.35,
      size.height * 0.93,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.86,
      size.width * 0.01,
      size.height * 0.1,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.07);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorCulturalSiteCardPainter extends CustomPainter {
  final Color color;

  WatercolorCulturalSiteCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro cultural site karty - evokuje historické dokumenty
    final path = Path();
    path.moveTo(size.width * 0.005, size.height * 0.12);
    path.quadraticBezierTo(
      size.width * 0.26,
      size.height * 0.025,
      size.width * 0.64,
      size.height * 0.075,
    );
    path.quadraticBezierTo(
      size.width * 0.995,
      size.height * 0.135,
      size.width * 0.98,
      size.height * 0.88,
    );
    path.quadraticBezierTo(
      size.width * 0.74,
      size.height * 0.975,
      size.width * 0.36,
      size.height * 0.925,
    );
    path.quadraticBezierTo(
      size.width * 0.015,
      size.height * 0.865,
      size.width * 0.005,
      size.height * 0.12,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorCulturalMapPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor efekt pro mapu kulturních míst - evokuje historické mapy
    final path1 = Path();
    path1.moveTo(size.width * 0.05, size.height * 0.05);
    path1.quadraticBezierTo(
      size.width * 0.38,
      size.height * 0.015,
      size.width * 0.78,
      size.height * 0.08,
    );
    path1.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.18,
      size.width * 0.92,
      size.height * 0.95,
    );
    path1.quadraticBezierTo(
      size.width * 0.62,
      size.height * 0.985,
      size.width * 0.22,
      size.height * 0.92,
    );
    path1.quadraticBezierTo(
      size.width * 0.03,
      size.height * 0.82,
      size.width * 0.05,
      size.height * 0.05,
    );
    path1.close();

    paint.color = const Color(0xFFB8860B).withValues(alpha: 0.04);
    canvas.drawPath(path1, paint);

    // Druhá vrstva - historické regiony
    final path2 = Path();
    path2.moveTo(size.width * 0.12, size.height * 0.1);
    path2.quadraticBezierTo(
      size.width * 0.42,
      size.height * 0.06,
      size.width * 0.72,
      size.height * 0.13,
    );
    path2.quadraticBezierTo(
      size.width * 0.88,
      size.height * 0.25,
      size.width * 0.85,
      size.height * 0.9,
    );
    path2.quadraticBezierTo(
      size.width * 0.58,
      size.height * 0.94,
      size.width * 0.28,
      size.height * 0.87,
    );
    path2.quadraticBezierTo(
      size.width * 0.15,
      size.height * 0.75,
      size.width * 0.12,
      size.height * 0.1,
    );
    path2.close();

    paint.color = const Color(0xFF8B4513).withValues(alpha: 0.03);
    canvas.drawPath(path2, paint);

    // Třetí vrstva - kulturní centra
    final path3 = Path();
    path3.moveTo(size.width * 0.25, size.height * 0.2);
    path3.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.15,
      size.width * 0.75,
      size.height * 0.25,
    );
    path3.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.4,
      size.width * 0.7,
      size.height * 0.8,
    );
    path3.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.85,
      size.width * 0.3,
      size.height * 0.75,
    );
    path3.quadraticBezierTo(
      size.width * 0.2,
      size.height * 0.6,
      size.width * 0.25,
      size.height * 0.2,
    );
    path3.close();

    paint.color = const Color(0xFF654321).withValues(alpha: 0.02);
    canvas.drawPath(path3, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
