import 'package:geolocator/geolocator.dart';
import 'diary_entry.dart';

/// 📝 DAILY PROMPT MODELS - Modely pro denní v<PERSON> a připom<PERSON>ky

/// Denní v<PERSON>z<PERSON>
class DailyPrompt {
  final String id;
  final String text;
  final String category;
  final TimeOfDay? timeOfDay;
  final List<String> weatherConditions;
  final List<String> locationTypes;
  final List<DiaryMood> targetMoods;
  final List<String> categories;
  final int usageCount;
  final DateTime createdAt;
  final String? personalizedMessage;
  final List<String>? suggestedTags;

  const DailyPrompt({
    required this.id,
    required this.text,
    required this.category,
    this.timeOfDay,
    this.weatherConditions = const [],
    this.locationTypes = const [],
    this.targetMoods = const [],
    this.categories = const [],
    this.usageCount = 0,
    required this.createdAt,
    this.personalizedMessage,
    this.suggestedTags,
  });

  DailyPrompt copyWith({
    String? id,
    String? text,
    String? category,
    TimeOfDay? timeOfDay,
    List<String>? weatherConditions,
    List<String>? locationTypes,
    List<DiaryMood>? targetMoods,
    List<String>? categories,
    int? usageCount,
    DateTime? createdAt,
    String? personalizedMessage,
    List<String>? suggestedTags,
  }) {
    return DailyPrompt(
      id: id ?? this.id,
      text: text ?? this.text,
      category: category ?? this.category,
      timeOfDay: timeOfDay ?? this.timeOfDay,
      weatherConditions: weatherConditions ?? this.weatherConditions,
      locationTypes: locationTypes ?? this.locationTypes,
      targetMoods: targetMoods ?? this.targetMoods,
      categories: categories ?? this.categories,
      usageCount: usageCount ?? this.usageCount,
      createdAt: createdAt ?? this.createdAt,
      personalizedMessage: personalizedMessage ?? this.personalizedMessage,
      suggestedTags: suggestedTags ?? this.suggestedTags,
    );
  }

  String get displayText => personalizedMessage ?? text;

  factory DailyPrompt.fallback() {
    return DailyPrompt(
      id: 'fallback',
      text: 'Jak se dnes cítíš? Napiš si něco o svém dni.',
      category: 'obecné',
      createdAt: DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'category': category,
      'timeOfDay': timeOfDay?.name,
      'weatherConditions': weatherConditions,
      'locationTypes': locationTypes,
      'targetMoods': targetMoods.map((m) => m.name).toList(),
      'categories': categories,
      'usageCount': usageCount,
      'createdAt': createdAt.toIso8601String(),
      'personalizedMessage': personalizedMessage,
      'suggestedTags': suggestedTags,
    };
  }

  factory DailyPrompt.fromJson(Map<String, dynamic> json) {
    return DailyPrompt(
      id: json['id'] as String,
      text: json['text'] as String,
      category: json['category'] as String,
      timeOfDay: json['timeOfDay'] != null 
          ? TimeOfDay.values.firstWhere((t) => t.name == json['timeOfDay'])
          : null,
      weatherConditions: (json['weatherConditions'] as List<dynamic>).cast<String>(),
      locationTypes: (json['locationTypes'] as List<dynamic>).cast<String>(),
      targetMoods: (json['targetMoods'] as List<dynamic>)
          .map((m) => DiaryMood.values.firstWhere((mood) => mood.name == m))
          .toList(),
      categories: (json['categories'] as List<dynamic>).cast<String>(),
      usageCount: json['usageCount'] as int? ?? 0,
      createdAt: DateTime.parse(json['createdAt'] as String),
      personalizedMessage: json['personalizedMessage'] as String?,
      suggestedTags: (json['suggestedTags'] as List<dynamic>?)?.cast<String>(),
    );
  }
}

/// Čas dne
enum TimeOfDay {
  morning,   // 5-12
  afternoon, // 12-17
  evening,   // 17-22
  night,     // 22-5
}

extension TimeOfDayExtension on TimeOfDay {
  String get displayName {
    switch (this) {
      case TimeOfDay.morning:
        return 'Ráno';
      case TimeOfDay.afternoon:
        return 'Odpoledne';
      case TimeOfDay.evening:
        return 'Večer';
      case TimeOfDay.night:
        return 'Noc';
    }
  }

  String get emoji {
    switch (this) {
      case TimeOfDay.morning:
        return '🌅';
      case TimeOfDay.afternoon:
        return '☀️';
      case TimeOfDay.evening:
        return '🌅';
      case TimeOfDay.night:
        return '🌙';
    }
  }
}

/// Chytré připomenutí
class SmartReminder {
  final String id;
  final String title;
  final ReminderType type;
  final DateTime? scheduledTime;
  final String? locationName;
  final double? latitude;
  final double? longitude;
  final double radiusMeters;
  final List<String> weatherConditions;
  final List<int> daysOfWeek; // 1-7 (pondělí-neděle)
  final bool isEnabled;
  final DateTime createdAt;
  final DateTime? lastTriggered;
  final int triggerCount;

  const SmartReminder({
    required this.id,
    required this.title,
    required this.type,
    this.scheduledTime,
    this.locationName,
    this.latitude,
    this.longitude,
    this.radiusMeters = 100,
    this.weatherConditions = const [],
    this.daysOfWeek = const [],
    this.isEnabled = true,
    required this.createdAt,
    this.lastTriggered,
    this.triggerCount = 0,
  });

  SmartReminder copyWith({
    String? id,
    String? title,
    ReminderType? type,
    DateTime? scheduledTime,
    String? locationName,
    double? latitude,
    double? longitude,
    double? radiusMeters,
    List<String>? weatherConditions,
    List<int>? daysOfWeek,
    bool? isEnabled,
    DateTime? createdAt,
    DateTime? lastTriggered,
    int? triggerCount,
  }) {
    return SmartReminder(
      id: id ?? this.id,
      title: title ?? this.title,
      type: type ?? this.type,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      locationName: locationName ?? this.locationName,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      radiusMeters: radiusMeters ?? this.radiusMeters,
      weatherConditions: weatherConditions ?? this.weatherConditions,
      daysOfWeek: daysOfWeek ?? this.daysOfWeek,
      isEnabled: isEnabled ?? this.isEnabled,
      createdAt: createdAt ?? this.createdAt,
      lastTriggered: lastTriggered ?? this.lastTriggered,
      triggerCount: triggerCount ?? this.triggerCount,
    );
  }

  String get description {
    switch (type) {
      case ReminderType.time:
        if (scheduledTime != null) {
          final time = '${scheduledTime!.hour.toString().padLeft(2, '0')}:${scheduledTime!.minute.toString().padLeft(2, '0')}';
          if (daysOfWeek.isNotEmpty) {
            final days = daysOfWeek.map((d) => _getDayName(d)).join(', ');
            return 'Každý $days v $time';
          }
          return 'Denně v $time';
        }
        return 'Časové připomenutí';
      
      case ReminderType.location:
        return locationName != null 
            ? 'Když dorazíš do: $locationName'
            : 'Lokační připomenutí';
      
      case ReminderType.weather:
        return weatherConditions.isNotEmpty
            ? 'Při počasí: ${weatherConditions.join(', ')}'
            : 'Připomenutí podle počasí';
    }
  }

  String _getDayName(int day) {
    switch (day) {
      case 1: return 'pondělí';
      case 2: return 'úterý';
      case 3: return 'středa';
      case 4: return 'čtvrtek';
      case 5: return 'pátek';
      case 6: return 'sobota';
      case 7: return 'neděle';
      default: return 'neznámý';
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'type': type.name,
      'scheduledTime': scheduledTime?.toIso8601String(),
      'locationName': locationName,
      'latitude': latitude,
      'longitude': longitude,
      'radiusMeters': radiusMeters,
      'weatherConditions': weatherConditions,
      'daysOfWeek': daysOfWeek,
      'isEnabled': isEnabled,
      'createdAt': createdAt.toIso8601String(),
      'lastTriggered': lastTriggered?.toIso8601String(),
      'triggerCount': triggerCount,
    };
  }

  factory SmartReminder.fromJson(Map<String, dynamic> json) {
    return SmartReminder(
      id: json['id'] as String,
      title: json['title'] as String,
      type: ReminderType.values.firstWhere((t) => t.name == json['type']),
      scheduledTime: json['scheduledTime'] != null 
          ? DateTime.parse(json['scheduledTime'] as String)
          : null,
      locationName: json['locationName'] as String?,
      latitude: json['latitude'] as double?,
      longitude: json['longitude'] as double?,
      radiusMeters: (json['radiusMeters'] as num?)?.toDouble() ?? 100,
      weatherConditions: (json['weatherConditions'] as List<dynamic>).cast<String>(),
      daysOfWeek: (json['daysOfWeek'] as List<dynamic>).cast<int>(),
      isEnabled: json['isEnabled'] as bool? ?? true,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastTriggered: json['lastTriggered'] != null 
          ? DateTime.parse(json['lastTriggered'] as String)
          : null,
      triggerCount: json['triggerCount'] as int? ?? 0,
    );
  }
}

/// Typ připomenutí
enum ReminderType {
  time,     // Časové
  location, // Lokační
  weather,  // Podle počasí
}

extension ReminderTypeExtension on ReminderType {
  String get displayName {
    switch (this) {
      case ReminderType.time:
        return 'Časové';
      case ReminderType.location:
        return 'Lokační';
      case ReminderType.weather:
        return 'Podle počasí';
    }
  }

  String get icon {
    switch (this) {
      case ReminderType.time:
        return '⏰';
      case ReminderType.location:
        return '📍';
      case ReminderType.weather:
        return '🌤️';
    }
  }
}

/// Kontext uživatele
class UserContext {
  final DateTime currentTime;
  final Position? location;
  final String weather;
  final List<DiaryEntry> recentEntries;
  final WritingPatterns writingPatterns;
  final int dayOfWeek;
  final bool isWeekend;
  final TimeOfDay timeOfDay;

  const UserContext({
    required this.currentTime,
    this.location,
    required this.weather,
    required this.recentEntries,
    required this.writingPatterns,
    required this.dayOfWeek,
    required this.isWeekend,
    required this.timeOfDay,
  });
}

/// Vzorce psaní
class WritingPatterns {
  final double averageWritingHour;
  final int entriesLast30Days;
  final double averageWordCount;
  final DiaryMood? dominantMood;
  final int totalEntries;

  const WritingPatterns({
    required this.averageWritingHour,
    required this.entriesLast30Days,
    required this.averageWordCount,
    this.dominantMood,
    required this.totalEntries,
  });

  factory WritingPatterns.empty() {
    return const WritingPatterns(
      averageWritingHour: 12.0,
      entriesLast30Days: 0,
      averageWordCount: 0.0,
      dominantMood: null,
      totalEntries: 0,
    );
  }

  bool get isRegularWriter => entriesLast30Days >= 15;
  bool get isActiveWriter => entriesLast30Days >= 5;
  
  String get writingFrequency {
    if (entriesLast30Days >= 25) return 'Velmi aktivní';
    if (entriesLast30Days >= 15) return 'Pravidelný';
    if (entriesLast30Days >= 5) return 'Občasný';
    return 'Začátečník';
  }

  TimeOfDay get preferredWritingTime {
    if (averageWritingHour >= 5 && averageWritingHour < 12) return TimeOfDay.morning;
    if (averageWritingHour >= 12 && averageWritingHour < 17) return TimeOfDay.afternoon;
    if (averageWritingHour >= 17 && averageWritingHour < 22) return TimeOfDay.evening;
    return TimeOfDay.night;
  }
}
