import 'package:flutter/material.dart';
import '../models/place.dart';

class PlaceInfoWindow extends StatelessWidget {
  final Place place;

  const PlaceInfoWindow({super.key, required this.place});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Hlavička
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          place.name,
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      if (place.isVisited)
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: const BoxDecoration(
                            color: Colors.green,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Hodnocení a typ
                  Row(
                    children: [
                      if (place.rating != null) ...[
                        Icon(Icons.star, color: Colors.amber[600], size: 20),
                        const SizedBox(width: 4),
                        Text(
                          place.rating!.toStringAsFixed(1),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(width: 16),
                      ],
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _getTypeColor(
                            place.type,
                          ).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _getTypeLabel(place.type),
                          style: TextStyle(
                            fontSize: 12,
                            color: _getTypeColor(place.type),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade100,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _getRegionLabel(place.region),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.blue.shade700,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Popis
                  Text(
                    place.description,
                    style: const TextStyle(fontSize: 16, height: 1.5),
                  ),

                  const SizedBox(height: 16),

                  // Tagy
                  if (place.tags.isNotEmpty) ...[
                    const Text(
                      'Tagy',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: place.tags
                          .map(
                            (tag) => Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade200,
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Text(
                                tag,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[700],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          )
                          .toList(),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Dodatečné informace
                  if (place.additionalInfo != null &&
                      place.additionalInfo!.isNotEmpty) ...[
                    const Text(
                      'Informace',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...place.additionalInfo!.entries.map(
                      (entry) => Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              width: 100,
                              child: Text(
                                _formatInfoKey(entry.key),
                                style: const TextStyle(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            Expanded(child: Text(entry.value.toString())),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Uživatelské poznámky
                  if (place.userNotes != null &&
                      place.userNotes!.isNotEmpty) ...[
                    const Text(
                      'Vaše poznámky',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Text(
                        place.userNotes!,
                        style: const TextStyle(
                          fontSize: 14,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Datum návštěvy
                  if (place.isVisited && place.visitedDate != null) ...[
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.green.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.green.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.check_circle,
                            color: Colors.green.shade600,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Navštíveno ${_formatDate(place.visitedDate!)}',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.green.shade700,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                ],
              ),
            ),
          ),

          // Tlačítka
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(top: BorderSide(color: Colors.grey.shade200)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      // Navigace k místu
                    },
                    icon: const Icon(Icons.directions),
                    label: const Text('Navigovat'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      // Označit jako navštívené / přidat poznámku
                    },
                    icon: Icon(place.isVisited ? Icons.edit : Icons.bookmark),
                    label: Text(place.isVisited ? 'Upravit' : 'Označit'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getTypeColor(PlaceType type) {
    switch (type) {
      case PlaceType.monument:
        return Colors.brown;
      case PlaceType.beach:
        return Colors.blue;
      case PlaceType.restaurant:
        return Colors.orange;
      case PlaceType.hotel:
        return Colors.purple;
      case PlaceType.museum:
        return Colors.indigo;
      case PlaceType.park:
        return Colors.green;
      case PlaceType.church:
        return Colors.grey;
      case PlaceType.castle:
        return Colors.red;
      case PlaceType.viewpoint:
        return Colors.teal;
      case PlaceType.other:
        return Colors.blueGrey;
    }
  }

  String _getTypeLabel(PlaceType type) {
    switch (type) {
      case PlaceType.monument:
        return 'Památka';
      case PlaceType.beach:
        return 'Pláž';
      case PlaceType.restaurant:
        return 'Restaurace';
      case PlaceType.hotel:
        return 'Hotel';
      case PlaceType.museum:
        return 'Muzeum';
      case PlaceType.park:
        return 'Park';
      case PlaceType.church:
        return 'Kostel';
      case PlaceType.castle:
        return 'Hrad';
      case PlaceType.viewpoint:
        return 'Vyhlídka';
      case PlaceType.other:
        return 'Ostatní';
    }
  }

  String _getRegionLabel(String region) {
    switch (region) {
      case 'istria':
        return 'Istrie';
      case 'dalmatia':
        return 'Dalmácie';
      case 'slavonia':
        return 'Slavonie';
      case 'lika':
        return 'Lika';
      case 'zagreb':
        return 'Zagreb';
      default:
        return region;
    }
  }

  String _formatInfoKey(String key) {
    switch (key) {
      case 'opening_hours':
        return 'Otevírací doba:';
      case 'entrance_fee':
        return 'Vstupné:';
      case 'best_time':
        return 'Nejlepší čas:';
      case 'duration':
        return 'Doba návštěvy:';
      case 'activities':
        return 'Aktivity:';
      default:
        return '$key:';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}.${date.month}.${date.year}';
  }
}
