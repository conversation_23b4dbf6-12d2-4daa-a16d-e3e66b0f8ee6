import 'package:geolocator/geolocator.dart';
import '../models/place.dart';
import '../models/route_plan.dart';
import 'location_service.dart';

class RoutePlanningService {
  static final RoutePlanningService _instance =
      RoutePlanningService._internal();
  factory RoutePlanningService() => _instance;
  RoutePlanningService._internal();

  final LocationService _locationService = LocationService();

  /// Vytvoření optimalizované trasy mezi místy
  Future<RoutePlan> createOptimizedRoute({
    required List<Place> places,
    required String routeName,
    Position? startPosition,
    RouteType routeType = RouteType.driving,
    bool returnToStart = false,
  }) async {
    if (places.isEmpty) {
      throw Exception('Seznam míst nemůže být prázdný');
    }

    try {
      // Získání výchozí pozice
      Position currentPosition;
      if (startPosition != null) {
        currentPosition = startPosition;
      } else {
        currentPosition = await _locationService.getCurrentPosition();
      }

      // Optimalizace pořadí míst pomocí algoritmu nejbližšího souseda
      final optimizedOrder = _optimizeRouteOrder(
        places,
        currentPosition,
        returnToStart,
      );

      // Vytvoření bodů trasy
      final routePoints = <RoutePoint>[];

      for (int i = 0; i < optimizedOrder.length; i++) {
        final place = optimizedOrder[i];
        routePoints.add(
          RoutePoint(
            id: 'point_${i + 1}',
            placeId: place.id,
            name: place.name,
            latitude: place.latitude,
            longitude: place.longitude,
            order: i + 1,
            stayDuration: Duration(minutes: _estimateStayDuration(place.type)),
            type: _getRoutePointType(place.type),
          ),
        );
      }

      // Výpočet celkové vzdálenosti a času
      final totalDistance = _calculateTotalDistance(
        routePoints,
        currentPosition,
      );
      final totalDuration = _calculateTotalDuration(routePoints, routeType);

      return RoutePlan(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: routeName,
        description: 'Optimalizovaná trasa přes ${places.length} míst',
        points: routePoints,
        startDate: DateTime.now(),
        endDate: DateTime.now().add(Duration(minutes: totalDuration)),
        type: routeType,
        totalDistance: totalDistance,
        estimatedDuration: Duration(minutes: totalDuration),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    } catch (e) {
      throw Exception('Chyba při vytváření trasy: $e');
    }
  }

  /// Optimalizace pořadí míst pomocí algoritmu nejbližšího souseda
  List<Place> _optimizeRouteOrder(
    List<Place> places,
    Position startPosition,
    bool returnToStart,
  ) {
    if (places.length <= 2) {
      return places;
    }

    final optimized = <Place>[];
    final remaining = List<Place>.from(places);

    double currentLat = startPosition.latitude;
    double currentLng = startPosition.longitude;

    while (remaining.isNotEmpty) {
      // Najít nejbližší místo
      Place? nearest;
      double minDistance = double.infinity;

      for (final place in remaining) {
        final distance = _locationService.calculateDistance(
          currentLat,
          currentLng,
          place.latitude,
          place.longitude,
        );

        if (distance < minDistance) {
          minDistance = distance;
          nearest = place;
        }
      }

      if (nearest != null) {
        optimized.add(nearest);
        remaining.remove(nearest);
        currentLat = nearest.latitude;
        currentLng = nearest.longitude;
      }
    }

    return optimized;
  }

  /// Výpočet celkové vzdálenosti trasy
  double _calculateTotalDistance(
    List<RoutePoint> points,
    Position startPosition,
  ) {
    if (points.isEmpty) return 0.0;

    double totalDistance = 0.0;
    double currentLat = startPosition.latitude;
    double currentLng = startPosition.longitude;

    for (final point in points) {
      totalDistance += _locationService.calculateDistance(
        currentLat,
        currentLng,
        point.latitude,
        point.longitude,
      );
      currentLat = point.latitude;
      currentLng = point.longitude;
    }

    return totalDistance / 1000; // Převod na kilometry
  }

  /// Výpočet celkového času trasy
  int _calculateTotalDuration(List<RoutePoint> points, RouteType routeType) {
    if (points.isEmpty) return 0;

    // Základní rychlosti podle typu trasy (km/h)
    final speeds = {
      RouteType.walking: 5.0,
      RouteType.cycling: 15.0,
      RouteType.driving: 50.0,
      RouteType.publicTransport: 30.0,
      RouteType.mixed: 35.0,
    };

    final speed = speeds[routeType] ?? 35.0;

    // Čas na cestu
    final travelTime =
        (_calculateTotalDistance(
                  points,
                  Position(
                    latitude: points.first.latitude,
                    longitude: points.first.longitude,
                    timestamp: DateTime.now(),
                    accuracy: 0,
                    altitude: 0,
                    altitudeAccuracy: 0,
                    heading: 0,
                    headingAccuracy: 0,
                    speed: 0,
                    speedAccuracy: 0,
                  ),
                ) /
                speed *
                60)
            .round(); // v minutách

    // Čas strávený na místech
    final stayTime = points.fold<int>(
      0,
      (sum, point) => sum + point.stayDuration.inMinutes,
    );

    return travelTime + stayTime;
  }

  /// Odhad času stráveného na místě podle typu
  int _estimateStayDuration(PlaceType type) {
    switch (type) {
      case PlaceType.monument:
        return 90; // 1.5 hodiny
      case PlaceType.museum:
        return 120; // 2 hodiny
      case PlaceType.beach:
        return 180; // 3 hodiny
      case PlaceType.restaurant:
        return 90; // 1.5 hodiny
      case PlaceType.park:
        return 120; // 2 hodiny
      case PlaceType.viewpoint:
        return 30; // 30 minut
      case PlaceType.church:
        return 45; // 45 minut
      case PlaceType.castle:
        return 120; // 2 hodiny
      case PlaceType.hotel:
        return 15; // 15 minut (check-in)
      case PlaceType.other:
        return 60; // 1 hodina
    }
  }

  /// Převod PlaceType na RoutePointType
  RoutePointType _getRoutePointType(PlaceType placeType) {
    switch (placeType) {
      case PlaceType.monument:
        return RoutePointType.attraction;
      case PlaceType.museum:
        return RoutePointType.museum;
      case PlaceType.beach:
        return RoutePointType.beach;
      case PlaceType.restaurant:
        return RoutePointType.restaurant;
      case PlaceType.park:
        return RoutePointType.park;
      case PlaceType.viewpoint:
        return RoutePointType.viewpoint;
      case PlaceType.church:
        return RoutePointType.attraction;
      case PlaceType.castle:
        return RoutePointType.attraction;
      case PlaceType.hotel:
        return RoutePointType.accommodation;
      case PlaceType.other:
        return RoutePointType.other;
    }
  }

  /// Získání navigačních instrukcí
  Future<List<NavigationInstruction>> getNavigationInstructions(
    RoutePlan route,
  ) async {
    final instructions = <NavigationInstruction>[];

    for (int i = 0; i < route.points.length; i++) {
      final point = route.points[i];

      if (i == 0) {
        instructions.add(
          NavigationInstruction(
            step: i + 1,
            instruction: 'Začněte na ${point.name}',
            distance: 0,
            duration: 0,
            latitude: point.latitude,
            longitude: point.longitude,
          ),
        );
      } else {
        final previousPoint = route.points[i - 1];
        final distance = _locationService.calculateDistance(
          previousPoint.latitude,
          previousPoint.longitude,
          point.latitude,
          point.longitude,
        );

        final duration = _estimateTravelTime(distance, route.type);

        instructions.add(
          NavigationInstruction(
            step: i + 1,
            instruction: 'Pokračujte k ${point.name}',
            distance: distance,
            duration: duration,
            latitude: point.latitude,
            longitude: point.longitude,
          ),
        );
      }
    }

    return instructions;
  }

  /// Odhad času cesty mezi dvěma body
  int _estimateTravelTime(double distanceInMeters, RouteType routeType) {
    final speeds = {
      RouteType.walking: 5.0,
      RouteType.cycling: 15.0,
      RouteType.driving: 50.0,
      RouteType.publicTransport: 30.0,
      RouteType.mixed: 35.0,
    };

    final speed = speeds[routeType] ?? 35.0;
    final distanceInKm = distanceInMeters / 1000;

    return (distanceInKm / speed * 60).round(); // v minutách
  }

  /// Kontrola dostupnosti offline map pro trasu
  Future<bool> checkOfflineMapAvailability(RoutePlan route) async {
    // Simulace kontroly - v reálné aplikaci by se kontrolovala databáze
    final regions = route.points
        .map((p) => _getRegionFromCoordinates(p.latitude, p.longitude))
        .toSet();

    // Předpokládáme, že máme offline mapy pro hlavní regiony
    final availableRegions = {'dalmatia', 'istria', 'zagreb'};

    return regions.every((region) => availableRegions.contains(region));
  }

  /// Určení regionu podle souřadnic
  String _getRegionFromCoordinates(double latitude, double longitude) {
    // Zjednodušené určení regionu podle souřadnic
    if (latitude > 45.5) {
      return 'zagreb';
    } else if (longitude < 15.0) {
      return 'istria';
    } else {
      return 'dalmatia';
    }
  }

  /// Aktualizace pokroku trasy
  Future<RoutePlan> updateRouteProgress(
    RoutePlan route,
    Position currentPosition,
  ) async {
    final updatedPoints = <RoutePoint>[];

    for (final point in route.points) {
      final distance = _locationService.calculateDistance(
        currentPosition.latitude,
        currentPosition.longitude,
        point.latitude,
        point.longitude,
      );

      // Pokud je uživatel blízko bodu (do 100m), označit jako navštívený
      final isVisited = point.isVisited || distance <= 100;

      updatedPoints.add(point.copyWith(isVisited: isVisited));
    }

    return route.copyWith(points: updatedPoints);
  }
}

class NavigationInstruction {
  final int step;
  final String instruction;
  final double distance; // v metrech
  final int duration; // v minutách
  final double latitude;
  final double longitude;

  NavigationInstruction({
    required this.step,
    required this.instruction,
    required this.distance,
    required this.duration,
    required this.latitude,
    required this.longitude,
  });
}
