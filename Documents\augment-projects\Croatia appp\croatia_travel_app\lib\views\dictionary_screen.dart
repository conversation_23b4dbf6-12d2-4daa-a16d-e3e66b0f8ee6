import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class DictionaryScreen extends StatefulWidget {
  const DictionaryScreen({super.key});

  @override
  State<DictionaryScreen> createState() => _DictionaryScreenState();
}

class _DictionaryScreenState extends State<DictionaryScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  List<DictionaryEntry> _allPhrases = [];
  List<DictionaryEntry> _filteredPhrases = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadPhrases();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadPhrases() {
    _allPhrases = [
      // Základní fráze
      DictionaryEntry(
        czech: 'Dobrý den',
        croatian: 'Dobar dan',
        pronunciation: 'dobar dan',
        category: 'basic',
      ),
      DictionaryEntry(
        czech: '<PERSON><PERSON><PERSON><PERSON>',
        croatian: 'Hvala',
        pronunciation: 'hvala',
        category: 'basic',
      ),
      DictionaryEntry(
        czech: 'Prosím',
        croatian: 'Molim',
        pronunciation: 'molim',
        category: 'basic',
      ),
      DictionaryEntry(
        czech: 'Promiňte',
        croatian: 'Oprostite',
        pronunciation: 'oprostite',
        category: 'basic',
      ),
      DictionaryEntry(
        czech: 'Ano',
        croatian: 'Da',
        pronunciation: 'da',
        category: 'basic',
      ),
      DictionaryEntry(
        czech: 'Ne',
        croatian: 'Ne',
        pronunciation: 'ne',
        category: 'basic',
      ),

      // Cestování
      DictionaryEntry(
        czech: 'Kde je nádraží?',
        croatian: 'Gdje je kolodvor?',
        pronunciation: 'gdje je kolodvor',
        category: 'travel',
      ),
      DictionaryEntry(
        czech: 'Kolik to stojí?',
        croatian: 'Koliko to košta?',
        pronunciation: 'koliko to košta',
        category: 'travel',
      ),
      DictionaryEntry(
        czech: 'Jízdenka',
        croatian: 'Karta',
        pronunciation: 'karta',
        category: 'travel',
      ),
      DictionaryEntry(
        czech: 'Hotel',
        croatian: 'Hotel',
        pronunciation: 'hotel',
        category: 'travel',
      ),

      // Jídlo
      DictionaryEntry(
        czech: 'Restaurace',
        croatian: 'Restoran',
        pronunciation: 'restoran',
        category: 'food',
      ),
      DictionaryEntry(
        czech: 'Jídelní lístek',
        croatian: 'Jelovnik',
        pronunciation: 'jelovnik',
        category: 'food',
      ),
      DictionaryEntry(
        czech: 'Pivo',
        croatian: 'Pivo',
        pronunciation: 'pivo',
        category: 'food',
      ),
      DictionaryEntry(
        czech: 'Víno',
        croatian: 'Vino',
        pronunciation: 'vino',
        category: 'food',
      ),
      DictionaryEntry(
        czech: 'Ryba',
        croatian: 'Riba',
        pronunciation: 'riba',
        category: 'food',
      ),

      // Nouzové situace
      DictionaryEntry(
        czech: 'Pomoc!',
        croatian: 'Upomoć!',
        pronunciation: 'upomoć',
        category: 'emergency',
      ),
      DictionaryEntry(
        czech: 'Zavolejte doktora',
        croatian: 'Pozovite doktora',
        pronunciation: 'pozovite doktora',
        category: 'emergency',
      ),
      DictionaryEntry(
        czech: 'Nemocnice',
        croatian: 'Bolnica',
        pronunciation: 'bolnica',
        category: 'emergency',
      ),
    ];

    _filteredPhrases = List.from(_allPhrases);
    setState(() {});
  }

  void _filterPhrases() {
    setState(() {
      if (_searchQuery.isEmpty) {
        _filteredPhrases = List.from(_allPhrases);
      } else {
        _filteredPhrases = _allPhrases.where((phrase) {
          return phrase.czech.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              ) ||
              phrase.croatian.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              ) ||
              phrase.pronunciation.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              );
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Chorvatský slovník',
          style: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF006994).withValues(alpha: 0.9),
                const Color(0xFF2E8B8B).withValues(alpha: 0.8),
                const Color(0xFFFF6B35).withValues(alpha: 0.7),
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: CustomPaint(
            painter: WatercolorDictionaryHeaderPainter(),
            size: Size.infinite,
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: _showLanguageSelector,
              icon: const Icon(Icons.language),
              tooltip: 'Změnit jazyk',
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          indicator: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Colors.white.withValues(alpha: 0.3),
          ),
          tabs: const [
            Tab(icon: Icon(Icons.star), text: 'Základní'),
            Tab(icon: Icon(Icons.flight), text: 'Cestování'),
            Tab(icon: Icon(Icons.restaurant), text: 'Jídlo'),
            Tab(icon: Icon(Icons.emergency), text: 'Nouzové'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Vyhledávací pole s watercolor efektem
          Padding(
            padding: const EdgeInsets.all(16),
            child: Container(
              child: CustomPaint(
                painter: WatercolorDictionarySearchPainter(),
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'Hledat fráze...',
                    hintStyle: GoogleFonts.inter(
                      color: const Color(0xFF666666),
                    ),
                    prefixIcon: Icon(
                      Icons.search,
                      color: const Color(0xFF006994),
                    ),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            onPressed: () {
                              setState(() {
                                _searchQuery = '';
                              });
                              _filterPhrases();
                            },
                            icon: Icon(
                              Icons.clear,
                              color: const Color(0xFF666666),
                            ),
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16),
                      borderSide: BorderSide(
                        color: const Color(0xFF2E8B8B).withValues(alpha: 0.3),
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16),
                      borderSide: BorderSide(
                        color: const Color(0xFF2E8B8B).withValues(alpha: 0.3),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16),
                      borderSide: BorderSide(
                        color: const Color(0xFF006994),
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: Colors.white.withValues(alpha: 0.9),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                    _filterPhrases();
                  },
                ),
              ),
            ),
          ),

          // Obsah podle tabů
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildPhrasesList('basic'),
                _buildPhrasesList('travel'),
                _buildPhrasesList('food'),
                _buildPhrasesList('emergency'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhrasesList(String category) {
    final categoryPhrases = _filteredPhrases
        .where((phrase) => phrase.category == category)
        .toList();

    if (categoryPhrases.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Žádné fráze nenalezeny',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: categoryPhrases.length,
      itemBuilder: (context, index) {
        final phrase = categoryPhrases[index];
        return _buildPhraseCard(phrase);
      },
    );
  }

  Widget _buildPhraseCard(DictionaryEntry phrase) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: CustomPaint(
        painter: WatercolorDictionaryCardPainter(
          _getCategoryColor(phrase.category),
        ),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Česky
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 6,
                    ),
                    child: CustomPaint(
                      painter: WatercolorLanguageTagPainter(
                        const Color(0xFF006994),
                      ),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        child: Text(
                          'CZ',
                          style: GoogleFonts.inter(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF006994),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      phrase.czech,
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xFF2C2C2C),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Chorvatsky
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 6,
                    ),
                    child: CustomPaint(
                      painter: WatercolorLanguageTagPainter(
                        const Color(0xFFFF6B35),
                      ),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        child: Text(
                          'HR',
                          style: GoogleFonts.inter(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFFFF6B35),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      phrase.croatian,
                      style: GoogleFonts.playfairDisplay(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFFFF6B35),
                      ),
                    ),
                  ),
                  Container(
                    child: CustomPaint(
                      painter: WatercolorAudioButtonPainter(
                        const Color(0xFF2E8B8B),
                      ),
                      child: IconButton(
                        onPressed: () => _playPronunciation(phrase),
                        icon: const Icon(
                          Icons.volume_up,
                          color: Color(0xFF2E8B8B),
                        ),
                        tooltip: 'Přehrát výslovnost',
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Výslovnost
              Row(
                children: [
                  Icon(
                    Icons.record_voice_over,
                    size: 16,
                    color: const Color(0xFF2E8B8B),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '[${phrase.pronunciation}]',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                      color: const Color(0xFF666666),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'basic':
        return const Color(0xFF006994);
      case 'travel':
        return const Color(0xFF2E8B8B);
      case 'food':
        return const Color(0xFFFF6B35);
      case 'emergency':
        return const Color(0xFFE53E3E);
      default:
        return const Color(0xFF666666);
    }
  }

  void _playPronunciation(DictionaryEntry phrase) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Přehrávání: ${phrase.croatian}'),
        duration: const Duration(seconds: 1),
        backgroundColor: const Color(0xFF2E8B8B),
      ),
    );
    // Zde by byla implementace text-to-speech
  }

  void _showLanguageSelector() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Vyberte jazyk',
          style: GoogleFonts.playfairDisplay(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildLanguageOption('🇨🇿 Čeština', 'CZ'),
            _buildLanguageOption('🇭🇷 Chorvatština', 'HR'),
            _buildLanguageOption('🇩🇪 Němčina', 'DE'),
            _buildLanguageOption('🇬🇧 Angličtina', 'EN'),
            _buildLanguageOption('🇸🇰 Slovenština', 'SK'),
            _buildLanguageOption('🇮🇹 Italština', 'IT'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Zavřít'),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageOption(String label, String code) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: CustomPaint(
        painter: WatercolorLanguageOptionPainter(const Color(0xFF006994)),
        child: ListTile(
          title: Text(
            label,
            style: GoogleFonts.inter(fontWeight: FontWeight.w500),
          ),
          onTap: () {
            Navigator.pop(context);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Jazyk $code bude dostupný v příští verzi'),
                backgroundColor: const Color(0xFF2E8B8B),
              ),
            );
          },
        ),
      ),
    );
  }
}

// Model pro slovníkové záznamy
class DictionaryEntry {
  final String czech;
  final String croatian;
  final String pronunciation;
  final String category;

  DictionaryEntry({
    required this.czech,
    required this.croatian,
    required this.pronunciation,
    required this.category,
  });
}

// Watercolor painters pro Dictionary Screen
class WatercolorDictionaryHeaderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor vlny pro Dictionary header
    final path1 = Path();
    path1.moveTo(0, size.height * 0.3);
    path1.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.1,
      size.width * 0.5,
      size.height * 0.4,
    );
    path1.quadraticBezierTo(
      size.width * 0.75,
      size.height * 0.7,
      size.width,
      size.height * 0.2,
    );
    path1.lineTo(size.width, size.height);
    path1.lineTo(0, size.height);
    path1.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.2);
    canvas.drawPath(path1, paint);

    // Druhá vrstva
    final path2 = Path();
    path2.moveTo(0, size.height * 0.5);
    path2.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.2,
      size.width * 0.7,
      size.height * 0.6,
    );
    path2.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.8,
      size.width,
      size.height * 0.4,
    );
    path2.lineTo(size.width, size.height);
    path2.lineTo(0, size.height);
    path2.close();

    paint.color = const Color(0xFFFF6B35).withValues(alpha: 0.15);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorDictionarySearchPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Jemný watercolor efekt kolem vyhledávacího pole
    final path = Path();
    path.moveTo(size.width * 0.05, size.height * 0.2);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.05,
      size.width * 0.7,
      size.height * 0.15,
    );
    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.25,
      size.width * 0.9,
      size.height * 0.8,
    );
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.95,
      size.width * 0.2,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.5,
      size.width * 0.05,
      size.height * 0.2,
    );
    path.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.1);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorDictionaryCardPainter extends CustomPainter {
  final Color color;

  WatercolorDictionaryCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro dictionary karty
    final path = Path();
    path.moveTo(size.width * 0.05, size.height * 0.1);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.02,
      size.width * 0.7,
      size.height * 0.08,
    );
    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.15,
      size.width * 0.98,
      size.height * 0.9,
    );
    path.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.98,
      size.width * 0.3,
      size.height * 0.92,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.85,
      size.width * 0.05,
      size.height * 0.1,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.1);
    canvas.drawPath(path, paint);

    // Druhá vrstva pro hloubku
    final path2 = Path();
    path2.moveTo(size.width * 0.1, size.height * 0.2);
    path2.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.05,
      size.width * 0.9,
      size.height * 0.15,
    );
    path2.lineTo(size.width * 0.85, size.height * 0.8);
    path2.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.95,
      size.width * 0.15,
      size.height * 0.85,
    );
    path2.close();

    paint.color = color.withValues(alpha: 0.05);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorLanguageTagPainter extends CustomPainter {
  final Color color;

  WatercolorLanguageTagPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Malý watercolor efekt kolem jazykového tagu
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width, size.height) / 3;

    final path = Path();
    for (int i = 0; i < 360; i += 30) {
      final angle = i * pi / 180;
      final variation = 0.7 + (sin(i * pi / 60) * 0.3);
      final x = center.dx + (radius * variation) * cos(angle);
      final y = center.dy + (radius * variation) * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    paint.color = color.withValues(alpha: 0.15);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorAudioButtonPainter extends CustomPainter {
  final Color color;

  WatercolorAudioButtonPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor kruh kolem audio tlačítka
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2.5;

    final path = Path();
    for (int i = 0; i < 360; i += 20) {
      final angle = i * pi / 180;
      final variation = 0.8 + (cos(i * pi / 90) * 0.2);
      final x = center.dx + (radius * variation) * cos(angle);
      final y = center.dy + (radius * variation) * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    paint.color = color.withValues(alpha: 0.2);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorLanguageOptionPainter extends CustomPainter {
  final Color color;

  WatercolorLanguageOptionPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Jemný watercolor efekt pro jazykové možnosti
    final path = Path();
    path.moveTo(size.width * 0.02, size.height * 0.2);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.05,
      size.width * 0.7,
      size.height * 0.15,
    );
    path.quadraticBezierTo(
      size.width * 0.98,
      size.height * 0.25,
      size.width * 0.95,
      size.height * 0.8,
    );
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.95,
      size.width * 0.2,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.5,
      size.width * 0.02,
      size.height * 0.2,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
