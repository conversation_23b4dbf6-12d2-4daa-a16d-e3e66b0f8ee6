import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class CurrencyConverterScreen extends StatefulWidget {
  const CurrencyConverterScreen({super.key});

  @override
  State<CurrencyConverterScreen> createState() =>
      _CurrencyConverterScreenState();
}

class _CurrencyConverterScreenState extends State<CurrencyConverterScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Převodník stav
  String _fromCurrency = 'EUR';
  String _toCurrency = 'HRK';
  double _amount = 100.0;
  double _convertedAmount = 0.0;

  // Kurzy (simulované - v reálné aplikaci by byly z API)
  final Map<String, Map<String, double>> _exchangeRates = {
    'EUR': {'HRK': 7.53, 'CZK': 25.12, 'USD': 1.09, 'GBP': 0.86, 'CHF': 0.97},
    'HRK': {
      'EUR': 0.133,
      'CZK': 3.34,
      'USD': 0.145,
      'GBP': 0.114,
      'CHF': 0.129,
    },
    'CZK': {
      'EUR': 0.040,
      'HRK': 0.30,
      'USD': 0.043,
      'GBP': 0.034,
      'CHF': 0.039,
    },
    'USD': {'EUR': 0.92, 'HRK': 6.91, 'CZK': 23.15, 'GBP': 0.79, 'CHF': 0.89},
    'GBP': {'EUR': 1.16, 'HRK': 8.77, 'CZK': 29.41, 'USD': 1.27, 'CHF': 1.13},
    'CHF': {'EUR': 1.03, 'HRK': 7.76, 'CZK': 25.89, 'USD': 1.12, 'GBP': 0.88},
  };

  // Historie převodů
  List<ConversionHistory> _history = [];

  // Oblíbené kurzy
  List<FavoriteCurrency> _favorites = [];

  final TextEditingController _amountController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _amountController.text = _amount.toString();
    _loadFavorites();
    _calculateConversion();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  void _loadFavorites() {
    setState(() {
      _favorites = [
        FavoriteCurrency('EUR', 'HRK', 'Euro → Kuna'),
        FavoriteCurrency('CZK', 'EUR', 'Koruna → Euro'),
        FavoriteCurrency('USD', 'HRK', 'Dolar → Kuna'),
        FavoriteCurrency('GBP', 'EUR', 'Libra → Euro'),
      ];
    });
  }

  void _calculateConversion() {
    if (_fromCurrency == _toCurrency) {
      _convertedAmount = _amount;
    } else {
      final rate = _exchangeRates[_fromCurrency]?[_toCurrency] ?? 1.0;
      _convertedAmount = _amount * rate;
    }

    // Přidat do historie
    if (_amount > 0) {
      final conversion = ConversionHistory(
        from: _fromCurrency,
        to: _toCurrency,
        amount: _amount,
        convertedAmount: _convertedAmount,
        rate: _exchangeRates[_fromCurrency]?[_toCurrency] ?? 1.0,
        timestamp: DateTime.now(),
      );

      setState(() {
        _history.insert(0, conversion);
        if (_history.length > 20) {
          _history = _history.take(20).toList();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Měnový převodník',
          style: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF006994).withValues(alpha: 0.9),
                const Color(0xFF2E8B8B).withValues(alpha: 0.8),
                const Color(0xFF4CAF50).withValues(alpha: 0.7),
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: CustomPaint(
            painter: WatercolorCurrencyHeaderPainter(),
            size: Size.infinite,
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: _refreshRates,
              icon: const Icon(Icons.refresh),
              tooltip: 'Aktualizovat kurzy',
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicator: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Colors.white.withValues(alpha: 0.3),
          ),
          tabs: const [
            Tab(icon: Icon(Icons.currency_exchange), text: 'Převodník'),
            Tab(icon: Icon(Icons.star), text: 'Oblíbené'),
            Tab(icon: Icon(Icons.history), text: 'Historie'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildConverterTab(),
          _buildFavoritesTab(),
          _buildHistoryTab(),
        ],
      ),
    );
  }

  Widget _buildConverterTab() {
    return Container(
      child: CustomPaint(
        painter: WatercolorCurrencyBackgroundPainter(),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // Hlavní převodník karta
              Container(
                child: CustomPaint(
                  painter: WatercolorCurrencyMainCardPainter(
                    const Color(0xFF4CAF50),
                  ),
                  child: Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'Převodník měn',
                          style: GoogleFonts.playfairDisplay(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF4CAF50),
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Z měny
                        _buildCurrencyInput(
                          'Z',
                          _fromCurrency,
                          _amount,
                          true,
                          const Color(0xFF006994),
                        ),

                        const SizedBox(height: 16),

                        // Swap tlačítko
                        Container(
                          child: CustomPaint(
                            painter: WatercolorSwapButtonPainter(
                              const Color(0xFF2E8B8B),
                            ),
                            child: IconButton(
                              onPressed: _swapCurrencies,
                              icon: const Icon(
                                Icons.swap_vert,
                                size: 32,
                                color: Color(0xFF2E8B8B),
                              ),
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Na měnu
                        _buildCurrencyInput(
                          'Na',
                          _toCurrency,
                          _convertedAmount,
                          false,
                          const Color(0xFF4CAF50),
                        ),

                        const SizedBox(height: 24),

                        // Kurz
                        Container(
                          padding: const EdgeInsets.all(16),
                          child: CustomPaint(
                            painter: WatercolorRateCardPainter(
                              const Color(0xFFFF6B35),
                            ),
                            child: Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.9),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.trending_up,
                                    color: const Color(0xFFFF6B35),
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    '1 $_fromCurrency = ${(_exchangeRates[_fromCurrency]?[_toCurrency] ?? 1.0).toStringAsFixed(4)} $_toCurrency',
                                    style: GoogleFonts.inter(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xFFFF6B35),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Rychlé převody
              _buildQuickConversions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCurrencyInput(
    String label,
    String currency,
    double amount,
    bool isEditable,
    Color color,
  ) {
    return Container(
      child: CustomPaint(
        painter: WatercolorCurrencyInputPainter(color),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF666666),
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  // Výběr měny
                  GestureDetector(
                    onTap: () => _showCurrencyPicker(isEditable),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: color.withValues(alpha: 0.3)),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            _getCurrencyFlag(currency),
                            style: const TextStyle(fontSize: 20),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            currency,
                            style: GoogleFonts.inter(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: color,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Icon(Icons.arrow_drop_down, color: color, size: 20),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Částka
                  Expanded(
                    child: isEditable
                        ? TextField(
                            controller: _amountController,
                            keyboardType: TextInputType.number,
                            style: GoogleFonts.playfairDisplay(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: color,
                            ),
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              hintText: '0.00',
                              hintStyle: GoogleFonts.playfairDisplay(
                                fontSize: 24,
                                color: Colors.grey[400],
                              ),
                            ),
                            onChanged: (value) {
                              setState(() {
                                _amount = double.tryParse(value) ?? 0.0;
                                _calculateConversion();
                              });
                            },
                          )
                        : Text(
                            amount.toStringAsFixed(2),
                            style: GoogleFonts.playfairDisplay(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: color,
                            ),
                          ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickConversions() {
    final quickAmounts = [10.0, 50.0, 100.0, 500.0];

    return Container(
      child: CustomPaint(
        painter: WatercolorQuickConversionsCardPainter(const Color(0xFF2E8B8B)),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Rychlé převody',
                style: GoogleFonts.playfairDisplay(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2E8B8B),
                ),
              ),
              const SizedBox(height: 16),
              Wrap(
                spacing: 12,
                runSpacing: 8,
                children: quickAmounts.map((amount) {
                  final rate =
                      _exchangeRates[_fromCurrency]?[_toCurrency] ?? 1.0;
                  final converted = amount * rate;

                  return Container(
                    child: CustomPaint(
                      painter: WatercolorQuickAmountPainter(
                        const Color(0xFF006994),
                      ),
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _amount = amount;
                            _amountController.text = amount.toString();
                            _calculateConversion();
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.9),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: const Color(
                                0xFF006994,
                              ).withValues(alpha: 0.3),
                            ),
                          ),
                          child: Column(
                            children: [
                              Text(
                                '${amount.round()} $_fromCurrency',
                                style: GoogleFonts.inter(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: const Color(0xFF006994),
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '${converted.toStringAsFixed(2)} $_toCurrency',
                                style: GoogleFonts.inter(
                                  fontSize: 10,
                                  color: const Color(0xFF666666),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFavoritesTab() {
    return CustomPaint(
      painter: WatercolorCurrencyBackgroundPainter(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _favorites.length,
        itemBuilder: (context, index) {
          final favorite = _favorites[index];
          final rate = _exchangeRates[favorite.from]?[favorite.to] ?? 1.0;

          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            child: CustomPaint(
              painter: WatercolorFavoriteCardPainter(const Color(0xFFFF6B35)),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Icon(Icons.star, color: const Color(0xFFFF6B35), size: 24),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            favorite.name,
                            style: GoogleFonts.inter(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF2C2C2C),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '1 ${favorite.from} = ${rate.toStringAsFixed(4)} ${favorite.to}',
                            style: GoogleFonts.inter(
                              fontSize: 14,
                              color: const Color(0xFF666666),
                            ),
                          ),
                        ],
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _fromCurrency = favorite.from;
                          _toCurrency = favorite.to;
                          _calculateConversion();
                        });
                        _tabController.animateTo(0);
                      },
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: const Color(0xFF006994).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.arrow_forward,
                          color: const Color(0xFF006994),
                          size: 20,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHistoryTab() {
    if (_history.isEmpty) {
      return CustomPaint(
        painter: WatercolorCurrencyBackgroundPainter(),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.history, size: 64, color: Colors.grey[400]),
              const SizedBox(height: 16),
              Text(
                'Žádná historie převodů',
                style: GoogleFonts.playfairDisplay(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF666666),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Převody se zobrazí zde',
                style: GoogleFonts.inter(color: Colors.grey[500]),
              ),
            ],
          ),
        ),
      );
    }

    return CustomPaint(
      painter: WatercolorCurrencyBackgroundPainter(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _history.length,
        itemBuilder: (context, index) {
          final conversion = _history[index];

          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            child: CustomPaint(
              painter: WatercolorHistoryCardPainter(const Color(0xFF2E8B8B)),
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${conversion.amount.toStringAsFixed(2)} ${conversion.from}',
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: const Color(0xFF006994),
                          ),
                        ),
                        Text(
                          '${conversion.convertedAmount.toStringAsFixed(2)} ${conversion.to}',
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: const Color(0xFF2E8B8B),
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '${conversion.rate.toStringAsFixed(4)}',
                          style: GoogleFonts.inter(
                            fontSize: 12,
                            color: const Color(0xFF666666),
                          ),
                        ),
                        Text(
                          '${conversion.timestamp.hour}:${conversion.timestamp.minute.toString().padLeft(2, '0')}',
                          style: GoogleFonts.inter(
                            fontSize: 10,
                            color: const Color(0xFF999999),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _swapCurrencies() {
    setState(() {
      final temp = _fromCurrency;
      _fromCurrency = _toCurrency;
      _toCurrency = temp;
      _calculateConversion();
    });
  }

  void _refreshRates() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Kurzy aktualizovány'),
        backgroundColor: const Color(0xFF4CAF50),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showCurrencyPicker(bool isFromCurrency) {
    final currencies = ['EUR', 'HRK', 'CZK', 'USD', 'GBP', 'CHF'];

    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Vyberte měnu',
              style: GoogleFonts.playfairDisplay(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...currencies.map(
              (currency) => Container(
                margin: const EdgeInsets.only(bottom: 8),
                child: CustomPaint(
                  painter: WatercolorCurrencyOptionPainter(
                    const Color(0xFF006994),
                  ),
                  child: ListTile(
                    leading: Text(
                      _getCurrencyFlag(currency),
                      style: const TextStyle(fontSize: 24),
                    ),
                    title: Text(
                      '$currency - ${_getCurrencyName(currency)}',
                      style: GoogleFonts.inter(fontWeight: FontWeight.w500),
                    ),
                    onTap: () {
                      setState(() {
                        if (isFromCurrency) {
                          _fromCurrency = currency;
                        } else {
                          _toCurrency = currency;
                        }
                        _calculateConversion();
                      });
                      Navigator.pop(context);
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getCurrencyFlag(String currency) {
    switch (currency) {
      case 'EUR':
        return '🇪🇺';
      case 'HRK':
        return '🇭🇷';
      case 'CZK':
        return '🇨🇿';
      case 'USD':
        return '🇺🇸';
      case 'GBP':
        return '🇬🇧';
      case 'CHF':
        return '🇨🇭';
      default:
        return '💰';
    }
  }

  String _getCurrencyName(String currency) {
    switch (currency) {
      case 'EUR':
        return 'Euro';
      case 'HRK':
        return 'Chorvatská kuna';
      case 'CZK':
        return 'Česká koruna';
      case 'USD':
        return 'Americký dolar';
      case 'GBP':
        return 'Britská libra';
      case 'CHF':
        return 'Švýcarský frank';
      default:
        return currency;
    }
  }
}

// Modely pro currency converter
class ConversionHistory {
  final String from;
  final String to;
  final double amount;
  final double convertedAmount;
  final double rate;
  final DateTime timestamp;

  ConversionHistory({
    required this.from,
    required this.to,
    required this.amount,
    required this.convertedAmount,
    required this.rate,
    required this.timestamp,
  });
}

class FavoriteCurrency {
  final String from;
  final String to;
  final String name;

  FavoriteCurrency(this.from, this.to, this.name);
}

// Watercolor painters pro Currency Converter Screen
class WatercolorCurrencyHeaderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor vlny pro Currency header
    final path1 = Path();
    path1.moveTo(0, size.height * 0.3);
    path1.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.1,
      size.width * 0.5,
      size.height * 0.4,
    );
    path1.quadraticBezierTo(
      size.width * 0.75,
      size.height * 0.7,
      size.width,
      size.height * 0.2,
    );
    path1.lineTo(size.width, size.height);
    path1.lineTo(0, size.height);
    path1.close();

    paint.color = const Color(0xFF4CAF50).withValues(alpha: 0.2);
    canvas.drawPath(path1, paint);

    // Druhá vrstva
    final path2 = Path();
    path2.moveTo(0, size.height * 0.5);
    path2.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.2,
      size.width * 0.7,
      size.height * 0.6,
    );
    path2.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.8,
      size.width,
      size.height * 0.4,
    );
    path2.lineTo(size.width, size.height);
    path2.lineTo(0, size.height);
    path2.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.15);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorCurrencyBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Jemný watercolor efekt pro pozadí currency
    final path = Path();
    path.moveTo(size.width * 0.1, size.height * 0.05);
    path.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.02,
      size.width * 0.9,
      size.height * 0.08,
    );
    path.lineTo(size.width * 0.95, size.height * 0.95);
    path.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.98,
      size.width * 0.05,
      size.height * 0.92,
    );
    path.close();

    paint.color = const Color(0xFF4CAF50).withValues(alpha: 0.03);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorCurrencyMainCardPainter extends CustomPainter {
  final Color color;

  WatercolorCurrencyMainCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro hlavní currency kartu
    final path = Path();
    path.moveTo(size.width * 0.05, size.height * 0.1);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.02,
      size.width * 0.7,
      size.height * 0.08,
    );
    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.15,
      size.width * 0.98,
      size.height * 0.9,
    );
    path.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.98,
      size.width * 0.3,
      size.height * 0.92,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.85,
      size.width * 0.05,
      size.height * 0.1,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.1);
    canvas.drawPath(path, paint);

    // Druhá vrstva pro hloubku
    final path2 = Path();
    path2.moveTo(size.width * 0.1, size.height * 0.2);
    path2.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.05,
      size.width * 0.9,
      size.height * 0.15,
    );
    path2.lineTo(size.width * 0.85, size.height * 0.8);
    path2.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.95,
      size.width * 0.15,
      size.height * 0.85,
    );
    path2.close();

    paint.color = color.withValues(alpha: 0.05);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorCurrencyInputPainter extends CustomPainter {
  final Color color;

  WatercolorCurrencyInputPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro currency input
    final path = Path();
    path.moveTo(size.width * 0.08, size.height * 0.15);
    path.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.05,
      size.width * 0.8,
      size.height * 0.12,
    );
    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.2,
      size.width * 0.92,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.95,
      size.width * 0.2,
      size.height * 0.88,
    );
    path.quadraticBezierTo(
      size.width * 0.05,
      size.height * 0.8,
      size.width * 0.08,
      size.height * 0.15,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.12);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorSwapButtonPainter extends CustomPainter {
  final Color color;

  WatercolorSwapButtonPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor kruh kolem swap tlačítka
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2.5;

    final path = Path();
    for (int i = 0; i < 360; i += 20) {
      final angle = i * pi / 180;
      final variation = 0.8 + (sin(i * pi / 60) * 0.2);
      final x = center.dx + (radius * variation) * cos(angle);
      final y = center.dy + (radius * variation) * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    paint.color = color.withValues(alpha: 0.2);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorRateCardPainter extends CustomPainter {
  final Color color;

  WatercolorRateCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro rate kartu
    final path = Path();
    path.moveTo(size.width * 0.1, size.height * 0.2);
    path.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.08,
      size.width * 0.8,
      size.height * 0.15,
    );
    path.quadraticBezierTo(
      size.width * 0.92,
      size.height * 0.25,
      size.width * 0.9,
      size.height * 0.8,
    );
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.92,
      size.width * 0.2,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.08,
      size.height * 0.75,
      size.width * 0.1,
      size.height * 0.2,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.1);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorQuickConversionsCardPainter extends CustomPainter {
  final Color color;

  WatercolorQuickConversionsCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro quick conversions kartu
    final path = Path();
    path.moveTo(size.width * 0.06, size.height * 0.12);
    path.quadraticBezierTo(
      size.width * 0.35,
      size.height * 0.03,
      size.width * 0.75,
      size.height * 0.09,
    );
    path.quadraticBezierTo(
      size.width * 0.94,
      size.height * 0.18,
      size.width * 0.97,
      size.height * 0.88,
    );
    path.quadraticBezierTo(
      size.width * 0.65,
      size.height * 0.97,
      size.width * 0.25,
      size.height * 0.91,
    );
    path.quadraticBezierTo(
      size.width * 0.03,
      size.height * 0.82,
      size.width * 0.06,
      size.height * 0.12,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.1);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorQuickAmountPainter extends CustomPainter {
  final Color color;

  WatercolorQuickAmountPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Malý watercolor efekt kolem quick amount
    final path = Path();
    path.moveTo(size.width * 0.1, size.height * 0.2);
    path.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.05,
      size.width * 0.9,
      size.height * 0.15,
    );
    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.5,
      size.width * 0.85,
      size.height * 0.8,
    );
    path.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.95,
      size.width * 0.15,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.05,
      size.height * 0.5,
      size.width * 0.1,
      size.height * 0.2,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorFavoriteCardPainter extends CustomPainter {
  final Color color;

  WatercolorFavoriteCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro favorite karty
    final path = Path();
    path.moveTo(size.width * 0.03, size.height * 0.2);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.05,
      size.width * 0.7,
      size.height * 0.15,
    );
    path.quadraticBezierTo(
      size.width * 0.97,
      size.height * 0.25,
      size.width * 0.95,
      size.height * 0.8,
    );
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.95,
      size.width * 0.2,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.5,
      size.width * 0.03,
      size.height * 0.2,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorHistoryCardPainter extends CustomPainter {
  final Color color;

  WatercolorHistoryCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Jemný watercolor efekt pro history karty
    final path = Path();
    path.moveTo(size.width * 0.02, size.height * 0.3);
    path.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.1,
      size.width * 0.8,
      size.height * 0.25,
    );
    path.quadraticBezierTo(
      size.width * 0.98,
      size.height * 0.4,
      size.width * 0.95,
      size.height * 0.7,
    );
    path.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.9,
      size.width * 0.1,
      size.height * 0.75,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.5,
      size.width * 0.02,
      size.height * 0.3,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.06);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorCurrencyOptionPainter extends CustomPainter {
  final Color color;

  WatercolorCurrencyOptionPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Jemný watercolor efekt pro currency options
    final path = Path();
    path.moveTo(size.width * 0.02, size.height * 0.2);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.05,
      size.width * 0.7,
      size.height * 0.15,
    );
    path.quadraticBezierTo(
      size.width * 0.98,
      size.height * 0.25,
      size.width * 0.95,
      size.height * 0.8,
    );
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.95,
      size.width * 0.2,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.5,
      size.width * 0.02,
      size.height * 0.2,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
