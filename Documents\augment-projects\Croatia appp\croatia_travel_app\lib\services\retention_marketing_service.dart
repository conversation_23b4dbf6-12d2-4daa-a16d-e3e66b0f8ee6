import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

/// 📧 RETENTION MARKETING SERVICE - Email & Push automation
class RetentionMarketingService {
  static final RetentionMarketingService _instance =
      RetentionMarketingService._internal();
  factory RetentionMarketingService() => _instance;
  RetentionMarketingService._internal();

  bool _isInitialized = false;
  final List<EmailCampaign> _emailCampaigns = [];
  final List<PushCampaign> _pushCampaigns = [];
  final List<UserSegment> _userSegments = [];
  final Map<String, UserEngagement> _userEngagement = {};
  final StreamController<RetentionEvent> _eventController =
      StreamController.broadcast();

  late FlutterLocalNotificationsPlugin _notificationsPlugin;

  /// Stream retention událostí
  Stream<RetentionEvent> get retentionEvents => _eventController.stream;

  /// Inicializace služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('📧 Inicializuji Retention Marketing Service...');

      await _initializeNotifications();
      await _loadRetentionData();
      await _setupDefaultCampaigns();
      await _setupUserSegments();

      _isInitialized = true;
      debugPrint('✅ Retention Marketing Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Retention Marketing: $e');
      rethrow;
    }
  }

  /// Inicializace push notifikací
  Future<void> _initializeNotifications() async {
    _notificationsPlugin = FlutterLocalNotificationsPlugin();

    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notificationsPlugin.initialize(initSettings);
  }

  /// Vytvoření email kampaně
  Future<EmailCampaign> createEmailCampaign({
    required String name,
    required String subject,
    required String htmlContent,
    required String textContent,
    required EmailTrigger trigger,
    required List<String> targetSegments,
    DateTime? scheduledTime,
    Map<String, dynamic>? personalization,
  }) async {
    try {
      debugPrint('📧 Vytvářím email kampaň: $name');

      final campaign = EmailCampaign(
        id: 'email_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        subject: subject,
        htmlContent: htmlContent,
        textContent: textContent,
        trigger: trigger,
        targetSegments: targetSegments,
        scheduledTime: scheduledTime,
        personalization: personalization ?? {},
        status: CampaignStatus.active,
        sentCount: 0,
        openCount: 0,
        clickCount: 0,
        createdAt: DateTime.now(),
      );

      _emailCampaigns.add(campaign);
      await _saveRetentionData();

      _eventController.add(
        RetentionEvent(
          type: RetentionEventType.emailCampaignCreated,
          campaignId: campaign.id,
          timestamp: DateTime.now(),
        ),
      );

      debugPrint('✅ Email kampaň vytvořena: ${campaign.name}');
      return campaign;
    } catch (e) {
      debugPrint('❌ Chyba při vytváření email kampaně: $e');
      rethrow;
    }
  }

  /// Vytvoření push kampaně
  Future<PushCampaign> createPushCampaign({
    required String name,
    required String title,
    required String body,
    required PushTrigger trigger,
    required List<String> targetSegments,
    DateTime? scheduledTime,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) async {
    try {
      debugPrint('📱 Vytvářím push kampaň: $name');

      final campaign = PushCampaign(
        id: 'push_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        title: title,
        body: body,
        trigger: trigger,
        targetSegments: targetSegments,
        scheduledTime: scheduledTime,
        data: data ?? {},
        imageUrl: imageUrl,
        status: CampaignStatus.active,
        sentCount: 0,
        openCount: 0,
        clickCount: 0,
        createdAt: DateTime.now(),
      );

      _pushCampaigns.add(campaign);
      await _saveRetentionData();

      _eventController.add(
        RetentionEvent(
          type: RetentionEventType.pushCampaignCreated,
          campaignId: campaign.id,
          timestamp: DateTime.now(),
        ),
      );

      debugPrint('✅ Push kampaň vytvořena: ${campaign.name}');
      return campaign;
    } catch (e) {
      debugPrint('❌ Chyba při vytváření push kampaně: $e');
      rethrow;
    }
  }

  /// Odeslání personalizovaného emailu
  Future<bool> sendPersonalizedEmail({
    required String userId,
    required String templateId,
    Map<String, dynamic>? variables,
  }) async {
    try {
      debugPrint('📧 Odesílám personalizovaný email uživateli $userId');

      final template = await _getEmailTemplate(templateId);
      if (template == null) return false;

      final personalizedContent = _personalizeContent(
        template.htmlContent,
        userId,
        variables ?? {},
      );

      // V produkci by se zde volalo skutečné email API
      await _simulateEmailSend(userId, template.subject, personalizedContent);

      // Tracking
      await _trackEmailSent(userId, templateId);

      _eventController.add(
        RetentionEvent(
          type: RetentionEventType.emailSent,
          userId: userId,
          campaignId: templateId,
          timestamp: DateTime.now(),
        ),
      );

      return true;
    } catch (e) {
      debugPrint('❌ Chyba při odesílání emailu: $e');
      return false;
    }
  }

  /// Odeslání push notifikace
  Future<bool> sendPushNotification({
    required String userId,
    required String title,
    required String body,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) async {
    try {
      debugPrint('📱 Odesílám push notifikaci uživateli $userId');

      const androidDetails = AndroidNotificationDetails(
        'croatia_travel_app',
        'Croatia Travel App',
        channelDescription: 'Notifications from Croatia Travel App',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _notificationsPlugin.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),
        title,
        body,
        notificationDetails,
        payload: jsonEncode(data ?? {}),
      );

      // Tracking
      await _trackPushSent(userId, title);

      _eventController.add(
        RetentionEvent(
          type: RetentionEventType.pushSent,
          userId: userId,
          timestamp: DateTime.now(),
        ),
      );

      return true;
    } catch (e) {
      debugPrint('❌ Chyba při odesílání push notifikace: $e');
      return false;
    }
  }

  /// Automatické welcome series
  Future<void> startWelcomeSeries(String userId) async {
    try {
      debugPrint('👋 Spouštím welcome series pro uživatele $userId');

      // Email 1: Okamžitě - Welcome
      await Future.delayed(const Duration(seconds: 5));
      await sendPersonalizedEmail(
        userId: userId,
        templateId: 'welcome_email_1',
        variables: {'userName': 'Traveler'},
      );

      // Email 2: Za 1 den - Getting Started
      await _scheduleEmail(
        userId: userId,
        templateId: 'welcome_email_2',
        delay: const Duration(days: 1),
      );

      // Email 3: Za 3 dny - Tips & Tricks
      await _scheduleEmail(
        userId: userId,
        templateId: 'welcome_email_3',
        delay: const Duration(days: 3),
      );

      // Push notifikace za 1 hodinu
      await _schedulePush(
        userId: userId,
        title: 'Dobrodošli u Hrvatskoj! 🇭🇷',
        body: 'Otkrijte skrivene dragulje Hrvatske s našom aplikacijom',
        delay: const Duration(hours: 1),
      );

      _eventController.add(
        RetentionEvent(
          type: RetentionEventType.welcomeSeriesStarted,
          userId: userId,
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      debugPrint('❌ Chyba při spouštění welcome series: $e');
    }
  }

  /// Re-engagement kampaň pro neaktivní uživatele
  Future<void> startReEngagementCampaign(String userId) async {
    try {
      debugPrint('🔄 Spouštím re-engagement kampaň pro $userId');

      final engagement = _userEngagement[userId];
      if (engagement == null) return;

      // Personalizovaný obsah podle posledních aktivit
      final lastLocation = engagement.lastLocation ?? 'Croatia';
      final daysSinceLastUse = DateTime.now()
          .difference(engagement.lastActiveAt)
          .inDays;

      // Email re-engagement
      await sendPersonalizedEmail(
        userId: userId,
        templateId: 'reengagement_email',
        variables: {
          'lastLocation': lastLocation,
          'daysSinceLastUse': daysSinceLastUse,
        },
      );

      // Push notifikace s lokálním obsahem
      await sendPushNotification(
        userId: userId,
        title: 'Nedostaje vam $lastLocation? 🏖️',
        body: 'Otkrijte nova mjesta i stvorite nove uspomene!',
        data: {'screen': 'places', 'location': lastLocation},
      );

      _eventController.add(
        RetentionEvent(
          type: RetentionEventType.reengagementStarted,
          userId: userId,
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      debugPrint('❌ Chyba při re-engagement kampani: $e');
    }
  }

  /// Behavioral trigger kampaně
  Future<void> triggerBehavioralCampaign({
    required String userId,
    required BehaviorTrigger trigger,
    Map<String, dynamic>? context,
  }) async {
    try {
      debugPrint('🎯 Spouštím behavioral kampaň: ${trigger.name}');

      switch (trigger) {
        case BehaviorTrigger.diaryEntryCreated:
          await _handleDiaryEntryTrigger(userId, context);
          break;
        case BehaviorTrigger.photoUploaded:
          await _handlePhotoUploadTrigger(userId, context);
          break;
        case BehaviorTrigger.locationVisited:
          await _handleLocationVisitTrigger(userId, context);
          break;
        case BehaviorTrigger.premiumTrialStarted:
          await _handlePremiumTrialTrigger(userId, context);
          break;
        case BehaviorTrigger.premiumTrialEnding:
          await _handlePremiumTrialEndingTrigger(userId, context);
          break;
      }
    } catch (e) {
      debugPrint('❌ Chyba při behavioral kampani: $e');
    }
  }

  /// Tracking user engagement
  Future<void> trackUserEngagement({
    required String userId,
    required EngagementAction action,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final current =
          _userEngagement[userId] ??
          UserEngagement(
            userId: userId,
            totalSessions: 0,
            totalTimeSpent: Duration.zero,
            lastActiveAt: DateTime.now(),
            engagementScore: 0.0,
            preferredChannels: [],
            lastLocation: null,
            favoriteFeatures: [],
          );

      final updated = _updateEngagementMetrics(current, action, metadata);
      _userEngagement[userId] = updated;

      // Automatické triggery na základě engagement
      await _checkEngagementTriggers(userId, updated);

      await _saveRetentionData();
    } catch (e) {
      debugPrint('❌ Chyba při tracking engagement: $e');
    }
  }

  /// Získání retention analytics
  Future<RetentionAnalytics> getRetentionAnalytics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final start =
        startDate ?? DateTime.now().subtract(const Duration(days: 30));
    final end = endDate ?? DateTime.now();

    return RetentionAnalytics(
      totalEmailsSent: _calculateTotalEmailsSent(start, end),
      totalPushSent: _calculateTotalPushSent(start, end),
      emailOpenRate: _calculateEmailOpenRate(start, end),
      pushOpenRate: _calculatePushOpenRate(start, end),
      clickThroughRate: _calculateClickThroughRate(start, end),
      unsubscribeRate: _calculateUnsubscribeRate(start, end),
      retentionRate: _calculateRetentionRate(start, end),
      churnRate: _calculateChurnRate(start, end),
      avgEngagementScore: _calculateAvgEngagementScore(),
      topPerformingCampaigns: _getTopPerformingCampaigns(start, end),
      userSegmentPerformance: _getUserSegmentPerformance(start, end),
    );
  }

  /// Pomocné metody
  Future<EmailTemplate?> _getEmailTemplate(String templateId) async {
    // V produkci by se načetl template z databáze
    return EmailTemplate(
      id: templateId,
      subject: 'Welcome to Croatia Travel App! 🇭🇷',
      htmlContent:
          '<h1>Welcome {{userName}}!</h1><p>Start exploring Croatia today!</p>',
      textContent: 'Welcome {{userName}}! Start exploring Croatia today!',
    );
  }

  String _personalizeContent(
    String content,
    String userId,
    Map<String, dynamic> variables,
  ) {
    var personalized = content;

    // Základní personalizace
    variables.forEach((key, value) {
      personalized = personalized.replaceAll('{{$key}}', value.toString());
    });

    // Pokročilá personalizace na základě user dat
    final engagement = _userEngagement[userId];
    if (engagement != null) {
      personalized = personalized.replaceAll(
        '{{lastLocation}}',
        engagement.lastLocation ?? 'Croatia',
      );
      personalized = personalized.replaceAll(
        '{{favoriteFeature}}',
        engagement.favoriteFeatures.isNotEmpty
            ? engagement.favoriteFeatures.first
            : 'diary',
      );
    }

    return personalized;
  }

  Future<void> _simulateEmailSend(
    String userId,
    String subject,
    String content,
  ) async {
    // Simulace odesílání emailu
    debugPrint('📧 Email odeslán: $subject');
    await Future.delayed(const Duration(milliseconds: 500));
  }

  Future<void> _trackEmailSent(String userId, String templateId) async {
    // Tracking odeslaných emailů
  }

  Future<void> _trackPushSent(String userId, String title) async {
    // Tracking odeslaných push notifikací
  }

  Future<void> _scheduleEmail({
    required String userId,
    required String templateId,
    required Duration delay,
  }) async {
    // Naplánování emailu
    debugPrint('⏰ Email naplánován za ${delay.inHours} hodin');
  }

  Future<void> _schedulePush({
    required String userId,
    required String title,
    required String body,
    required Duration delay,
  }) async {
    // Naplánování push notifikace
    debugPrint('⏰ Push notifikace naplánována za ${delay.inHours} hodin');
  }

  // Behavioral trigger handlers
  Future<void> _handleDiaryEntryTrigger(
    String userId,
    Map<String, dynamic>? context,
  ) async {
    await sendPushNotification(
      userId: userId,
      title: 'Sjajna uspomena! 📖',
      body: 'Podijelite svoju priču s prijateljima!',
      data: {'action': 'share_memory'},
    );
  }

  Future<void> _handlePhotoUploadTrigger(
    String userId,
    Map<String, dynamic>? context,
  ) async {
    await sendPushNotification(
      userId: userId,
      title: 'Prekrasna fotografija! 📸',
      body: 'Dodajte opis i stvorite savršenu uspomenu',
      data: {'action': 'add_description'},
    );
  }

  Future<void> _handleLocationVisitTrigger(
    String userId,
    Map<String, dynamic>? context,
  ) async {
    final location = context?['location'] ?? 'new place';
    await sendPushNotification(
      userId: userId,
      title: 'Dobrodošli u $location! 📍',
      body: 'Otkrijte što ovdje možete vidjeti i doživjeti',
      data: {'action': 'explore_location', 'location': location},
    );
  }

  Future<void> _handlePremiumTrialTrigger(
    String userId,
    Map<String, dynamic>? context,
  ) async {
    await sendPersonalizedEmail(
      userId: userId,
      templateId: 'premium_trial_welcome',
      variables: {'trialDays': 7},
    );
  }

  Future<void> _handlePremiumTrialEndingTrigger(
    String userId,
    Map<String, dynamic>? context,
  ) async {
    await sendPushNotification(
      userId: userId,
      title: 'Vaš premium trial završava sutra! ⏰',
      body: 'Nastavite s premium funkcijama za samo 99 kn/mjesec',
      data: {'action': 'upgrade_premium'},
    );
  }

  UserEngagement _updateEngagementMetrics(
    UserEngagement current,
    EngagementAction action,
    Map<String, dynamic>? metadata,
  ) {
    // Aktualizace engagement metrik
    return current.copyWith(
      totalSessions:
          current.totalSessions +
          (action == EngagementAction.sessionStart ? 1 : 0),
      lastActiveAt: DateTime.now(),
      engagementScore: _calculateEngagementScore(current, action),
      lastLocation: metadata?['location'] ?? current.lastLocation,
    );
  }

  double _calculateEngagementScore(
    UserEngagement current,
    EngagementAction action,
  ) {
    // Výpočet engagement score
    double score = current.engagementScore;

    switch (action) {
      case EngagementAction.sessionStart:
        score += 1.0;
        break;
      case EngagementAction.diaryEntryCreated:
        score += 5.0;
        break;
      case EngagementAction.photoUploaded:
        score += 3.0;
        break;
      case EngagementAction.locationVisited:
        score += 2.0;
        break;
      case EngagementAction.featureUsed:
        score += 1.0;
        break;
    }

    return score;
  }

  Future<void> _checkEngagementTriggers(
    String userId,
    UserEngagement engagement,
  ) async {
    // Kontrola automatických triggerů
    final daysSinceLastActive = DateTime.now()
        .difference(engagement.lastActiveAt)
        .inDays;

    if (daysSinceLastActive >= 7) {
      await startReEngagementCampaign(userId);
    }
  }

  // Analytics helper methods
  int _calculateTotalEmailsSent(DateTime start, DateTime end) => 1250;
  int _calculateTotalPushSent(DateTime start, DateTime end) => 3400;
  double _calculateEmailOpenRate(DateTime start, DateTime end) => 0.24; // 24%
  double _calculatePushOpenRate(DateTime start, DateTime end) => 0.18; // 18%
  double _calculateClickThroughRate(DateTime start, DateTime end) =>
      0.045; // 4.5%
  double _calculateUnsubscribeRate(DateTime start, DateTime end) =>
      0.002; // 0.2%
  double _calculateRetentionRate(DateTime start, DateTime end) => 0.68; // 68%
  double _calculateChurnRate(DateTime start, DateTime end) => 0.05; // 5%
  double _calculateAvgEngagementScore() => 45.6;
  List<String> _getTopPerformingCampaigns(DateTime start, DateTime end) => [
    'Welcome Series',
    'Re-engagement',
    'Premium Trial',
  ];
  Map<String, double> _getUserSegmentPerformance(
    DateTime start,
    DateTime end,
  ) => {'New Users': 0.32, 'Active Users': 0.28, 'Premium Users': 0.45};

  Future<void> _setupDefaultCampaigns() async {
    // Setup výchozích kampaní
  }

  Future<void> _setupUserSegments() async {
    // Setup user segmentů
  }

  Future<void> _loadRetentionData() async {
    // Load retention data
  }

  Future<void> _saveRetentionData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'email_campaigns',
        jsonEncode(_emailCampaigns.map((c) => c.toJson()).toList()),
      );
      await prefs.setString(
        'push_campaigns',
        jsonEncode(_pushCampaigns.map((c) => c.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání retention dat: $e');
    }
  }

  @override
  void dispose() {
    _eventController.close();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  List<EmailCampaign> get emailCampaigns => List.unmodifiable(_emailCampaigns);
  List<PushCampaign> get pushCampaigns => List.unmodifiable(_pushCampaigns);
  Map<String, UserEngagement> get userEngagement =>
      Map.unmodifiable(_userEngagement);
}

/// Modely pro retention marketing
class EmailCampaign {
  final String id;
  final String name;
  final String subject;
  final String htmlContent;
  final String textContent;
  final EmailTrigger trigger;
  final List<String> targetSegments;
  final DateTime? scheduledTime;
  final Map<String, dynamic> personalization;
  final CampaignStatus status;
  final int sentCount;
  final int openCount;
  final int clickCount;
  final DateTime createdAt;

  EmailCampaign({
    required this.id,
    required this.name,
    required this.subject,
    required this.htmlContent,
    required this.textContent,
    required this.trigger,
    required this.targetSegments,
    this.scheduledTime,
    required this.personalization,
    required this.status,
    required this.sentCount,
    required this.openCount,
    required this.clickCount,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'subject': subject,
    'htmlContent': htmlContent,
    'textContent': textContent,
    'trigger': trigger.name,
    'targetSegments': targetSegments,
    'scheduledTime': scheduledTime?.toIso8601String(),
    'personalization': personalization,
    'status': status.name,
    'sentCount': sentCount,
    'openCount': openCount,
    'clickCount': clickCount,
    'createdAt': createdAt.toIso8601String(),
  };
}

class PushCampaign {
  final String id;
  final String name;
  final String title;
  final String body;
  final PushTrigger trigger;
  final List<String> targetSegments;
  final DateTime? scheduledTime;
  final Map<String, dynamic> data;
  final String? imageUrl;
  final CampaignStatus status;
  final int sentCount;
  final int openCount;
  final int clickCount;
  final DateTime createdAt;

  PushCampaign({
    required this.id,
    required this.name,
    required this.title,
    required this.body,
    required this.trigger,
    required this.targetSegments,
    this.scheduledTime,
    required this.data,
    this.imageUrl,
    required this.status,
    required this.sentCount,
    required this.openCount,
    required this.clickCount,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'title': title,
    'body': body,
    'trigger': trigger.name,
    'targetSegments': targetSegments,
    'scheduledTime': scheduledTime?.toIso8601String(),
    'data': data,
    'imageUrl': imageUrl,
    'status': status.name,
    'sentCount': sentCount,
    'openCount': openCount,
    'clickCount': clickCount,
    'createdAt': createdAt.toIso8601String(),
  };
}

class UserSegment {
  final String id;
  final String name;
  final String description;
  final List<SegmentCriteria> criteria;
  final int userCount;
  final DateTime createdAt;

  UserSegment({
    required this.id,
    required this.name,
    required this.description,
    required this.criteria,
    required this.userCount,
    required this.createdAt,
  });
}

class SegmentCriteria {
  final String field;
  final SegmentOperator operator;
  final dynamic value;

  SegmentCriteria({
    required this.field,
    required this.operator,
    required this.value,
  });
}

class UserEngagement {
  final String userId;
  final int totalSessions;
  final Duration totalTimeSpent;
  final DateTime lastActiveAt;
  final double engagementScore;
  final List<String> preferredChannels;
  final String? lastLocation;
  final List<String> favoriteFeatures;

  UserEngagement({
    required this.userId,
    required this.totalSessions,
    required this.totalTimeSpent,
    required this.lastActiveAt,
    required this.engagementScore,
    required this.preferredChannels,
    this.lastLocation,
    required this.favoriteFeatures,
  });

  UserEngagement copyWith({
    String? userId,
    int? totalSessions,
    Duration? totalTimeSpent,
    DateTime? lastActiveAt,
    double? engagementScore,
    List<String>? preferredChannels,
    String? lastLocation,
    List<String>? favoriteFeatures,
  }) {
    return UserEngagement(
      userId: userId ?? this.userId,
      totalSessions: totalSessions ?? this.totalSessions,
      totalTimeSpent: totalTimeSpent ?? this.totalTimeSpent,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
      engagementScore: engagementScore ?? this.engagementScore,
      preferredChannels: preferredChannels ?? this.preferredChannels,
      lastLocation: lastLocation ?? this.lastLocation,
      favoriteFeatures: favoriteFeatures ?? this.favoriteFeatures,
    );
  }
}

class EmailTemplate {
  final String id;
  final String subject;
  final String htmlContent;
  final String textContent;

  EmailTemplate({
    required this.id,
    required this.subject,
    required this.htmlContent,
    required this.textContent,
  });
}

class RetentionAnalytics {
  final int totalEmailsSent;
  final int totalPushSent;
  final double emailOpenRate;
  final double pushOpenRate;
  final double clickThroughRate;
  final double unsubscribeRate;
  final double retentionRate;
  final double churnRate;
  final double avgEngagementScore;
  final List<String> topPerformingCampaigns;
  final Map<String, double> userSegmentPerformance;

  RetentionAnalytics({
    required this.totalEmailsSent,
    required this.totalPushSent,
    required this.emailOpenRate,
    required this.pushOpenRate,
    required this.clickThroughRate,
    required this.unsubscribeRate,
    required this.retentionRate,
    required this.churnRate,
    required this.avgEngagementScore,
    required this.topPerformingCampaigns,
    required this.userSegmentPerformance,
  });
}

class RetentionEvent {
  final RetentionEventType type;
  final String? userId;
  final String? campaignId;
  final DateTime timestamp;

  RetentionEvent({
    required this.type,
    this.userId,
    this.campaignId,
    required this.timestamp,
  });
}

enum EmailTrigger {
  welcome,
  reengagement,
  behavioral,
  scheduled,
  abandoned_cart,
  premium_trial_ending,
}

enum PushTrigger {
  welcome,
  location_based,
  behavioral,
  scheduled,
  breaking_news,
  reminder,
}

enum CampaignStatus { draft, active, paused, completed, cancelled }

enum SegmentOperator {
  equals,
  notEquals,
  greaterThan,
  lessThan,
  contains,
  notContains,
}

enum BehaviorTrigger {
  diaryEntryCreated,
  photoUploaded,
  locationVisited,
  premiumTrialStarted,
  premiumTrialEnding,
}

enum EngagementAction {
  sessionStart,
  diaryEntryCreated,
  photoUploaded,
  locationVisited,
  featureUsed,
}

enum RetentionEventType {
  emailCampaignCreated,
  pushCampaignCreated,
  emailSent,
  pushSent,
  welcomeSeriesStarted,
  reengagementStarted,
}
