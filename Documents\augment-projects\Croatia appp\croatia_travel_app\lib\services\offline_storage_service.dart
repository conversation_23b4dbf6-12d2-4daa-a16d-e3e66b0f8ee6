import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../models/offline_data.dart';
import '../models/map_place.dart';
import '../models/ticket.dart';

/// Služba pro offline ukládání dat
class OfflineStorageService extends ChangeNotifier {
  static final OfflineStorageService _instance =
      OfflineStorageService._internal();
  factory OfflineStorageService() => _instance;
  OfflineStorageService._internal();

  Database? _database;
  bool _isInitialized = false;
  String? _databasePath;

  // Gettery
  bool get isInitialized => _isInitialized;
  String? get databasePath => _databasePath;

  /// Inicializuje offline storage
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _initializeDatabase();
      _isInitialized = true;
      debugPrint('Offline storage inicializován');
    } catch (e) {
      debugPrint('Chyba při inicializaci offline storage: $e');
      throw Exception('Nepodařilo se inicializovat offline storage');
    }
  }

  /// Inicializuje databázi
  Future<void> _initializeDatabase() async {
    final documentsDirectory = await getApplicationDocumentsDirectory();
    _databasePath = join(documentsDirectory.path, 'croatia_travel_offline.db');

    _database = await openDatabase(
      _databasePath!,
      version: 1,
      onCreate: (db, version) => _createTables(db, version),
      onUpgrade: _upgradeDatabase,
    );
  }

  /// Vytvoří tabulky v databázi
  Future<void> _createTables(Database db, [int? version]) async {
    // Tabulka pro offline balíčky
    await db.execute('''
      CREATE TABLE IF NOT EXISTS offline_packages (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        type TEXT NOT NULL,
        status TEXT NOT NULL,
        downloaded_at INTEGER,
        last_updated INTEGER,
        size_bytes INTEGER NOT NULL,
        version INTEGER NOT NULL,
        region TEXT NOT NULL,
        metadata TEXT NOT NULL,
        dependencies TEXT NOT NULL
      )
    ''');

    // Tabulka pro mapová místa
    await db.execute('''
      CREATE TABLE IF NOT EXISTS offline_places (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        latitude REAL NOT NULL,
        longitude REAL NOT NULL,
        type TEXT NOT NULL,
        category TEXT NOT NULL,
        address TEXT NOT NULL,
        phone_number TEXT,
        website TEXT,
        image_url TEXT,
        rating REAL,
        review_count INTEGER,
        price REAL,
        price_range TEXT,
        tags TEXT NOT NULL,
        is_verified INTEGER NOT NULL,
        is_open INTEGER NOT NULL,
        opening_hours TEXT,
        additional_data TEXT NOT NULL,
        cached_at INTEGER NOT NULL
      )
    ''');

    // Tabulka pro vstupenky
    await db.execute('''
      CREATE TABLE IF NOT EXISTS offline_tickets (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        type TEXT NOT NULL,
        venue_id TEXT NOT NULL,
        venue_name TEXT NOT NULL,
        location TEXT NOT NULL,
        latitude REAL,
        longitude REAL,
        region TEXT NOT NULL,
        pricing_data TEXT NOT NULL,
        availability_data TEXT NOT NULL,
        provider_data TEXT NOT NULL,
        images TEXT NOT NULL,
        tags TEXT NOT NULL,
        features_data TEXT NOT NULL,
        is_active INTEGER NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        cached_at INTEGER NOT NULL
      )
    ''');

    // Tabulka pro AI odpovědi
    await db.execute('''
      CREATE TABLE IF NOT EXISTS offline_ai_responses (
        id TEXT PRIMARY KEY,
        question TEXT NOT NULL,
        answer TEXT NOT NULL,
        keywords TEXT NOT NULL,
        category TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        use_count INTEGER NOT NULL,
        relevance_score REAL NOT NULL
      )
    ''');

    // Tabulka pro offline mapy
    await db.execute('''
      CREATE TABLE IF NOT EXISTS offline_map_areas (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        north_latitude REAL NOT NULL,
        south_latitude REAL NOT NULL,
        east_longitude REAL NOT NULL,
        west_longitude REAL NOT NULL,
        zoom_level INTEGER NOT NULL,
        status TEXT NOT NULL,
        downloaded_at INTEGER,
        size_bytes INTEGER NOT NULL,
        tile_count INTEGER NOT NULL
      )
    ''');

    // Tabulka pro statistiky
    await db.execute('''
      CREATE TABLE IF NOT EXISTS offline_stats (
        id TEXT PRIMARY KEY,
        total_packages INTEGER NOT NULL,
        downloaded_packages INTEGER NOT NULL,
        total_size_bytes INTEGER NOT NULL,
        downloaded_size_bytes INTEGER NOT NULL,
        last_sync_at INTEGER NOT NULL,
        sync_count INTEGER NOT NULL,
        packages_by_type TEXT NOT NULL
      )
    ''');

    // Indexy pro rychlejší vyhledávání
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_places_type ON offline_places(type)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_places_category ON offline_places(category)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_places_location ON offline_places(latitude, longitude)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_tickets_region ON offline_tickets(region)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_ai_keywords ON offline_ai_responses(keywords)',
    );
  }

  /// Upgraduje databázi
  Future<void> _upgradeDatabase(
    Database db,
    int oldVersion,
    int newVersion,
  ) async {
    // Zde by byly migrace pro budoucí verze
    debugPrint('Upgrade databáze z verze $oldVersion na $newVersion');
  }

  /// Uloží offline balíček
  Future<void> saveOfflinePackage(OfflineDataPackage package) async {
    await _ensureInitialized();

    await _database!.insert('offline_packages', {
      'id': package.id,
      'name': package.name,
      'description': package.description,
      'type': package.type.toString(),
      'status': package.status.toString(),
      'downloaded_at': package.downloadedAt?.millisecondsSinceEpoch,
      'last_updated': package.lastUpdated?.millisecondsSinceEpoch,
      'size_bytes': package.sizeBytes,
      'version': package.version,
      'region': package.region,
      'metadata': jsonEncode(package.metadata),
      'dependencies': jsonEncode(package.dependencies),
    }, conflictAlgorithm: ConflictAlgorithm.replace);

    notifyListeners();
  }

  /// Načte všechny offline balíčky
  Future<List<OfflineDataPackage>> getOfflinePackages() async {
    await _ensureInitialized();

    final List<Map<String, dynamic>> maps = await _database!.query(
      'offline_packages',
    );

    return maps
        .map(
          (map) => OfflineDataPackage(
            id: map['id'],
            name: map['name'],
            description: map['description'],
            type: OfflineDataType.values.firstWhere(
              (e) => e.toString() == map['type'],
            ),
            status: OfflineDataStatus.values.firstWhere(
              (e) => e.toString() == map['status'],
            ),
            downloadedAt: map['downloaded_at'] != null
                ? DateTime.fromMillisecondsSinceEpoch(map['downloaded_at'])
                : null,
            lastUpdated: map['last_updated'] != null
                ? DateTime.fromMillisecondsSinceEpoch(map['last_updated'])
                : null,
            sizeBytes: map['size_bytes'],
            version: map['version'],
            region: map['region'],
            metadata: jsonDecode(map['metadata']),
            dependencies: List<String>.from(jsonDecode(map['dependencies'])),
          ),
        )
        .toList();
  }

  /// Uloží mapová místa offline
  Future<void> savePlacesOffline(List<MapPlace> places) async {
    await _ensureInitialized();

    final batch = _database!.batch();
    final now = DateTime.now().millisecondsSinceEpoch;

    for (final place in places) {
      batch.insert('offline_places', {
        'id': place.id,
        'name': place.name,
        'description': place.description,
        'latitude': place.position.latitude,
        'longitude': place.position.longitude,
        'type': place.type.toString(),
        'category': place.category.toString(),
        'address': place.address,
        'phone_number': place.phoneNumber,
        'website': place.website,
        'image_url': place.imageUrl,
        'rating': place.rating,
        'review_count': place.reviewCount,
        'price': place.price,
        'price_range': place.priceRange,
        'tags': jsonEncode(place.tags),
        'is_verified': place.isVerified ? 1 : 0,
        'is_open': place.isOpen ? 1 : 0,
        'opening_hours': place.openingHours,
        'additional_data': jsonEncode(place.additionalData),
        'cached_at': now,
      }, conflictAlgorithm: ConflictAlgorithm.replace);
    }

    await batch.commit();
    notifyListeners();
  }

  /// Načte mapová místa offline
  Future<List<MapPlace>> getPlacesOffline() async {
    await _ensureInitialized();

    final List<Map<String, dynamic>> maps = await _database!.query(
      'offline_places',
    );

    return maps
        .map(
          (map) => MapPlace(
            id: map['id'],
            name: map['name'],
            description: map['description'],
            position: LatLng(map['latitude'], map['longitude']),
            type: MapPlaceType.values.firstWhere(
              (e) => e.toString() == map['type'],
            ),
            category: MapPlaceCategory.values.firstWhere(
              (e) => e.toString() == map['category'],
            ),
            address: map['address'],
            phoneNumber: map['phone_number'],
            website: map['website'],
            imageUrl: map['image_url'],
            rating: map['rating'],
            reviewCount: map['review_count'],
            price: map['price'],
            priceRange: map['price_range'],
            tags: List<String>.from(jsonDecode(map['tags'])),
            isVerified: map['is_verified'] == 1,
            isOpen: map['is_open'] == 1,
            openingHours: map['opening_hours'],
            additionalData: jsonDecode(map['additional_data']),
          ),
        )
        .toList();
  }

  /// Uloží vstupenky offline
  Future<void> saveTicketsOffline(List<Ticket> tickets) async {
    await _ensureInitialized();

    final batch = _database!.batch();
    final now = DateTime.now().millisecondsSinceEpoch;

    for (final ticket in tickets) {
      batch.insert('offline_tickets', {
        'id': ticket.id,
        'title': ticket.title,
        'description': ticket.description,
        'type': ticket.type.toString(),
        'venue_id': ticket.venueId,
        'venue_name': ticket.venueName,
        'location': ticket.location,
        'latitude': ticket.latitude,
        'longitude': ticket.longitude,
        'region': ticket.region,
        'pricing_data': jsonEncode(ticket.pricing.toJson()),
        'availability_data': jsonEncode(
          ticket.availability.map((a) => a.toJson()).toList(),
        ),
        'provider_data': jsonEncode(ticket.provider.toJson()),
        'images': jsonEncode(ticket.images),
        'tags': jsonEncode(ticket.tags),
        'features_data': jsonEncode(ticket.features.toJson()),
        'is_active': ticket.isActive ? 1 : 0,
        'created_at': ticket.createdAt.millisecondsSinceEpoch,
        'updated_at': ticket.updatedAt.millisecondsSinceEpoch,
        'cached_at': now,
      }, conflictAlgorithm: ConflictAlgorithm.replace);
    }

    await batch.commit();
    notifyListeners();
  }

  /// Uloží AI odpovědi offline
  Future<void> saveAIResponsesOffline(List<OfflineAIResponse> responses) async {
    await _ensureInitialized();

    final batch = _database!.batch();

    for (final response in responses) {
      batch.insert('offline_ai_responses', {
        'id': response.id,
        'question': response.question,
        'answer': response.answer,
        'keywords': jsonEncode(response.keywords),
        'category': response.category,
        'created_at': response.createdAt.millisecondsSinceEpoch,
        'use_count': response.useCount,
        'relevance_score': response.relevanceScore,
      }, conflictAlgorithm: ConflictAlgorithm.replace);
    }

    await batch.commit();
    notifyListeners();
  }

  /// Načte AI odpovědi offline
  Future<List<OfflineAIResponse>> getAIResponsesOffline() async {
    await _ensureInitialized();

    final List<Map<String, dynamic>> maps = await _database!.query(
      'offline_ai_responses',
    );

    return maps
        .map(
          (map) => OfflineAIResponse(
            id: map['id'],
            question: map['question'],
            answer: map['answer'],
            keywords: List<String>.from(jsonDecode(map['keywords'])),
            category: map['category'],
            createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
            useCount: map['use_count'],
            relevanceScore: map['relevance_score'],
          ),
        )
        .toList();
  }

  /// Zkontroluje, zda jsou data dostupná offline
  Future<bool> isDataAvailableOffline(OfflineDataType type) async {
    await _ensureInitialized();

    final packages = await getOfflinePackages();
    return packages.any(
      (package) => package.type == type && package.isAvailable,
    );
  }

  /// Získá velikost offline dat
  Future<int> getOfflineDataSize() async {
    await _ensureInitialized();

    if (_databasePath == null) return 0;

    final file = File(_databasePath!);
    if (await file.exists()) {
      return await file.length();
    }

    return 0;
  }

  /// Vymaže všechna offline data
  Future<void> clearAllOfflineData() async {
    await _ensureInitialized();

    await _database!.delete('offline_packages');
    await _database!.delete('offline_places');
    await _database!.delete('offline_tickets');
    await _database!.delete('offline_ai_responses');
    await _database!.delete('offline_map_areas');
    await _database!.delete('offline_stats');

    notifyListeners();
  }

  /// Vymaže konkrétní typ dat
  Future<void> clearOfflineDataType(OfflineDataType type) async {
    await _ensureInitialized();

    await _database!.delete(
      'offline_packages',
      where: 'type = ?',
      whereArgs: [type.toString()],
    );

    switch (type) {
      case OfflineDataType.places:
        await _database!.delete('offline_places');
        break;
      case OfflineDataType.tickets:
        await _database!.delete('offline_tickets');
        break;
      case OfflineDataType.aiResponses:
        await _database!.delete('offline_ai_responses');
        break;
      case OfflineDataType.maps:
        await _database!.delete('offline_map_areas');
        break;
      default:
        break;
    }

    notifyListeners();
  }

  /// Zajistí, že je služba inicializována
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  @override
  void dispose() {
    _database?.close();
    super.dispose();
  }
}
