import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/diary_entry.dart';

class DiaryEntryEditScreen extends StatefulWidget {
  final DiaryEntry? entry;
  final Map<String, dynamic>? template;

  const DiaryEntryEditScreen({
    super.key,
    this.entry,
    this.template,
  });

  @override
  State<DiaryEntryEditScreen> createState() => _DiaryEntryEditScreenState();
}

class _DiaryEntryEditScreenState extends State<DiaryEntryEditScreen> {
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  final _locationController = TextEditingController();
  
  DateTime _selectedDate = DateTime.now();
  DiaryMood? _selectedMood;
  double? _rating;

  @override
  void initState() {
    super.initState();
    _initializeFields();
  }

  void _initializeFields() {
    if (widget.entry != null) {
      // Editace existují<PERSON><PERSON><PERSON>u
      final entry = widget.entry!;
      _titleController.text = entry.title;
      _contentController.text = entry.content;
      _locationController.text = entry.location ?? '';
      _selectedDate = entry.date;
      _selectedMood = entry.mood;
      _rating = entry.rating;
    } else if (widget.template != null) {
      // Nový záznam s template
      if (widget.template!['date'] != null) {
        _selectedDate = widget.template!['date'];
      }
      if (widget.template!['location'] != null) {
        _locationController.text = widget.template!['location'];
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.entry != null ? 'Upravit záznam' : 'Nový záznam',
          style: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF006994),
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _saveEntry,
            child: Text(
              'Uložit',
              style: GoogleFonts.inter(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Datum
            Card(
              child: ListTile(
                leading: const Icon(Icons.calendar_today, color: Color(0xFF006994)),
                title: const Text('Datum'),
                subtitle: Text(
                  '${_selectedDate.day}.${_selectedDate.month}.${_selectedDate.year}',
                ),
                onTap: _selectDate,
              ),
            ),

            const SizedBox(height: 16),

            // Titulek
            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Titulek',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.title),
              ),
            ),

            const SizedBox(height: 16),

            // Lokace
            TextField(
              controller: _locationController,
              decoration: const InputDecoration(
                labelText: 'Lokace',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.location_on),
              ),
            ),

            const SizedBox(height: 16),

            // Nálada
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Nálada',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 8,
                      children: DiaryMood.values.map((mood) {
                        final isSelected = _selectedMood == mood;
                        return FilterChip(
                          label: Text('${mood.emoji} ${mood.name}'),
                          selected: isSelected,
                          onSelected: (selected) {
                            setState(() {
                              _selectedMood = selected ? mood : null;
                            });
                          },
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Hodnocení
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Hodnocení',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: Slider(
                            value: _rating ?? 3.0,
                            min: 1.0,
                            max: 5.0,
                            divisions: 8,
                            label: _rating?.toStringAsFixed(1) ?? '3.0',
                            onChanged: (value) {
                              setState(() {
                                _rating = value;
                              });
                            },
                          ),
                        ),
                        Text(
                          '${_rating?.toStringAsFixed(1) ?? '3.0'} ⭐',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Obsah
            TextField(
              controller: _contentController,
              decoration: const InputDecoration(
                labelText: 'Obsah deníku',
                border: OutlineInputBorder(),
                alignLabelWithHint: true,
              ),
              maxLines: 10,
              minLines: 5,
            ),

            const SizedBox(height: 24),

            // Tlačítka pro média
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _addPhoto,
                    icon: const Icon(Icons.photo_camera),
                    label: const Text('Přidat foto'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF2E8B8B),
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _addVoiceNote,
                    icon: const Icon(Icons.mic),
                    label: const Text('Hlasová poznámka'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFF6B35),
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  void _addPhoto() {
    // Implementace přidání fotografie
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Funkce přidání fotografie bude brzy dostupná')),
    );
  }

  void _addVoiceNote() {
    // Implementace hlasové poznámky
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Funkce hlasové poznámky bude brzy dostupná')),
    );
  }

  void _saveEntry() {
    if (_titleController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Zadejte titulek záznamu')),
      );
      return;
    }

    if (_contentController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Zadejte obsah záznamu')),
      );
      return;
    }

    // Vytvoření nebo aktualizace záznamu
    final entry = DiaryEntry(
      id: widget.entry?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      title: _titleController.text,
      content: _contentController.text,
      date: _selectedDate,
      location: _locationController.text.isNotEmpty ? _locationController.text : null,
      mood: _selectedMood,
      rating: _rating,
      createdAt: widget.entry?.createdAt ?? DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // Zde by se záznam uložil do databáze
    Navigator.of(context).pop(entry);
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _locationController.dispose();
    super.dispose();
  }
}
