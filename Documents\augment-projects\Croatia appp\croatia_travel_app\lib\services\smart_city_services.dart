import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import '../models/smart_city_models.dart';

/// Kombinovaná služba pro všechny chytré město funkce
class SmartCityServices {
  static final SmartCityServices _instance = SmartCityServices._internal();
  factory SmartCityServices() => _instance;
  SmartCityServices._internal();

  final Dio _dio = Dio();

  /// Inicializace služby
  Future<void> initialize() async {
    _dio.options = BaseOptions(
      connectTimeout: const Duration(seconds: 15),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'User-Agent': 'CroatiaTravel/1.0 (Smart City)',
        'Accept': 'application/json',
      },
    );

    if (kDebugMode) {
      _dio.interceptors.add(
        LogInterceptor(
          requestBody: false,
          responseBody: false,
          logPrint: (obj) => debugPrint('[SmartCity] $obj'),
        ),
      );
    }
  }

  // ========== MĚSTSKÉ SLUŽBY ==========

  /// Získání městských služeb
  Future<List<CityService>> getCityServices({
    required String cityId,
    ServiceCategory? category,
    bool onlyOpen = false,
  }) async {
    try {
      final response = await _dio.get('/city-services/$cityId');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['services'] ?? [];
        var services = data.map((json) => CityService.fromJson(json)).toList();

        if (category != null) {
          services = services.where((s) => s.category == category).toList();
        }

        if (onlyOpen) {
          services = services.where((s) => s.isOpenNow).toList();
        }

        return services;
      }
    } catch (e) {
      debugPrint('Chyba při načítání městských služeb: $e');
    }

    return _generateMockCityServices(cityId);
  }

  /// Rezervace termínu u služby
  Future<ServiceAppointment?> bookAppointment({
    required String serviceId,
    required String userId,
    required DateTime preferredTime,
    required String serviceType,
    String? notes,
  }) async {
    try {
      final response = await _dio.post(
        '/city-services/appointments',
        data: {
          'service_id': serviceId,
          'user_id': userId,
          'preferred_time': preferredTime.toIso8601String(),
          'service_type': serviceType,
          if (notes != null) 'notes': notes,
        },
      );

      if (response.statusCode == 200) {
        return ServiceAppointment.fromJson(response.data);
      }
    } catch (e) {
      debugPrint('Chyba při rezervaci termínu: $e');
    }

    return _createMockAppointment(
      serviceId,
      userId,
      preferredTime,
      serviceType,
    );
  }

  /// Získání veřejných zařízení
  Future<List<PublicFacility>> getPublicFacilities({
    required String cityId,
    FacilityType? type,
    bool onlyOpen = false,
  }) async {
    try {
      final response = await _dio.get('/public-facilities/$cityId');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['facilities'] ?? [];
        var facilities = data
            .map((json) => PublicFacility.fromJson(json))
            .toList();

        if (type != null) {
          facilities = facilities.where((f) => f.type == type).toList();
        }

        return facilities;
      }
    } catch (e) {
      debugPrint('Chyba při načítání veřejných zařízení: $e');
    }

    return _generateMockPublicFacilities(cityId);
  }

  // ========== MOCK DATA GENERÁTORY ==========

  List<CityService> _generateMockCityServices(String cityId) {
    return [
      CityService(
        id: 'service_admin_1',
        name: 'Městský úřad - Občanské průkazy',
        description: 'Vydávání a obnovování občanských průkazů',
        category: ServiceCategory.administration,
        address: 'Trg bana Jelačića 1',
        latitude: 45.815,
        longitude: 15.982,
        phone: '+385 1 4814 444',
        email: '<EMAIL>',
        website: 'https://www.zagreb.hr/obcanske-prukazy',
        workingHours: [
          WorkingHours(
            dayOfWeek: 1,
            openHour: 8,
            openMinute: 0,
            closeHour: 16,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 2,
            openHour: 8,
            openMinute: 0,
            closeHour: 16,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 3,
            openHour: 8,
            openMinute: 0,
            closeHour: 16,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 4,
            openHour: 8,
            openMinute: 0,
            closeHour: 16,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 5,
            openHour: 8,
            openMinute: 0,
            closeHour: 16,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 6,
            openHour: 0,
            openMinute: 0,
            closeHour: 0,
            closeMinute: 0,
            isClosed: true,
          ),
          WorkingHours(
            dayOfWeek: 7,
            openHour: 0,
            openMinute: 0,
            closeHour: 0,
            closeMinute: 0,
            isClosed: true,
          ),
        ],
        services: [
          'Vydání občanského průkazu',
          'Obnovení občanského průkazu',
          'Ztráta/krádež',
        ],
        hasOnlineServices: true,
        requiresAppointment: true,
        rating: 4.2,
        reviewCount: 156,
      ),
      CityService(
        id: 'service_health_1',
        name: 'Poliklinika Centr',
        description: 'Základní zdravotní péče pro občany',
        category: ServiceCategory.health,
        address: 'Ilica 15',
        latitude: 45.817,
        longitude: 15.981,
        phone: '+385 1 4567 890',
        email: '<EMAIL>',
        workingHours: [
          WorkingHours(
            dayOfWeek: 1,
            openHour: 7,
            openMinute: 0,
            closeHour: 19,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 2,
            openHour: 7,
            openMinute: 0,
            closeHour: 19,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 3,
            openHour: 7,
            openMinute: 0,
            closeHour: 19,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 4,
            openHour: 7,
            openMinute: 0,
            closeHour: 19,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 5,
            openHour: 7,
            openMinute: 0,
            closeHour: 19,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 6,
            openHour: 8,
            openMinute: 0,
            closeHour: 14,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 7,
            openHour: 0,
            openMinute: 0,
            closeHour: 0,
            closeMinute: 0,
            isClosed: true,
          ),
        ],
        services: ['Praktický lékař', 'Zubař', 'Gynekologie', 'Pediatrie'],
        hasOnlineServices: false,
        requiresAppointment: true,
        rating: 4.5,
        reviewCount: 89,
      ),
    ];
  }

  List<PublicFacility> _generateMockPublicFacilities(String cityId) {
    return [
      PublicFacility(
        id: 'facility_library_1',
        name: 'Městská knihovna',
        description: 'Hlavní pobočka městské knihovny s rozsáhlou sbírkou',
        type: FacilityType.library,
        latitude: 45.818,
        longitude: 15.980,
        address: 'Starčevićev trg 6',
        workingHours: [
          WorkingHours(
            dayOfWeek: 1,
            openHour: 9,
            openMinute: 0,
            closeHour: 20,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 2,
            openHour: 9,
            openMinute: 0,
            closeHour: 20,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 3,
            openHour: 9,
            openMinute: 0,
            closeHour: 20,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 4,
            openHour: 9,
            openMinute: 0,
            closeHour: 20,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 5,
            openHour: 9,
            openMinute: 0,
            closeHour: 20,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 6,
            openHour: 9,
            openMinute: 0,
            closeHour: 15,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 7,
            openHour: 0,
            openMinute: 0,
            closeHour: 0,
            closeMinute: 0,
            isClosed: true,
          ),
        ],
        amenities: ['WiFi', 'Počítače', 'Studovna', 'Dětský koutek'],
        isAccessible: true,
        isFree: true,
        currency: 'HRK',
        rating: 4.6,
        reviewCount: 78,
        photos: [],
      ),
      PublicFacility(
        id: 'facility_park_1',
        name: 'Park Maksimir',
        description: 'Největší park ve městě s jezery a zoo',
        type: FacilityType.park,
        latitude: 45.830,
        longitude: 16.002,
        address: 'Maksimirski perivoj',
        workingHours: [
          WorkingHours(
            dayOfWeek: 1,
            openHour: 6,
            openMinute: 0,
            closeHour: 22,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 2,
            openHour: 6,
            openMinute: 0,
            closeHour: 22,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 3,
            openHour: 6,
            openMinute: 0,
            closeHour: 22,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 4,
            openHour: 6,
            openMinute: 0,
            closeHour: 22,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 5,
            openHour: 6,
            openMinute: 0,
            closeHour: 22,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 6,
            openHour: 6,
            openMinute: 0,
            closeHour: 22,
            closeMinute: 0,
          ),
          WorkingHours(
            dayOfWeek: 7,
            openHour: 6,
            openMinute: 0,
            closeHour: 22,
            closeMinute: 0,
          ),
        ],
        amenities: ['Dětské hřiště', 'Cyklostezky', 'Jezera', 'Zoo'],
        isAccessible: true,
        isFree: true,
        currency: 'HRK',
        rating: 4.8,
        reviewCount: 312,
        photos: [],
      ),
    ];
  }

  ServiceAppointment _createMockAppointment(
    String serviceId,
    String userId,
    DateTime preferredTime,
    String serviceType,
  ) {
    return ServiceAppointment(
      id: 'apt_${DateTime.now().millisecondsSinceEpoch}',
      serviceId: serviceId,
      userId: userId,
      appointmentTime: preferredTime,
      serviceType: serviceType,
      status: AppointmentStatus.confirmed,
      confirmationCode: 'APT${1000 + (serviceId.hashCode % 9000)}',
      createdAt: DateTime.now(),
    );
  }

  void dispose() {
    // Cleanup resources
  }
}

/// Termín u služby
class ServiceAppointment {
  final String id;
  final String serviceId;
  final String userId;
  final DateTime appointmentTime;
  final String serviceType;
  final AppointmentStatus status;
  final String confirmationCode;
  final String? notes;
  final DateTime createdAt;

  ServiceAppointment({
    required this.id,
    required this.serviceId,
    required this.userId,
    required this.appointmentTime,
    required this.serviceType,
    required this.status,
    required this.confirmationCode,
    this.notes,
    required this.createdAt,
  });

  factory ServiceAppointment.fromJson(Map<String, dynamic> json) {
    return ServiceAppointment(
      id: json['id'] ?? '',
      serviceId: json['service_id'] ?? '',
      userId: json['user_id'] ?? '',
      appointmentTime: DateTime.parse(json['appointment_time']),
      serviceType: json['service_type'] ?? '',
      status: AppointmentStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => AppointmentStatus.pending,
      ),
      confirmationCode: json['confirmation_code'] ?? '',
      notes: json['notes'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }
}

enum AppointmentStatus { pending, confirmed, completed, cancelled }
