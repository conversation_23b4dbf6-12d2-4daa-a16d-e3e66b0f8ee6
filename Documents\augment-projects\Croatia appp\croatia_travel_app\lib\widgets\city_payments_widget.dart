import 'package:flutter/material.dart';
import '../models/city_services.dart';
import '../services/city_services_service.dart';

class CityPaymentsWidget extends StatefulWidget {
  const CityPaymentsWidget({super.key});

  @override
  State<CityPaymentsWidget> createState() => _CityPaymentsWidgetState();
}

class _CityPaymentsWidgetState extends State<CityPaymentsWidget> {
  final CityServicesService _cityService = CityServicesService();

  List<CityPayment> _payments = [];
  List<CityPayment> _overduePayments = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadPayments();
  }

  Future<void> _loadPayments() async {
    try {
      setState(() => _isLoading = true);

      // V reálné aplikaci by se použilo ID uživatele
      final payments = await _cityService.getUserPayments();

      setState(() {
        _payments = payments;
        _overduePayments = payments.where((p) => p.isOverdue).toList();
      });
    } catch (e) {
      _showError('Chyba při načítání plateb: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _loadPayments,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildQuickActions(),
            const SizedBox(height: 24),
            if (_overduePayments.isNotEmpty) ...[
              _buildOverduePayments(),
              const SizedBox(height: 24),
            ],
            _buildPaymentsSummary(),
            const SizedBox(height: 24),
            _buildAllPayments(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Rychlé akce',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _payAllOverdue,
                    icon: const Icon(Icons.payment),
                    label: Text('Zaplatit vše (${_overduePayments.length})'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _overduePayments.isNotEmpty
                          ? Colors.red
                          : null,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _setupAutoPay,
                    icon: const Icon(Icons.autorenew),
                    label: const Text('Automatické platby'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOverduePayments() {
    return Card(
      color: Colors.red.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.warning, color: Colors.red),
                const SizedBox(width: 8),
                Text(
                  'Neuhrazené poplatky',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _overduePayments.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final payment = _overduePayments[index];
                return _buildPaymentTile(payment, isOverdue: true);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentsSummary() {
    final totalAmount = _payments.fold<double>(
      0,
      (sum, payment) =>
          payment.status == PaymentStatus.pending ? sum + payment.amount : sum,
    );
    final paidAmount = _payments.fold<double>(
      0,
      (sum, payment) =>
          payment.status == PaymentStatus.paid ? sum + payment.amount : sum,
    );

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Přehled plateb',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'K úhradě',
                    '${totalAmount.toStringAsFixed(2)} HRK',
                    Colors.orange,
                    Icons.schedule,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSummaryCard(
                    'Uhrazeno',
                    '${paidAmount.toStringAsFixed(2)} HRK',
                    Colors.green,
                    Icons.check_circle,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'Po splatnosti',
                    '${_overduePayments.length}',
                    Colors.red,
                    Icons.warning,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSummaryCard(
                    'Celkem plateb',
                    '${_payments.length}',
                    Colors.blue,
                    Icons.receipt,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAllPayments() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Všechny platby',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else if (_payments.isEmpty)
              const Text('Žádné platby')
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _payments.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final payment = _payments[index];
                  return _buildPaymentTile(payment);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentTile(CityPayment payment, {bool isOverdue = false}) {
    return ListTile(
      leading: Icon(
        _getPaymentCategoryIcon(payment.category),
        color: _getPaymentStatusColor(payment.status),
      ),
      title: Text(payment.title),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(payment.description),
          Text(
            'Splatnost: ${_formatDate(payment.dueDate)}',
            style: TextStyle(
              color: isOverdue ? Colors.red : null,
              fontWeight: isOverdue ? FontWeight.bold : null,
            ),
          ),
          Text('Stav: ${_getPaymentStatusText(payment.status)}'),
        ],
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            '${payment.amount} ${payment.currency}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: isOverdue ? Colors.red : null,
            ),
          ),
          if (payment.status == PaymentStatus.pending)
            ElevatedButton(
              onPressed: () => _payNow(payment),
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(80, 30),
                backgroundColor: isOverdue ? Colors.red : null,
              ),
              child: const Text('Zaplatit'),
            ),
        ],
      ),
      onTap: () => _showPaymentDetails(payment),
    );
  }

  IconData _getPaymentCategoryIcon(PaymentCategory category) {
    switch (category) {
      case PaymentCategory.communalWaste:
        return Icons.delete;
      case PaymentCategory.propertyTax:
        return Icons.home;
      case PaymentCategory.businessLicense:
        return Icons.business;
      case PaymentCategory.parking:
        return Icons.local_parking;
      case PaymentCategory.utilities:
        return Icons.electrical_services;
      case PaymentCategory.fines:
        return Icons.gavel;
      case PaymentCategory.other:
        return Icons.receipt;
    }
  }

  Color _getPaymentStatusColor(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.pending:
        return Colors.orange;
      case PaymentStatus.paid:
        return Colors.green;
      case PaymentStatus.overdue:
        return Colors.red;
      case PaymentStatus.cancelled:
        return Colors.grey;
      case PaymentStatus.refunded:
        return Colors.blue;
    }
  }

  String _getPaymentStatusText(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.pending:
        return 'Čeká na úhradu';
      case PaymentStatus.paid:
        return 'Uhrazeno';
      case PaymentStatus.overdue:
        return 'Po splatnosti';
      case PaymentStatus.cancelled:
        return 'Zrušeno';
      case PaymentStatus.refunded:
        return 'Vráceno';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}.${date.month}.${date.year}';
  }

  void _payAllOverdue() {
    if (_overduePayments.isEmpty) {
      _showError('Žádné neuhrazené poplatky');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Zaplatit všechny neuhrazené poplatky'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Celková částka: ${_overduePayments.fold<double>(0, (sum, p) => sum + p.amount).toStringAsFixed(2)} HRK',
            ),
            Text('Počet plateb: ${_overduePayments.length}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zrušit'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _processMultiplePayments(_overduePayments);
            },
            child: const Text('Zaplatit'),
          ),
        ],
      ),
    );
  }

  void _setupAutoPay() {
    // Implementace nastavení automatických plateb
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Otevírám nastavení automatických plateb...'),
      ),
    );
  }

  void _payNow(CityPayment payment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Zaplatit ${payment.title}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Částka: ${payment.amount} ${payment.currency}'),
            Text('Splatnost: ${_formatDate(payment.dueDate)}'),
            const SizedBox(height: 16),
            const Text('Vyberte způsob platby:'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zrušit'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _processPayment(payment, PaymentMethod.card);
            },
            child: const Text('Kartou'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _processPayment(payment, PaymentMethod.bankTransfer);
            },
            child: const Text('Převodem'),
          ),
        ],
      ),
    );
  }

  Future<void> _processPayment(
    CityPayment payment,
    PaymentMethod method,
  ) async {
    try {
      final success = await _cityService.payFee(payment.id, payment.amount);

      if (mounted && success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Platba ${payment.title} byla úspěšně provedena'),
          ),
        );
        _loadPayments(); // Obnovení seznamu
      } else if (mounted) {
        _showError('Platba se nezdařila');
      }
    } catch (e) {
      if (mounted) {
        _showError('Chyba při platbě: $e');
      }
    }
  }

  Future<void> _processMultiplePayments(List<CityPayment> payments) async {
    for (final payment in payments) {
      await _processPayment(payment, PaymentMethod.card);
    }
  }

  void _showPaymentDetails(CityPayment payment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(payment.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(payment.description),
            const SizedBox(height: 8),
            Text('Částka: ${payment.amount} ${payment.currency}'),
            Text('Splatnost: ${_formatDate(payment.dueDate)}'),
            Text('Stav: ${_getPaymentStatusText(payment.status)}'),
            if (payment.referenceNumber != null)
              Text('Referenční číslo: ${payment.referenceNumber}'),
            if (payment.isRecurring)
              Text(
                'Opakující se platba: ${payment.recurringPeriod?.inDays} dní',
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zavřít'),
          ),
          if (payment.status == PaymentStatus.pending)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _payNow(payment);
              },
              child: const Text('Zaplatit'),
            ),
        ],
      ),
    );
  }
}
