import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../services/camera_service.dart';
import '../models/camera_models.dart';

/// Obrazovka pro galerii cestovních fotografií
class PhotoGalleryScreen extends StatefulWidget {
  const PhotoGalleryScreen({super.key});

  @override
  State<PhotoGalleryScreen> createState() => _PhotoGalleryScreenState();
}

class _PhotoGalleryScreenState extends State<PhotoGalleryScreen> with TickerProviderStateMixin {
  final CameraService _cameraService = CameraService();
  late TabController _tabController;
  
  String _selectedFilter = 'all';
  bool _isGridView = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeService();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeService() async {
    try {
      await _cameraService.initialize();
      setState(() {});
    } catch (e) {
      debugPrint('Chyba při inicializaci camera služby: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: Text(
          'Cestovní fotografie',
          style: GoogleFonts.inter(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF2C3E50),
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: Color(0xFF2C3E50)),
        actions: [
          IconButton(
            icon: Icon(_isGridView ? Icons.view_list : Icons.grid_view),
            onPressed: () {
              setState(() {
                _isGridView = !_isGridView;
              });
            },
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.filter_list),
            onSelected: (value) {
              setState(() {
                _selectedFilter = value;
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'all',
                child: Text('Všechny fotky'),
              ),
              const PopupMenuItem(
                value: 'favorites',
                child: Text('Oblíbené'),
              ),
              const PopupMenuItem(
                value: 'geotagged',
                child: Text('S GPS'),
              ),
              const PopupMenuItem(
                value: 'croatia',
                child: Text('Z Chorvatska'),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: const Color(0xFF006994),
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: const Color(0xFF006994),
          labelStyle: GoogleFonts.inter(fontWeight: FontWeight.w600),
          tabs: const [
            Tab(text: 'Všechny'),
            Tab(text: 'Oblíbené'),
            Tab(text: 'Chorvatsko'),
            Tab(text: 'Statistiky'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllPhotosTab(),
          _buildFavoritesTab(),
          _buildCroatiaTab(),
          _buildStatsTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final photo = await _cameraService.pickFromGallery();
          if (photo != null) {
            setState(() {});
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Foto přidáno do galerie! 📸'),
                  backgroundColor: Color(0xFF4CAF50),
                ),
              );
            }
          }
        },
        backgroundColor: const Color(0xFF006994),
        child: const Icon(Icons.add_photo_alternate, color: Colors.white),
      ),
    );
  }

  Widget _buildAllPhotosTab() {
    return ListenableBuilder(
      listenable: _cameraService,
      builder: (context, child) {
        final photos = _getFilteredPhotos();
        
        if (photos.isEmpty) {
          return _buildEmptyState();
        }
        
        return _isGridView 
            ? _buildGridView(photos)
            : _buildListView(photos);
      },
    );
  }

  Widget _buildFavoritesTab() {
    return ListenableBuilder(
      listenable: _cameraService,
      builder: (context, child) {
        final photos = _cameraService.getFilteredPhotos(isFavorite: true);
        
        if (photos.isEmpty) {
          return _buildEmptyFavorites();
        }
        
        return _isGridView 
            ? _buildGridView(photos)
            : _buildListView(photos);
      },
    );
  }

  Widget _buildCroatiaTab() {
    return ListenableBuilder(
      listenable: _cameraService,
      builder: (context, child) {
        final photos = _cameraService.getCroatianPhotos();
        
        if (photos.isEmpty) {
          return _buildEmptyCroatia();
        }
        
        return _isGridView 
            ? _buildGridView(photos)
            : _buildListView(photos);
      },
    );
  }

  Widget _buildStatsTab() {
    return ListenableBuilder(
      listenable: _cameraService,
      builder: (context, child) {
        final stats = _cameraService.getPhotoStats();
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Statistiky fotografií',
                style: GoogleFonts.inter(
                  fontSize: 24,
                  fontWeight: FontWeight.w700,
                  color: const Color(0xFF2C3E50),
                ),
              ),
              const SizedBox(height: 20),
              
              _buildStatCard('Celkem fotografií', stats['total'].toString(), Icons.photo_library, const Color(0xFF3498DB)),
              _buildStatCard('Oblíbené', stats['favorites'].toString(), Icons.favorite, const Color(0xFFE74C3C)),
              _buildStatCard('S GPS značkami', stats['geotagged'].toString(), Icons.location_on, const Color(0xFF2ECC71)),
              _buildStatCard('Z Chorvatska', stats['croatian'].toString(), Icons.flag, const Color(0xFFFF6B35)),
              _buildStatCard('Nahrané do cloudu', stats['uploaded'].toString(), Icons.cloud_upload, const Color(0xFF9B59B6)),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value,
                    style: GoogleFonts.inter(
                      fontSize: 24,
                      fontWeight: FontWeight.w700,
                      color: const Color(0xFF2C3E50),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGridView(List<TravelPhoto> photos) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1,
      ),
      itemCount: photos.length,
      itemBuilder: (context, index) {
        final photo = photos[index];
        return _buildPhotoGridItem(photo);
      },
    );
  }

  Widget _buildListView(List<TravelPhoto> photos) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: photos.length,
      itemBuilder: (context, index) {
        final photo = photos[index];
        return _buildPhotoListItem(photo);
      },
    );
  }

  Widget _buildPhotoGridItem(TravelPhoto photo) {
    return GestureDetector(
      onTap: () => _showPhotoDetail(photo),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            fit: StackFit.expand,
            children: [
              Image.file(
                File(photo.filePath),
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey[300],
                    child: const Icon(Icons.broken_image, size: 48),
                  );
                },
              ),
              
              // Overlay s informacemi
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.bottomCenter,
                      end: Alignment.topCenter,
                      colors: [
                        Colors.black.withValues(alpha: 0.7),
                        Colors.transparent,
                      ],
                    ),
                  ),
                  child: Row(
                    children: [
                      if (photo.hasLocation)
                        const Icon(Icons.location_on, color: Colors.white, size: 16),
                      if (photo.isFavorite)
                        const Icon(Icons.favorite, color: Colors.red, size: 16),
                      const Spacer(),
                      Text(
                        _formatDate(photo.takenAt),
                        style: GoogleFonts.inter(
                          fontSize: 10,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPhotoListItem(TravelPhoto photo) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        contentPadding: const EdgeInsets.all(12),
        leading: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.file(
            File(photo.filePath),
            width: 60,
            height: 60,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                width: 60,
                height: 60,
                color: Colors.grey[300],
                child: const Icon(Icons.broken_image),
              );
            },
          ),
        ),
        title: Text(
          photo.title ?? 'Bez názvu',
          style: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF2C3E50),
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (photo.description != null) ...[
              const SizedBox(height: 4),
              Text(
                photo.description!,
                style: GoogleFonts.inter(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
            const SizedBox(height: 4),
            Row(
              children: [
                Text(
                  _formatDate(photo.takenAt),
                  style: GoogleFonts.inter(
                    fontSize: 10,
                    color: Colors.grey[500],
                  ),
                ),
                if (photo.hasLocation) ...[
                  const SizedBox(width: 8),
                  const Icon(Icons.location_on, size: 12, color: Colors.grey),
                ],
                if (photo.isFavorite) ...[
                  const SizedBox(width: 8),
                  const Icon(Icons.favorite, size: 12, color: Colors.red),
                ],
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton(
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'favorite',
              child: Row(
                children: [
                  Icon(photo.isFavorite ? Icons.favorite_border : Icons.favorite),
                  const SizedBox(width: 8),
                  Text(photo.isFavorite ? 'Odebrat z oblíbených' : 'Přidat do oblíbených'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit),
                  SizedBox(width: 8),
                  Text('Upravit'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Smazat', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
          onSelected: (value) => _handlePhotoAction(photo, value.toString()),
        ),
        onTap: () => _showPhotoDetail(photo),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.photo_library_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Žádné fotografie',
            style: GoogleFonts.inter(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Přidejte první fotografii pomocí tlačítka +',
            style: GoogleFonts.inter(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyFavorites() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_border,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Žádné oblíbené fotografie',
            style: GoogleFonts.inter(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Označte fotografie jako oblíbené pomocí ❤️',
            style: GoogleFonts.inter(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyCroatia() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.flag_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Žádné fotografie z Chorvatska',
            style: GoogleFonts.inter(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Fotografie pořízené v Chorvatsku se zobrazí zde automaticky',
            style: GoogleFonts.inter(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  List<TravelPhoto> _getFilteredPhotos() {
    switch (_selectedFilter) {
      case 'favorites':
        return _cameraService.getFilteredPhotos(isFavorite: true);
      case 'geotagged':
        return _cameraService.getFilteredPhotos(hasLocation: true);
      case 'croatia':
        return _cameraService.getCroatianPhotos();
      default:
        return _cameraService.photos;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}.${date.month}.${date.year}';
  }

  void _handlePhotoAction(TravelPhoto photo, String action) {
    switch (action) {
      case 'favorite':
        _cameraService.toggleFavorite(photo.id);
        break;
      case 'edit':
        _showEditDialog(photo);
        break;
      case 'delete':
        _showDeleteDialog(photo);
        break;
    }
  }

  void _showPhotoDetail(TravelPhoto photo) {
    // TODO: Implementovat detail fotografie
  }

  void _showEditDialog(TravelPhoto photo) {
    // TODO: Implementovat editaci fotografie
  }

  void _showDeleteDialog(TravelPhoto photo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Smazat fotografii'),
        content: const Text('Opravdu chcete smazat tuto fotografii? Tato akce je nevratná.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Zrušit'),
          ),
          TextButton(
            onPressed: () {
              _cameraService.deletePhoto(photo.id);
              Navigator.pop(context);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Smazat'),
          ),
        ],
      ),
    );
  }
}
