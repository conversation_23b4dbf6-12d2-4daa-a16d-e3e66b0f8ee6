import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/achievement_system.dart';

/// 🏆 ACHIEVEMENT SYSTEM SERVICE - Systé<PERSON> úspěchů a gamifikace
class AchievementSystemService {
  static final AchievementSystemService _instance =
      AchievementSystemService._internal();
  factory AchievementSystemService() => _instance;
  AchievementSystemService._internal();

  bool _isInitialized = false;
  final List<Achievement> _allAchievements = [];
  final List<Achievement> _unlockedAchievements = [];
  final List<Challenge> _activeChallenges = [];
  final List<Challenge> _completedChallenges = [];
  UserProgress _userProgress = UserProgress.empty();
  final StreamController<AchievementEvent> _eventController =
      StreamController.broadcast();

  /// Stream událostí achievementů
  Stream<AchievementEvent> get achievementEvents => _eventController.stream;

  /// Inicializace služby
  Future<void> initialize(String userId) async {
    if (_isInitialized) return;

    try {
      debugPrint('🏆 Inicializuji Achievement System Service...');

      await _loadAchievements();
      await _loadUserProgress(userId);
      await _loadChallenges();

      _isInitialized = true;
      debugPrint('✅ Achievement System Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Achievement System: $e');
      await _createDefaultAchievements();
      _isInitialized = true;
    }
  }

  /// Kontrola a odemknutí achievementů na základě akce
  Future<List<Achievement>> checkAchievements({
    required AchievementTrigger trigger,
    Map<String, dynamic>? data,
  }) async {
    final newlyUnlocked = <Achievement>[];

    for (final achievement in _allAchievements) {
      if (_unlockedAchievements.any((a) => a.id == achievement.id)) continue;

      if (achievement.trigger == trigger) {
        final isUnlocked = await _checkAchievementCondition(achievement, data);

        if (isUnlocked) {
          await _unlockAchievement(achievement);
          newlyUnlocked.add(achievement);
        }
      }
    }

    return newlyUnlocked;
  }

  /// Odemknutí achievementu
  Future<void> _unlockAchievement(Achievement achievement) async {
    _unlockedAchievements.add(achievement);

    // Přidání XP
    _userProgress = _userProgress.copyWith(
      totalXP: _userProgress.totalXP + achievement.xpReward,
      level: _calculateLevel(_userProgress.totalXP + achievement.xpReward),
    );

    // Uložení pokroku
    await _saveUserProgress();

    // Odeslání události
    _eventController.add(
      AchievementEvent(
        type: AchievementEventType.unlocked,
        achievement: achievement,
        timestamp: DateTime.now(),
      ),
    );

    debugPrint(
      '🏆 Achievement odemčen: ${achievement.name} (+${achievement.xpReward} XP)',
    );
  }

  /// Kontrola podmínky achievementu
  Future<bool> _checkAchievementCondition(
    Achievement achievement,
    Map<String, dynamic>? data,
  ) async {
    switch (achievement.trigger) {
      case AchievementTrigger.firstEntry:
        return data?['entryCount'] == 1;

      case AchievementTrigger.entryCount:
        final count = data?['entryCount'] ?? 0;
        return count >= achievement.targetValue;

      case AchievementTrigger.streak:
        final streak = data?['streak'] ?? 0;
        return streak >= achievement.targetValue;

      case AchievementTrigger.wordCount:
        final totalWords = data?['totalWords'] ?? 0;
        return totalWords >= achievement.targetValue;

      case AchievementTrigger.photoCount:
        final photoCount = data?['photoCount'] ?? 0;
        return photoCount >= achievement.targetValue;

      case AchievementTrigger.locationCount:
        final locationCount = data?['locationCount'] ?? 0;
        return locationCount >= achievement.targetValue;

      case AchievementTrigger.moodStreak:
        final moodStreak = data?['moodStreak'] ?? 0;
        return moodStreak >= achievement.targetValue;

      case AchievementTrigger.socialShare:
        final shareCount = data?['shareCount'] ?? 0;
        return shareCount >= achievement.targetValue;

      case AchievementTrigger.challengeComplete:
        return data?['challengeCompleted'] == true;

      case AchievementTrigger.perfectWeek:
        return data?['perfectWeek'] == true;

      default:
        return false;
    }
  }

  /// Vytvoření nové výzvy
  Future<Challenge> createChallenge({
    required String name,
    required String description,
    required ChallengeType type,
    required int targetValue,
    required Duration duration,
    int xpReward = 100,
    List<String>? tags,
  }) async {
    final challenge = Challenge(
      id: 'challenge_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      description: description,
      type: type,
      targetValue: targetValue,
      currentProgress: 0,
      xpReward: xpReward,
      startDate: DateTime.now(),
      endDate: DateTime.now().add(duration),
      isActive: true,
      tags: tags ?? [],
    );

    _activeChallenges.add(challenge);
    await _saveChallenges();

    _eventController.add(
      AchievementEvent(
        type: AchievementEventType.challengeStarted,
        challenge: challenge,
        timestamp: DateTime.now(),
      ),
    );

    return challenge;
  }

  /// Aktualizace pokroku výzvy
  Future<void> updateChallengeProgress({
    required String challengeId,
    required int progress,
  }) async {
    final challengeIndex = _activeChallenges.indexWhere(
      (c) => c.id == challengeId,
    );
    if (challengeIndex == -1) return;

    final challenge = _activeChallenges[challengeIndex];
    final updatedChallenge = challenge.copyWith(
      currentProgress: min(progress, challenge.targetValue),
    );

    _activeChallenges[challengeIndex] = updatedChallenge;

    // Kontrola dokončení
    if (updatedChallenge.isCompleted && !challenge.isCompleted) {
      await _completeChallenge(updatedChallenge);
    }

    await _saveChallenges();
  }

  /// Dokončení výzvy
  Future<void> _completeChallenge(Challenge challenge) async {
    // Přesun do dokončených
    _activeChallenges.removeWhere((c) => c.id == challenge.id);
    _completedChallenges.add(challenge);

    // Přidání XP
    _userProgress = _userProgress.copyWith(
      totalXP: _userProgress.totalXP + challenge.xpReward,
      level: _calculateLevel(_userProgress.totalXP + challenge.xpReward),
      challengesCompleted: _userProgress.challengesCompleted + 1,
    );

    await _saveUserProgress();

    // Kontrola achievementů
    await checkAchievements(
      trigger: AchievementTrigger.challengeComplete,
      data: {'challengeCompleted': true},
    );

    _eventController.add(
      AchievementEvent(
        type: AchievementEventType.challengeCompleted,
        challenge: challenge,
        timestamp: DateTime.now(),
      ),
    );

    debugPrint(
      '🎯 Výzva dokončena: ${challenge.name} (+${challenge.xpReward} XP)',
    );
  }

  /// Získání denních výzev
  Future<List<Challenge>> getDailyChallenges() async {
    final today = DateTime.now();
    final dailyChallenges = <Challenge>[];

    // Generování denních výzev pokud neexistují
    final existingDaily = _activeChallenges
        .where(
          (c) =>
              c.type == ChallengeType.daily &&
              c.startDate.day == today.day &&
              c.startDate.month == today.month &&
              c.startDate.year == today.year,
        )
        .toList();

    if (existingDaily.isEmpty) {
      dailyChallenges.addAll(await _generateDailyChallenges());
    } else {
      dailyChallenges.addAll(existingDaily);
    }

    return dailyChallenges;
  }

  /// Generování denních výzev
  Future<List<Challenge>> _generateDailyChallenges() async {
    final challenges = <Challenge>[];

    // Výzva 1: Napsat zápis
    challenges.add(
      await createChallenge(
        name: 'Denní zápis',
        description: 'Napište alespoň jeden zápis dnes',
        type: ChallengeType.daily,
        targetValue: 1,
        duration: const Duration(days: 1),
        xpReward: 50,
        tags: ['denní', 'psaní'],
      ),
    );

    // Výzva 2: Přidat fotku
    challenges.add(
      await createChallenge(
        name: 'Zachyťte moment',
        description: 'Přidejte fotografii k dnešnímu zápisu',
        type: ChallengeType.daily,
        targetValue: 1,
        duration: const Duration(days: 1),
        xpReward: 30,
        tags: ['denní', 'fotografie'],
      ),
    );

    // Výzva 3: Napsat více slov
    challenges.add(
      await createChallenge(
        name: 'Detailní popis',
        description: 'Napište alespoň 100 slov',
        type: ChallengeType.daily,
        targetValue: 100,
        duration: const Duration(days: 1),
        xpReward: 40,
        tags: ['denní', 'slova'],
      ),
    );

    return challenges;
  }

  /// Získání týdenních výzev
  Future<List<Challenge>> getWeeklyChallenges() async {
    return _activeChallenges
        .where((c) => c.type == ChallengeType.weekly)
        .toList();
  }

  /// Získání leaderboardu
  Future<Leaderboard> getLeaderboard({
    LeaderboardType type = LeaderboardType.totalXP,
    LeaderboardPeriod period = LeaderboardPeriod.allTime,
  }) async {
    // Simulace leaderboardu - v produkci by se načítalo ze serveru
    final entries = <LeaderboardEntry>[
      LeaderboardEntry(
        userId: 'user_1',
        username: 'TravelWriter',
        score: 2500,
        rank: 1,
        avatar: '👤',
      ),
      LeaderboardEntry(
        userId: 'current_user',
        username: 'Já',
        score: _userProgress.totalXP,
        rank: 2,
        avatar: '🙋‍♂️',
      ),
      LeaderboardEntry(
        userId: 'user_3',
        username: 'AdventureSeeker',
        score: 1800,
        rank: 3,
        avatar: '🧗‍♀️',
      ),
    ];

    return Leaderboard(
      type: type,
      period: period,
      entries: entries,
      userRank: 2,
      totalParticipants: 150,
      lastUpdated: DateTime.now(),
    );
  }

  /// Výpočet levelu na základě XP
  int _calculateLevel(int xp) {
    // Exponenciální růst: level = sqrt(xp / 100)
    return (sqrt(xp / 100)).floor() + 1;
  }

  /// Výpočet XP potřebného pro další level
  int getXPForNextLevel(int currentLevel) {
    return (pow(currentLevel, 2) * 100).toInt();
  }

  /// Získání pokroku k dalšímu levelu
  double getLevelProgress() {
    final currentLevel = _userProgress.level;
    final currentXP = _userProgress.totalXP;
    final xpForCurrentLevel = getXPForNextLevel(currentLevel - 1);
    final xpForNextLevel = getXPForNextLevel(currentLevel);

    final progressXP = currentXP - xpForCurrentLevel;
    final neededXP = xpForNextLevel - xpForCurrentLevel;

    return neededXP > 0 ? progressXP / neededXP : 0.0;
  }

  /// Načítání a ukládání dat
  Future<void> _loadAchievements() async {
    await _createDefaultAchievements();
  }

  Future<void> _loadUserProgress(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressJson = prefs.getString('user_progress_$userId');

      if (progressJson != null) {
        final data = jsonDecode(progressJson);
        _userProgress = UserProgress.fromJson(data);
      }
    } catch (e) {
      debugPrint('❌ Chyba při načítání pokroku: $e');
    }
  }

  Future<void> _saveUserProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'user_progress_${_userProgress.userId}',
        jsonEncode(_userProgress.toJson()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání pokroku: $e');
    }
  }

  Future<void> _loadChallenges() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final challengesJson = prefs.getString('active_challenges');

      if (challengesJson != null) {
        final List<dynamic> data = jsonDecode(challengesJson);
        _activeChallenges.clear();
        _activeChallenges.addAll(
          data.map((json) => Challenge.fromJson(json)).toList(),
        );
      }
    } catch (e) {
      debugPrint('❌ Chyba při načítání výzev: $e');
    }
  }

  Future<void> _saveChallenges() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'active_challenges',
        jsonEncode(_activeChallenges.map((c) => c.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání výzev: $e');
    }
  }

  Future<void> _createDefaultAchievements() async {
    _allAchievements.addAll([
      // Milníky
      Achievement(
        id: 'first_entry',
        name: 'První krok',
        description: 'Napište svůj první zápis do deníku',
        icon: '🌟',
        category: AchievementCategory.milestone,
        rarity: AchievementRarity.common,
        xpReward: 100,
        trigger: AchievementTrigger.firstEntry,
        targetValue: 1,
      ),
      Achievement(
        id: 'entries_10',
        name: 'Začátečník',
        description: 'Napište 10 zápisů',
        icon: '📝',
        category: AchievementCategory.milestone,
        rarity: AchievementRarity.common,
        xpReward: 200,
        trigger: AchievementTrigger.entryCount,
        targetValue: 10,
      ),
      Achievement(
        id: 'entries_100',
        name: 'Zkušený pisatel',
        description: 'Napište 100 zápisů',
        icon: '📚',
        category: AchievementCategory.milestone,
        rarity: AchievementRarity.uncommon,
        xpReward: 500,
        trigger: AchievementTrigger.entryCount,
        targetValue: 100,
      ),

      // Konzistence
      Achievement(
        id: 'streak_7',
        name: 'Týdenní série',
        description: 'Pište 7 dní v řadě',
        icon: '🔥',
        category: AchievementCategory.consistency,
        rarity: AchievementRarity.uncommon,
        xpReward: 300,
        trigger: AchievementTrigger.streak,
        targetValue: 7,
      ),
      Achievement(
        id: 'streak_30',
        name: 'Měsíční mistr',
        description: 'Pište 30 dní v řadě',
        icon: '🏆',
        category: AchievementCategory.consistency,
        rarity: AchievementRarity.rare,
        xpReward: 1000,
        trigger: AchievementTrigger.streak,
        targetValue: 30,
      ),

      // Obsah
      Achievement(
        id: 'words_10000',
        name: 'Mistr slov',
        description: 'Napište celkem 10,000 slov',
        icon: '✍️',
        category: AchievementCategory.content,
        rarity: AchievementRarity.rare,
        xpReward: 800,
        trigger: AchievementTrigger.wordCount,
        targetValue: 10000,
      ),
      Achievement(
        id: 'photos_50',
        name: 'Fotograf',
        description: 'Přidejte 50 fotografií',
        icon: '📸',
        category: AchievementCategory.content,
        rarity: AchievementRarity.uncommon,
        xpReward: 400,
        trigger: AchievementTrigger.photoCount,
        targetValue: 50,
      ),

      // Objevování
      Achievement(
        id: 'locations_20',
        name: 'Světoběžník',
        description: 'Navštivte 20 různých míst',
        icon: '🌍',
        category: AchievementCategory.exploration,
        rarity: AchievementRarity.rare,
        xpReward: 600,
        trigger: AchievementTrigger.locationCount,
        targetValue: 20,
      ),
    ]);
  }

  void dispose() {
    _eventController.close();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  UserProgress get userProgress => _userProgress;
  List<Achievement> get unlockedAchievements =>
      List.unmodifiable(_unlockedAchievements);
  List<Achievement> get allAchievements => List.unmodifiable(_allAchievements);
  List<Challenge> get activeChallenges => List.unmodifiable(_activeChallenges);
  List<Challenge> get completedChallenges =>
      List.unmodifiable(_completedChallenges);
}
