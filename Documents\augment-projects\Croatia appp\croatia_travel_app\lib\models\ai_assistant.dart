/// AI Asistent pro cestovní poradenství
class AIAssistant {
  final String id;
  final String name;
  final String description;
  final List<String> capabilities;
  final List<String> supportedLanguages;
  final AIPersonality personality;
  final bool isOnline;
  final DateTime lastUpdated;

  AIAssistant({
    required this.id,
    required this.name,
    required this.description,
    required this.capabilities,
    required this.supportedLanguages,
    required this.personality,
    required this.isOnline,
    required this.lastUpdated,
  });
}

/// Osobnost AI asistenta
enum AIPersonality {
  friendly, // Přátelský
  professional, // Profesionální
  casual, // Neformální
  expert, // <PERSON>d<PERSON><PERSON>
  humorous, // Vtipný
}

/// Konverzace s AI asistentem
class AIConversation {
  final String id;
  final String userId;
  final String title;
  final List<AIMessage> messages;
  final ConversationContext context;
  final DateTime createdAt;
  final DateTime lastMessageAt;
  final bool isActive;

  AIConversation({
    required this.id,
    required this.userId,
    required this.title,
    required this.messages,
    required this.context,
    required this.createdAt,
    required this.lastMessageAt,
    required this.isActive,
  });

  /// Přidá novou zprávu do konverzace
  void addMessage(AIMessage message) {
    messages.add(message);
  }

  /// Získá poslední zprávu
  AIMessage? get lastMessage {
    return messages.isNotEmpty ? messages.last : null;
  }

  /// Počet zpráv v konverzaci
  int get messageCount => messages.length;
}

/// Zpráva v konverzaci
class AIMessage {
  final String id;
  final String conversationId;
  final MessageSender sender;
  final String content;
  final MessageType type;
  final List<AIAttachment>? attachments;
  final AIMessageMetadata? metadata;
  final DateTime timestamp;
  final bool isRead;

  AIMessage({
    required this.id,
    required this.conversationId,
    required this.sender,
    required this.content,
    required this.type,
    this.attachments,
    this.metadata,
    required this.timestamp,
    required this.isRead,
  });

  /// Je zpráva od uživatele?
  bool get isFromUser => sender == MessageSender.user;

  /// Je zpráva od AI?
  bool get isFromAI => sender == MessageSender.ai;
}

/// Odesílatel zprávy
enum MessageSender {
  user, // Uživatel
  ai, // AI asistent
  system, // Systém
}

/// Typ zprávy
enum MessageType {
  text, // Textová zpráva
  voice, // Hlasová zpráva
  image, // Obrázek
  location, // Lokace
  recommendation, // Doporučení
  booking, // Rezervace
  emergency, // Nouzová situace
}

/// Příloha ke zprávě
class AIAttachment {
  final String id;
  final AttachmentType type;
  final String url;
  final String? title;
  final String? description;
  final Map<String, dynamic>? metadata;

  AIAttachment({
    required this.id,
    required this.type,
    required this.url,
    this.title,
    this.description,
    this.metadata,
  });
}

/// Typ přílohy
enum AttachmentType {
  image, // Obrázek
  audio, // Audio
  video, // Video
  document, // Dokument
  location, // Lokace
  place, // Místo
  route, // Trasa
}

/// Metadata zprávy
class AIMessageMetadata {
  final double? confidence;
  final String? intent;
  final Map<String, dynamic>? entities;
  final String? language;
  final AIResponseSource? source;
  final Duration? processingTime;

  AIMessageMetadata({
    this.confidence,
    this.intent,
    this.entities,
    this.language,
    this.source,
    this.processingTime,
  });
}

/// Zdroj AI odpovědi
enum AIResponseSource {
  knowledge, // Znalostní báze
  realtime, // Real-time data
  external, // Externí API
  cached, // Cache
}

/// Kontext konverzace
class ConversationContext {
  final String? currentLocation;
  final double? latitude;
  final double? longitude;
  final String? currentCity;
  final String? currentRegion;
  final List<String> userPreferences;
  final Map<String, dynamic> sessionData;
  final String? travelPlan;
  final DateTime? tripStartDate;
  final DateTime? tripEndDate;

  ConversationContext({
    this.currentLocation,
    this.latitude,
    this.longitude,
    this.currentCity,
    this.currentRegion,
    required this.userPreferences,
    required this.sessionData,
    this.travelPlan,
    this.tripStartDate,
    this.tripEndDate,
  });

  /// Má uživatel nastavenou lokaci?
  bool get hasLocation => latitude != null && longitude != null;

  /// Je uživatel na cestách?
  bool get isOnTrip {
    if (tripStartDate == null || tripEndDate == null) return false;
    final now = DateTime.now();
    return now.isAfter(tripStartDate!) && now.isBefore(tripEndDate!);
  }
}

/// AI doporučení
class AIRecommendation {
  final String id;
  final String title;
  final String description;
  final RecommendationType type;
  final double confidence;
  final String? placeId;
  final String? imageUrl;
  final double? rating;
  final String? priceRange;
  final double? distance;
  final Map<String, dynamic> details;
  final DateTime createdAt;

  AIRecommendation({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.confidence,
    this.placeId,
    this.imageUrl,
    this.rating,
    this.priceRange,
    this.distance,
    required this.details,
    required this.createdAt,
  });
}

/// Typ doporučení
enum RecommendationType {
  restaurant, // Restaurace
  accommodation, // Ubytování
  attraction, // Atrakce
  activity, // Aktivita
  route, // Trasa
  event, // Událost
  shopping, // Nákupy
  nightlife, // Noční život
}

/// AI schopnosti
class AICapability {
  final String id;
  final String name;
  final String description;
  final CapabilityType type;
  final bool isEnabled;
  final Map<String, dynamic> configuration;

  AICapability({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.isEnabled,
    required this.configuration,
  });
}

/// Typ schopnosti AI
enum CapabilityType {
  textGeneration, // Generování textu
  translation, // Překlad
  voiceRecognition, // Rozpoznávání řeči
  voiceSynthesis, // Syntéza řeči
  imageAnalysis, // Analýza obrázků
  locationServices, // Lokační služby
  recommendations, // Doporučení
  planning, // Plánování
  booking, // Rezervace
  emergency, // Nouzové služby
}

/// Hlasové ovládání
class VoiceCommand {
  final String id;
  final String transcript;
  final String? normalizedText;
  final double confidence;
  final String language;
  final VoiceCommandType type;
  final Map<String, dynamic>? parameters;
  final DateTime timestamp;

  VoiceCommand({
    required this.id,
    required this.transcript,
    this.normalizedText,
    required this.confidence,
    required this.language,
    required this.type,
    this.parameters,
    required this.timestamp,
  });
}

/// Typ hlasového příkazu
enum VoiceCommandType {
  search, // Vyhledávání
  navigation, // Navigace
  booking, // Rezervace
  information, // Informace
  translation, // Překlad
  emergency, // Nouzová situace
  general, // Obecný dotaz
}
