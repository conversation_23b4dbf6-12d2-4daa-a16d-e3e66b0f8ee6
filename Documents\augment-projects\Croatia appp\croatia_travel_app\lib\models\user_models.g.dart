// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserProfile _$UserProfileFromJson(Map<String, dynamic> json) => UserProfile(
      id: json['id'] as String,
      email: json['email'] as String?,
      displayName: json['displayName'] as String?,
      photoUrl: json['photoUrl'] as String?,
      travelPreferences: (json['travelPreferences'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$TravelTypeEnumMap, e))
              .toList() ??
          const [],
      ageGroup: $enumDecodeNullable(_$AgeGroupEnumMap, json['ageGroup']),
      budgetCategory:
          $enumDecodeNullable(_$BudgetCategoryEnumMap, json['budgetCategory']),
      languages: (json['languages'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const ['cs'],
      visitedPlaces: (json['visitedPlaces'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      wishlist: (json['wishlist'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      settings: json['settings'] as Map<String, dynamic>? ?? const {},
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastActiveAt: DateTime.parse(json['lastActiveAt'] as String),
      isVerified: json['isVerified'] as bool? ?? false,
    );

Map<String, dynamic> _$UserProfileToJson(UserProfile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'displayName': instance.displayName,
      'photoUrl': instance.photoUrl,
      'travelPreferences': instance.travelPreferences
          .map((e) => _$TravelTypeEnumMap[e]!)
          .toList(),
      'ageGroup': _$AgeGroupEnumMap[instance.ageGroup],
      'budgetCategory': _$BudgetCategoryEnumMap[instance.budgetCategory],
      'languages': instance.languages,
      'visitedPlaces': instance.visitedPlaces,
      'wishlist': instance.wishlist,
      'settings': instance.settings,
      'createdAt': instance.createdAt.toIso8601String(),
      'lastActiveAt': instance.lastActiveAt.toIso8601String(),
      'isVerified': instance.isVerified,
    };

const _$TravelTypeEnumMap = {
  TravelType.culture: 'culture',
  TravelType.nature: 'nature',
  TravelType.beach: 'beach',
  TravelType.adventure: 'adventure',
  TravelType.food: 'food',
  TravelType.nightlife: 'nightlife',
  TravelType.family: 'family',
  TravelType.romantic: 'romantic',
  TravelType.business: 'business',
  TravelType.backpacking: 'backpacking',
};

const _$AgeGroupEnumMap = {
  AgeGroup.young: 'young',
  AgeGroup.adult: 'adult',
  AgeGroup.middleAge: 'middleAge',
  AgeGroup.senior: 'senior',
};

const _$BudgetCategoryEnumMap = {
  BudgetCategory.budget: 'budget',
  BudgetCategory.mid: 'mid',
  BudgetCategory.luxury: 'luxury',
};

PersonalizedRecommendation _$PersonalizedRecommendationFromJson(
        Map<String, dynamic> json) =>
    PersonalizedRecommendation(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: json['type'] as String,
      imageUrl: json['imageUrl'] as String?,
      score: (json['score'] as num).toDouble(),
      reasons: (json['reasons'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      data: json['data'] as Map<String, dynamic>? ?? const {},
      createdAt: DateTime.parse(json['createdAt'] as String),
      isViewed: json['isViewed'] as bool? ?? false,
      isLiked: json['isLiked'] as bool? ?? false,
      isBookmarked: json['isBookmarked'] as bool? ?? false,
    );

Map<String, dynamic> _$PersonalizedRecommendationToJson(
        PersonalizedRecommendation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'type': instance.type,
      'imageUrl': instance.imageUrl,
      'score': instance.score,
      'reasons': instance.reasons,
      'data': instance.data,
      'createdAt': instance.createdAt.toIso8601String(),
      'isViewed': instance.isViewed,
      'isLiked': instance.isLiked,
      'isBookmarked': instance.isBookmarked,
    };

TravelItinerary _$TravelItineraryFromJson(Map<String, dynamic> json) =>
    TravelItinerary(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      days: (json['days'] as List<dynamic>?)
              ?.map((e) => ItineraryDay.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      totalBudget: (json['totalBudget'] as num?)?.toDouble(),
      coverImageUrl: json['coverImageUrl'] as String?,
      isPublic: json['isPublic'] as bool? ?? false,
      isGenerated: json['isGenerated'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$TravelItineraryToJson(TravelItinerary instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'days': instance.days,
      'tags': instance.tags,
      'totalBudget': instance.totalBudget,
      'coverImageUrl': instance.coverImageUrl,
      'isPublic': instance.isPublic,
      'isGenerated': instance.isGenerated,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

ItineraryDay _$ItineraryDayFromJson(Map<String, dynamic> json) => ItineraryDay(
      id: json['id'] as String,
      date: DateTime.parse(json['date'] as String),
      title: json['title'] as String?,
      activities: (json['activities'] as List<dynamic>?)
              ?.map(
                  (e) => ItineraryActivity.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$ItineraryDayToJson(ItineraryDay instance) =>
    <String, dynamic>{
      'id': instance.id,
      'date': instance.date.toIso8601String(),
      'title': instance.title,
      'activities': instance.activities,
      'notes': instance.notes,
    };

ItineraryActivity _$ItineraryActivityFromJson(Map<String, dynamic> json) =>
    ItineraryActivity(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: json['endTime'] == null
          ? null
          : DateTime.parse(json['endTime'] as String),
      location: json['location'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      type: json['type'] as String,
      estimatedCost: (json['estimatedCost'] as num?)?.toDouble(),
      bookingUrl: json['bookingUrl'] as String?,
      isCompleted: json['isCompleted'] as bool? ?? false,
    );

Map<String, dynamic> _$ItineraryActivityToJson(ItineraryActivity instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime?.toIso8601String(),
      'location': instance.location,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'type': instance.type,
      'estimatedCost': instance.estimatedCost,
      'bookingUrl': instance.bookingUrl,
      'isCompleted': instance.isCompleted,
    };
