/// 🚌 MODEL PRO DOPRAVNÍ SPOJENÍ
class TransportConnection {
  final String id;
  final String fromCity;
  final String toCity;
  final String fromStation;
  final String toStation;
  final TransportType type;
  final String operatorName;
  final String operatorWebsite;
  final String operatorPhone;
  final Duration duration;
  final double? price;
  final String currency;
  final List<String> departureTimes;
  final List<String> arrivalTimes;
  final Map<String, bool> operatingDays;
  final bool isDirectConnection;
  final List<String>? intermediateStops;
  final String? notes;
  final bool bookingRequired;
  final String? bookingUrl;
  final double? distance;
  final Map<String, dynamic>? additionalInfo;

  TransportConnection({
    required this.id,
    required this.fromCity,
    required this.toCity,
    required this.fromStation,
    required this.toStation,
    required this.type,
    required this.operatorName,
    required this.operatorWebsite,
    required this.operatorPhone,
    required this.duration,
    this.price,
    this.currency = 'EUR',
    required this.departureTimes,
    required this.arrivalTimes,
    required this.operatingDays,
    this.isDirectConnection = true,
    this.intermediateStops,
    this.notes,
    this.bookingRequired = false,
    this.bookingUrl,
    this.distance,
    this.additionalInfo,
  });

  /// Převod na JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fromCity': fromCity,
      'toCity': toCity,
      'fromStation': fromStation,
      'toStation': toStation,
      'type': type.toString(),
      'operatorName': operatorName,
      'operatorWebsite': operatorWebsite,
      'operatorPhone': operatorPhone,
      'duration': duration.inMinutes,
      'price': price,
      'currency': currency,
      'departureTimes': departureTimes,
      'arrivalTimes': arrivalTimes,
      'operatingDays': operatingDays,
      'isDirectConnection': isDirectConnection,
      'intermediateStops': intermediateStops,
      'notes': notes,
      'bookingRequired': bookingRequired,
      'bookingUrl': bookingUrl,
      'distance': distance,
      'additionalInfo': additionalInfo,
    };
  }

  /// Vytvoření z JSON
  factory TransportConnection.fromJson(Map<String, dynamic> json) {
    return TransportConnection(
      id: json['id'],
      fromCity: json['fromCity'],
      toCity: json['toCity'],
      fromStation: json['fromStation'],
      toStation: json['toStation'],
      type: TransportType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => TransportType.bus,
      ),
      operatorName: json['operatorName'],
      operatorWebsite: json['operatorWebsite'],
      operatorPhone: json['operatorPhone'],
      duration: Duration(minutes: json['duration']),
      price: json['price']?.toDouble(),
      currency: json['currency'] ?? 'EUR',
      departureTimes: List<String>.from(json['departureTimes']),
      arrivalTimes: List<String>.from(json['arrivalTimes']),
      operatingDays: Map<String, bool>.from(json['operatingDays']),
      isDirectConnection: json['isDirectConnection'] ?? true,
      intermediateStops: json['intermediateStops'] != null
          ? List<String>.from(json['intermediateStops'])
          : null,
      notes: json['notes'],
      bookingRequired: json['bookingRequired'] ?? false,
      bookingUrl: json['bookingUrl'],
      distance: json['distance']?.toDouble(),
      additionalInfo: json['additionalInfo'],
    );
  }

  @override
  String toString() {
    return 'TransportConnection(from: $fromCity, to: $toCity, type: $type, operator: $operatorName)';
  }
}

/// 🚌 TYPY DOPRAVY
enum TransportType {
  bus, // Autobus
  ferry, // Trajekt
  catamaran, // Katamaran
  train, // Vlak
  plane, // Letadlo
  taxi, // Taxi
  transfer, // Transfer
}

/// 📱 ROZŠÍŘENÍ PRO TYPY DOPRAVY
extension TransportTypeExtension on TransportType {
  /// Název typu v chorvatštině
  String get displayNameHr {
    switch (this) {
      case TransportType.bus:
        return 'Autobus';
      case TransportType.ferry:
        return 'Trajekt';
      case TransportType.catamaran:
        return 'Katamaran';
      case TransportType.train:
        return 'Vlak';
      case TransportType.plane:
        return 'Avion';
      case TransportType.taxi:
        return 'Taxi';
      case TransportType.transfer:
        return 'Transfer';
    }
  }

  /// Název typu v angličtině
  String get displayNameEn {
    switch (this) {
      case TransportType.bus:
        return 'Bus';
      case TransportType.ferry:
        return 'Ferry';
      case TransportType.catamaran:
        return 'Catamaran';
      case TransportType.train:
        return 'Train';
      case TransportType.plane:
        return 'Plane';
      case TransportType.taxi:
        return 'Taxi';
      case TransportType.transfer:
        return 'Transfer';
    }
  }

  /// Ikona pro typ dopravy
  String get icon {
    switch (this) {
      case TransportType.bus:
        return '🚌';
      case TransportType.ferry:
        return '⛴️';
      case TransportType.catamaran:
        return '🚤';
      case TransportType.train:
        return '🚂';
      case TransportType.plane:
        return '✈️';
      case TransportType.taxi:
        return '🚕';
      case TransportType.transfer:
        return '🚐';
    }
  }

  /// Barva pro typ dopravy
  int get colorValue {
    switch (this) {
      case TransportType.bus:
        return 0xFF2196F3; // Modrá
      case TransportType.ferry:
        return 0xFF00BCD4; // Cyan
      case TransportType.catamaran:
        return 0xFF009688; // Teal
      case TransportType.train:
        return 0xFF4CAF50; // Zelená
      case TransportType.plane:
        return 0xFFFF9800; // Oranžová
      case TransportType.taxi:
        return 0xFFFFEB3B; // Žlutá
      case TransportType.transfer:
        return 0xFF9C27B0; // Fialová
    }
  }
}

/// 🔍 VYHLEDÁVACÍ KRITÉRIA
class TransportSearchCriteria {
  final String fromCity;
  final String toCity;
  final DateTime? departureDate;
  final DateTime? returnDate;
  final List<TransportType>? preferredTypes;
  final bool directOnly;
  final double? maxPrice;
  final int? maxDuration; // v minutách
  final bool bookingRequired;

  TransportSearchCriteria({
    required this.fromCity,
    required this.toCity,
    this.departureDate,
    this.returnDate,
    this.preferredTypes,
    this.directOnly = false,
    this.maxPrice,
    this.maxDuration,
    this.bookingRequired = false,
  });
}

/// 📊 VÝSLEDEK VYHLEDÁVÁNÍ
class TransportSearchResult {
  final List<TransportConnection> connections;
  final int totalResults;
  final Duration searchDuration;
  final Map<TransportType, int> resultsByType;
  final String? errorMessage;

  TransportSearchResult({
    required this.connections,
    required this.totalResults,
    required this.searchDuration,
    required this.resultsByType,
    this.errorMessage,
  });
}

/// 🎫 INFORMACE O JÍZDENCE
class TicketInfo {
  final String operatorName;
  final String bookingUrl;
  final String phone;
  final double? price;
  final String currency;
  final bool onlineBooking;
  final bool mobileTicket;
  final List<String> paymentMethods;
  final String? notes;

  TicketInfo({
    required this.operatorName,
    required this.bookingUrl,
    required this.phone,
    this.price,
    this.currency = 'EUR',
    this.onlineBooking = true,
    this.mobileTicket = false,
    required this.paymentMethods,
    this.notes,
  });
}

/// 🚏 STANICE/ZASTÁVKA
class TransportStation {
  final String id;
  final String name;
  final String city;
  final String address;
  final double latitude;
  final double longitude;
  final List<TransportType> supportedTypes;
  final Map<String, dynamic>? facilities;
  final String? phone;
  final String? website;

  TransportStation({
    required this.id,
    required this.name,
    required this.city,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.supportedTypes,
    this.facilities,
    this.phone,
    this.website,
  });
}

/// 🚌 DOPRAVCE
class TransportOperator {
  final String id;
  final String name;
  final String website;
  final String phone;
  final String email;
  final List<TransportType> operatedTypes;
  final List<String> operatingRegions;
  final bool onlineBooking;
  final bool mobileApp;
  final List<String> paymentMethods;
  final String? logoUrl;

  TransportOperator({
    required this.id,
    required this.name,
    required this.website,
    required this.phone,
    required this.email,
    required this.operatedTypes,
    required this.operatingRegions,
    this.onlineBooking = false,
    this.mobileApp = false,
    required this.paymentMethods,
    this.logoUrl,
  });
}
