import 'package:flutter/material.dart';

/// Business background painter
class WatercolorBusinessBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..strokeWidth = 2;

    // Gradient pozadí
    final gradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        const Color(0xFF006994),
        const Color(0xFF4CAF50),
        const Color(0xFF2E8B8B),
      ],
    );

    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    paint.shader = gradient.createShader(rect);
    canvas.drawRect(rect, paint);

    // Watercolor efekty
    paint.shader = null;

    // Velké watercolor skvrny
    paint.color = Colors.white.withValues(alpha: 0.1);
    canvas.drawCircle(
      Offset(size.width * 0.2, size.height * 0.3),
      size.width * 0.3,
      paint,
    );

    paint.color = Colors.white.withValues(alpha: 0.05);
    canvas.drawCircle(
      Offset(size.width * 0.8, size.height * 0.7),
      size.width * 0.4,
      paint,
    );

    // Menší detaily
    paint.color = Colors.white.withValues(alpha: 0.08);
    canvas.drawCircle(
      Offset(size.width * 0.1, size.height * 0.8),
      size.width * 0.15,
      paint,
    );

    canvas.drawCircle(
      Offset(size.width * 0.9, size.height * 0.2),
      size.width * 0.12,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Business card painter
class WatercolorBusinessCardPainter extends CustomPainter {
  final Color color;

  WatercolorBusinessCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Hlavní watercolor efekt
    final path = Path();
    path.moveTo(size.width * 0.05, size.height * 0.1);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.05,
      size.width * 0.6,
      size.height * 0.08,
    );
    path.quadraticBezierTo(
      size.width * 0.85,
      size.height * 0.12,
      size.width * 0.95,
      size.height * 0.2,
    );
    path.lineTo(size.width * 0.92, size.height * 0.85);
    path.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.95,
      size.width * 0.4,
      size.height * 0.92,
    );
    path.quadraticBezierTo(
      size.width * 0.15,
      size.height * 0.88,
      size.width * 0.08,
      size.height * 0.8,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);

    // Dodatečné watercolor skvrny
    paint.color = color.withValues(alpha: 0.05);
    canvas.drawCircle(
      Offset(size.width * 0.2, size.height * 0.3),
      size.width * 0.15,
      paint,
    );

    canvas.drawCircle(
      Offset(size.width * 0.8, size.height * 0.7),
      size.width * 0.12,
      paint,
    );

    // Jemné detaily
    paint.color = color.withValues(alpha: 0.03);
    canvas.drawCircle(
      Offset(size.width * 0.1, size.height * 0.1),
      size.width * 0.08,
      paint,
    );

    canvas.drawCircle(
      Offset(size.width * 0.9, size.height * 0.9),
      size.width * 0.1,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// General watercolor background painter
class WatercolorBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill;

    // Watercolor spots
    final spots = [
      {'x': size.width * 0.1, 'y': size.height * 0.2, 'radius': 80.0, 'color': Colors.white.withValues(alpha: 0.1)},
      {'x': size.width * 0.8, 'y': size.height * 0.3, 'radius': 60.0, 'color': Colors.white.withValues(alpha: 0.08)},
      {'x': size.width * 0.3, 'y': size.height * 0.7, 'radius': 100.0, 'color': Colors.white.withValues(alpha: 0.06)},
      {'x': size.width * 0.9, 'y': size.height * 0.8, 'radius': 70.0, 'color': Colors.white.withValues(alpha: 0.1)},
    ];

    for (final spot in spots) {
      paint.color = spot['color'] as Color;
      canvas.drawCircle(
        Offset(spot['x'] as double, spot['y'] as double),
        spot['radius'] as double,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
