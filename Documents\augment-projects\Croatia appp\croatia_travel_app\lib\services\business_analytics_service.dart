import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/business_analytics.dart';

/// 📈 BUSINESS ANALYTICS SERVICE - B2B analytika a reporting
class BusinessAnalyticsService {
  static final BusinessAnalyticsService _instance =
      BusinessAnalyticsService._internal();
  factory BusinessAnalyticsService() => _instance;
  BusinessAnalyticsService._internal();

  bool _isInitialized = false;
  final List<BusinessClient> _clients = [];
  final List<RevenueRecord> _revenueRecords = [];
  final List<UserEngagementMetric> _engagementMetrics = [];
  final Map<String, BusinessReport> _reportCache = {};
  Timer? _analyticsTimer;

  /// Inicializace služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('📈 Inicializuji Business Analytics Service...');

      await _loadBusinessData();
      await _generateMockData();
      _startAnalyticsTimer();

      _isInitialized = true;
      debugPrint('✅ Business Analytics Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Business Analytics: $e');
      await _generateMockData();
      _isInitialized = true;
    }
  }

  /// Získání kompletního business dashboardu
  Future<BusinessDashboard> getBusinessDashboard({
    DateTime? fromDate,
    DateTime? toDate,
    String? clientId,
  }) async {
    try {
      final now = DateTime.now();
      final from = fromDate ?? now.subtract(const Duration(days: 30));
      final to = toDate ?? now;

      // Generování všech metrik
      final revenueMetrics = await _generateRevenueMetrics(from, to, clientId);
      final userMetrics = await _generateUserMetrics(from, to, clientId);
      final engagementMetrics = await _generateEngagementMetrics(
        from,
        to,
        clientId,
      );
      final retentionMetrics = await _generateRetentionMetrics(
        from,
        to,
        clientId,
      );
      final conversionMetrics = await _generateConversionMetrics(
        from,
        to,
        clientId,
      );
      final performanceMetrics = await _generatePerformanceMetrics(
        from,
        to,
        clientId,
      );
      final competitorAnalysis = await _generateCompetitorAnalysis();
      final forecasting = await _generateForecasting(from, to);

      return BusinessDashboard(
        periodStart: from,
        periodEnd: to,
        clientId: clientId,
        revenueMetrics: revenueMetrics,
        userMetrics: userMetrics,
        engagementMetrics: engagementMetrics,
        retentionMetrics: retentionMetrics,
        conversionMetrics: conversionMetrics,
        performanceMetrics: performanceMetrics,
        competitorAnalysis: competitorAnalysis,
        forecasting: forecasting,
        generatedAt: DateTime.now(),
      );
    } catch (e) {
      debugPrint('❌ Chyba při generování business dashboardu: $e');
      return BusinessDashboard.empty();
    }
  }

  /// Generování revenue metrik
  Future<RevenueMetrics> _generateRevenueMetrics(
    DateTime from,
    DateTime to,
    String? clientId,
  ) async {
    final filteredRecords = _revenueRecords
        .where(
          (record) =>
              record.date.isAfter(from.subtract(const Duration(days: 1))) &&
              record.date.isBefore(to.add(const Duration(days: 1))) &&
              (clientId == null || record.clientId == clientId),
        )
        .toList();

    final totalRevenue = filteredRecords.fold<double>(
      0,
      (sum, record) => sum + record.amount,
    );
    final subscriptionRevenue = filteredRecords
        .where((r) => r.type == RevenueType.subscription)
        .fold<double>(0, (sum, record) => sum + record.amount);
    final oneTimeRevenue = filteredRecords
        .where((r) => r.type == RevenueType.oneTime)
        .fold<double>(0, (sum, record) => sum + record.amount);

    // Výpočet růstu
    final previousPeriod = Duration(days: to.difference(from).inDays);
    final previousFrom = from.subtract(previousPeriod);
    final previousTo = from;

    final previousRecords = _revenueRecords
        .where(
          (record) =>
              record.date.isAfter(
                previousFrom.subtract(const Duration(days: 1)),
              ) &&
              record.date.isBefore(previousTo.add(const Duration(days: 1))),
        )
        .toList();

    final previousRevenue = previousRecords.fold<double>(
      0,
      (sum, record) => sum + record.amount,
    );
    final growthRate = previousRevenue > 0
        ? (totalRevenue - previousRevenue) / previousRevenue
        : 0.0;

    // ARR (Annual Recurring Revenue)
    final monthlySubscriptionRevenue = subscriptionRevenue;
    final arr = monthlySubscriptionRevenue * 12;

    // ARPU (Average Revenue Per User)
    final uniqueUsers = filteredRecords.map((r) => r.userId).toSet().length;
    final arpu = uniqueUsers > 0 ? totalRevenue / uniqueUsers : 0.0;

    return RevenueMetrics(
      totalRevenue: totalRevenue,
      subscriptionRevenue: subscriptionRevenue,
      oneTimeRevenue: oneTimeRevenue,
      growthRate: growthRate,
      arr: arr,
      arpu: arpu,
      revenueBySource: _calculateRevenueBySource(filteredRecords),
      monthlyTrend: _calculateMonthlyRevenueTrend(filteredRecords),
    );
  }

  /// Generování user metrik
  Future<UserMetrics> _generateUserMetrics(
    DateTime from,
    DateTime to,
    String? clientId,
  ) async {
    // Simulace user dat
    final totalUsers = 15420 + Random().nextInt(1000);
    final activeUsers = (totalUsers * 0.65).round() + Random().nextInt(100);
    final newUsers = 1250 + Random().nextInt(200);
    final premiumUsers = (totalUsers * 0.15).round() + Random().nextInt(50);

    final previousTotalUsers = 14800 + Random().nextInt(500);
    final userGrowthRate =
        (totalUsers - previousTotalUsers) / previousTotalUsers;

    return UserMetrics(
      totalUsers: totalUsers,
      activeUsers: activeUsers,
      newUsers: newUsers,
      premiumUsers: premiumUsers,
      userGrowthRate: userGrowthRate,
      usersByCountry: _generateUsersByCountry(),
      usersByPlatform: _generateUsersByPlatform(),
      userAcquisitionChannels: _generateAcquisitionChannels(),
    );
  }

  /// Generování engagement metrik
  Future<EngagementMetrics> _generateEngagementMetrics(
    DateTime from,
    DateTime to,
    String? clientId,
  ) async {
    return EngagementMetrics(
      dailyActiveUsers: 8500 + Random().nextInt(1000),
      weeklyActiveUsers: 12000 + Random().nextInt(1500),
      monthlyActiveUsers: 15000 + Random().nextInt(2000),
      averageSessionDuration: Duration(minutes: 12 + Random().nextInt(8)),
      sessionsPerUser: 3.2 + Random().nextDouble(),
      bounceRate: 0.25 + Random().nextDouble() * 0.1,
      pageViews: 45000 + Random().nextInt(10000),
      featureUsage: _generateFeatureUsage(),
      userJourney: _generateUserJourney(),
    );
  }

  /// Generování retention metrik
  Future<RetentionMetrics> _generateRetentionMetrics(
    DateTime from,
    DateTime to,
    String? clientId,
  ) async {
    return RetentionMetrics(
      day1Retention: 0.85 + Random().nextDouble() * 0.1,
      day7Retention: 0.65 + Random().nextDouble() * 0.15,
      day30Retention: 0.45 + Random().nextDouble() * 0.2,
      cohortAnalysis: _generateCohortAnalysis(),
      churnRate: 0.05 + Random().nextDouble() * 0.03,
      ltv: 125.0 + Random().nextDouble() * 50,
      retentionBySegment: _generateRetentionBySegment(),
    );
  }

  /// Generování conversion metrik
  Future<ConversionMetrics> _generateConversionMetrics(
    DateTime from,
    DateTime to,
    String? clientId,
  ) async {
    return ConversionMetrics(
      trialToSubscription: 0.18 + Random().nextDouble() * 0.1,
      freeToTrial: 0.12 + Random().nextDouble() * 0.08,
      visitorToSignup: 0.08 + Random().nextDouble() * 0.05,
      conversionFunnel: _generateConversionFunnel(),
      conversionByChannel: _generateConversionByChannel(),
      timeToConversion: Duration(days: 7 + Random().nextInt(14)),
    );
  }

  /// Generování performance metrik
  Future<PerformanceMetrics> _generatePerformanceMetrics(
    DateTime from,
    DateTime to,
    String? clientId,
  ) async {
    return PerformanceMetrics(
      appLoadTime: Duration(milliseconds: 1200 + Random().nextInt(800)),
      apiResponseTime: Duration(milliseconds: 150 + Random().nextInt(100)),
      crashRate: 0.001 + Random().nextDouble() * 0.002,
      errorRate: 0.005 + Random().nextDouble() * 0.003,
      uptime: 0.998 + Random().nextDouble() * 0.002,
      performanceScore: 85 + Random().nextInt(10),
      technicalMetrics: _generateTechnicalMetrics(),
    );
  }

  /// LEGAL COMPLIANCE: Upraveno - pouze vlastní data a veřejné informace
  /// Odstraněna competitive intelligence, ponechána pouze vlastní analýza
  Future<CompetitorAnalysis> _generateCompetitorAnalysis() async {
    debugPrint(
      '⚖️ LEGAL NOTICE: Using only own data and public market information',
    );

    return CompetitorAnalysis(
      marketShare: 0.12 + Random().nextDouble() * 0.05, // Vlastní market share
      competitorComparison: [], // Odstraněno - competitive intelligence
      marketTrends: _generatePublicMarketTrends(), // Pouze veřejné trendy
      competitiveAdvantages: [
        'Unikátní Croatian Local Intelligence',
        'Pokročilé AI writing suggestions',
        'Family sharing funkce',
        'Offline mode podpora',
        'Watercolor design systém',
        'GDPR compliance',
        'Legal compliance framework',
      ],
      threatAnalysis:
          _generatePublicThreatAnalysis(), // Pouze veřejné informace
    );
  }

  /// Generování forecasting
  Future<BusinessForecasting> _generateForecasting(
    DateTime from,
    DateTime to,
  ) async {
    final currentRevenue = 125000.0;
    final growthRate = 0.15;

    return BusinessForecasting(
      revenueProjection: _generateRevenueProjection(currentRevenue, growthRate),
      userGrowthProjection: _generateUserGrowthProjection(),
      marketSizeProjection: _generateMarketSizeProjection(),
      riskFactors: [
        'Zvýšená konkurence v travel app segmentu',
        'Možné změny v cestovních omezeních',
        'Ekonomická nestabilita',
        'Technologické změny',
      ],
      opportunities: [
        'Expanze do nových evropských trhů',
        'B2B partnerships s travel agenturami',
        'AI-powered personalizace',
        'Blockchain integrace pro NFT vzpomínky',
      ],
    );
  }

  /// Vytvoření custom reportu
  Future<BusinessReport> generateCustomReport({
    required String reportName,
    required ReportType type,
    required DateTime fromDate,
    required DateTime toDate,
    List<String>? metrics,
    List<String>? dimensions,
    String? clientId,
  }) async {
    try {
      final reportId = 'report_${DateTime.now().millisecondsSinceEpoch}';

      // Generování dat podle typu reportu
      final data = await _generateReportData(type, fromDate, toDate, clientId);

      final report = BusinessReport(
        id: reportId,
        name: reportName,
        type: type,
        fromDate: fromDate,
        toDate: toDate,
        data: data,
        metrics: metrics ?? [],
        dimensions: dimensions ?? [],
        generatedAt: DateTime.now(),
        generatedBy: 'system',
      );

      // Uložení do cache
      _reportCache[reportId] = report;

      return report;
    } catch (e) {
      debugPrint('❌ Chyba při generování custom reportu: $e');
      return BusinessReport.empty();
    }
  }

  /// Export reportu
  Future<String?> exportReport({
    required String reportId,
    required ExportFormat format,
  }) async {
    try {
      final report = _reportCache[reportId];
      if (report == null) {
        throw Exception('Report $reportId nenalezen');
      }

      // Simulace exportu
      final fileName =
          'report_${report.name}_${DateTime.now().millisecondsSinceEpoch}';

      switch (format) {
        case ExportFormat.pdf:
          return '${fileName}.pdf';
        case ExportFormat.excel:
          return '${fileName}.xlsx';
        case ExportFormat.csv:
          return '${fileName}.csv';
        case ExportFormat.json:
          return '${fileName}.json';
      }
    } catch (e) {
      debugPrint('❌ Chyba při exportu reportu: $e');
      return null;
    }
  }

  /// Pomocné metody pro generování dat
  Map<String, double> _calculateRevenueBySource(List<RevenueRecord> records) {
    final bySource = <String, double>{};
    for (final record in records) {
      bySource[record.source] = (bySource[record.source] ?? 0) + record.amount;
    }
    return bySource;
  }

  List<MonthlyRevenue> _calculateMonthlyRevenueTrend(
    List<RevenueRecord> records,
  ) {
    final monthlyData = <String, double>{};

    for (final record in records) {
      final monthKey =
          '${record.date.year}-${record.date.month.toString().padLeft(2, '0')}';
      monthlyData[monthKey] = (monthlyData[monthKey] ?? 0) + record.amount;
    }

    return monthlyData.entries.map((entry) {
      final parts = entry.key.split('-');
      return MonthlyRevenue(
        year: int.parse(parts[0]),
        month: int.parse(parts[1]),
        revenue: entry.value,
      );
    }).toList()..sort(
      (a, b) => a.year.compareTo(b.year) != 0
          ? a.year.compareTo(b.year)
          : a.month.compareTo(b.month),
    );
  }

  Map<String, int> _generateUsersByCountry() {
    return {
      'Česká republika': 8500 + Random().nextInt(1000),
      'Slovensko': 2800 + Random().nextInt(500),
      'Chorvatsko': 1200 + Random().nextInt(300),
      'Německo': 1800 + Random().nextInt(400),
      'Rakousko': 900 + Random().nextInt(200),
      'Ostatní': 220 + Random().nextInt(100),
    };
  }

  Map<String, int> _generateUsersByPlatform() {
    return {
      'iOS': 9200 + Random().nextInt(1000),
      'Android': 5800 + Random().nextInt(800),
      'Web': 420 + Random().nextInt(100),
    };
  }

  Map<String, double> _generateAcquisitionChannels() {
    return {
      'Organic Search': 0.35 + Random().nextDouble() * 0.1,
      'Social Media': 0.25 + Random().nextDouble() * 0.1,
      'Paid Ads': 0.20 + Random().nextDouble() * 0.1,
      'Referrals': 0.15 + Random().nextDouble() * 0.05,
      'Direct': 0.05 + Random().nextDouble() * 0.03,
    };
  }

  Map<String, double> _generateFeatureUsage() {
    return {
      'Diary Writing': 0.95 + Random().nextDouble() * 0.05,
      'Photo Upload': 0.85 + Random().nextDouble() * 0.1,
      'Advanced Search': 0.65 + Random().nextDouble() * 0.15,
      'Family Sharing': 0.45 + Random().nextDouble() * 0.2,
      'AI Suggestions': 0.55 + Random().nextDouble() * 0.2,
      'Croatian Intelligence': 0.35 + Random().nextDouble() * 0.15,
    };
  }

  List<UserJourneyStep> _generateUserJourney() {
    return [
      UserJourneyStep('App Download', 1.0, Duration.zero),
      UserJourneyStep('Registration', 0.85, Duration(minutes: 2)),
      UserJourneyStep('First Entry', 0.72, Duration(minutes: 8)),
      UserJourneyStep('Photo Upload', 0.65, Duration(minutes: 15)),
      UserJourneyStep('Feature Discovery', 0.58, Duration(hours: 1)),
      UserJourneyStep('Premium Trial', 0.25, Duration(days: 3)),
      UserJourneyStep('Subscription', 0.18, Duration(days: 7)),
    ];
  }

  Map<String, List<double>> _generateCohortAnalysis() {
    final cohorts = <String, List<double>>{};
    final now = DateTime.now();

    for (int i = 0; i < 6; i++) {
      final cohortDate = DateTime(now.year, now.month - i, 1);
      final cohortKey =
          '${cohortDate.year}-${cohortDate.month.toString().padLeft(2, '0')}';

      // Generování retention dat pro kohortu
      final retentionData = <double>[];
      double retention = 1.0;

      for (int week = 0; week < 12; week++) {
        retentionData.add(retention);
        retention *= (0.85 + Random().nextDouble() * 0.1); // Postupný pokles
      }

      cohorts[cohortKey] = retentionData;
    }

    return cohorts;
  }

  Map<String, double> _generateRetentionBySegment() {
    return {
      'Premium Users': 0.85 + Random().nextDouble() * 0.1,
      'Free Users': 0.45 + Random().nextDouble() * 0.15,
      'Power Users': 0.92 + Random().nextDouble() * 0.05,
      'New Users': 0.35 + Random().nextDouble() * 0.2,
      'Returning Users': 0.75 + Random().nextDouble() * 0.15,
    };
  }

  List<ConversionStep> _generateConversionFunnel() {
    return [
      ConversionStep('Visitors', 100000, 1.0),
      ConversionStep('App Downloads', 8500, 0.085),
      ConversionStep('Registrations', 7200, 0.847),
      ConversionStep('First Entry', 5800, 0.806),
      ConversionStep('Trial Start', 1800, 0.310),
      ConversionStep('Subscription', 320, 0.178),
    ];
  }

  Map<String, double> _generateConversionByChannel() {
    return {
      'Organic Search': 0.12 + Random().nextDouble() * 0.05,
      'Social Media': 0.08 + Random().nextDouble() * 0.04,
      'Paid Ads': 0.15 + Random().nextDouble() * 0.08,
      'Referrals': 0.25 + Random().nextDouble() * 0.1,
      'Direct': 0.18 + Random().nextDouble() * 0.07,
    };
  }

  Map<String, dynamic> _generateTechnicalMetrics() {
    return {
      'CPU Usage': '${45 + Random().nextInt(20)}%',
      'Memory Usage': '${60 + Random().nextInt(25)}%',
      'Storage Usage': '${35 + Random().nextInt(30)}%',
      'Network Latency': '${80 + Random().nextInt(40)}ms',
      'Database Queries/sec': 1200 + Random().nextInt(500),
      'Cache Hit Rate': '${85 + Random().nextInt(10)}%',
    };
  }

  List<CompetitorData> _generateCompetitorComparison() {
    return [
      CompetitorData('Croatia Travel App', 0.12, 4.6, 15420),
      CompetitorData('TravelDiary Pro', 0.18, 4.4, 22000),
      CompetitorData('Journey Journal', 0.08, 4.2, 9800),
      CompetitorData('Wanderlust Diary', 0.15, 4.5, 18500),
      CompetitorData('Travel Memories', 0.06, 4.1, 7200),
    ];
  }

  List<MarketTrend> _generateMarketTrends() {
    return [
      MarketTrend('Travel App Market Growth', 0.15, TrendDirection.up),
      MarketTrend('Digital Diary Adoption', 0.22, TrendDirection.up),
      MarketTrend('AI Integration Demand', 0.35, TrendDirection.up),
      MarketTrend('Privacy Concerns', 0.08, TrendDirection.down),
      MarketTrend('Subscription Model Acceptance', 0.18, TrendDirection.up),
    ];
  }

  List<ThreatLevel> _generateThreatAnalysis() {
    return [
      ThreatLevel('New Competitors', ThreatSeverity.medium, 0.6),
      ThreatLevel('Market Saturation', ThreatSeverity.low, 0.3),
      ThreatLevel('Technology Disruption', ThreatSeverity.high, 0.8),
      ThreatLevel('Economic Downturn', ThreatSeverity.medium, 0.5),
      ThreatLevel('Regulatory Changes', ThreatSeverity.low, 0.2),
    ];
  }

  List<RevenueProjection> _generateRevenueProjection(
    double currentRevenue,
    double growthRate,
  ) {
    final projections = <RevenueProjection>[];
    double revenue = currentRevenue;

    for (int month = 1; month <= 12; month++) {
      revenue *= (1 + growthRate / 12);
      projections.add(
        RevenueProjection(
          month: month,
          projectedRevenue: revenue,
          confidence: 0.85 - (month * 0.02), // Klesající confidence
        ),
      );
    }

    return projections;
  }

  List<UserGrowthProjection> _generateUserGrowthProjection() {
    final projections = <UserGrowthProjection>[];
    int users = 15420;

    for (int month = 1; month <= 12; month++) {
      users = (users * 1.08).round(); // 8% měsíční růst
      projections.add(
        UserGrowthProjection(
          month: month,
          projectedUsers: users,
          organicGrowth: users * 0.6,
          paidGrowth: users * 0.4,
        ),
      );
    }

    return projections;
  }

  MarketSizeProjection _generateMarketSizeProjection() {
    return MarketSizeProjection(
      currentMarketSize: 2.5e9, // 2.5 miliard USD
      projectedMarketSize: 4.2e9, // 4.2 miliard USD za 5 let
      cagr: 0.11, // 11% CAGR
      addressableMarket: 450e6, // 450 milionů USD
    );
  }

  Future<Map<String, dynamic>> _generateReportData(
    ReportType type,
    DateTime fromDate,
    DateTime toDate,
    String? clientId,
  ) async {
    switch (type) {
      case ReportType.revenue:
        final metrics = await _generateRevenueMetrics(
          fromDate,
          toDate,
          clientId,
        );
        return metrics.toJson();
      case ReportType.users:
        final metrics = await _generateUserMetrics(fromDate, toDate, clientId);
        return metrics.toJson();
      case ReportType.engagement:
        final metrics = await _generateEngagementMetrics(
          fromDate,
          toDate,
          clientId,
        );
        return metrics.toJson();
      case ReportType.retention:
        final metrics = await _generateRetentionMetrics(
          fromDate,
          toDate,
          clientId,
        );
        return metrics.toJson();
      case ReportType.performance:
        final metrics = await _generatePerformanceMetrics(
          fromDate,
          toDate,
          clientId,
        );
        return metrics.toJson();
      case ReportType.custom:
        return {'message': 'Custom report data'};
    }
  }

  /// Načítání a ukládání dat
  Future<void> _loadBusinessData() async {
    // Načtení z SharedPreferences nebo databáze
  }

  Future<void> _generateMockData() async {
    // Generování mock business klientů
    _clients.addAll([
      BusinessClient(
        id: 'client_001',
        name: 'Croatian Tourism Board',
        type: ClientType.government,
        subscriptionTier: SubscriptionTier.enterprise,
        monthlyRevenue: 15000.0,
        contractStart: DateTime.now().subtract(const Duration(days: 365)),
        contractEnd: DateTime.now().add(const Duration(days: 365)),
        isActive: true,
      ),
      BusinessClient(
        id: 'client_002',
        name: 'Adriatic Travel Agency',
        type: ClientType.travelAgency,
        subscriptionTier: SubscriptionTier.professional,
        monthlyRevenue: 8500.0,
        contractStart: DateTime.now().subtract(const Duration(days: 180)),
        contractEnd: DateTime.now().add(const Duration(days: 545)),
        isActive: true,
      ),
    ]);

    // Generování revenue záznamů
    final now = DateTime.now();
    for (int i = 0; i < 90; i++) {
      final date = now.subtract(Duration(days: i));
      _revenueRecords.add(
        RevenueRecord(
          id: 'rev_$i',
          clientId: _clients[Random().nextInt(_clients.length)].id,
          userId: 'user_${Random().nextInt(1000)}',
          amount: 50.0 + Random().nextDouble() * 200,
          type: Random().nextBool()
              ? RevenueType.subscription
              : RevenueType.oneTime,
          source: [
            'App Store',
            'Google Play',
            'Web',
            'Partner',
          ][Random().nextInt(4)],
          date: date,
        ),
      );
    }
  }

  void _startAnalyticsTimer() {
    _analyticsTimer?.cancel();
    _analyticsTimer = Timer.periodic(const Duration(hours: 1), (_) {
      // Pravidelná aktualizace analytics dat
      _updateAnalyticsData();
    });
  }

  Future<void> _updateAnalyticsData() async {
    // Aktualizace real-time dat
  }

  @override
  void dispose() {
    _analyticsTimer?.cancel();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  List<BusinessClient> get clients => List.unmodifiable(_clients);
  List<RevenueRecord> get revenueRecords => List.unmodifiable(_revenueRecords);
}
