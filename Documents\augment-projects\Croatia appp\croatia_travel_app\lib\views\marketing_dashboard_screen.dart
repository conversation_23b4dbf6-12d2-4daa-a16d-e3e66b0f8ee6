import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';

/// 📊 MARKETING DASHBOARD SCREEN - Kompletní marketing overview
class MarketingDashboardScreen extends StatefulWidget {
  const MarketingDashboardScreen({super.key});

  @override
  State<MarketingDashboardScreen> createState() =>
      _MarketingDashboardScreenState();
}

class _MarketingDashboardScreenState extends State<MarketingDashboardScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF006994), // Adriatic Blue
              Color(0xFF2E8B8B), // Teal
              Color(0xFFFF6B35), // Sunset Orange
            ],
            stops: [0.0, 0.6, 1.0],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(
                          Icons.arrow_back_ios_new,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    const Spacer(),
                    Column(
                      children: [
                        Text(
                          '📊 Marketing Dashboard',
                          style: GoogleFonts.inter(
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          'Growth & Retention Analytics',
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: Colors.white.withValues(alpha: 0.8),
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    const SizedBox(width: 48), // Balance for back button
                  ],
                ),
              ),

              // Tab bar
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: TabBar(
                  controller: _tabController,
                  indicator: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  indicatorPadding: const EdgeInsets.all(4),
                  labelColor: Colors.white,
                  unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
                  labelStyle: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                  unselectedLabelStyle: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  tabs: const [
                    Tab(text: '🚀 Viral'),
                    Tab(text: '🎯 Referral'),
                    Tab(text: '📱 Social'),
                    Tab(text: '📧 Retention'),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // Tab content
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildViralTab(),
                    _buildReferralTab(),
                    _buildSocialTab(),
                    _buildRetentionTab(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildViralTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Viral metrics overview
          _buildMetricsGrid([
            {
              'title': 'Viral Coefficient',
              'value': '1.4x',
              'trend': '+12%',
              'icon': '🚀',
            },
            {
              'title': 'Shares Today',
              'value': '847',
              'trend': '+23%',
              'icon': '📤',
            },
            {
              'title': 'Content Created',
              'value': '2.1k',
              'trend': '+45%',
              'icon': '🎨',
            },
            {
              'title': 'Reach Estimate',
              'value': '125k',
              'trend': '+67%',
              'icon': '👥',
            },
          ]),

          const SizedBox(height: 24),

          // Top performing content
          _buildSectionCard(
            title: '🏆 Top Performing Content',
            children: [
              _buildContentItem(
                'Instagram Story - Split Sunset',
                '2.4k shares',
                '89% engagement',
              ),
              _buildContentItem(
                'TikTok - Croatian Food Tour',
                '1.8k shares',
                '76% engagement',
              ),
              _buildContentItem(
                'Year in Review 2024',
                '1.2k shares',
                '92% engagement',
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Platform breakdown
          _buildSectionCard(
            title: '📱 Platform Performance',
            children: [
              _buildPlatformItem(
                'Instagram',
                '45%',
                '2.1k shares',
                Colors.purple,
              ),
              _buildPlatformItem('TikTok', '32%', '1.5k shares', Colors.black),
              _buildPlatformItem('Facebook', '15%', '700 shares', Colors.blue),
              _buildPlatformItem(
                'Twitter',
                '8%',
                '380 shares',
                Colors.lightBlue,
              ),
            ],
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildReferralTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Referral metrics
          _buildMetricsGrid([
            {
              'title': 'Active Referrers',
              'value': '1,247',
              'trend': '+34%',
              'icon': '👥',
            },
            {
              'title': 'Conversion Rate',
              'value': '23.4%',
              'trend': '+8%',
              'icon': '🎯',
            },
            {
              'title': 'Avg. Referrals',
              'value': '3.2',
              'trend': '+15%',
              'icon': '🔗',
            },
            {
              'title': 'Total Rewards',
              'value': '45k kn',
              'trend': '+28%',
              'icon': '🎁',
            },
          ]),

          const SizedBox(height: 24),

          // Top referrers leaderboard
          _buildSectionCard(
            title: '🏅 Top Referrers',
            children: [
              _buildLeaderboardItem(
                1,
                'Ana M.',
                '47 referrals',
                '2,340 kn earned',
              ),
              _buildLeaderboardItem(
                2,
                'Marko P.',
                '39 referrals',
                '1,950 kn earned',
              ),
              _buildLeaderboardItem(
                3,
                'Petra K.',
                '31 referrals',
                '1,550 kn earned',
              ),
              _buildLeaderboardItem(
                4,
                'Ivan S.',
                '28 referrals',
                '1,400 kn earned',
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Referral funnel
          _buildSectionCard(
            title: '📊 Referral Funnel',
            children: [
              _buildFunnelStep('Codes Shared', '5,247', '100%'),
              _buildFunnelStep('App Downloads', '3,891', '74.2%'),
              _buildFunnelStep('Registrations', '2,456', '63.1%'),
              _buildFunnelStep('Active Users', '1,847', '75.2%'),
            ],
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildSocialTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Social metrics
          _buildMetricsGrid([
            {
              'title': 'Total Followers',
              'value': '47.2k',
              'trend': '+18%',
              'icon': '👥',
            },
            {
              'title': 'Engagement Rate',
              'value': '4.7%',
              'trend': '+0.8%',
              'icon': '❤️',
            },
            {
              'title': 'Posts This Week',
              'value': '23',
              'trend': '+12%',
              'icon': '📝',
            },
            {'title': 'Reach', 'value': '156k', 'trend': '+25%', 'icon': '📈'},
          ]),

          const SizedBox(height: 24),

          // Best posting times
          _buildSectionCard(
            title: '⏰ Best Posting Times',
            children: [
              _buildTimeSlot('Instagram', '18:00 - 20:00', '4.2% engagement'),
              _buildTimeSlot('TikTok', '20:00 - 22:00', '5.8% engagement'),
              _buildTimeSlot('Facebook', '15:00 - 17:00', '3.1% engagement'),
              _buildTimeSlot('Twitter', '12:00 - 14:00', '2.9% engagement'),
            ],
          ),

          const SizedBox(height: 24),

          // Hashtag performance
          _buildSectionCard(
            title: '#️⃣ Top Hashtags',
            children: [
              _buildHashtagItem('#Croatia', '12.4k uses', '+23%'),
              _buildHashtagItem('#TravelDiary', '8.7k uses', '+18%'),
              _buildHashtagItem('#Split', '6.2k uses', '+31%'),
              _buildHashtagItem('#Zagreb', '5.1k uses', '+15%'),
            ],
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildRetentionTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Retention metrics
          _buildMetricsGrid([
            {
              'title': 'Email Open Rate',
              'value': '24.3%',
              'trend': '+2.1%',
              'icon': '📧',
            },
            {
              'title': 'Push Open Rate',
              'value': '18.7%',
              'trend': '+1.4%',
              'icon': '📱',
            },
            {
              'title': 'Retention Rate',
              'value': '68.2%',
              'trend': '+5.3%',
              'icon': '🔄',
            },
            {
              'title': 'Churn Rate',
              'value': '4.8%',
              'trend': '-1.2%',
              'icon': '📉',
            },
          ]),

          const SizedBox(height: 24),

          // Campaign performance
          _buildSectionCard(
            title: '📊 Campaign Performance',
            children: [
              _buildCampaignItem('Welcome Series', '89.2% open', '12.4% click'),
              _buildCampaignItem('Re-engagement', '34.7% open', '8.9% click'),
              _buildCampaignItem('Premium Trial', '67.3% open', '23.1% click'),
              _buildCampaignItem('Weekly Digest', '45.8% open', '6.7% click'),
            ],
          ),

          const SizedBox(height: 24),

          // User segments
          _buildSectionCard(
            title: '👥 User Segments',
            children: [
              _buildSegmentItem('New Users', '2,847 users', '32% engagement'),
              _buildSegmentItem(
                'Active Users',
                '8,234 users',
                '67% engagement',
              ),
              _buildSegmentItem(
                'Premium Users',
                '1,456 users',
                '89% engagement',
              ),
              _buildSegmentItem(
                'Inactive Users',
                '3,891 users',
                '12% engagement',
              ),
            ],
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildMetricsGrid(List<Map<String, String>> metrics) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.5,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: metrics.length,
      itemBuilder: (context, index) {
        final metric = metrics[index];
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(metric['icon']!, style: const TextStyle(fontSize: 20)),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      metric['trend']!,
                      style: GoogleFonts.inter(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: Colors.green,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                metric['value']!,
                style: GoogleFonts.inter(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Text(
                metric['title']!,
                style: GoogleFonts.inter(
                  fontSize: 12,
                  color: Colors.white.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ).animate().fadeIn(delay: (index * 100).ms).slideY(begin: 0.3);
      },
    );
  }

  Widget _buildSectionCard({
    required String title,
    required List<Widget> children,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.inter(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildContentItem(String title, String shares, String engagement) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Icon(Icons.trending_up, color: Colors.white, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                Text(
                  '$shares • $engagement',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlatformItem(
    String platform,
    String percentage,
    String shares,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(Icons.share, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  platform,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                Text(
                  '$percentage • $shares',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
          Text(
            percentage,
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLeaderboardItem(
    int rank,
    String name,
    String referrals,
    String earned,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: rank <= 3
                  ? Colors.orange.withValues(alpha: 0.2)
                  : Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                '$rank',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: rank <= 3 ? Colors.orange : Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                Text(
                  '$referrals • $earned',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFunnelStep(String step, String value, String percentage) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Expanded(
            child: Text(
              step,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
          ),
          Text(
            value,
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            percentage,
            style: GoogleFonts.inter(
              fontSize: 12,
              color: Colors.white.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeSlot(String platform, String time, String engagement) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Expanded(
            child: Text(
              platform,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
          Text(
            time,
            style: GoogleFonts.inter(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            engagement,
            style: GoogleFonts.inter(
              fontSize: 12,
              color: Colors.green,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHashtagItem(String hashtag, String uses, String trend) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Expanded(
            child: Text(
              hashtag,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
          Text(
            uses,
            style: GoogleFonts.inter(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            trend,
            style: GoogleFonts.inter(
              fontSize: 12,
              color: Colors.green,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCampaignItem(
    String campaign,
    String openRate,
    String clickRate,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Expanded(
            child: Text(
              campaign,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
          Text(
            '$openRate • $clickRate',
            style: GoogleFonts.inter(
              fontSize: 12,
              color: Colors.white.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSegmentItem(String segment, String users, String engagement) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Expanded(
            child: Text(
              segment,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
          Text(
            '$users • $engagement',
            style: GoogleFonts.inter(
              fontSize: 12,
              color: Colors.white.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }
}
