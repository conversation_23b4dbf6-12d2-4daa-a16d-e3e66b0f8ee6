import 'package:json_annotation/json_annotation.dart';

part 'offline_data.g.dart';

/// Stav offline dat
enum OfflineDataStatus {
  notDownloaded,
  downloading,
  downloaded,
  outdated,
  error,
}

/// Typ offline dat
enum OfflineDataType {
  places,
  tickets,
  restaurants,
  accommodations,
  beaches,
  maps,
  aiResponses,
  emergency,
}

/// Model pro offline data bal<PERSON><PERSON><PERSON>
@JsonSerializable()
class OfflineDataPackage {
  final String id;
  final String name;
  final String description;
  final OfflineDataType type;
  final OfflineDataStatus status;
  final DateTime? downloadedAt;
  final DateTime? lastUpdated;
  final int sizeBytes;
  final int version;
  final String region;
  final Map<String, dynamic> metadata;
  final List<String> dependencies;

  const OfflineDataPackage({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.status,
    this.downloadedAt,
    this.lastUpdated,
    required this.sizeBytes,
    required this.version,
    required this.region,
    this.metadata = const {},
    this.dependencies = const [],
  });

  /// Zkontroluje, zda jsou data aktuální
  bool get isUpToDate {
    if (downloadedAt == null || lastUpdated == null) return false;

    final daysSinceUpdate = DateTime.now().difference(downloadedAt!).inDays;
    return daysSinceUpdate < 7; // Data jsou aktuální 7 dní
  }

  /// Zkontroluje, zda jsou data dostupná offline
  bool get isAvailable {
    return status == OfflineDataStatus.downloaded && isUpToDate;
  }

  /// Velikost v MB
  double get sizeMB {
    return sizeBytes / (1024 * 1024);
  }

  /// Popis velikosti
  String get sizeDescription {
    if (sizeBytes < 1024) {
      return '$sizeBytes B';
    } else if (sizeBytes < 1024 * 1024) {
      return '${(sizeBytes / 1024).toStringAsFixed(1)} KB';
    } else if (sizeBytes < 1024 * 1024 * 1024) {
      return '${(sizeBytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(sizeBytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// Popis stavu v češtině
  String get statusDescription {
    switch (status) {
      case OfflineDataStatus.notDownloaded:
        return 'Nestaženo';
      case OfflineDataStatus.downloading:
        return 'Stahování...';
      case OfflineDataStatus.downloaded:
        return isUpToDate ? 'Aktuální' : 'Zastaralé';
      case OfflineDataStatus.outdated:
        return 'Zastaralé';
      case OfflineDataStatus.error:
        return 'Chyba';
    }
  }

  factory OfflineDataPackage.fromJson(Map<String, dynamic> json) =>
      _$OfflineDataPackageFromJson(json);

  Map<String, dynamic> toJson() => _$OfflineDataPackageToJson(this);

  OfflineDataPackage copyWith({
    String? id,
    String? name,
    String? description,
    OfflineDataType? type,
    OfflineDataStatus? status,
    DateTime? downloadedAt,
    DateTime? lastUpdated,
    int? sizeBytes,
    int? version,
    String? region,
    Map<String, dynamic>? metadata,
    List<String>? dependencies,
  }) {
    return OfflineDataPackage(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      downloadedAt: downloadedAt ?? this.downloadedAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      sizeBytes: sizeBytes ?? this.sizeBytes,
      version: version ?? this.version,
      region: region ?? this.region,
      metadata: metadata ?? this.metadata,
      dependencies: dependencies ?? this.dependencies,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OfflineDataPackage && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Model pro offline mapu
@JsonSerializable()
class OfflineMapArea {
  final String id;
  final String name;
  final String description;
  final double northLatitude;
  final double southLatitude;
  final double eastLongitude;
  final double westLongitude;
  final int zoomLevel;
  final OfflineDataStatus status;
  final DateTime? downloadedAt;
  final int sizeBytes;
  final int tileCount;

  const OfflineMapArea({
    required this.id,
    required this.name,
    required this.description,
    required this.northLatitude,
    required this.southLatitude,
    required this.eastLongitude,
    required this.westLongitude,
    required this.zoomLevel,
    required this.status,
    this.downloadedAt,
    required this.sizeBytes,
    required this.tileCount,
  });

  /// Zkontroluje, zda pozice spadá do této oblasti
  bool containsPosition(double latitude, double longitude) {
    return latitude >= southLatitude &&
        latitude <= northLatitude &&
        longitude >= westLongitude &&
        longitude <= eastLongitude;
  }

  /// Velikost oblasti v km²
  double get areaSqKm {
    const double earthRadius = 6371; // km
    final double latDiff = (northLatitude - southLatitude) * (3.14159 / 180);
    final double lonDiff = (eastLongitude - westLongitude) * (3.14159 / 180);
    final double avgLat = (northLatitude + southLatitude) / 2 * (3.14159 / 180);

    final double latDistance = latDiff * earthRadius;
    final double lonDistance = lonDiff * earthRadius * (avgLat.cos());

    return latDistance * lonDistance;
  }

  factory OfflineMapArea.fromJson(Map<String, dynamic> json) =>
      _$OfflineMapAreaFromJson(json);

  Map<String, dynamic> toJson() => _$OfflineMapAreaToJson(this);
}

/// Model pro offline AI odpověď
@JsonSerializable()
class OfflineAIResponse {
  final String id;
  final String question;
  final String answer;
  final List<String> keywords;
  final String category;
  final DateTime createdAt;
  final int useCount;
  final double relevanceScore;

  const OfflineAIResponse({
    required this.id,
    required this.question,
    required this.answer,
    required this.keywords,
    required this.category,
    required this.createdAt,
    this.useCount = 0,
    this.relevanceScore = 1.0,
  });

  /// Zkontroluje, zda odpověď odpovídá dotazu
  bool matchesQuery(String query) {
    final lowerQuery = query.toLowerCase();

    // Kontrola přímé shody
    if (question.toLowerCase().contains(lowerQuery)) return true;

    // Kontrola klíčových slov
    for (final keyword in keywords) {
      if (keyword.toLowerCase().contains(lowerQuery) ||
          lowerQuery.contains(keyword.toLowerCase())) {
        return true;
      }
    }

    return false;
  }

  factory OfflineAIResponse.fromJson(Map<String, dynamic> json) =>
      _$OfflineAIResponseFromJson(json);

  Map<String, dynamic> toJson() => _$OfflineAIResponseToJson(this);

  OfflineAIResponse copyWith({
    String? id,
    String? question,
    String? answer,
    List<String>? keywords,
    String? category,
    DateTime? createdAt,
    int? useCount,
    double? relevanceScore,
  }) {
    return OfflineAIResponse(
      id: id ?? this.id,
      question: question ?? this.question,
      answer: answer ?? this.answer,
      keywords: keywords ?? this.keywords,
      category: category ?? this.category,
      createdAt: createdAt ?? this.createdAt,
      useCount: useCount ?? this.useCount,
      relevanceScore: relevanceScore ?? this.relevanceScore,
    );
  }
}

/// Model pro offline statistiky
@JsonSerializable()
class OfflineStats {
  final int totalPackages;
  final int downloadedPackages;
  final int totalSizeBytes;
  final int downloadedSizeBytes;
  final DateTime lastSyncAt;
  final int syncCount;
  final Map<OfflineDataType, int> packagesByType;

  const OfflineStats({
    required this.totalPackages,
    required this.downloadedPackages,
    required this.totalSizeBytes,
    required this.downloadedSizeBytes,
    required this.lastSyncAt,
    required this.syncCount,
    required this.packagesByType,
  });

  /// Procento stažených dat
  double get downloadProgress {
    if (totalPackages == 0) return 0.0;
    return downloadedPackages / totalPackages;
  }

  /// Procento využitého místa
  double get storageProgress {
    if (totalSizeBytes == 0) return 0.0;
    return downloadedSizeBytes / totalSizeBytes;
  }

  /// Popis velikosti
  String get totalSizeDescription {
    return _formatBytes(totalSizeBytes);
  }

  String get downloadedSizeDescription {
    return _formatBytes(downloadedSizeBytes);
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  factory OfflineStats.fromJson(Map<String, dynamic> json) =>
      _$OfflineStatsFromJson(json);

  Map<String, dynamic> toJson() => _$OfflineStatsToJson(this);
}

// Extension pro cos funkci
extension MathExtensions on double {
  double cos() {
    // Placeholder - v reálné aplikaci by se použila dart:math
    return 1.0;
  }
}
