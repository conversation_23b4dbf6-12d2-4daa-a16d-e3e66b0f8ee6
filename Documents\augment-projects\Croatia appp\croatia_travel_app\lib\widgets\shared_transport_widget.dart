import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import '../models/parking.dart';
import '../services/shared_transport_service.dart';

class SharedTransportWidget extends StatefulWidget {
  const SharedTransportWidget({super.key});

  @override
  State<SharedTransportWidget> createState() => _SharedTransportWidgetState();
}

class _SharedTransportWidgetState extends State<SharedTransportWidget> {
  final SharedTransportService _sharedTransportService =
      SharedTransportService();

  List<SharedVehicle> _nearbyVehicles = [];
  List<SharedVehicleRental> _activeRentals = [];
  bool _isLoading = false;
  Position? _currentPosition;
  SharedVehicleType? _selectedType;
  String? _selectedOperator;
  List<String> _availableOperators = [];

  @override
  void initState() {
    super.initState();
    _loadCurrentLocation();
    _loadOperators();
  }

  @override
  void dispose() {
    _sharedTransportService.dispose();
    super.dispose();
  }

  Future<void> _loadCurrentLocation() async {
    try {
      setState(() => _isLoading = true);

      _currentPosition = await Geolocator.getCurrentPosition();
      await _loadNearbyVehicles();
      await _loadActiveRentals();
    } catch (e) {
      _showError('Chyba při načítání polohy: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadOperators() async {
    try {
      final operators = await _sharedTransportService.getAvailableOperators(
        'Zagreb',
      );
      setState(() => _availableOperators = operators);
    } catch (e) {
      _showError('Chyba při načítání operátorů: $e');
    }
  }

  Future<void> _loadNearbyVehicles() async {
    if (_currentPosition == null) return;

    try {
      final vehicles = await _sharedTransportService.findNearbyVehicles(
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
        type: _selectedType,
      );

      setState(() => _nearbyVehicles = vehicles);
    } catch (e) {
      _showError('Chyba při načítání vozidel: $e');
    }
  }

  Future<void> _loadActiveRentals() async {
    try {
      // V reálné aplikaci by se použilo ID uživatele
      final rentals = await _sharedTransportService.getActiveRentals();
      setState(() => _activeRentals = rentals);
    } catch (e) {
      _showError('Chyba při načítání aktivních půjčování: $e');
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _loadCurrentLocation,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildQuickActions(),
            const SizedBox(height: 24),
            if (_activeRentals.isNotEmpty) ...[
              _buildActiveRentals(),
              const SizedBox(height: 24),
            ],
            _buildFilters(),
            const SizedBox(height: 16),
            _buildNearbyVehicles(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Rychlé akce',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _findNearestVehicle,
                    icon: const Icon(Icons.search),
                    label: const Text('Najít vozidlo'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _scanQRCode,
                    icon: const Icon(Icons.qr_code_scanner),
                    label: const Text('Skenovat QR'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveRentals() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Aktivní půjčování',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _activeRentals.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final rental = _activeRentals[index];
                return _buildRentalTile(rental);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRentalTile(SharedVehicleRental rental) {
    return ListTile(
      leading: Icon(
        _getVehicleTypeIcon(SharedVehicleType.eBike), // Placeholder
        color: Colors.green,
      ),
      title: Text('Vozidlo ${rental.vehicleId}'),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Trvání: ${rental.duration.inMinutes} min'),
          if (rental.totalCost != null)
            Text('Cena: ${rental.totalCost} ${rental.currency}'),
        ],
      ),
      trailing: PopupMenuButton<String>(
        onSelected: (value) => _handleRentalAction(rental, value),
        itemBuilder: (context) => [
          const PopupMenuItem(value: 'pause', child: Text('Pozastavit')),
          const PopupMenuItem(value: 'end', child: Text('Ukončit')),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Filtry',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<SharedVehicleType?>(
                    value: _selectedType,
                    decoration: const InputDecoration(
                      labelText: 'Typ vozidla',
                      border: OutlineInputBorder(),
                    ),
                    items: [
                      const DropdownMenuItem(
                        value: null,
                        child: Text('Všechny typy'),
                      ),
                      ...SharedVehicleType.values.map(
                        (type) => DropdownMenuItem(
                          value: type,
                          child: Text(_getVehicleTypeName(type)),
                        ),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() => _selectedType = value);
                      _loadNearbyVehicles();
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButtonFormField<String?>(
                    value: _selectedOperator,
                    decoration: const InputDecoration(
                      labelText: 'Operátor',
                      border: OutlineInputBorder(),
                    ),
                    items: [
                      const DropdownMenuItem(
                        value: null,
                        child: Text('Všichni'),
                      ),
                      ..._availableOperators.map(
                        (operator) => DropdownMenuItem(
                          value: operator,
                          child: Text(operator),
                        ),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() => _selectedOperator = value);
                      _loadNearbyVehicles();
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNearbyVehicles() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Vozidla v okolí',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else if (_nearbyVehicles.isEmpty)
              const Text('Žádná vozidla v okolí')
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _nearbyVehicles.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final vehicle = _nearbyVehicles[index];
                  return _buildVehicleTile(vehicle);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildVehicleTile(SharedVehicle vehicle) {
    final batteryColor = _getBatteryColor(vehicle.batteryLevel);

    return ListTile(
      leading: Icon(
        _getVehicleTypeIcon(vehicle.type),
        color: vehicle.isAvailable ? Colors.green : Colors.grey,
      ),
      title: Text('${vehicle.brand} ${vehicle.model}'),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (vehicle.operatorName != null)
            Text('Operátor: ${vehicle.operatorName}'),
          Row(
            children: [
              Icon(Icons.battery_std, size: 16, color: batteryColor),
              Text(
                ' ${vehicle.batteryLevel}% (${vehicle.batteryStatusText})',
                style: TextStyle(color: batteryColor),
              ),
            ],
          ),
          Text(
            '${vehicle.pricePerMinute} ${vehicle.currency}/min + ${vehicle.unlockFee} ${vehicle.currency}',
          ),
        ],
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: vehicle.isAvailable ? Colors.green : Colors.grey,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              vehicle.isAvailable ? 'Dostupné' : 'Nedostupné',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      onTap: vehicle.isAvailable ? () => _showVehicleDetails(vehicle) : null,
    );
  }

  String _getVehicleTypeName(SharedVehicleType type) {
    switch (type) {
      case SharedVehicleType.bike:
        return 'Kolo';
      case SharedVehicleType.eBike:
        return 'E-kolo';
      case SharedVehicleType.scooter:
        return 'Koloběžka';
      case SharedVehicleType.eScooter:
        return 'E-koloběžka';
      case SharedVehicleType.car:
        return 'Auto';
      case SharedVehicleType.eCar:
        return 'E-auto';
    }
  }

  IconData _getVehicleTypeIcon(SharedVehicleType type) {
    switch (type) {
      case SharedVehicleType.bike:
      case SharedVehicleType.eBike:
        return Icons.pedal_bike;
      case SharedVehicleType.scooter:
      case SharedVehicleType.eScooter:
        return Icons.electric_scooter;
      case SharedVehicleType.car:
      case SharedVehicleType.eCar:
        return Icons.directions_car;
    }
  }

  Color _getBatteryColor(int batteryLevel) {
    if (batteryLevel >= 80) return Colors.green;
    if (batteryLevel >= 50) return Colors.orange;
    if (batteryLevel >= 20) return Colors.red;
    return Colors.grey;
  }

  void _findNearestVehicle() {
    // Implementace vyhledání nejbližšího vozidla
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Hledám nejbližší vozidlo...')),
    );
  }

  void _scanQRCode() {
    // Implementace skenování QR kódu
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Otevírám skener QR kódu...')));
  }

  void _handleRentalAction(SharedVehicleRental rental, String action) {
    switch (action) {
      case 'pause':
        _pauseRental(rental);
        break;
      case 'end':
        _endRental(rental);
        break;
    }
  }

  void _pauseRental(SharedVehicleRental rental) {
    // Implementace pozastavení půjčování
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Pozastavuji půjčování...')));
  }

  void _endRental(SharedVehicleRental rental) {
    // Implementace ukončení půjčování
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Ukončuji půjčování...')));
  }

  void _showVehicleDetails(SharedVehicle vehicle) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${vehicle.brand} ${vehicle.model}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (vehicle.operatorName != null)
              Text('Operátor: ${vehicle.operatorName}'),
            Text('Typ: ${_getVehicleTypeName(vehicle.type)}'),
            Text('Baterie: ${vehicle.batteryLevel}%'),
            Text('Cena: ${vehicle.pricePerMinute} ${vehicle.currency}/min'),
            Text('Odemčení: ${vehicle.unlockFee} ${vehicle.currency}'),
            if (vehicle.features.isNotEmpty)
              Text('Vybavení: ${vehicle.features.join(', ')}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zavřít'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _reserveVehicle(vehicle);
            },
            child: const Text('Rezervovat'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _unlockVehicle(vehicle);
            },
            child: const Text('Odemknout'),
          ),
        ],
      ),
    );
  }

  void _reserveVehicle(SharedVehicle vehicle) {
    // Implementace rezervace vozidla
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Rezervuji ${vehicle.brand} ${vehicle.model}...')),
    );
  }

  void _unlockVehicle(SharedVehicle vehicle) {
    // Implementace odemčení vozidla
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Odemykám ${vehicle.brand} ${vehicle.model}...')),
    );
  }
}
