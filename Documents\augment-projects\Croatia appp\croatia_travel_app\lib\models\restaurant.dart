import 'dart:math';
import 'package:json_annotation/json_annotation.dart';

part 'restaurant.g.dart';

@JsonSerializable()
class Restaurant {
  final String id;
  final String name;
  final String description;
  final String address;
  final double latitude;
  final double longitude;
  final String region;
  final String cuisineType;
  final String priceRange;
  final double rating;
  final int reviewCount;
  final List<String> specialties;
  final String phone;
  final String? website;
  final String? email;
  final List<WorkingHours> workingHours;
  final List<String> features;
  final bool isActive;
  final DateTime lastUpdated;

  Restaurant({
    required this.id,
    required this.name,
    required this.description,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.region,
    required this.cuisineType,
    required this.priceRange,
    required this.rating,
    required this.reviewCount,
    required this.specialties,
    required this.phone,
    this.website,
    this.email,
    required this.workingHours,
    required this.features,
    required this.isActive,
    required this.lastUpdated,
  });

  /// Je restaurace aktuálně otevřená
  bool get isOpenNow {
    final now = DateTime.now();
    final today = now.weekday;

    final todayHours = workingHours.where((h) => h.dayOfWeek == today).toList();
    if (todayHours.isEmpty) return false;

    final currentTime = now.hour * 60 + now.minute;

    return todayHours.any(
      (hours) =>
          currentTime >= hours.openMinutes && currentTime <= hours.closeMinutes,
    );
  }

  /// Má restaurace vysoké hodnocení (4.5+)
  bool get isTopRated => rating >= 4.5;

  /// Je tradiční chorvatská restaurace
  bool get isTraditionalCroatian => cuisineType == 'traditional';

  /// Má restaurace speciality
  bool get hasSpecialties => specialties.isNotEmpty;

  /// Vzdálenost od zadané pozice (v km)
  double distanceFrom(double lat, double lng) {
    // Haversine formula pro výpočet vzdálenosti
    const double earthRadius = 6371; // km

    final double dLat = _toRadians(latitude - lat);
    final double dLng = _toRadians(longitude - lng);

    final double a =
        sin(dLat / 2) * sin(dLat / 2) +
        cos(_toRadians(lat)) *
            cos(_toRadians(latitude)) *
            sin(dLng / 2) *
            sin(dLng / 2);

    final double c = 2 * asin(sqrt(a));

    return earthRadius * c;
  }

  double _toRadians(double degrees) {
    return degrees * (pi / 180);
  }

  factory Restaurant.fromJson(Map<String, dynamic> json) =>
      _$RestaurantFromJson(json);
  Map<String, dynamic> toJson() => _$RestaurantToJson(this);
}

@JsonSerializable()
class WorkingHours {
  final int dayOfWeek; // 1 = pondělí, 7 = neděle
  final int openHour;
  final int openMinute;
  final int closeHour;
  final int closeMinute;

  WorkingHours({
    required this.dayOfWeek,
    required this.openHour,
    required this.openMinute,
    required this.closeHour,
    required this.closeMinute,
  });

  /// Celkové minuty od půlnoci pro otevření
  int get openMinutes => openHour * 60 + openMinute;

  /// Celkové minuty od půlnoci pro zavření
  int get closeMinutes => closeHour * 60 + closeMinute;

  /// Formátovaný čas otevření
  String get openTimeFormatted =>
      '${openHour.toString().padLeft(2, '0')}:${openMinute.toString().padLeft(2, '0')}';

  /// Formátovaný čas zavření
  String get closeTimeFormatted =>
      '${closeHour.toString().padLeft(2, '0')}:${closeMinute.toString().padLeft(2, '0')}';

  /// Název dne v týdnu
  String get dayName {
    switch (dayOfWeek) {
      case 1:
        return 'Pondělí';
      case 2:
        return 'Úterý';
      case 3:
        return 'Středa';
      case 4:
        return 'Čtvrtek';
      case 5:
        return 'Pátek';
      case 6:
        return 'Sobota';
      case 7:
        return 'Neděle';
      default:
        return 'Neznámý';
    }
  }

  factory WorkingHours.fromJson(Map<String, dynamic> json) =>
      _$WorkingHoursFromJson(json);
  Map<String, dynamic> toJson() => _$WorkingHoursToJson(this);
}

/// Typy kuchyně
enum CuisineType {
  traditional, // Tradiční chorvatská
  seafood, // Mořské plody
  mediterranean, // Středomořská
  international, // Mezinárodní
  pizza, // Pizza
  grill, // Grill
  vegetarian, // Vegetariánská
  fastFood, // Fast food
}

/// Cenové kategorie
enum PriceRange {
  budget, // € (do 100 HRK)
  mid, // €€ (100-200 HRK)
  upscale, // €€€ (200-400 HRK)
  fine, // €€€€ (400+ HRK)
}

/// Regiony Chorvatska
enum RestaurantRegion {
  dalmatia, // Dalmácie
  istria, // Istrie
  slavonia, // Slavonie
  zagreb, // Zagreb
  lika, // Lika
  kvarner, // Kvarner
}

/// Funkce restaurace
enum RestaurantFeature {
  terrace, // Terasa
  seaView, // Výhled na moře
  parking, // Parkování
  wifi, // WiFi
  creditCards, // Platební karty
  delivery, // Rozvoz
  takeaway, // S sebou
  reservation, // Rezervace
  liveMusic, // Živá hudba
  petFriendly, // Pet friendly
  wheelchair, // Bezbariérový přístup
  airConditioning, // Klimatizace
}

/// Hodnocení restaurace od uživatele
@JsonSerializable()
class RestaurantReview {
  final String id;
  final String restaurantId;
  final String userId;
  final String userName;
  final double rating;
  final String comment;
  final List<String> photos;
  final DateTime createdAt;
  final bool isVerified;

  RestaurantReview({
    required this.id,
    required this.restaurantId,
    required this.userId,
    required this.userName,
    required this.rating,
    required this.comment,
    required this.photos,
    required this.createdAt,
    required this.isVerified,
  });

  factory RestaurantReview.fromJson(Map<String, dynamic> json) =>
      _$RestaurantReviewFromJson(json);
  Map<String, dynamic> toJson() => _$RestaurantReviewToJson(this);
}

/// Oblíbená restaurace uživatele
@JsonSerializable()
class FavoriteRestaurant {
  final String id;
  final String userId;
  final String restaurantId;
  final DateTime addedAt;
  final String? notes;

  FavoriteRestaurant({
    required this.id,
    required this.userId,
    required this.restaurantId,
    required this.addedAt,
    this.notes,
  });

  factory FavoriteRestaurant.fromJson(Map<String, dynamic> json) =>
      _$FavoriteRestaurantFromJson(json);
  Map<String, dynamic> toJson() => _$FavoriteRestaurantToJson(this);
}

/// Návštěva restaurace
@JsonSerializable()
class RestaurantVisit {
  final String id;
  final String userId;
  final String restaurantId;
  final DateTime visitDate;
  final double? userRating;
  final String? notes;
  final List<String> photos;
  final double? totalSpent;

  RestaurantVisit({
    required this.id,
    required this.userId,
    required this.restaurantId,
    required this.visitDate,
    this.userRating,
    this.notes,
    required this.photos,
    this.totalSpent,
  });

  factory RestaurantVisit.fromJson(Map<String, dynamic> json) =>
      _$RestaurantVisitFromJson(json);
  Map<String, dynamic> toJson() => _$RestaurantVisitToJson(this);
}
