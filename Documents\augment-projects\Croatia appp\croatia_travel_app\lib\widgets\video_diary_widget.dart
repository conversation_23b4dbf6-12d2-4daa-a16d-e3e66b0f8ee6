import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/video_diary.dart';
import '../services/video_diary_service.dart';

class VideoDiaryWidget extends StatefulWidget {
  final List<VideoDiary> videos;
  final Function(VideoDiary)? onVideoTap;
  final Function(VideoDiary)? onVideoEdit;
  final Function(VideoDiary)? onVideoDelete;
  final bool showControls;

  const VideoDiaryWidget({
    super.key,
    required this.videos,
    this.onVideoTap,
    this.onVideoEdit,
    this.onVideoDelete,
    this.showControls = true,
  });

  @override
  State<VideoDiaryWidget> createState() => _VideoDiaryWidgetState();
}

class _VideoDiaryWidgetState extends State<VideoDiaryWidget> {
  final VideoDiaryService _videoService = VideoDiaryService();

  @override
  Widget build(BuildContext context) {
    if (widget.videos.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(
                Icons.videocam,
                color: const Color(0xFF006994),
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Video deník',
                style: GoogleFonts.playfairDisplay(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF006994),
                ),
              ),
              const Spacer(),
              if (widget.showControls)
                IconButton(
                  onPressed: _showVideoOptions,
                  icon: const Icon(Icons.add_circle_outline),
                  color: const Color(0xFF006994),
                ),
            ],
          ),
        ),

        // Video grid
        Expanded(
          child: GridView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 16 / 9,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: widget.videos.length,
            itemBuilder: (context, index) {
              final video = widget.videos[index];
              return _buildVideoCard(video);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: const Color(0xFF006994).withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.videocam_outlined,
              size: 60,
              color: Color(0xFF006994),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Žádná videa',
            style: GoogleFonts.playfairDisplay(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2C2C2C),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Začněte natáčet své cestovní vzpomínky',
            style: GoogleFonts.inter(
              fontSize: 16,
              color: const Color(0xFF666666),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: _showVideoOptions,
            icon: const Icon(Icons.videocam),
            label: const Text('Natočit první video'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF006994),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoCard(VideoDiary video) {
    return GestureDetector(
      onTap: () => widget.onVideoTap?.call(video),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            children: [
              // Thumbnail nebo placeholder
              Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xFF006994).withValues(alpha: 0.8),
                      const Color(0xFF2E8B8B).withValues(alpha: 0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: video.hasThumbnail
                    ? Image.asset(
                        video.thumbnailPath!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) =>
                            _buildVideoPlaceholder(),
                      )
                    : _buildVideoPlaceholder(),
              ),

              // Play button overlay
              Center(
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.9),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.play_arrow,
                    color: Color(0xFF006994),
                    size: 30,
                  ),
                ),
              ),

              // Video info overlay
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.7),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        video.title,
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            size: 12,
                            color: Colors.white.withValues(alpha: 0.8),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            video.formattedDuration,
                            style: GoogleFonts.inter(
                              fontSize: 12,
                              color: Colors.white.withValues(alpha: 0.8),
                            ),
                          ),
                          const Spacer(),
                          if (video.isEdited)
                            Icon(
                              Icons.edit,
                              size: 12,
                              color: Colors.white.withValues(alpha: 0.8),
                            ),
                          if (video.isFavorite)
                            Icon(
                              Icons.favorite,
                              size: 12,
                              color: const Color(0xFFFF6B35),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // Controls menu
              if (widget.showControls)
                Positioned(
                  top: 8,
                  right: 8,
                  child: GestureDetector(
                    onTap: () => _showVideoMenu(video),
                    child: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.5),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.more_vert,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ),

              // Quality indicator
              Positioned(
                top: 8,
                left: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    video.quality.displayName.split(' ')[1], // Jen rozlišení
                    style: GoogleFonts.inter(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVideoPlaceholder() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF006994).withValues(alpha: 0.3),
            const Color(0xFF2E8B8B).withValues(alpha: 0.3),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: const Icon(
        Icons.videocam_outlined,
        size: 40,
        color: Colors.white,
      ),
    );
  }

  void _showVideoOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Color(0xFFF8F6F0),
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[400],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  Text(
                    'Přidat video',
                    style: GoogleFonts.playfairDisplay(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF006994),
                    ),
                  ),
                  const SizedBox(height: 24),
                  _buildOptionTile(
                    icon: Icons.videocam,
                    title: 'Natočit video',
                    subtitle: 'Natočte nové video přímo v aplikaci',
                    onTap: _startVideoRecording,
                  ),
                  const SizedBox(height: 16),
                  _buildOptionTile(
                    icon: Icons.video_library,
                    title: 'Vybrat z galerie',
                    subtitle: 'Importujte existující video',
                    onTap: _pickVideoFromGallery,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFF006994).withValues(alpha: 0.2)),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF006994).withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: const Color(0xFF006994)),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF2C2C2C),
                    ),
                  ),
                  Text(
                    subtitle,
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: const Color(0xFF666666),
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Color(0xFF666666),
            ),
          ],
        ),
      ),
    );
  }

  void _showVideoMenu(VideoDiary video) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Color(0xFFF8F6F0),
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[400],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  ListTile(
                    leading: const Icon(Icons.edit, color: Color(0xFF006994)),
                    title: const Text('Upravit video'),
                    onTap: () {
                      Navigator.pop(context);
                      widget.onVideoEdit?.call(video);
                    },
                  ),
                  ListTile(
                    leading: Icon(
                      video.isFavorite ? Icons.favorite : Icons.favorite_border,
                      color: const Color(0xFFFF6B35),
                    ),
                    title: Text(video.isFavorite ? 'Odebrat z oblíbených' : 'Přidat do oblíbených'),
                    onTap: () {
                      Navigator.pop(context);
                      // Toggle favorite
                    },
                  ),
                  ListTile(
                    leading: const Icon(Icons.share, color: Color(0xFF2E8B8B)),
                    title: const Text('Sdílet video'),
                    onTap: () {
                      Navigator.pop(context);
                      // Share video
                    },
                  ),
                  ListTile(
                    leading: const Icon(Icons.delete, color: Colors.red),
                    title: const Text('Smazat video'),
                    onTap: () {
                      Navigator.pop(context);
                      widget.onVideoDelete?.call(video);
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _startVideoRecording() async {
    Navigator.pop(context);
    // Implementace nahrávání videa
    final path = await _videoService.startVideoRecording();
    if (path != null) {
      // Zobrazit recording UI
    }
  }

  Future<void> _pickVideoFromGallery() async {
    Navigator.pop(context);
    final video = await _videoService.pickVideoFromGallery();
    if (video != null) {
      // Přidat video do seznamu
    }
  }
}
