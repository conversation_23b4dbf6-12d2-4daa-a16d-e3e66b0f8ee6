import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class MapControlsWidget extends StatelessWidget {
  final Function(MapType) onMapTypeChanged;
  final Function(bool) onShowVisitedToggled;
  final Function(bool) onShowEventsToggled;
  final bool showVisitedOnly;
  final bool showEvents;
  final MapType mapType;

  const MapControlsWidget({
    super.key,
    required this.onMapTypeChanged,
    required this.onShowVisitedToggled,
    required this.onShowEventsToggled,
    required this.showVisitedOnly,
    required this.showEvents,
    required this.mapType,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Typ mapy
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildMapTypeButton(
                context,
                'Normální',
                MapType.normal,
                Icons.map,
              ),
              const Divider(height: 1),
              _buildMapTypeButton(
                context,
                'Satelit',
                MapType.satellite,
                Icons.satellite,
              ),
              const Divider(height: 1),
              _buildMapTypeButton(
                context,
                'Terén',
                MapType.terrain,
                Icons.terrain,
              ),
            ],
          ),
        ),

        const SizedBox(height: 8),

        // Filtry
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildFilterButton(
                context,
                'Navštívená',
                showVisitedOnly,
                Icons.check_circle,
                onShowVisitedToggled,
              ),
              const Divider(height: 1),
              _buildFilterButton(
                context,
                'Události',
                showEvents,
                Icons.event,
                onShowEventsToggled,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMapTypeButton(
    BuildContext context,
    String label,
    MapType type,
    IconData icon,
  ) {
    final isSelected = mapType == type;

    return InkWell(
      onTap: () => onMapTypeChanged(type),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 18,
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Colors.grey[600],
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Colors.grey[700],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterButton(
    BuildContext context,
    String label,
    bool isActive,
    IconData icon,
    Function(bool) onToggle,
  ) {
    return InkWell(
      onTap: () => onToggle(!isActive),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 18,
              color: isActive
                  ? Theme.of(context).colorScheme.primary
                  : Colors.grey[600],
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                color: isActive
                    ? Theme.of(context).colorScheme.primary
                    : Colors.grey[700],
              ),
            ),
            const SizedBox(width: 4),
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: isActive
                    ? Theme.of(context).colorScheme.primary
                    : Colors.grey[300],
                shape: BoxShape.circle,
              ),
              child: isActive
                  ? const Icon(Icons.check, size: 8, color: Colors.white)
                  : null,
            ),
          ],
        ),
      ),
    );
  }
}
