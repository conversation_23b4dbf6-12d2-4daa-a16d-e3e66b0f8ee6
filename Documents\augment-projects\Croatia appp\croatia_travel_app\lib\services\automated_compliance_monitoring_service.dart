import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/compliance_monitoring.dart';

/// 🔄 AUTOMATED COMPLIANCE MONITORING SERVICE - Continuous compliance monitoring
class AutomatedComplianceMonitoringService {
  static final AutomatedComplianceMonitoringService _instance =
      AutomatedComplianceMonitoringService._internal();
  factory AutomatedComplianceMonitoringService() => _instance;
  AutomatedComplianceMonitoringService._internal();

  bool _isInitialized = false;
  final List<ComplianceRule> _rules = [];
  final List<ComplianceViolation> _violations = [];
  final Map<String, Timer> _monitoringTimers = {};
  final StreamController<ComplianceEvent> _eventController =
      StreamController.broadcast();

  /// Stream compliance událostí
  Stream<ComplianceEvent> get complianceEvents => _eventController.stream;

  /// Inicializace služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🔄 Inicializuji Automated Compliance Monitoring Service...');

      await _loadComplianceRules();
      await _loadViolationHistory();
      await startMonitoring();

      _isInitialized = true;
      debugPrint('✅ Automated Compliance Monitoring Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Compliance Monitoring: $e');
      await _createDefaultRules();
      _isInitialized = true;
    }
  }

  /// Spuštění continuous monitoring
  Future<void> startContinuousMonitoring() async {
    debugPrint('🔄 Spouštím continuous compliance monitoring...');

    // Real-time monitoring (každou minutu)
    _monitoringTimers['realtime'] = Timer.periodic(const Duration(minutes: 1), (
      _,
    ) async {
      await _performRealtimeChecks();
    });

    // Hourly monitoring
    _monitoringTimers['hourly'] = Timer.periodic(const Duration(hours: 1), (
      _,
    ) async {
      await _performHourlyChecks();
    });

    // Daily monitoring
    _monitoringTimers['daily'] = Timer.periodic(const Duration(hours: 24), (
      _,
    ) async {
      await _performDailyChecks();
    });

    // Weekly monitoring
    _monitoringTimers['weekly'] = Timer.periodic(const Duration(days: 7), (
      _,
    ) async {
      await _performWeeklyChecks();
    });

    _eventController.add(
      ComplianceEvent(
        type: ComplianceEventType.monitoringStarted,
        timestamp: DateTime.now(),
        data: {
          'monitoringTypes': ['realtime', 'hourly', 'daily', 'weekly'],
        },
      ),
    );
  }

  /// Zastavení monitoring
  Future<void> stopMonitoring() async {
    debugPrint('⏹️ Zastavuji compliance monitoring...');

    for (final timer in _monitoringTimers.values) {
      timer.cancel();
    }
    _monitoringTimers.clear();

    _eventController.add(
      ComplianceEvent(
        type: ComplianceEventType.monitoringStopped,
        timestamp: DateTime.now(),
        data: {},
      ),
    );
  }

  /// Přidání nového compliance rule
  Future<bool> addComplianceRule({
    required String name,
    required String description,
    required ComplianceFramework framework,
    required RuleSeverity severity,
    required Duration checkInterval,
    required Future<ComplianceCheckResult> Function() checkFunction,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final rule = ComplianceRule(
        id: 'rule_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        description: description,
        framework: framework,
        severity: severity,
        checkInterval: checkInterval,
        isActive: true,
        createdAt: DateTime.now(),
        lastChecked: null,
        metadata: metadata ?? {},
      );

      _rules.add(rule);
      await _saveComplianceRules();

      // Spuštění monitoring pro nové pravidlo
      await _startRuleMonitoring(rule, checkFunction);

      _eventController.add(
        ComplianceEvent(
          type: ComplianceEventType.ruleAdded,
          ruleId: rule.id,
          timestamp: DateTime.now(),
          data: {'framework': framework.name, 'severity': severity.name},
        ),
      );

      debugPrint('✅ Compliance rule přidáno: $name');
      return true;
    } catch (e) {
      debugPrint('❌ Chyba při přidávání compliance rule: $e');
      return false;
    }
  }

  /// Manuální spuštění compliance check
  Future<ComplianceReport> runComplianceCheck({
    ComplianceFramework? framework,
    RuleSeverity? minSeverity,
  }) async {
    try {
      debugPrint('🔍 Spouštím manuální compliance check...');

      final checkId = 'check_${DateTime.now().millisecondsSinceEpoch}';
      final startTime = DateTime.now();

      // Filtrování rules podle parametrů
      var rulesToCheck = _rules.where((rule) => rule.isActive);

      if (framework != null) {
        rulesToCheck = rulesToCheck.where(
          (rule) => rule.framework == framework,
        );
      }

      if (minSeverity != null) {
        rulesToCheck = rulesToCheck.where(
          (rule) =>
              _getSeverityLevel(rule.severity) >=
              _getSeverityLevel(minSeverity),
        );
      }

      final results = <ComplianceCheckResult>[];
      final violations = <ComplianceViolation>[];

      // Spuštění kontrol
      for (final rule in rulesToCheck) {
        final result = await _executeRuleCheck(rule);
        results.add(result);

        if (!result.isCompliant) {
          final violation = ComplianceViolation(
            id: 'violation_${DateTime.now().millisecondsSinceEpoch}',
            ruleId: rule.id,
            ruleName: rule.name,
            framework: rule.framework,
            severity: rule.severity,
            description: result.details,
            detectedAt: DateTime.now(),
            status: ViolationStatus.open,
            recommendedActions: result.recommendations,
          );

          violations.add(violation);
          _violations.add(violation);
        }

        // Aktualizace rule status
        final ruleIndex = _rules.indexWhere((r) => r.id == rule.id);
        _rules[ruleIndex] = rule.copyWith(lastChecked: DateTime.now());
      }

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      // Výpočet compliance score
      final totalRules = results.length;
      final compliantRules = results.where((r) => r.isCompliant).length;
      final complianceScore = totalRules > 0
          ? (compliantRules / totalRules * 100)
          : 100.0;

      final report = ComplianceReport(
        id: checkId,
        startTime: startTime,
        endTime: endTime,
        duration: duration,
        framework: framework,
        totalRulesChecked: totalRules,
        compliantRules: compliantRules,
        violationsFound: violations.length,
        complianceScore: complianceScore,
        results: results,
        violations: violations,
        recommendations: _generateComplianceRecommendations(violations),
      );

      await _saveViolationHistory();

      _eventController.add(
        ComplianceEvent(
          type: ComplianceEventType.checkCompleted,
          checkId: checkId,
          timestamp: DateTime.now(),
          data: {
            'complianceScore': complianceScore,
            'violationsFound': violations.length,
            'framework': framework?.name,
          },
        ),
      );

      debugPrint(
        '✅ Compliance check dokončen: ${complianceScore.toStringAsFixed(1)}%',
      );
      return report;
    } catch (e) {
      debugPrint('❌ Chyba při compliance check: $e');
      rethrow;
    }
  }

  /// Získání compliance dashboard
  Future<ComplianceDashboard> getComplianceDashboard() async {
    final now = DateTime.now();
    final last24h = now.subtract(const Duration(hours: 24));
    final last7d = now.subtract(const Duration(days: 7));
    final last30d = now.subtract(const Duration(days: 30));

    // Recent violations
    final recentViolations = _violations
        .where((v) => v.detectedAt.isAfter(last24h))
        .toList();
    final openViolations = _violations
        .where((v) => v.status == ViolationStatus.open)
        .toList();

    // Compliance trends
    final weeklyTrend = await _calculateComplianceTrend(last7d, now);
    final monthlyTrend = await _calculateComplianceTrend(last30d, now);

    // Framework status
    final frameworkStatus = <ComplianceFramework, FrameworkStatus>{};
    for (final framework in ComplianceFramework.values) {
      frameworkStatus[framework] = await _getFrameworkStatus(framework);
    }

    // Critical issues
    final criticalIssues = _violations
        .where(
          (v) =>
              v.severity == RuleSeverity.critical &&
              v.status == ViolationStatus.open,
        )
        .toList();

    return ComplianceDashboard(
      generatedAt: now,
      overallComplianceScore: await _calculateOverallComplianceScore(),
      activeRules: _rules.where((r) => r.isActive).length,
      totalViolations: _violations.length,
      openViolations: openViolations.length,
      recentViolations: recentViolations.length,
      criticalIssues: criticalIssues.length,
      weeklyTrend: weeklyTrend,
      monthlyTrend: monthlyTrend,
      frameworkStatus: frameworkStatus,
      upcomingDeadlines: _getUpcomingDeadlines(),
      lastFullCheck: _getLastFullCheckTime(),
      monitoringStatus: _getMonitoringStatus(),
    );
  }

  /// Real-time monitoring checks
  Future<void> _performRealtimeChecks() async {
    // Kontrola kritických pravidel
    final criticalRules = _rules.where(
      (rule) =>
          rule.isActive &&
          rule.severity == RuleSeverity.critical &&
          rule.checkInterval.inMinutes <= 1,
    );

    for (final rule in criticalRules) {
      await _executeAndProcessRuleCheck(rule);
    }
  }

  /// Hourly monitoring checks
  Future<void> _performHourlyChecks() async {
    final hourlyRules = _rules.where(
      (rule) =>
          rule.isActive &&
          rule.checkInterval.inHours <= 1 &&
          (rule.lastChecked == null ||
              DateTime.now().difference(rule.lastChecked!).inHours >= 1),
    );

    for (final rule in hourlyRules) {
      await _executeAndProcessRuleCheck(rule);
    }
  }

  /// Daily monitoring checks
  Future<void> _performDailyChecks() async {
    final dailyRules = _rules.where(
      (rule) =>
          rule.isActive &&
          rule.checkInterval.inDays <= 1 &&
          (rule.lastChecked == null ||
              DateTime.now().difference(rule.lastChecked!).inDays >= 1),
    );

    for (final rule in dailyRules) {
      await _executeAndProcessRuleCheck(rule);
    }

    // Generování daily compliance reportu
    await _generateDailyReport();
  }

  /// Weekly monitoring checks
  Future<void> _performWeeklyChecks() async {
    final weeklyRules = _rules.where(
      (rule) =>
          rule.isActive &&
          rule.checkInterval.inDays <= 7 &&
          (rule.lastChecked == null ||
              DateTime.now().difference(rule.lastChecked!).inDays >= 7),
    );

    for (final rule in weeklyRules) {
      await _executeAndProcessRuleCheck(rule);
    }

    // Generování weekly compliance reportu
    await _generateWeeklyReport();
  }

  /// Spuštění monitoring pro konkrétní pravidlo
  Future<void> _startRuleMonitoring(
    ComplianceRule rule,
    Future<ComplianceCheckResult> Function() checkFunction,
  ) async {
    // Uložení check function (v produkci by se použil jiný mechanismus)
    // Pro demo účely simulujeme check function

    Timer.periodic(rule.checkInterval, (_) async {
      if (rule.isActive) {
        await _executeAndProcessRuleCheck(rule);
      }
    });
  }

  /// Spuštění a zpracování rule check
  Future<void> _executeAndProcessRuleCheck(ComplianceRule rule) async {
    try {
      final result = await _executeRuleCheck(rule);

      if (!result.isCompliant) {
        final violation = ComplianceViolation(
          id: 'violation_${DateTime.now().millisecondsSinceEpoch}',
          ruleId: rule.id,
          ruleName: rule.name,
          framework: rule.framework,
          severity: rule.severity,
          description: result.details,
          detectedAt: DateTime.now(),
          status: ViolationStatus.open,
          recommendedActions: result.recommendations,
        );

        _violations.add(violation);
        await _saveViolationHistory();

        _eventController.add(
          ComplianceEvent(
            type: ComplianceEventType.violationDetected,
            ruleId: rule.id,
            violationId: violation.id,
            timestamp: DateTime.now(),
            data: {
              'severity': rule.severity.name,
              'framework': rule.framework.name,
            },
          ),
        );

        // Okamžité upozornění pro kritické violations
        if (rule.severity == RuleSeverity.critical) {
          await _sendCriticalAlert(violation);
        }
      }

      // Aktualizace rule
      final ruleIndex = _rules.indexWhere((r) => r.id == rule.id);
      _rules[ruleIndex] = rule.copyWith(lastChecked: DateTime.now());
      await _saveComplianceRules();
    } catch (e) {
      debugPrint('❌ Chyba při rule check ${rule.name}: $e');
    }
  }

  /// Simulace rule check execution
  Future<ComplianceCheckResult> _executeRuleCheck(ComplianceRule rule) async {
    // Simulace různých typů kontrol podle framework
    await Future.delayed(const Duration(milliseconds: 100));

    switch (rule.framework) {
      case ComplianceFramework.gdpr:
        return await _checkGDPRCompliance(rule);
      case ComplianceFramework.soc2:
        return await _checkSOC2Compliance(rule);
      case ComplianceFramework.iso27001:
        return await _checkISO27001Compliance(rule);
      case ComplianceFramework.pci:
        return await _checkPCICompliance(rule);
      case ComplianceFramework.hipaa:
        return await _checkHIPAACompliance(rule);
      case ComplianceFramework.custom:
        return await _checkCustomCompliance(rule);
    }
  }

  /// GDPR compliance checks
  Future<ComplianceCheckResult> _checkGDPRCompliance(
    ComplianceRule rule,
  ) async {
    // Simulace GDPR kontrol
    final isCompliant = Random().nextDouble() > 0.1; // 90% compliance rate

    return ComplianceCheckResult(
      ruleId: rule.id,
      isCompliant: isCompliant,
      checkedAt: DateTime.now(),
      details: isCompliant
          ? 'GDPR compliance verified'
          : 'GDPR compliance violation detected',
      evidence: isCompliant
          ? ['Privacy policy updated', 'Consent mechanisms active']
          : ['Missing consent records', 'Outdated privacy policy'],
      recommendations: isCompliant
          ? ['Continue monitoring']
          : ['Update privacy policy', 'Implement proper consent management'],
    );
  }

  /// SOC 2 compliance checks
  Future<ComplianceCheckResult> _checkSOC2Compliance(
    ComplianceRule rule,
  ) async {
    final isCompliant = Random().nextDouble() > 0.05; // 95% compliance rate

    return ComplianceCheckResult(
      ruleId: rule.id,
      isCompliant: isCompliant,
      checkedAt: DateTime.now(),
      details: isCompliant
          ? 'SOC 2 controls operating effectively'
          : 'SOC 2 control deficiency identified',
      evidence: isCompliant
          ? ['Access controls verified', 'Monitoring systems active']
          : ['Access control gaps', 'Monitoring system issues'],
      recommendations: isCompliant
          ? ['Maintain current controls']
          : ['Strengthen access controls', 'Enhance monitoring'],
    );
  }

  /// ISO 27001 compliance checks
  Future<ComplianceCheckResult> _checkISO27001Compliance(
    ComplianceRule rule,
  ) async {
    final isCompliant = Random().nextDouble() > 0.08; // 92% compliance rate

    return ComplianceCheckResult(
      ruleId: rule.id,
      isCompliant: isCompliant,
      checkedAt: DateTime.now(),
      details: isCompliant
          ? 'ISO 27001 requirements met'
          : 'ISO 27001 non-conformity detected',
      evidence: isCompliant
          ? ['Security policies current', 'Risk assessments completed']
          : ['Outdated security policies', 'Missing risk assessments'],
      recommendations: isCompliant
          ? ['Continue compliance program']
          : ['Update security policies', 'Complete risk assessments'],
    );
  }

  /// PCI compliance checks
  Future<ComplianceCheckResult> _checkPCICompliance(ComplianceRule rule) async {
    final isCompliant = Random().nextDouble() > 0.03; // 97% compliance rate

    return ComplianceCheckResult(
      ruleId: rule.id,
      isCompliant: isCompliant,
      checkedAt: DateTime.now(),
      details: isCompliant
          ? 'PCI DSS requirements satisfied'
          : 'PCI DSS violation detected',
      evidence: isCompliant
          ? ['Payment data encrypted', 'Network security verified']
          : ['Unencrypted payment data', 'Network security gaps'],
      recommendations: isCompliant
          ? ['Maintain security measures']
          : ['Encrypt payment data', 'Strengthen network security'],
    );
  }

  /// HIPAA compliance checks
  Future<ComplianceCheckResult> _checkHIPAACompliance(
    ComplianceRule rule,
  ) async {
    final isCompliant = Random().nextDouble() > 0.02; // 98% compliance rate

    return ComplianceCheckResult(
      ruleId: rule.id,
      isCompliant: isCompliant,
      checkedAt: DateTime.now(),
      details: isCompliant
          ? 'HIPAA safeguards in place'
          : 'HIPAA safeguard deficiency',
      evidence: isCompliant
          ? ['PHI properly protected', 'Access controls verified']
          : ['PHI exposure risk', 'Inadequate access controls'],
      recommendations: isCompliant
          ? ['Continue safeguards']
          : ['Enhance PHI protection', 'Strengthen access controls'],
    );
  }

  /// Custom compliance checks
  Future<ComplianceCheckResult> _checkCustomCompliance(
    ComplianceRule rule,
  ) async {
    final isCompliant = Random().nextDouble() > 0.15; // 85% compliance rate

    return ComplianceCheckResult(
      ruleId: rule.id,
      isCompliant: isCompliant,
      checkedAt: DateTime.now(),
      details: isCompliant
          ? 'Custom compliance rule satisfied'
          : 'Custom compliance rule violation',
      evidence: isCompliant ? ['Requirements met'] : ['Requirements not met'],
      recommendations: isCompliant
          ? ['Continue monitoring']
          : ['Address compliance gaps'],
    );
  }

  /// Pomocné metody
  int _getSeverityLevel(RuleSeverity severity) {
    switch (severity) {
      case RuleSeverity.critical:
        return 4;
      case RuleSeverity.high:
        return 3;
      case RuleSeverity.medium:
        return 2;
      case RuleSeverity.low:
        return 1;
    }
  }

  List<String> _generateComplianceRecommendations(
    List<ComplianceViolation> violations,
  ) {
    final recommendations = <String>[];

    final criticalViolations = violations.where(
      (v) => v.severity == RuleSeverity.critical,
    );
    if (criticalViolations.isNotEmpty) {
      recommendations.add('Okamžitě řešte kritické compliance violations');
    }

    final gdprViolations = violations.where(
      (v) => v.framework == ComplianceFramework.gdpr,
    );
    if (gdprViolations.isNotEmpty) {
      recommendations.add('Proveďte GDPR compliance review');
    }

    recommendations.addAll([
      'Implementujte automated compliance testing',
      'Proveďte regular compliance training',
      'Aktualizujte compliance policies',
      'Zlepšete incident response procedures',
    ]);

    return recommendations;
  }

  Future<double> _calculateComplianceTrend(DateTime from, DateTime to) async {
    // Simulace výpočtu trendu
    return 0.85 + Random().nextDouble() * 0.1; // 85-95%
  }

  Future<FrameworkStatus> _getFrameworkStatus(
    ComplianceFramework framework,
  ) async {
    final frameworkRules = _rules.where(
      (r) => r.framework == framework && r.isActive,
    );
    final frameworkViolations = _violations.where(
      (v) => v.framework == framework && v.status == ViolationStatus.open,
    );

    final complianceRate = frameworkRules.isNotEmpty
        ? (frameworkRules.length - frameworkViolations.length) /
              frameworkRules.length
        : 1.0;

    return FrameworkStatus(
      framework: framework,
      complianceRate: complianceRate,
      activeRules: frameworkRules.length,
      openViolations: frameworkViolations.length,
      lastChecked: frameworkRules.isNotEmpty
          ? frameworkRules
                .map((r) => r.lastChecked)
                .where((d) => d != null)
                .fold<DateTime?>(
                  null,
                  (latest, date) =>
                      latest == null || date!.isAfter(latest) ? date : latest,
                )
          : null,
    );
  }

  Future<double> _calculateOverallComplianceScore() async {
    if (_rules.isEmpty) return 100.0;

    final activeRules = _rules.where((r) => r.isActive);
    final openViolations = _violations.where(
      (v) => v.status == ViolationStatus.open,
    );

    return ((activeRules.length - openViolations.length) /
            activeRules.length *
            100)
        .clamp(0.0, 100.0);
  }

  List<DateTime> _getUpcomingDeadlines() {
    // Simulace upcoming deadlines
    final now = DateTime.now();
    return [
      now.add(const Duration(days: 7)),
      now.add(const Duration(days: 30)),
      now.add(const Duration(days: 90)),
    ];
  }

  DateTime? _getLastFullCheckTime() {
    if (_rules.isEmpty) return null;

    final lastCheckedTimes = _rules
        .where((r) => r.lastChecked != null)
        .map((r) => r.lastChecked!)
        .toList();

    if (lastCheckedTimes.isEmpty) return null;

    lastCheckedTimes.sort((a, b) => b.compareTo(a));
    return lastCheckedTimes.first;
  }

  MonitoringStatus _getMonitoringStatus() {
    return MonitoringStatus(
      isActive: _monitoringTimers.isNotEmpty,
      activeMonitors: _monitoringTimers.keys.toList(),
      lastActivity: DateTime.now(),
    );
  }

  Future<void> _sendCriticalAlert(ComplianceViolation violation) async {
    // Simulace odeslání kritického upozornění
    debugPrint('🚨 CRITICAL COMPLIANCE ALERT: ${violation.ruleName}');

    _eventController.add(
      ComplianceEvent(
        type: ComplianceEventType.criticalAlert,
        violationId: violation.id,
        timestamp: DateTime.now(),
        data: {
          'severity': violation.severity.name,
          'framework': violation.framework.name,
          'description': violation.description,
        },
      ),
    );
  }

  Future<void> _generateDailyReport() async {
    final report = await runComplianceCheck();
    debugPrint(
      '📊 Daily compliance report generated: ${report.complianceScore.toStringAsFixed(1)}%',
    );
  }

  Future<void> _generateWeeklyReport() async {
    final report = await runComplianceCheck();
    debugPrint(
      '📈 Weekly compliance report generated: ${report.complianceScore.toStringAsFixed(1)}%',
    );
  }

  /// Načítání a ukládání dat
  Future<void> _loadComplianceRules() async {
    await _createDefaultRules();
  }

  Future<void> _loadViolationHistory() async {
    // Load violation history from storage
  }

  Future<void> _saveComplianceRules() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'compliance_rules',
        jsonEncode(_rules.map((r) => r.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání compliance rules: $e');
    }
  }

  Future<void> _saveViolationHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'compliance_violations',
        jsonEncode(_violations.map((v) => v.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání violations: $e');
    }
  }

  Future<void> _createDefaultRules() async {
    _rules.addAll([
      ComplianceRule(
        id: 'gdpr_privacy_policy',
        name: 'GDPR Privacy Policy Current',
        description: 'Privacy policy must be current and GDPR compliant',
        framework: ComplianceFramework.gdpr,
        severity: RuleSeverity.critical,
        checkInterval: const Duration(hours: 24),
        isActive: true,
        createdAt: DateTime.now(),
        lastChecked: null,
        metadata: {'article': 'Article 13', 'requirement': 'Transparency'},
      ),
      ComplianceRule(
        id: 'soc2_access_control',
        name: 'SOC 2 Access Controls',
        description:
            'Access controls must be properly implemented and monitored',
        framework: ComplianceFramework.soc2,
        severity: RuleSeverity.high,
        checkInterval: const Duration(hours: 1),
        isActive: true,
        createdAt: DateTime.now(),
        lastChecked: null,
        metadata: {'control': 'CC6.1', 'category': 'Security'},
      ),
      ComplianceRule(
        id: 'iso27001_risk_assessment',
        name: 'ISO 27001 Risk Assessment',
        description: 'Risk assessments must be conducted regularly',
        framework: ComplianceFramework.iso27001,
        severity: RuleSeverity.medium,
        checkInterval: const Duration(days: 30),
        isActive: true,
        createdAt: DateTime.now(),
        lastChecked: null,
        metadata: {'clause': 'A.12.6', 'category': 'Risk Management'},
      ),
    ]);
  }

  @override
  void dispose() {
    for (final timer in _monitoringTimers.values) {
      timer.cancel();
    }
    _eventController.close();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  List<ComplianceRule> get rules => List.unmodifiable(_rules);
  List<ComplianceViolation> get violations => List.unmodifiable(_violations);
  bool get isMonitoring => _monitoringTimers.isNotEmpty;
}
