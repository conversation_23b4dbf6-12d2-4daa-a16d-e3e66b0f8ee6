import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:geolocator/geolocator.dart';
import '../models/daily_prompt.dart';
import '../models/diary_entry.dart';

/// 📝 DAILY PROMPTS SERVICE - Denní výzvy a chytré př<PERSON>ínky
class DailyPromptsService {
  static final DailyPromptsService _instance = DailyPromptsService._internal();
  factory DailyPromptsService() => _instance;
  DailyPromptsService._internal();

  bool _isInitialized = false;
  final List<DailyPrompt> _prompts = [];
  final List<SmartReminder> _reminders = [];
  final List<DiaryEntry> _userEntries = [];
  Timer? _reminderTimer;

  /// Inicializace služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('📝 Inicializuji Daily Prompts Service...');
      
      await _loadPrompts();
      await _loadReminders();
      await _loadUserEntries();
      _startReminderTimer();
      
      _isInitialized = true;
      debugPrint('✅ Daily Prompts Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Daily Prompts: $e');
      await _createDefaultPrompts();
      _isInitialized = true;
    }
  }

  /// Získání denní výzvy
  Future<DailyPrompt> getTodaysPrompt() async {
    await _ensureInitialized();

    final today = DateTime.now();
    final todayKey = '${today.year}-${today.month}-${today.day}';
    
    // Kontrola, zda už má uživatel prompt pro dnes
    final prefs = await SharedPreferences.getInstance();
    final savedPromptJson = prefs.getString('daily_prompt_$todayKey');
    
    if (savedPromptJson != null) {
      return DailyPrompt.fromJson(jsonDecode(savedPromptJson));
    }

    // Generování nového promptu na základě kontextu
    final prompt = await _generateContextualPrompt();
    
    // Uložení promptu pro dnešek
    await prefs.setString('daily_prompt_$todayKey', jsonEncode(prompt.toJson()));
    
    return prompt;
  }

  /// Generování kontextového promptu
  Future<DailyPrompt> _generateContextualPrompt() async {
    final context = await _analyzeUserContext();
    final availablePrompts = _getPromptsForContext(context);
    
    if (availablePrompts.isEmpty) {
      return _getRandomPrompt();
    }

    // Výběr promptu na základě kontextu a historie
    final prompt = _selectBestPrompt(availablePrompts, context);
    
    return prompt.copyWith(
      personalizedMessage: _personalizePrompt(prompt, context),
      suggestedTags: _generateSuggestedTags(context),
    );
  }

  /// Analýza uživatelského kontextu
  Future<UserContext> _analyzeUserContext() async {
    final now = DateTime.now();
    
    // Analýza lokace
    Position? position;
    try {
      position = await Geolocator.getCurrentPosition();
    } catch (e) {
      debugPrint('Nelze získat lokaci: $e');
    }

    // Analýza počasí (simulace)
    final weather = await _getCurrentWeather();
    
    // Analýza historie zápisů
    final recentEntries = _userEntries
        .where((entry) => now.difference(entry.date).inDays <= 7)
        .toList();
    
    // Analýza vzorců
    final patterns = _analyzeWritingPatterns();
    
    return UserContext(
      currentTime: now,
      location: position,
      weather: weather,
      recentEntries: recentEntries,
      writingPatterns: patterns,
      dayOfWeek: now.weekday,
      isWeekend: now.weekday >= 6,
      timeOfDay: _getTimeOfDay(now),
    );
  }

  /// Získání promptů pro kontext
  List<DailyPrompt> _getPromptsForContext(UserContext context) {
    return _prompts.where((prompt) {
      // Filtrování podle času
      if (prompt.timeOfDay != null && prompt.timeOfDay != context.timeOfDay) {
        return false;
      }
      
      // Filtrování podle počasí
      if (prompt.weatherConditions.isNotEmpty && 
          !prompt.weatherConditions.contains(context.weather)) {
        return false;
      }
      
      // Filtrování podle lokace
      if (prompt.locationTypes.isNotEmpty && context.location != null) {
        final locationType = _getLocationType(context.location!);
        if (!prompt.locationTypes.contains(locationType)) {
          return false;
        }
      }
      
      // Filtrování podle nálady
      if (context.recentEntries.isNotEmpty) {
        final recentMoods = context.recentEntries
            .where((e) => e.mood != null)
            .map((e) => e.mood!)
            .toList();
        
        if (prompt.targetMoods.isNotEmpty && 
            !prompt.targetMoods.any((mood) => recentMoods.contains(mood))) {
          return false;
        }
      }
      
      return true;
    }).toList();
  }

  /// Výběr nejlepšího promptu
  DailyPrompt _selectBestPrompt(List<DailyPrompt> prompts, UserContext context) {
    if (prompts.length == 1) return prompts.first;
    
    // Skórování promptů
    final scored = prompts.map((prompt) {
      double score = 0.0;
      
      // Bonus za shodu s časem
      if (prompt.timeOfDay == context.timeOfDay) score += 2.0;
      
      // Bonus za shodu s počasím
      if (prompt.weatherConditions.contains(context.weather)) score += 1.5;
      
      // Bonus za novost (méně používané prompty)
      score += (10 - prompt.usageCount) * 0.1;
      
      // Bonus za sezónnost
      if (_isSeasonalPrompt(prompt, context.currentTime)) score += 1.0;
      
      return MapEntry(prompt, score);
    }).toList();
    
    scored.sort((a, b) => b.value.compareTo(a.value));
    return scored.first.key;
  }

  /// Personalizace promptu
  String _personalizePrompt(DailyPrompt prompt, UserContext context) {
    var message = prompt.text;
    
    // Přidání kontextových informací
    if (context.weather.isNotEmpty) {
      message += '\n\n💡 Dnes je ${context.weather}. ';
    }
    
    if (context.location != null) {
      final locationType = _getLocationType(context.location!);
      message += 'Jsi v oblasti typu: $locationType. ';
    }
    
    // Přidání motivačních zpráv na základě historie
    if (context.recentEntries.isEmpty) {
      message += '\n\n🌟 Začni svůj deník dnes!';
    } else if (context.recentEntries.length >= 5) {
      message += '\n\n🔥 Skvělá práce! Píšeš pravidelně.';
    }
    
    return message;
  }

  /// Generování návrhů tagů
  List<String> _generateSuggestedTags(UserContext context) {
    final tags = <String>[];
    
    // Tagy na základě počasí
    if (context.weather.isNotEmpty) {
      tags.add(context.weather);
    }
    
    // Tagy na základě času
    switch (context.timeOfDay) {
      case TimeOfDay.morning:
        tags.addAll(['ráno', 'začátek dne']);
        break;
      case TimeOfDay.afternoon:
        tags.addAll(['odpoledne', 'polední pauza']);
        break;
      case TimeOfDay.evening:
        tags.addAll(['večer', 'západ slunce']);
        break;
      case TimeOfDay.night:
        tags.addAll(['noc', 'reflexe']);
        break;
    }
    
    // Tagy na základě dne v týdnu
    if (context.isWeekend) {
      tags.addAll(['víkend', 'volno']);
    } else {
      tags.addAll(['všední den', 'práce']);
    }
    
    return tags;
  }

  /// Vytvoření chytrého připomenutí
  Future<SmartReminder> createSmartReminder({
    required String title,
    required ReminderType type,
    DateTime? scheduledTime,
    String? locationName,
    double? latitude,
    double? longitude,
    double? radiusMeters,
    List<String>? weatherConditions,
    List<int>? daysOfWeek,
    bool isEnabled = true,
  }) async {
    final reminder = SmartReminder(
      id: 'reminder_${DateTime.now().millisecondsSinceEpoch}',
      title: title,
      type: type,
      scheduledTime: scheduledTime,
      locationName: locationName,
      latitude: latitude,
      longitude: longitude,
      radiusMeters: radiusMeters ?? 100,
      weatherConditions: weatherConditions ?? [],
      daysOfWeek: daysOfWeek ?? [],
      isEnabled: isEnabled,
      createdAt: DateTime.now(),
      lastTriggered: null,
      triggerCount: 0,
    );

    _reminders.add(reminder);
    await _saveReminders();
    
    debugPrint('📝 Vytvořeno chytré připomenutí: ${reminder.title}');
    return reminder;
  }

  /// Kontrola připomenutí
  Future<void> checkReminders() async {
    if (!_isInitialized) return;

    final now = DateTime.now();
    final context = await _analyzeUserContext();
    
    for (final reminder in _reminders) {
      if (!reminder.isEnabled) continue;
      
      final shouldTrigger = await _shouldTriggerReminder(reminder, context);
      
      if (shouldTrigger) {
        await _triggerReminder(reminder);
      }
    }
  }

  /// Kontrola, zda spustit připomenutí
  Future<bool> _shouldTriggerReminder(SmartReminder reminder, UserContext context) async {
    // Kontrola času
    if (reminder.type == ReminderType.time && reminder.scheduledTime != null) {
      final now = context.currentTime;
      final scheduled = reminder.scheduledTime!;
      
      if (now.hour == scheduled.hour && now.minute == scheduled.minute) {
        // Kontrola dní v týdnu
        if (reminder.daysOfWeek.isNotEmpty && 
            !reminder.daysOfWeek.contains(now.weekday)) {
          return false;
        }
        return true;
      }
    }
    
    // Kontrola lokace
    if (reminder.type == ReminderType.location && 
        reminder.latitude != null && 
        reminder.longitude != null &&
        context.location != null) {
      
      final distance = Geolocator.distanceBetween(
        context.location!.latitude,
        context.location!.longitude,
        reminder.latitude!,
        reminder.longitude!,
      );
      
      if (distance <= reminder.radiusMeters) {
        return true;
      }
    }
    
    // Kontrola počasí
    if (reminder.type == ReminderType.weather && 
        reminder.weatherConditions.isNotEmpty) {
      
      if (reminder.weatherConditions.contains(context.weather)) {
        return true;
      }
    }
    
    return false;
  }

  /// Spuštění připomenutí
  Future<void> _triggerReminder(SmartReminder reminder) async {
    try {
      // Aktualizace statistik
      final updatedReminder = reminder.copyWith(
        lastTriggered: DateTime.now(),
        triggerCount: reminder.triggerCount + 1,
      );
      
      final index = _reminders.indexWhere((r) => r.id == reminder.id);
      if (index >= 0) {
        _reminders[index] = updatedReminder;
        await _saveReminders();
      }
      
      // Zde by bylo volání notifikační služby
      debugPrint('🔔 Spuštěno připomenutí: ${reminder.title}');
      
    } catch (e) {
      debugPrint('❌ Chyba při spuštění připomenutí: $e');
    }
  }

  /// Analýza vzorců psaní
  WritingPatterns _analyzeWritingPatterns() {
    if (_userEntries.isEmpty) {
      return WritingPatterns.empty();
    }

    // Analýza času psaní
    final writingTimes = _userEntries.map((e) => e.date.hour).toList();
    final averageWritingHour = writingTimes.reduce((a, b) => a + b) / writingTimes.length;
    
    // Analýza frekvence
    final now = DateTime.now();
    final last30Days = _userEntries
        .where((e) => now.difference(e.date).inDays <= 30)
        .length;
    
    // Analýza délek zápisů
    final wordCounts = _userEntries.map((e) => e.wordCount).toList();
    final averageWordCount = wordCounts.isNotEmpty 
        ? wordCounts.reduce((a, b) => a + b) / wordCounts.length 
        : 0.0;
    
    // Analýza nálad
    final moods = _userEntries
        .where((e) => e.mood != null)
        .map((e) => e.mood!)
        .toList();
    
    final moodCounts = <DiaryMood, int>{};
    for (final mood in moods) {
      moodCounts[mood] = (moodCounts[mood] ?? 0) + 1;
    }
    
    final dominantMood = moodCounts.isNotEmpty
        ? moodCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key
        : null;

    return WritingPatterns(
      averageWritingHour: averageWritingHour,
      entriesLast30Days: last30Days,
      averageWordCount: averageWordCount,
      dominantMood: dominantMood,
      totalEntries: _userEntries.length,
    );
  }

  /// Pomocné metody
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  void _startReminderTimer() {
    _reminderTimer?.cancel();
    _reminderTimer = Timer.periodic(const Duration(minutes: 1), (_) {
      checkReminders();
    });
  }

  TimeOfDay _getTimeOfDay(DateTime time) {
    final hour = time.hour;
    if (hour >= 5 && hour < 12) return TimeOfDay.morning;
    if (hour >= 12 && hour < 17) return TimeOfDay.afternoon;
    if (hour >= 17 && hour < 22) return TimeOfDay.evening;
    return TimeOfDay.night;
  }

  String _getLocationType(Position position) {
    // Simulace detekce typu lokace
    // V produkci by zde byla integrace s mapovými službami
    final random = Random();
    final types = ['město', 'příroda', 'pláž', 'hory', 'vesnice'];
    return types[random.nextInt(types.length)];
  }

  bool _isSeasonalPrompt(DailyPrompt prompt, DateTime date) {
    final month = date.month;
    
    // Kontrola sezónních promptů
    if (prompt.categories.contains('léto') && [6, 7, 8].contains(month)) return true;
    if (prompt.categories.contains('zima') && [12, 1, 2].contains(month)) return true;
    if (prompt.categories.contains('jaro') && [3, 4, 5].contains(month)) return true;
    if (prompt.categories.contains('podzim') && [9, 10, 11].contains(month)) return true;
    
    return false;
  }

  Future<String> _getCurrentWeather() async {
    // Simulace získání počasí
    final conditions = ['slunečno', 'oblačno', 'deštivo', 'sněžení', 'mlhavo'];
    return conditions[Random().nextInt(conditions.length)];
  }

  DailyPrompt _getRandomPrompt() {
    if (_prompts.isEmpty) return DailyPrompt.fallback();
    return _prompts[Random().nextInt(_prompts.length)];
  }

  /// Načítání a ukládání dat
  Future<void> _loadPrompts() async {
    // Načtení z SharedPreferences nebo vytvoření výchozích
    await _createDefaultPrompts();
  }

  Future<void> _loadReminders() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final remindersJson = prefs.getString('smart_reminders');
      
      if (remindersJson != null) {
        final List<dynamic> data = jsonDecode(remindersJson);
        _reminders.clear();
        _reminders.addAll(
          data.map((json) => SmartReminder.fromJson(json)).toList(),
        );
      }
    } catch (e) {
      debugPrint('❌ Chyba při načítání připomenutí: $e');
    }
  }

  Future<void> _saveReminders() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'smart_reminders',
        jsonEncode(_reminders.map((r) => r.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání připomenutí: $e');
    }
  }

  Future<void> _loadUserEntries() async {
    // V produkci by zde bylo načtení z diary service
    // Pro demo používáme prázdný seznam
  }

  Future<void> _createDefaultPrompts() async {
    _prompts.addAll([
      DailyPrompt(
        id: 'morning_reflection',
        text: 'Jak se dnes ráno cítíš? Co tě čeká za den?',
        category: 'reflexe',
        timeOfDay: TimeOfDay.morning,
        weatherConditions: [],
        locationTypes: [],
        targetMoods: [],
        categories: ['ráno', 'plánování'],
        usageCount: 0,
        createdAt: DateTime.now(),
      ),
      DailyPrompt(
        id: 'beach_day',
        text: 'Jaký je tvůj nejoblíbenější moment na pláži? Popište atmosféru kolem sebe.',
        category: 'cestování',
        timeOfDay: null,
        weatherConditions: ['slunečno'],
        locationTypes: ['pláž'],
        targetMoods: [DiaryMood.happy, DiaryMood.relaxed],
        categories: ['pláž', 'léto', 'relaxace'],
        usageCount: 0,
        createdAt: DateTime.now(),
      ),
      DailyPrompt(
        id: 'evening_gratitude',
        text: 'Za co jsi dnes vděčný/á? Jaké byly nejlepší momenty dne?',
        category: 'vděčnost',
        timeOfDay: TimeOfDay.evening,
        weatherConditions: [],
        locationTypes: [],
        targetMoods: [],
        categories: ['večer', 'vděčnost', 'reflexe'],
        usageCount: 0,
        createdAt: DateTime.now(),
      ),
    ]);
  }

  @override
  void dispose() {
    _reminderTimer?.cancel();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  List<DailyPrompt> get allPrompts => List.unmodifiable(_prompts);
  List<SmartReminder> get allReminders => List.unmodifiable(_reminders);
}
