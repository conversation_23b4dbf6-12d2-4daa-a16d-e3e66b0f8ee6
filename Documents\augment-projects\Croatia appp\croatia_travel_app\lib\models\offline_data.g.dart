// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'offline_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OfflineDataPackage _$OfflineDataPackageFromJson(Map<String, dynamic> json) =>
    OfflineDataPackage(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: $enumDecode(_$OfflineDataTypeEnumMap, json['type']),
      status: $enumDecode(_$OfflineDataStatusEnumMap, json['status']),
      downloadedAt: json['downloadedAt'] == null
          ? null
          : DateTime.parse(json['downloadedAt'] as String),
      lastUpdated: json['lastUpdated'] == null
          ? null
          : DateTime.parse(json['lastUpdated'] as String),
      sizeBytes: (json['sizeBytes'] as num).toInt(),
      version: (json['version'] as num).toInt(),
      region: json['region'] as String,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      dependencies: (json['dependencies'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$OfflineDataPackageToJson(OfflineDataPackage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': _$OfflineDataTypeEnumMap[instance.type]!,
      'status': _$OfflineDataStatusEnumMap[instance.status]!,
      'downloadedAt': instance.downloadedAt?.toIso8601String(),
      'lastUpdated': instance.lastUpdated?.toIso8601String(),
      'sizeBytes': instance.sizeBytes,
      'version': instance.version,
      'region': instance.region,
      'metadata': instance.metadata,
      'dependencies': instance.dependencies,
    };

const _$OfflineDataTypeEnumMap = {
  OfflineDataType.places: 'places',
  OfflineDataType.tickets: 'tickets',
  OfflineDataType.restaurants: 'restaurants',
  OfflineDataType.accommodations: 'accommodations',
  OfflineDataType.beaches: 'beaches',
  OfflineDataType.maps: 'maps',
  OfflineDataType.aiResponses: 'aiResponses',
  OfflineDataType.emergency: 'emergency',
};

const _$OfflineDataStatusEnumMap = {
  OfflineDataStatus.notDownloaded: 'notDownloaded',
  OfflineDataStatus.downloading: 'downloading',
  OfflineDataStatus.downloaded: 'downloaded',
  OfflineDataStatus.outdated: 'outdated',
  OfflineDataStatus.error: 'error',
};

OfflineMapArea _$OfflineMapAreaFromJson(Map<String, dynamic> json) =>
    OfflineMapArea(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      northLatitude: (json['northLatitude'] as num).toDouble(),
      southLatitude: (json['southLatitude'] as num).toDouble(),
      eastLongitude: (json['eastLongitude'] as num).toDouble(),
      westLongitude: (json['westLongitude'] as num).toDouble(),
      zoomLevel: (json['zoomLevel'] as num).toInt(),
      status: $enumDecode(_$OfflineDataStatusEnumMap, json['status']),
      downloadedAt: json['downloadedAt'] == null
          ? null
          : DateTime.parse(json['downloadedAt'] as String),
      sizeBytes: (json['sizeBytes'] as num).toInt(),
      tileCount: (json['tileCount'] as num).toInt(),
    );

Map<String, dynamic> _$OfflineMapAreaToJson(OfflineMapArea instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'northLatitude': instance.northLatitude,
      'southLatitude': instance.southLatitude,
      'eastLongitude': instance.eastLongitude,
      'westLongitude': instance.westLongitude,
      'zoomLevel': instance.zoomLevel,
      'status': _$OfflineDataStatusEnumMap[instance.status]!,
      'downloadedAt': instance.downloadedAt?.toIso8601String(),
      'sizeBytes': instance.sizeBytes,
      'tileCount': instance.tileCount,
    };

OfflineAIResponse _$OfflineAIResponseFromJson(Map<String, dynamic> json) =>
    OfflineAIResponse(
      id: json['id'] as String,
      question: json['question'] as String,
      answer: json['answer'] as String,
      keywords:
          (json['keywords'] as List<dynamic>).map((e) => e as String).toList(),
      category: json['category'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      useCount: (json['useCount'] as num?)?.toInt() ?? 0,
      relevanceScore: (json['relevanceScore'] as num?)?.toDouble() ?? 1.0,
    );

Map<String, dynamic> _$OfflineAIResponseToJson(OfflineAIResponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'question': instance.question,
      'answer': instance.answer,
      'keywords': instance.keywords,
      'category': instance.category,
      'createdAt': instance.createdAt.toIso8601String(),
      'useCount': instance.useCount,
      'relevanceScore': instance.relevanceScore,
    };

OfflineStats _$OfflineStatsFromJson(Map<String, dynamic> json) => OfflineStats(
      totalPackages: (json['totalPackages'] as num).toInt(),
      downloadedPackages: (json['downloadedPackages'] as num).toInt(),
      totalSizeBytes: (json['totalSizeBytes'] as num).toInt(),
      downloadedSizeBytes: (json['downloadedSizeBytes'] as num).toInt(),
      lastSyncAt: DateTime.parse(json['lastSyncAt'] as String),
      syncCount: (json['syncCount'] as num).toInt(),
      packagesByType: (json['packagesByType'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(
            $enumDecode(_$OfflineDataTypeEnumMap, k), (e as num).toInt()),
      ),
    );

Map<String, dynamic> _$OfflineStatsToJson(OfflineStats instance) =>
    <String, dynamic>{
      'totalPackages': instance.totalPackages,
      'downloadedPackages': instance.downloadedPackages,
      'totalSizeBytes': instance.totalSizeBytes,
      'downloadedSizeBytes': instance.downloadedSizeBytes,
      'lastSyncAt': instance.lastSyncAt.toIso8601String(),
      'syncCount': instance.syncCount,
      'packagesByType': instance.packagesByType
          .map((k, e) => MapEntry(_$OfflineDataTypeEnumMap[k]!, e)),
    };
