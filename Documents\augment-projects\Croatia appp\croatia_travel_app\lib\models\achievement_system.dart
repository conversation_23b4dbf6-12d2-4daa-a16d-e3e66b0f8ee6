/// 🏆 ACHIEVEMENT SYSTEM MODELS - Modely pro systém ú<PERSON>ů
library;

/// Achievement/Úspěch
class Achievement {
  final String id;
  final String name;
  final String description;
  final String icon;
  final AchievementCategory category;
  final AchievementRarity rarity;
  final int xpReward;
  final AchievementTrigger trigger;
  final int targetValue;
  final DateTime? unlockedAt;
  final bool isSecret;

  const Achievement({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.category,
    required this.rarity,
    required this.xpReward,
    required this.trigger,
    required this.targetValue,
    this.unlockedAt,
    this.isSecret = false,
  });

  bool get isUnlocked => unlockedAt != null;

  Achievement copyWith({
    String? id,
    String? name,
    String? description,
    String? icon,
    AchievementCategory? category,
    AchievementRarity? rarity,
    int? xpReward,
    AchievementTrigger? trigger,
    int? targetValue,
    DateTime? unlockedAt,
    bool? isSecret,
  }) {
    return Achievement(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      category: category ?? this.category,
      rarity: rarity ?? this.rarity,
      xpReward: xpReward ?? this.xpReward,
      trigger: trigger ?? this.trigger,
      targetValue: targetValue ?? this.targetValue,
      unlockedAt: unlockedAt ?? this.unlockedAt,
      isSecret: isSecret ?? this.isSecret,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon,
      'category': category.name,
      'rarity': rarity.name,
      'xpReward': xpReward,
      'trigger': trigger.name,
      'targetValue': targetValue,
      'unlockedAt': unlockedAt?.toIso8601String(),
      'isSecret': isSecret,
    };
  }

  factory Achievement.fromJson(Map<String, dynamic> json) {
    return Achievement(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      icon: json['icon'] as String,
      category: AchievementCategory.values.firstWhere(
        (c) => c.name == json['category'],
      ),
      rarity: AchievementRarity.values.firstWhere(
        (r) => r.name == json['rarity'],
      ),
      xpReward: json['xpReward'] as int,
      trigger: AchievementTrigger.values.firstWhere(
        (t) => t.name == json['trigger'],
      ),
      targetValue: json['targetValue'] as int,
      unlockedAt: json['unlockedAt'] != null
          ? DateTime.parse(json['unlockedAt'] as String)
          : null,
      isSecret: json['isSecret'] as bool? ?? false,
    );
  }
}

/// Kategorie achievementů
enum AchievementCategory {
  milestone, // Milníky
  consistency, // Konzistence
  content, // Obsah
  social, // Sociální
  exploration, // Objevování
  special, // Speciální
}

extension AchievementCategoryExtension on AchievementCategory {
  String get displayName {
    switch (this) {
      case AchievementCategory.milestone:
        return 'Milníky';
      case AchievementCategory.consistency:
        return 'Konzistence';
      case AchievementCategory.content:
        return 'Obsah';
      case AchievementCategory.social:
        return 'Sociální';
      case AchievementCategory.exploration:
        return 'Objevování';
      case AchievementCategory.special:
        return 'Speciální';
    }
  }

  String get icon {
    switch (this) {
      case AchievementCategory.milestone:
        return '🎯';
      case AchievementCategory.consistency:
        return '🔥';
      case AchievementCategory.content:
        return '📝';
      case AchievementCategory.social:
        return '👥';
      case AchievementCategory.exploration:
        return '🌍';
      case AchievementCategory.special:
        return '⭐';
    }
  }
}

/// Vzácnost achievementů
enum AchievementRarity {
  common, // Běžné
  uncommon, // Neobvyklé
  rare, // Vzácné
  epic, // Epické
  legendary, // Legendární
}

extension AchievementRarityExtension on AchievementRarity {
  String get displayName {
    switch (this) {
      case AchievementRarity.common:
        return 'Běžné';
      case AchievementRarity.uncommon:
        return 'Neobvyklé';
      case AchievementRarity.rare:
        return 'Vzácné';
      case AchievementRarity.epic:
        return 'Epické';
      case AchievementRarity.legendary:
        return 'Legendární';
    }
  }

  String get color {
    switch (this) {
      case AchievementRarity.common:
        return '#9E9E9E';
      case AchievementRarity.uncommon:
        return '#4CAF50';
      case AchievementRarity.rare:
        return '#2196F3';
      case AchievementRarity.epic:
        return '#9C27B0';
      case AchievementRarity.legendary:
        return '#FF9800';
    }
  }
}

/// Trigger pro achievement
enum AchievementTrigger {
  firstEntry, // První zápis
  entryCount, // Počet zápisů
  streak, // Série zápisů
  wordCount, // Počet slov
  photoCount, // Počet fotek
  locationCount, // Počet lokací
  moodStreak, // Série pozitivních nálad
  socialShare, // Sdílení
  challengeComplete, // Dokončení výzvy
  perfectWeek, // Perfektní týden
  levelUp, // Zvýšení levelu
}

/// Výzva/Challenge
class Challenge {
  final String id;
  final String name;
  final String description;
  final ChallengeType type;
  final int targetValue;
  final int currentProgress;
  final int xpReward;
  final DateTime startDate;
  final DateTime endDate;
  final bool isActive;
  final List<String> tags;

  const Challenge({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.targetValue,
    required this.currentProgress,
    required this.xpReward,
    required this.startDate,
    required this.endDate,
    required this.isActive,
    this.tags = const [],
  });

  bool get isCompleted => currentProgress >= targetValue;
  bool get isExpired => DateTime.now().isAfter(endDate);
  double get progressPercentage =>
      targetValue > 0 ? currentProgress / targetValue : 0.0;
  Duration get timeRemaining => endDate.difference(DateTime.now());

  Challenge copyWith({
    String? id,
    String? name,
    String? description,
    ChallengeType? type,
    int? targetValue,
    int? currentProgress,
    int? xpReward,
    DateTime? startDate,
    DateTime? endDate,
    bool? isActive,
    List<String>? tags,
  }) {
    return Challenge(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      targetValue: targetValue ?? this.targetValue,
      currentProgress: currentProgress ?? this.currentProgress,
      xpReward: xpReward ?? this.xpReward,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isActive: isActive ?? this.isActive,
      tags: tags ?? this.tags,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'targetValue': targetValue,
      'currentProgress': currentProgress,
      'xpReward': xpReward,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'isActive': isActive,
      'tags': tags,
    };
  }

  factory Challenge.fromJson(Map<String, dynamic> json) {
    return Challenge(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: ChallengeType.values.firstWhere((t) => t.name == json['type']),
      targetValue: json['targetValue'] as int,
      currentProgress: json['currentProgress'] as int,
      xpReward: json['xpReward'] as int,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      isActive: json['isActive'] as bool,
      tags: (json['tags'] as List<dynamic>).cast<String>(),
    );
  }
}

/// Typ výzvy
enum ChallengeType {
  daily, // Denní
  weekly, // Týdenní
  monthly, // Měsíční
  special, // Speciální
  community, // Komunitní
}

extension ChallengeTypeExtension on ChallengeType {
  String get displayName {
    switch (this) {
      case ChallengeType.daily:
        return 'Denní';
      case ChallengeType.weekly:
        return 'Týdenní';
      case ChallengeType.monthly:
        return 'Měsíční';
      case ChallengeType.special:
        return 'Speciální';
      case ChallengeType.community:
        return 'Komunitní';
    }
  }

  String get icon {
    switch (this) {
      case ChallengeType.daily:
        return '📅';
      case ChallengeType.weekly:
        return '📆';
      case ChallengeType.monthly:
        return '🗓️';
      case ChallengeType.special:
        return '⭐';
      case ChallengeType.community:
        return '👥';
    }
  }
}

/// Pokrok uživatele
class UserProgress {
  final String userId;
  final int totalXP;
  final int level;
  final int entriesWritten;
  final int currentStreak;
  final int longestStreak;
  final int challengesCompleted;
  final int achievementsUnlocked;
  final DateTime lastActivity;
  final Map<String, int> categoryProgress;

  const UserProgress({
    required this.userId,
    required this.totalXP,
    required this.level,
    required this.entriesWritten,
    required this.currentStreak,
    required this.longestStreak,
    required this.challengesCompleted,
    required this.achievementsUnlocked,
    required this.lastActivity,
    this.categoryProgress = const {},
  });

  factory UserProgress.empty() {
    return UserProgress(
      userId: '',
      totalXP: 0,
      level: 1,
      entriesWritten: 0,
      currentStreak: 0,
      longestStreak: 0,
      challengesCompleted: 0,
      achievementsUnlocked: 0,
      lastActivity: DateTime.now(),
    );
  }

  UserProgress copyWith({
    String? userId,
    int? totalXP,
    int? level,
    int? entriesWritten,
    int? currentStreak,
    int? longestStreak,
    int? challengesCompleted,
    int? achievementsUnlocked,
    DateTime? lastActivity,
    Map<String, int>? categoryProgress,
  }) {
    return UserProgress(
      userId: userId ?? this.userId,
      totalXP: totalXP ?? this.totalXP,
      level: level ?? this.level,
      entriesWritten: entriesWritten ?? this.entriesWritten,
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      challengesCompleted: challengesCompleted ?? this.challengesCompleted,
      achievementsUnlocked: achievementsUnlocked ?? this.achievementsUnlocked,
      lastActivity: lastActivity ?? this.lastActivity,
      categoryProgress: categoryProgress ?? this.categoryProgress,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'totalXP': totalXP,
      'level': level,
      'entriesWritten': entriesWritten,
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'challengesCompleted': challengesCompleted,
      'achievementsUnlocked': achievementsUnlocked,
      'lastActivity': lastActivity.toIso8601String(),
      'categoryProgress': categoryProgress,
    };
  }

  factory UserProgress.fromJson(Map<String, dynamic> json) {
    return UserProgress(
      userId: json['userId'] as String,
      totalXP: json['totalXP'] as int,
      level: json['level'] as int,
      entriesWritten: json['entriesWritten'] as int,
      currentStreak: json['currentStreak'] as int,
      longestStreak: json['longestStreak'] as int,
      challengesCompleted: json['challengesCompleted'] as int,
      achievementsUnlocked: json['achievementsUnlocked'] as int,
      lastActivity: DateTime.parse(json['lastActivity'] as String),
      categoryProgress: Map<String, int>.from(json['categoryProgress'] ?? {}),
    );
  }
}

/// Leaderboard
class Leaderboard {
  final LeaderboardType type;
  final LeaderboardPeriod period;
  final List<LeaderboardEntry> entries;
  final int userRank;
  final int totalParticipants;
  final DateTime lastUpdated;

  const Leaderboard({
    required this.type,
    required this.period,
    required this.entries,
    required this.userRank,
    required this.totalParticipants,
    required this.lastUpdated,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'period': period.name,
      'entries': entries.map((e) => e.toJson()).toList(),
      'userRank': userRank,
      'totalParticipants': totalParticipants,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }
}

/// Typ leaderboardu
enum LeaderboardType {
  totalXP, // Celkové XP
  currentStreak, // Aktuální série
  longestStreak, // Nejdelší série
  entriesCount, // Počet zápisů
  challengesCompleted, // Dokončené výzvy
}

/// Období leaderboardu
enum LeaderboardPeriod {
  daily, // Denní
  weekly, // Týdenní
  monthly, // Měsíční
  allTime, // Celkové
}

/// Záznam v leaderboardu
class LeaderboardEntry {
  final String userId;
  final String username;
  final int score;
  final int rank;
  final String avatar;

  const LeaderboardEntry({
    required this.userId,
    required this.username,
    required this.score,
    required this.rank,
    required this.avatar,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'username': username,
      'score': score,
      'rank': rank,
      'avatar': avatar,
    };
  }
}

/// Událost achievementu
class AchievementEvent {
  final AchievementEventType type;
  final Achievement? achievement;
  final Challenge? challenge;
  final DateTime timestamp;
  final Map<String, dynamic>? data;

  const AchievementEvent({
    required this.type,
    this.achievement,
    this.challenge,
    required this.timestamp,
    this.data,
  });
}

/// Typ události achievementu
enum AchievementEventType {
  unlocked, // Achievement odemčen
  challengeStarted, // Výzva začala
  challengeCompleted, // Výzva dokončena
  levelUp, // Zvýšení levelu
  streakBroken, // Série přerušena
  streakMilestone, // Milník série
}

/// Gamifikační statistiky
class GamificationStats {
  final int totalAchievements;
  final int unlockedAchievements;
  final int activeChallenges;
  final int completedChallenges;
  final double completionRate;
  final Map<AchievementCategory, int> categoryStats;

  const GamificationStats({
    required this.totalAchievements,
    required this.unlockedAchievements,
    required this.activeChallenges,
    required this.completedChallenges,
    required this.completionRate,
    this.categoryStats = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'totalAchievements': totalAchievements,
      'unlockedAchievements': unlockedAchievements,
      'activeChallenges': activeChallenges,
      'completedChallenges': completedChallenges,
      'completionRate': completionRate,
      'categoryStats': categoryStats.map((k, v) => MapEntry(k.name, v)),
    };
  }
}
