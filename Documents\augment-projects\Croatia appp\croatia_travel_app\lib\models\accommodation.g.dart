// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'accommodation.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Accommodation _$AccommodationFromJson(Map<String, dynamic> json) =>
    Accommodation(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      address: json['address'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      region: json['region'] as String,
      accommodationType:
          $enumDecode(_$AccommodationTypeEnumMap, json['accommodationType']),
      priceRange: json['priceRange'] as String,
      rating: (json['rating'] as num).toDouble(),
      reviewCount: (json['reviewCount'] as num).toInt(),
      amenities:
          (json['amenities'] as List<dynamic>).map((e) => e as String).toList(),
      phone: json['phone'] as String,
      website: json['website'] as String?,
      email: json['email'] as String?,
      hasWifi: json['hasWifi'] as bool,
      hasParking: json['hasParking'] as bool,
      hasPool: json['hasPool'] as bool,
      hasRestaurant: json['hasRestaurant'] as bool,
      hasAirConditioning: json['hasAirConditioning'] as bool,
      hasSeaView: json['hasSeaView'] as bool,
      hasPetPolicy: json['hasPetPolicy'] as bool,
      hasAccessibility: json['hasAccessibility'] as bool,
      roomCount: (json['roomCount'] as num?)?.toInt(),
      checkInTime: json['checkInTime'] as String?,
      checkOutTime: json['checkOutTime'] as String?,
      photos:
          (json['photos'] as List<dynamic>).map((e) => e as String).toList(),
      isActive: json['isActive'] as bool,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$AccommodationToJson(Accommodation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'address': instance.address,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'region': instance.region,
      'accommodationType':
          _$AccommodationTypeEnumMap[instance.accommodationType]!,
      'priceRange': instance.priceRange,
      'rating': instance.rating,
      'reviewCount': instance.reviewCount,
      'amenities': instance.amenities,
      'phone': instance.phone,
      'website': instance.website,
      'email': instance.email,
      'hasWifi': instance.hasWifi,
      'hasParking': instance.hasParking,
      'hasPool': instance.hasPool,
      'hasRestaurant': instance.hasRestaurant,
      'hasAirConditioning': instance.hasAirConditioning,
      'hasSeaView': instance.hasSeaView,
      'hasPetPolicy': instance.hasPetPolicy,
      'hasAccessibility': instance.hasAccessibility,
      'roomCount': instance.roomCount,
      'checkInTime': instance.checkInTime,
      'checkOutTime': instance.checkOutTime,
      'photos': instance.photos,
      'isActive': instance.isActive,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };

const _$AccommodationTypeEnumMap = {
  AccommodationType.hotel: 'hotel',
  AccommodationType.apartment: 'apartment',
  AccommodationType.villa: 'villa',
  AccommodationType.guesthouse: 'guesthouse',
  AccommodationType.hostel: 'hostel',
  AccommodationType.camping: 'camping',
  AccommodationType.resort: 'resort',
  AccommodationType.boutique: 'boutique',
};

AccommodationReview _$AccommodationReviewFromJson(Map<String, dynamic> json) =>
    AccommodationReview(
      id: json['id'] as String,
      accommodationId: json['accommodationId'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      rating: (json['rating'] as num).toDouble(),
      comment: json['comment'] as String,
      photos:
          (json['photos'] as List<dynamic>).map((e) => e as String).toList(),
      stayDate: DateTime.parse(json['stayDate'] as String),
      nightsStayed: (json['nightsStayed'] as num).toInt(),
      roomType: json['roomType'] as String,
      likedAmenities: (json['likedAmenities'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      dislikedAspects: (json['dislikedAspects'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      wouldRecommend: json['wouldRecommend'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$AccommodationReviewToJson(
        AccommodationReview instance) =>
    <String, dynamic>{
      'id': instance.id,
      'accommodationId': instance.accommodationId,
      'userId': instance.userId,
      'userName': instance.userName,
      'rating': instance.rating,
      'comment': instance.comment,
      'photos': instance.photos,
      'stayDate': instance.stayDate.toIso8601String(),
      'nightsStayed': instance.nightsStayed,
      'roomType': instance.roomType,
      'likedAmenities': instance.likedAmenities,
      'dislikedAspects': instance.dislikedAspects,
      'wouldRecommend': instance.wouldRecommend,
      'createdAt': instance.createdAt.toIso8601String(),
    };

FavoriteAccommodation _$FavoriteAccommodationFromJson(
        Map<String, dynamic> json) =>
    FavoriteAccommodation(
      id: json['id'] as String,
      userId: json['userId'] as String,
      accommodationId: json['accommodationId'] as String,
      addedAt: DateTime.parse(json['addedAt'] as String),
      notes: json['notes'] as String?,
      preferredRoomTypes: (json['preferredRoomTypes'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$FavoriteAccommodationToJson(
        FavoriteAccommodation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'accommodationId': instance.accommodationId,
      'addedAt': instance.addedAt.toIso8601String(),
      'notes': instance.notes,
      'preferredRoomTypes': instance.preferredRoomTypes,
    };

AccommodationStay _$AccommodationStayFromJson(Map<String, dynamic> json) =>
    AccommodationStay(
      id: json['id'] as String,
      userId: json['userId'] as String,
      accommodationId: json['accommodationId'] as String,
      checkInDate: DateTime.parse(json['checkInDate'] as String),
      checkOutDate: DateTime.parse(json['checkOutDate'] as String),
      nightsStayed: (json['nightsStayed'] as num).toInt(),
      roomType: json['roomType'] as String,
      userRating: (json['userRating'] as num?)?.toDouble(),
      notes: json['notes'] as String?,
      photos:
          (json['photos'] as List<dynamic>).map((e) => e as String).toList(),
      totalCost: (json['totalCost'] as num?)?.toDouble(),
      usedAmenities: (json['usedAmenities'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$AccommodationStayToJson(AccommodationStay instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'accommodationId': instance.accommodationId,
      'checkInDate': instance.checkInDate.toIso8601String(),
      'checkOutDate': instance.checkOutDate.toIso8601String(),
      'nightsStayed': instance.nightsStayed,
      'roomType': instance.roomType,
      'userRating': instance.userRating,
      'notes': instance.notes,
      'photos': instance.photos,
      'totalCost': instance.totalCost,
      'usedAmenities': instance.usedAmenities,
    };

RoomType _$RoomTypeFromJson(Map<String, dynamic> json) => RoomType(
      id: json['id'] as String,
      accommodationId: json['accommodationId'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      capacity: (json['capacity'] as num).toInt(),
      bedCount: (json['bedCount'] as num).toInt(),
      bedType: json['bedType'] as String,
      size: (json['size'] as num).toDouble(),
      amenities:
          (json['amenities'] as List<dynamic>).map((e) => e as String).toList(),
      pricePerNight: (json['pricePerNight'] as num).toDouble(),
      currency: json['currency'] as String,
      isAvailable: json['isAvailable'] as bool,
    );

Map<String, dynamic> _$RoomTypeToJson(RoomType instance) => <String, dynamic>{
      'id': instance.id,
      'accommodationId': instance.accommodationId,
      'name': instance.name,
      'description': instance.description,
      'capacity': instance.capacity,
      'bedCount': instance.bedCount,
      'bedType': instance.bedType,
      'size': instance.size,
      'amenities': instance.amenities,
      'pricePerNight': instance.pricePerNight,
      'currency': instance.currency,
      'isAvailable': instance.isAvailable,
    };
