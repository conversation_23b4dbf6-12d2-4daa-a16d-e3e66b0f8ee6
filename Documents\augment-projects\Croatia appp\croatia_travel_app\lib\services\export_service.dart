import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import '../models/diary_entry.dart';
import '../models/place.dart';
import '../models/route_plan.dart';
import '../models/voice_note.dart';

class ExportService {
  static final ExportService _instance = ExportService._internal();
  factory ExportService() => _instance;
  ExportService._internal();

  /// Export deníku do PDF
  Future<String> exportDiaryToPdf({
    required List<DiaryEntry> entries,
    required String title,
    bool includePhotos = true,
    bool includeVoiceNotes = false,
    bool includeMap = false,
  }) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'diary_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final filePath = '${directory.path}/$fileName';

      // Simulace vytvoření PDF
      // V reálné aplikaci by se pou<PERSON><PERSON> knihovna jako pdf nebo printing
      final pdfContent = _generatePdfContent(entries, title, includePhotos);

      final file = File(filePath);
      await file.writeAsString(pdfContent); // Placeholder pro PDF data

      return filePath;
    } catch (e) {
      debugPrint('Chyba při exportu do PDF: $e');
      rethrow;
    }
  }

  /// Export deníku do HTML
  Future<String> exportDiaryToHtml({
    required List<DiaryEntry> entries,
    required String title,
    bool includePhotos = true,
    bool includeVoiceNotes = false,
    bool includeMap = false,
    String? customCss,
  }) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'diary_${DateTime.now().millisecondsSinceEpoch}.html';
      final filePath = '${directory.path}/$fileName';

      final htmlContent = _generateHtmlContent(
        entries,
        title,
        includePhotos,
        includeVoiceNotes,
        includeMap,
        customCss,
      );

      final file = File(filePath);
      await file.writeAsString(htmlContent);

      return filePath;
    } catch (e) {
      debugPrint('Chyba při exportu do HTML: $e');
      rethrow;
    }
  }

  /// Export trasy do GPX
  Future<String> exportRouteToGpx(RoutePlan route) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'route_${route.name.replaceAll(' ', '_')}.gpx';
      final filePath = '${directory.path}/$fileName';

      final gpxContent = _generateGpxContent(route);

      final file = File(filePath);
      await file.writeAsString(gpxContent);

      return filePath;
    } catch (e) {
      debugPrint('Chyba při exportu do GPX: $e');
      rethrow;
    }
  }

  /// Export míst do KML
  Future<String> exportPlacesToKml(List<Place> places, String title) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'places_${DateTime.now().millisecondsSinceEpoch}.kml';
      final filePath = '${directory.path}/$fileName';

      final kmlContent = _generateKmlContent(places, title);

      final file = File(filePath);
      await file.writeAsString(kmlContent);

      return filePath;
    } catch (e) {
      debugPrint('Chyba při exportu do KML: $e');
      rethrow;
    }
  }

  /// Export dat do JSON
  Future<String> exportToJson({
    List<DiaryEntry>? entries,
    List<Place>? places,
    List<RoutePlan>? routes,
    List<VoiceNote>? voiceNotes,
  }) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName =
          'croatia_travel_export_${DateTime.now().millisecondsSinceEpoch}.json';
      final filePath = '${directory.path}/$fileName';

      final jsonContent = _generateJsonContent(
        entries,
        places,
        routes,
        voiceNotes,
      );

      final file = File(filePath);
      await file.writeAsString(jsonContent);

      return filePath;
    } catch (e) {
      debugPrint('Chyba při exportu do JSON: $e');
      rethrow;
    }
  }

  /// Vytvoření cestovního itineráře
  Future<String> createTravelItinerary({
    required RoutePlan route,
    required List<DiaryEntry> entries,
    required List<Place> places,
    ExportFormat format = ExportFormat.pdf,
    bool includePhotos = true,
    bool includeMap = true,
  }) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final extension = format == ExportFormat.pdf ? 'pdf' : 'html';
      final fileName =
          'itinerary_${route.name.replaceAll(' ', '_')}.$extension';
      final filePath = '${directory.path}/$fileName';

      String content;
      if (format == ExportFormat.pdf) {
        content = _generateItineraryPdf(
          route,
          entries,
          places,
          includePhotos,
          includeMap,
        );
      } else {
        content = _generateItineraryHtml(
          route,
          entries,
          places,
          includePhotos,
          includeMap,
        );
      }

      final file = File(filePath);
      await file.writeAsString(content);

      return filePath;
    } catch (e) {
      debugPrint('Chyba při vytváření itineráře: $e');
      rethrow;
    }
  }

  /// Sdílení exportovaného souboru
  Future<void> shareExportedFile(String filePath, {String? subject}) async {
    try {
      // Simulace sdílení - v reálné aplikaci by se použil share_plus package
      debugPrint('Sdílení souboru: $filePath');
      debugPrint('Předmět: ${subject ?? 'Export z Croatia Travel App'}');
    } catch (e) {
      debugPrint('Chyba při sdílení souboru: $e');
      rethrow;
    }
  }

  /// Generování PDF obsahu
  String _generatePdfContent(
    List<DiaryEntry> entries,
    String title,
    bool includePhotos,
  ) {
    // Placeholder pro PDF generování
    // V reálné aplikaci by se použila knihovna pro PDF
    return 'PDF Content for $title with ${entries.length} entries';
  }

  /// Generování HTML obsahu
  String _generateHtmlContent(
    List<DiaryEntry> entries,
    String title,
    bool includePhotos,
    bool includeVoiceNotes,
    bool includeMap,
    String? customCss,
  ) {
    final buffer = StringBuffer();

    buffer.writeln('<!DOCTYPE html>');
    buffer.writeln('<html lang="cs">');
    buffer.writeln('<head>');
    buffer.writeln('<meta charset="UTF-8">');
    buffer.writeln(
      '<meta name="viewport" content="width=device-width, initial-scale=1.0">',
    );
    buffer.writeln('<title>$title</title>');
    buffer.writeln('<style>');
    buffer.writeln(customCss ?? _getDefaultCss());
    buffer.writeln('</style>');
    buffer.writeln('</head>');
    buffer.writeln('<body>');

    buffer.writeln('<header>');
    buffer.writeln('<h1>$title</h1>');
    buffer.writeln('<p>Exportováno z Croatia Travel App</p>');
    buffer.writeln(
      '<p>Datum exportu: ${DateTime.now().toString().split('.')[0]}</p>',
    );
    buffer.writeln('</header>');

    buffer.writeln('<main>');

    for (final entry in entries) {
      buffer.writeln('<article class="diary-entry">');
      buffer.writeln('<h2>${entry.title}</h2>');
      buffer.writeln('<div class="entry-meta">');
      buffer.writeln(
        '<span class="date">${entry.date.toString().split(' ')[0]}</span>',
      );

      if (entry.location != null) {
        buffer.writeln('<span class="location">📍 ${entry.location}</span>');
      }

      if (entry.mood != null) {
        buffer.writeln(
          '<span class="mood">${entry.mood!.emoji} ${entry.mood!.displayName}</span>',
        );
      }

      if (entry.rating != null) {
        final stars = '⭐' * entry.rating!.round();
        buffer.writeln('<span class="rating">$stars</span>');
      }

      buffer.writeln('</div>');

      buffer.writeln('<div class="entry-content">');
      buffer.writeln('<p>${entry.content.replaceAll('\n', '</p><p>')}</p>');
      buffer.writeln('</div>');

      if (entry.tags.isNotEmpty) {
        buffer.writeln('<div class="tags">');
        for (final tag in entry.tags) {
          buffer.writeln('<span class="tag">#$tag</span>');
        }
        buffer.writeln('</div>');
      }

      if (includePhotos && entry.hasPhotos) {
        buffer.writeln('<div class="photos">');
        buffer.writeln('<h3>Fotografie</h3>');
        for (final photo in entry.photos) {
          buffer.writeln('<img src="$photo" alt="Fotografie" class="photo">');
        }
        buffer.writeln('</div>');
      }

      if (includeVoiceNotes && entry.hasVoiceNotes) {
        buffer.writeln('<div class="voice-notes">');
        buffer.writeln('<h3>Hlasové poznámky</h3>');
        buffer.writeln(
          '<p>Počet hlasových poznámek: ${entry.voiceNotes.length}</p>',
        );
        buffer.writeln('</div>');
      }

      buffer.writeln('</article>');
    }

    buffer.writeln('</main>');

    buffer.writeln('<footer>');
    buffer.writeln('<p>Vytvořeno pomocí Croatia Travel App</p>');
    buffer.writeln('</footer>');

    buffer.writeln('</body>');
    buffer.writeln('</html>');

    return buffer.toString();
  }

  /// Generování GPX obsahu
  String _generateGpxContent(RoutePlan route) {
    final buffer = StringBuffer();

    buffer.writeln('<?xml version="1.0" encoding="UTF-8"?>');
    buffer.writeln('<gpx version="1.1" creator="Croatia Travel App">');
    buffer.writeln('<metadata>');
    buffer.writeln('<name>${route.name}</name>');
    buffer.writeln('<desc>${route.description}</desc>');
    buffer.writeln('<time>${DateTime.now().toIso8601String()}</time>');
    buffer.writeln('</metadata>');

    // Waypoints
    for (final point in route.points) {
      buffer.writeln('<wpt lat="${point.latitude}" lon="${point.longitude}">');
      buffer.writeln('<name>${point.name}</name>');
      buffer.writeln('<desc>${point.notes ?? ''}</desc>');
      buffer.writeln('</wpt>');
    }

    // Track
    buffer.writeln('<trk>');
    buffer.writeln('<name>${route.name}</name>');
    buffer.writeln('<trkseg>');
    for (final point in route.points) {
      buffer.writeln(
        '<trkpt lat="${point.latitude}" lon="${point.longitude}">',
      );
      buffer.writeln('<name>${point.name}</name>');
      buffer.writeln('</trkpt>');
    }
    buffer.writeln('</trkseg>');
    buffer.writeln('</trk>');

    buffer.writeln('</gpx>');

    return buffer.toString();
  }

  /// Generování KML obsahu
  String _generateKmlContent(List<Place> places, String title) {
    final buffer = StringBuffer();

    buffer.writeln('<?xml version="1.0" encoding="UTF-8"?>');
    buffer.writeln('<kml xmlns="http://www.opengis.net/kml/2.2">');
    buffer.writeln('<Document>');
    buffer.writeln('<name>$title</name>');
    buffer.writeln('<description>Místa z Croatia Travel App</description>');

    for (final place in places) {
      buffer.writeln('<Placemark>');
      buffer.writeln('<name>${place.name}</name>');
      buffer.writeln('<description>${place.description}</description>');
      buffer.writeln('<Point>');
      buffer.writeln(
        '<coordinates>${place.longitude},${place.latitude},0</coordinates>',
      );
      buffer.writeln('</Point>');
      buffer.writeln('</Placemark>');
    }

    buffer.writeln('</Document>');
    buffer.writeln('</kml>');

    return buffer.toString();
  }

  /// Generování JSON obsahu
  String _generateJsonContent(
    List<DiaryEntry>? entries,
    List<Place>? places,
    List<RoutePlan>? routes,
    List<VoiceNote>? voiceNotes,
  ) {
    final data = <String, dynamic>{
      'exportDate': DateTime.now().toIso8601String(),
      'appVersion': '1.0.0',
      'exportType': 'full_backup',
    };

    if (entries != null) {
      data['diaryEntries'] = entries
          .map(
            (e) => {
              'id': e.id,
              'title': e.title,
              'content': e.content,
              'date': e.date.toIso8601String(),
              'location': e.location,
              'latitude': e.latitude,
              'longitude': e.longitude,
              'photos': e.photos,
              'voiceNotes': e.voiceNotes,
              'tags': e.tags,
              'mood': e.mood?.name,
              'weather': e.weather,
              'rating': e.rating,
              'isPrivate': e.isPrivate,
              'createdAt': e.createdAt.toIso8601String(),
              'updatedAt': e.updatedAt.toIso8601String(),
            },
          )
          .toList();
    }

    if (places != null) {
      data['places'] = places
          .map(
            (p) => {
              'id': p.id,
              'name': p.name,
              'description': p.description,
              'latitude': p.latitude,
              'longitude': p.longitude,
              'region': p.region,
              'type': p.type.name,
              'images': p.images,
              'isVisited': p.isVisited,
              'visitedDate': p.visitedDate?.toIso8601String(),
              'rating': p.rating,
              'tags': p.tags,
              'additionalInfo': p.additionalInfo,
            },
          )
          .toList();
    }

    // Podobně pro routes a voiceNotes...

    return data.toString(); // V reálné aplikaci by se použil json.encode()
  }

  /// Výchozí CSS styly
  String _getDefaultCss() {
    return '''
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      
      header {
        text-align: center;
        border-bottom: 2px solid #007AFF;
        padding-bottom: 20px;
        margin-bottom: 30px;
      }
      
      .diary-entry {
        margin-bottom: 40px;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 8px;
        background: #fafafa;
      }
      
      .entry-meta {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
        font-size: 14px;
        color: #666;
      }
      
      .tags {
        margin-top: 15px;
      }
      
      .tag {
        background: #007AFF;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        margin-right: 5px;
      }
      
      .photo {
        max-width: 100%;
        height: auto;
        margin: 10px 0;
        border-radius: 4px;
      }
    ''';
  }

  /// Generování itineráře v PDF
  String _generateItineraryPdf(
    RoutePlan route,
    List<DiaryEntry> entries,
    List<Place> places,
    bool includePhotos,
    bool includeMap,
  ) {
    // Placeholder pro PDF itinerář
    return 'PDF Itinerary for ${route.name}';
  }

  /// Generování itineráře v HTML
  String _generateItineraryHtml(
    RoutePlan route,
    List<DiaryEntry> entries,
    List<Place> places,
    bool includePhotos,
    bool includeMap,
  ) {
    // Podobně jako _generateHtmlContent, ale zaměřeno na itinerář
    return '<html><body><h1>Itinerář: ${route.name}</h1></body></html>';
  }
}

enum ExportFormat { pdf, html, json, gpx, kml }
