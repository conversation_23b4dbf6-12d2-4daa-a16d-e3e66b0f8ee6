import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:speech_to_text/speech_to_text.dart';
import '../models/place.dart';
import '../models/event.dart';
import '../models/cuisine.dart';
import '../models/voice_note.dart';
import '../data/local_database.dart';

class VoiceSearchService {
  static final VoiceSearchService _instance = VoiceSearchService._internal();
  factory VoiceSearchService() => _instance;
  VoiceSearchService._internal();

  final SpeechToText _speechToText = SpeechToText();
  final LocalDatabase _localDb = LocalDatabase();

  StreamController<VoiceSearchEvent>? _eventController;
  bool _isListening = false;
  bool _speechEnabled = false;

  /// Stream událostí hlasového vyhledávání
  Stream<VoiceSearchEvent> get eventStream {
    _eventController ??= StreamController<VoiceSearchEvent>.broadcast();
    return _eventController!.stream;
  }

  /// Inicializace služby
  Future<void> initialize() async {
    try {
      _speechEnabled = await _speechToText.initialize(
        onStatus: (status) {
          _eventController?.add(
            VoiceSearchEvent(
              type: VoiceSearchEventType.statusChanged,
              status: status,
            ),
          );
        },
        onError: (error) {
          _eventController?.add(
            VoiceSearchEvent(
              type: VoiceSearchEventType.error,
              error: error.errorMsg,
            ),
          );
        },
      );
    } catch (e) {
      debugPrint('Chyba při inicializaci hlasového vyhledávání: $e');
    }
  }

  /// Spuštění hlasového vyhledávání
  Future<void> startVoiceSearch({
    String language = 'cs-CZ',
    VoiceSearchType searchType = VoiceSearchType.all,
  }) async {
    if (!_speechEnabled) {
      await initialize();
    }

    if (!_speechEnabled) {
      _eventController?.add(
        VoiceSearchEvent(
          type: VoiceSearchEventType.error,
          error: 'Hlasové vyhledávání není dostupné',
        ),
      );
      return;
    }

    if (_isListening) {
      await stopVoiceSearch();
    }

    try {
      await _speechToText.listen(
        onResult: (result) => _processSearchResult(result, searchType),
        localeId: language,
        listenFor: const Duration(seconds: 10),
        pauseFor: const Duration(seconds: 2),
        listenOptions: SpeechListenOptions(partialResults: true),
      );

      _isListening = true;
      _eventController?.add(
        VoiceSearchEvent(
          type: VoiceSearchEventType.listeningStarted,
          searchType: searchType,
        ),
      );
    } catch (e) {
      _eventController?.add(
        VoiceSearchEvent(
          type: VoiceSearchEventType.error,
          error: 'Chyba při spuštění hlasového vyhledávání: $e',
        ),
      );
    }
  }

  /// Zastavení hlasového vyhledávání
  Future<void> stopVoiceSearch() async {
    if (_isListening) {
      await _speechToText.stop();
      _isListening = false;

      _eventController?.add(
        VoiceSearchEvent(type: VoiceSearchEventType.listeningStopped),
      );
    }
  }

  /// Zpracování výsledku rozpoznávání
  void _processSearchResult(dynamic result, VoiceSearchType searchType) {
    final query = result.recognizedWords;
    final isFinal = result.finalResult;

    _eventController?.add(
      VoiceSearchEvent(
        type: VoiceSearchEventType.transcriptionUpdated,
        query: query,
        isPartial: !isFinal,
      ),
    );

    if (isFinal && query.isNotEmpty) {
      _performSearch(query, searchType);
    }
  }

  /// Provedení vyhledávání
  Future<void> _performSearch(String query, VoiceSearchType searchType) async {
    try {
      _eventController?.add(
        VoiceSearchEvent(
          type: VoiceSearchEventType.searchStarted,
          query: query,
          searchType: searchType,
        ),
      );

      final results = await _searchByType(query, searchType);

      _eventController?.add(
        VoiceSearchEvent(
          type: VoiceSearchEventType.searchCompleted,
          query: query,
          searchType: searchType,
          results: results,
        ),
      );
    } catch (e) {
      _eventController?.add(
        VoiceSearchEvent(
          type: VoiceSearchEventType.error,
          error: 'Chyba při vyhledávání: $e',
        ),
      );
    }
  }

  /// Vyhledávání podle typu
  Future<VoiceSearchResults> _searchByType(
    String query,
    VoiceSearchType searchType,
  ) async {
    switch (searchType) {
      case VoiceSearchType.places:
        return VoiceSearchResults(places: await _searchPlaces(query));
      case VoiceSearchType.events:
        return VoiceSearchResults(events: await _searchEvents(query));
      case VoiceSearchType.cuisine:
        return VoiceSearchResults(cuisine: await _searchCuisine(query));
      case VoiceSearchType.voiceNotes:
        return VoiceSearchResults(voiceNotes: await _searchVoiceNotes(query));
      case VoiceSearchType.all:
        return VoiceSearchResults(
          places: await _searchPlaces(query),
          events: await _searchEvents(query),
          cuisine: await _searchCuisine(query),
          voiceNotes: await _searchVoiceNotes(query),
        );
    }
  }

  /// Vyhledávání míst
  Future<List<Place>> _searchPlaces(String query) async {
    // Simulace vyhledávání míst
    final allPlaces = await _localDb.getAllPlaces();
    final lowerQuery = query.toLowerCase();

    return allPlaces
        .where((place) {
          return place.name.toLowerCase().contains(lowerQuery) ||
              place.description.toLowerCase().contains(lowerQuery) ||
              place.tags.any((tag) => tag.toLowerCase().contains(lowerQuery)) ||
              place.region.toLowerCase().contains(lowerQuery);
        })
        .take(10)
        .toList();
  }

  /// Vyhledávání událostí
  Future<List<Event>> _searchEvents(String query) async {
    // Simulace vyhledávání událostí
    final allEvents = await _localDb.getAllEvents();
    final lowerQuery = query.toLowerCase();

    return allEvents
        .where((event) {
          return event.name.toLowerCase().contains(lowerQuery) ||
              event.description.toLowerCase().contains(lowerQuery) ||
              event.location.toLowerCase().contains(lowerQuery) ||
              event.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));
        })
        .take(10)
        .toList();
  }

  /// Vyhledávání kuchyně
  Future<List<CuisineItem>> _searchCuisine(String query) async {
    // Simulace vyhledávání kuchyně
    final allCuisine = await _localDb.getAllCuisineItems();
    final lowerQuery = query.toLowerCase();

    return allCuisine
        .where((item) {
          return item.name.toLowerCase().contains(lowerQuery) ||
              item.description.toLowerCase().contains(lowerQuery) ||
              item.region.toLowerCase().contains(lowerQuery) ||
              item.ingredients.any(
                (ingredient) => ingredient.toLowerCase().contains(lowerQuery),
              ) ||
              item.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));
        })
        .take(10)
        .toList()
        .cast<CuisineItem>();
  }

  /// Vyhledávání hlasových poznámek
  Future<List<VoiceNote>> _searchVoiceNotes(String query) async {
    // Simulace vyhledávání hlasových poznámek
    final allVoiceNotes = await _localDb.getAllVoiceNotes();
    final lowerQuery = query.toLowerCase();

    return allVoiceNotes
        .where((note) {
          return note.title.toLowerCase().contains(lowerQuery) ||
              (note.description?.toLowerCase().contains(lowerQuery) ?? false) ||
              (note.transcription?.toLowerCase().contains(lowerQuery) ??
                  false) ||
              note.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));
        })
        .take(10)
        .toList()
        .cast<VoiceNote>();
  }

  /// Inteligentní zpracování dotazu
  VoiceSearchType _detectSearchType(String query) {
    final lowerQuery = query.toLowerCase();

    // Klíčová slova pro různé typy vyhledávání
    final placeKeywords = [
      'místo',
      'město',
      'památka',
      'hrad',
      'kostel',
      'muzeum',
      'park',
    ];
    final eventKeywords = ['událost', 'festival', 'koncert', 'výstava', 'akce'];
    final cuisineKeywords = [
      'jídlo',
      'restaurace',
      'kuchyně',
      'recept',
      'pokrm',
    ];
    final voiceKeywords = ['poznámka', 'nahrávka', 'hlas', 'záznam'];

    if (placeKeywords.any((keyword) => lowerQuery.contains(keyword))) {
      return VoiceSearchType.places;
    } else if (eventKeywords.any((keyword) => lowerQuery.contains(keyword))) {
      return VoiceSearchType.events;
    } else if (cuisineKeywords.any((keyword) => lowerQuery.contains(keyword))) {
      return VoiceSearchType.cuisine;
    } else if (voiceKeywords.any((keyword) => lowerQuery.contains(keyword))) {
      return VoiceSearchType.voiceNotes;
    }

    return VoiceSearchType.all;
  }

  /// Rychlé hlasové vyhledávání s automatickou detekcí typu
  Future<void> quickVoiceSearch({String language = 'cs-CZ'}) async {
    if (!_speechEnabled) {
      await initialize();
    }

    if (!_speechEnabled) {
      _eventController?.add(
        VoiceSearchEvent(
          type: VoiceSearchEventType.error,
          error: 'Hlasové vyhledávání není dostupné',
        ),
      );
      return;
    }

    try {
      await _speechToText.listen(
        onResult: (result) {
          final query = result.recognizedWords;
          final isFinal = result.finalResult;

          if (isFinal && query.isNotEmpty) {
            final searchType = _detectSearchType(query);
            _performSearch(query, searchType);
          }
        },
        localeId: language,
        listenFor: const Duration(seconds: 5),
        pauseFor: const Duration(seconds: 1),
        listenOptions: SpeechListenOptions(partialResults: false),
      );

      _eventController?.add(
        VoiceSearchEvent(type: VoiceSearchEventType.quickSearchStarted),
      );
    } catch (e) {
      _eventController?.add(
        VoiceSearchEvent(
          type: VoiceSearchEventType.error,
          error: 'Chyba při rychlém vyhledávání: $e',
        ),
      );
    }
  }

  /// Kontrola dostupnosti hlasového vyhledávání
  bool get isAvailable => _speechEnabled;

  /// Kontrola stavu naslouchání
  bool get isListening => _isListening;

  void dispose() {
    _eventController?.close();
  }
}

// Modely pro hlasové vyhledávání
class VoiceSearchEvent {
  final VoiceSearchEventType type;
  final String? query;
  final String? error;
  final String? status;
  final VoiceSearchType? searchType;
  final VoiceSearchResults? results;
  final bool? isPartial;

  VoiceSearchEvent({
    required this.type,
    this.query,
    this.error,
    this.status,
    this.searchType,
    this.results,
    this.isPartial,
  });
}

class VoiceSearchResults {
  final List<Place> places;
  final List<Event> events;
  final List<CuisineItem> cuisine;
  final List<VoiceNote> voiceNotes;

  VoiceSearchResults({
    this.places = const [],
    this.events = const [],
    this.cuisine = const [],
    this.voiceNotes = const [],
  });

  bool get isEmpty =>
      places.isEmpty && events.isEmpty && cuisine.isEmpty && voiceNotes.isEmpty;

  int get totalResults =>
      places.length + events.length + cuisine.length + voiceNotes.length;
}

enum VoiceSearchType { all, places, events, cuisine, voiceNotes }

enum VoiceSearchEventType {
  listeningStarted,
  listeningStopped,
  transcriptionUpdated,
  searchStarted,
  searchCompleted,
  quickSearchStarted,
  statusChanged,
  error,
}
