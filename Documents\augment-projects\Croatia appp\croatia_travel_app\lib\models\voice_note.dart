import 'package:json_annotation/json_annotation.dart';

part 'voice_note.g.dart';

@JsonSerializable()
class VoiceNote {
  final String id;
  final String title;
  final String? description;
  final String filePath;
  final Duration duration;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final int fileSize; // v bytech
  final String? transcription;
  final List<String> tags;
  final String? location;
  final double? latitude;
  final double? longitude;
  final VoiceNoteType type;
  final bool isTranscribed;
  final bool isFavorite;
  final VoiceQuality quality;

  VoiceNote({
    required this.id,
    required this.title,
    this.description,
    required this.filePath,
    required this.duration,
    required this.createdAt,
    this.updatedAt,
    required this.fileSize,
    this.transcription,
    this.tags = const [],
    this.location,
    this.latitude,
    this.longitude,
    this.type = VoiceNoteType.personal,
    this.isTranscribed = false,
    this.isFavorite = false,
    this.quality = VoiceQuality.medium,
  });

  factory VoiceNote.fromJson(Map<String, dynamic> json) => _$VoiceNoteFromJson(json);
  Map<String, dynamic> toJson() => _$VoiceNoteToJson(this);

  VoiceNote copyWith({
    String? id,
    String? title,
    String? description,
    String? filePath,
    Duration? duration,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? fileSize,
    String? transcription,
    List<String>? tags,
    String? location,
    double? latitude,
    double? longitude,
    VoiceNoteType? type,
    bool? isTranscribed,
    bool? isFavorite,
    VoiceQuality? quality,
  }) {
    return VoiceNote(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      filePath: filePath ?? this.filePath,
      duration: duration ?? this.duration,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      fileSize: fileSize ?? this.fileSize,
      transcription: transcription ?? this.transcription,
      tags: tags ?? this.tags,
      location: location ?? this.location,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      type: type ?? this.type,
      isTranscribed: isTranscribed ?? this.isTranscribed,
      isFavorite: isFavorite ?? this.isFavorite,
      quality: quality ?? this.quality,
    );
  }

  /// Formátovaná doba trvání
  String get formattedDuration {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// Formátovaná velikost souboru
  String get formattedFileSize {
    if (fileSize < 1024) {
      return '$fileSize B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  /// Kontrola existence souboru
  bool get hasValidFile => filePath.isNotEmpty;

  /// Kontrola lokace
  bool get hasLocation => latitude != null && longitude != null;

  /// Excerpt z přepisu
  String get transcriptionExcerpt {
    if (transcription == null || transcription!.isEmpty) {
      return 'Bez přepisu';
    }
    if (transcription!.length <= 100) {
      return transcription!;
    }
    return '${transcription!.substring(0, 97)}...';
  }
}

@JsonSerializable()
class VoiceRecordingEvent {
  final VoiceEventType type;
  final String? error;
  final String? status;
  final String? filePath;
  final Duration? duration;
  final VoiceNote? voiceNote;
  final String? transcription;
  final bool? isPartial;

  VoiceRecordingEvent({
    required this.type,
    this.error,
    this.status,
    this.filePath,
    this.duration,
    this.voiceNote,
    this.transcription,
    this.isPartial,
  });

  factory VoiceRecordingEvent.fromJson(Map<String, dynamic> json) => 
      _$VoiceRecordingEventFromJson(json);
  Map<String, dynamic> toJson() => _$VoiceRecordingEventToJson(this);
}

enum VoiceNoteType {
  @JsonValue('personal')
  personal,
  @JsonValue('diary')
  diary,
  @JsonValue('reminder')
  reminder,
  @JsonValue('review')
  review,
  @JsonValue('guide')
  guide,
  @JsonValue('interview')
  interview,
  @JsonValue('memo')
  memo,
}

enum VoiceEventType {
  @JsonValue('recording_started')
  recordingStarted,
  @JsonValue('recording_stopped')
  recordingStopped,
  @JsonValue('recording_progress')
  recordingProgress,
  @JsonValue('playback_started')
  playbackStarted,
  @JsonValue('playback_stopped')
  playbackStopped,
  @JsonValue('playback_completed')
  playbackCompleted,
  @JsonValue('listening_started')
  listeningStarted,
  @JsonValue('listening_stopped')
  listeningStopped,
  @JsonValue('transcription_updated')
  transcriptionUpdated,
  @JsonValue('tts_started')
  ttsStarted,
  @JsonValue('tts_completed')
  ttsCompleted,
  @JsonValue('voice_note_deleted')
  voiceNoteDeleted,
  @JsonValue('speech_status_changed')
  speechStatusChanged,
  @JsonValue('error')
  error,
}

enum VoiceQuality {
  @JsonValue('low')
  low,
  @JsonValue('medium')
  medium,
  @JsonValue('high')
  high,
  @JsonValue('lossless')
  lossless,
}

extension VoiceNoteTypeExtension on VoiceNoteType {
  String get displayName {
    switch (this) {
      case VoiceNoteType.personal:
        return 'Osobní';
      case VoiceNoteType.diary:
        return 'Deník';
      case VoiceNoteType.reminder:
        return 'Připomínka';
      case VoiceNoteType.review:
        return 'Recenze';
      case VoiceNoteType.guide:
        return 'Průvodce';
      case VoiceNoteType.interview:
        return 'Rozhovor';
      case VoiceNoteType.memo:
        return 'Poznámka';
    }
  }

  String get icon {
    switch (this) {
      case VoiceNoteType.personal:
        return '👤';
      case VoiceNoteType.diary:
        return '📔';
      case VoiceNoteType.reminder:
        return '⏰';
      case VoiceNoteType.review:
        return '⭐';
      case VoiceNoteType.guide:
        return '🗺️';
      case VoiceNoteType.interview:
        return '🎤';
      case VoiceNoteType.memo:
        return '📝';
    }
  }
}

extension VoiceQualityExtension on VoiceQuality {
  String get displayName {
    switch (this) {
      case VoiceQuality.low:
        return 'Nízká (úspora místa)';
      case VoiceQuality.medium:
        return 'Střední (doporučeno)';
      case VoiceQuality.high:
        return 'Vysoká';
      case VoiceQuality.lossless:
        return 'Bezeztrátová';
    }
  }

  int get bitrate {
    switch (this) {
      case VoiceQuality.low:
        return 32000; // 32 kbps
      case VoiceQuality.medium:
        return 64000; // 64 kbps
      case VoiceQuality.high:
        return 128000; // 128 kbps
      case VoiceQuality.lossless:
        return 320000; // 320 kbps
    }
  }
}

// Statistiky hlasových poznámek
@JsonSerializable()
class VoiceNoteStatistics {
  final int totalNotes;
  final Duration totalDuration;
  final int totalFileSize;
  final Map<VoiceNoteType, int> notesByType;
  final int transcribedNotes;
  final int favoriteNotes;
  final DateTime? oldestNote;
  final DateTime? newestNote;
  final double averageDuration;

  VoiceNoteStatistics({
    required this.totalNotes,
    required this.totalDuration,
    required this.totalFileSize,
    required this.notesByType,
    required this.transcribedNotes,
    required this.favoriteNotes,
    this.oldestNote,
    this.newestNote,
    required this.averageDuration,
  });

  factory VoiceNoteStatistics.fromJson(Map<String, dynamic> json) => 
      _$VoiceNoteStatisticsFromJson(json);
  Map<String, dynamic> toJson() => _$VoiceNoteStatisticsToJson(this);

  String get formattedTotalDuration {
    final hours = totalDuration.inHours;
    final minutes = totalDuration.inMinutes % 60;
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    }
    return '${minutes}m';
  }

  String get formattedTotalFileSize {
    if (totalFileSize < 1024 * 1024) {
      return '${(totalFileSize / 1024).toStringAsFixed(1)} KB';
    } else if (totalFileSize < 1024 * 1024 * 1024) {
      return '${(totalFileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(totalFileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
}

// Filtry pro hlasové poznámky
@JsonSerializable()
class VoiceNoteFilter {
  final VoiceNoteType? type;
  final bool? isFavorite;
  final bool? isTranscribed;
  final DateTime? dateFrom;
  final DateTime? dateTo;
  final Duration? minDuration;
  final Duration? maxDuration;
  final List<String> tags;
  final String? searchQuery;

  VoiceNoteFilter({
    this.type,
    this.isFavorite,
    this.isTranscribed,
    this.dateFrom,
    this.dateTo,
    this.minDuration,
    this.maxDuration,
    this.tags = const [],
    this.searchQuery,
  });

  factory VoiceNoteFilter.fromJson(Map<String, dynamic> json) => 
      _$VoiceNoteFilterFromJson(json);
  Map<String, dynamic> toJson() => _$VoiceNoteFilterToJson(this);

  bool matches(VoiceNote note) {
    if (type != null && note.type != type) return false;
    if (isFavorite != null && note.isFavorite != isFavorite) return false;
    if (isTranscribed != null && note.isTranscribed != isTranscribed) return false;
    
    if (dateFrom != null && note.createdAt.isBefore(dateFrom!)) return false;
    if (dateTo != null && note.createdAt.isAfter(dateTo!)) return false;
    
    if (minDuration != null && note.duration < minDuration!) return false;
    if (maxDuration != null && note.duration > maxDuration!) return false;
    
    if (tags.isNotEmpty) {
      final hasMatchingTag = tags.any((tag) => note.tags.contains(tag));
      if (!hasMatchingTag) return false;
    }
    
    if (searchQuery != null && searchQuery!.isNotEmpty) {
      final query = searchQuery!.toLowerCase();
      final titleMatch = note.title.toLowerCase().contains(query);
      final descriptionMatch = note.description?.toLowerCase().contains(query) ?? false;
      final transcriptionMatch = note.transcription?.toLowerCase().contains(query) ?? false;
      
      if (!titleMatch && !descriptionMatch && !transcriptionMatch) return false;
    }
    
    return true;
  }
}
