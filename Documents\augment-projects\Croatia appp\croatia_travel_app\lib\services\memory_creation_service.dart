import 'dart:io';
import 'package:flutter/foundation.dart';
import '../models/memory.dart';
import '../data/local_database.dart';

class MemoryCreationService {
  static final MemoryCreationService _instance =
      MemoryCreationService._internal();
  factory MemoryCreationService() => _instance;
  MemoryCreationService._internal();

  final LocalDatabase _localDb = LocalDatabase();

  /// Vytvoření vzpomínky z fotografií
  Future<TravelMemory> createMemoryFromPhotos({
    required List<File> photos,
    required String title,
    String? description,
    List<String>? visitedPlaces,
    DateTime? date,
    MemoryTemplate? template,
  }) async {
    try {
      // Vytvoření vzpomínky
      final memory = TravelMemory(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title,
        description: description ?? 'Vzpomínka z cesty',
        photos: photos.map((p) => p.path).toList(),
        visitedPlaces: visitedPlaces ?? [],
        createdAt: DateTime.now(),
        date: date ?? DateTime.now(),
        template: template ?? MemoryTemplate.classic,
        metadata: MemoryMetadata(
          totalPhotos: photos.length,
          selectedPhotos: photos.length,
          processingTime: DateTime.now(),
          autoGenerated: description == null,
        ),
      );

      // Uložení do databáze
      // Konverze TravelMemory na Memory pro databázi
      final dbMemory = Memory(
        id: memory.id,
        title: memory.title,
        description: memory.description,
        date: memory.date,
        location: memory.visitedPlaces.isNotEmpty
            ? memory.visitedPlaces.first
            : null,
        photos: memory.photos,
      );
      await _localDb.saveMemory(dbMemory);

      return memory;
    } catch (e) {
      throw Exception('Chyba při vytváření vzpomínky: $e');
    }
  }

  /// Vytvoření video prezentace
  Future<VideoMemory> createVideoPresentation({
    required TravelMemory memory,
    required VideoTemplate template,
    String? musicUrl,
  }) async {
    try {
      final videoMemory = VideoMemory(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        memoryId: memory.id,
        title: '${memory.title} - Video',
        videoPath: '/path/to/video.mp4',
        template: template,
        duration: const Duration(minutes: 2),
        createdAt: DateTime.now(),
        metadata: VideoMetadata(
          resolution: VideoResolution.hd1080,
          frameRate: 30,
          bitrate: 5000,
          fileSize: 50000000,
        ),
      );

      // Konverze VideoMemory na Memory pro databázi
      final dbMemory = Memory(
        id: videoMemory.id,
        title: videoMemory.title,
        description: 'Video vzpomínka',
        date: videoMemory.createdAt,
        location: null,
        photos: videoMemory.thumbnailPath != null
            ? [videoMemory.thumbnailPath!]
            : [],
      );
      await _localDb.saveVideoMemory(dbMemory);

      return videoMemory;
    } catch (e) {
      throw Exception('Chyba při vytváření videa: $e');
    }
  }

  /// Získání všech vzpomínek
  Future<List<TravelMemory>> getAllMemories() async {
    final dbMemories = await _localDb.getAllMemories();
    // Konverze Memory na TravelMemory
    return dbMemories
        .map(
          (memory) => TravelMemory(
            id: memory.id,
            title: memory.title,
            description: memory.description,
            photos: memory.photos,
            visitedPlaces: memory.location != null ? [memory.location!] : [],
            createdAt: memory.date,
            date: memory.date,
            template: MemoryTemplate.classic,
            metadata: MemoryMetadata(
              totalPhotos: memory.photos.length,
              selectedPhotos: memory.photos.length,
              processingTime: DateTime.now(),
              autoGenerated: false,
            ),
          ),
        )
        .toList();
  }

  /// Vyhledání vzpomínek
  Future<List<TravelMemory>> searchMemories(String query) async {
    final allMemories = await getAllMemories();
    final lowerQuery = query.toLowerCase();

    return allMemories
        .where(
          (memory) =>
              memory.title.toLowerCase().contains(lowerQuery) ||
              memory.description.toLowerCase().contains(lowerQuery) ||
              memory.visitedPlaces.any(
                (place) => place.toLowerCase().contains(lowerQuery),
              ),
        )
        .toList();
  }

  /// Sdílení vzpomínky
  Future<void> shareMemory(TravelMemory memory, ShareFormat format) async {
    switch (format) {
      case ShareFormat.images:
        await _shareImages(memory);
        break;
      case ShareFormat.pdf:
        await _sharePdf(memory);
        break;
      case ShareFormat.video:
        await _shareVideo(memory);
        break;
      case ShareFormat.link:
        await _shareLink(memory);
        break;
    }
  }

  Future<void> _shareImages(TravelMemory memory) async {
    // Implementace sdílení obrázků
    if (kDebugMode) {
      print('Sdílím obrázky z vzpomínky: ${memory.title}');
    }
  }

  Future<void> _sharePdf(TravelMemory memory) async {
    // Implementace vytvoření a sdílení PDF
    if (kDebugMode) {
      print('Vytvářím PDF z vzpomínky: ${memory.title}');
    }
  }

  Future<void> _shareVideo(TravelMemory memory) async {
    // Implementace sdílení videa
    if (kDebugMode) {
      print('Sdílím video z vzpomínky: ${memory.title}');
    }
  }

  Future<void> _shareLink(TravelMemory memory) async {
    // Implementace sdílení odkazu
    if (kDebugMode) {
      print('Sdílím odkaz na vzpomínku: ${memory.title}');
    }
  }

  /// Smazání vzpomínky
  Future<void> deleteMemory(String memoryId) async {
    // Implementace smazání vzpomínky
    if (kDebugMode) {
      print('Mažu vzpomínku: $memoryId');
    }
  }

  /// Aktualizace vzpomínky
  Future<void> updateMemory(TravelMemory memory) async {
    // Konverze TravelMemory na Memory pro databázi
    final dbMemory = Memory(
      id: memory.id,
      title: memory.title,
      description: memory.description,
      date: memory.date,
      location: memory.visitedPlaces.isNotEmpty
          ? memory.visitedPlaces.first
          : null,
      photos: memory.photos,
    );
    await _localDb.saveMemory(dbMemory);
  }

  /// Získání statistik vzpomínek
  Future<Map<String, dynamic>> getMemoryStatistics() async {
    final memories = await getAllMemories();

    return {
      'totalMemories': memories.length,
      'totalPhotos': memories.fold<int>(0, (sum, m) => sum + m.photos.length),
      'averagePhotosPerMemory': memories.isNotEmpty
          ? memories.fold<int>(0, (sum, m) => sum + m.photos.length) /
                memories.length
          : 0,
      'oldestMemory': memories.isNotEmpty
          ? memories.map((m) => m.date).reduce((a, b) => a.isBefore(b) ? a : b)
          : null,
      'newestMemory': memories.isNotEmpty
          ? memories.map((m) => m.date).reduce((a, b) => a.isAfter(b) ? a : b)
          : null,
    };
  }

  /// Exportování všech vzpomínek
  Future<String> exportAllMemories() async {
    final memories = await getAllMemories();

    // Simulace exportu
    if (kDebugMode) {
      print('Exportuji ${memories.length} vzpomínek');
    }

    return '/path/to/export.zip';
  }

  /// Importování vzpomínek
  Future<void> importMemories(String filePath) async {
    // Simulace importu
    if (kDebugMode) {
      print('Importuji vzpomínky z: $filePath');
    }
  }

  /// Optimalizace úložiště
  Future<void> optimizeStorage() async {
    // Simulace optimalizace
    if (kDebugMode) {
      print('Optimalizuji úložiště vzpomínek');
    }
  }

  /// Zálohování vzpomínek
  Future<void> backupMemories() async {
    // Simulace zálohování
    if (kDebugMode) {
      print('Zálohuji vzpomínky');
    }
  }

  /// Obnovení vzpomínek ze zálohy
  Future<void> restoreMemories(String backupPath) async {
    // Simulace obnovení
    if (kDebugMode) {
      print('Obnovuji vzpomínky ze zálohy: $backupPath');
    }
  }
}
