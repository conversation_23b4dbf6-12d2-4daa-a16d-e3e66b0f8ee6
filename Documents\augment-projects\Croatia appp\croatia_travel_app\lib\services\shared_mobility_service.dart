import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import '../models/smart_city_models.dart';

/// Služba pro sdílenou mobilitu (car sharing, bike sharing, scootery)
class SharedMobilityService {
  static final SharedMobilityService _instance =
      SharedMobilityService._internal();
  factory SharedMobilityService() => _instance;
  SharedMobilityService._internal();

  final Dio _dio = Dio();
  final Map<String, List<SharedVehicle>> _cache = {};
  final Map<String, DateTime> _lastUpdate = {};

  /// Inicializace služby
  Future<void> initialize() async {
    _dio.options = BaseOptions(
      connectTimeout: const Duration(seconds: 15),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'User-Agent': 'CroatiaTravel/1.0 (Shared Mobility)',
        'Accept': 'application/json',
      },
    );

    if (kDebugMode) {
      _dio.interceptors.add(
        LogInterceptor(
          requestBody: false,
          responseBody: false,
          logPrint: (obj) => debugPrint('[SharedMobility] $obj'),
        ),
      );
    }
  }

  /// Získání dostupných vozidel v okolí
  Future<List<SharedVehicle>> getNearbyVehicles({
    required double latitude,
    required double longitude,
    double radiusKm = 1.0,
    List<VehicleType>? types,
    bool onlyAvailable = true,
  }) async {
    try {
      debugPrint('🚗 Hledám sdílená vozidla v okolí $latitude, $longitude');

      // Kontrola cache
      final cacheKey =
          '${latitude.toStringAsFixed(3)}_${longitude.toStringAsFixed(3)}';
      if (_isCacheValid(cacheKey)) {
        debugPrint('✅ Používám cache pro sdílená vozidla');
        return _filterVehicles(_cache[cacheKey]!, types, onlyAvailable);
      }

      // Pokus o získání skutečných dat
      final realData = await _fetchRealVehicleData(
        latitude,
        longitude,
        radiusKm,
      );
      if (realData.isNotEmpty) {
        _cache[cacheKey] = realData;
        _lastUpdate[cacheKey] = DateTime.now();
        return _filterVehicles(realData, types, onlyAvailable);
      }

      // Fallback na mock data
      debugPrint('⚠️ Používám mock data pro sdílená vozidla');
      final mockData = _generateMockVehicles(latitude, longitude);
      _cache[cacheKey] = mockData;
      _lastUpdate[cacheKey] = DateTime.now();

      return _filterVehicles(mockData, types, onlyAvailable);
    } catch (e) {
      debugPrint('❌ Chyba při načítání sdílených vozidel: $e');
      return _generateMockVehicles(latitude, longitude);
    }
  }

  /// Rezervace vozidla
  Future<VehicleReservation?> reserveVehicle({
    required String vehicleId,
    required String userId,
    required Duration duration,
  }) async {
    try {
      debugPrint('🚗 Rezervuji vozidlo $vehicleId pro $userId');

      final startTime = DateTime.now();
      final reservationData = {
        'vehicle_id': vehicleId,
        'user_id': userId,
        'start_time': startTime.toIso8601String(),
        'duration_minutes': duration.inMinutes,
      };

      final response = await _dio.post(
        '/shared-mobility/reservations',
        data: reservationData,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return _parseVehicleReservation(response.data);
      }
    } catch (e) {
      debugPrint('❌ Chyba při rezervaci vozidla: $e');
    }

    // Mock rezervace pro demo
    return _createMockReservation(vehicleId, userId, duration);
  }

  /// Odemčení vozidla
  Future<bool> unlockVehicle(String reservationId, String unlockCode) async {
    try {
      debugPrint('🔓 Odemykám vozidlo s kódem $unlockCode');

      final response = await _dio.post(
        '/shared-mobility/unlock',
        data: {'reservation_id': reservationId, 'unlock_code': unlockCode},
      );

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('❌ Chyba při odemykání vozidla: $e');
      return false;
    }
  }

  /// Ukončení jízdy
  Future<bool> endTrip({
    required String reservationId,
    required double endLatitude,
    required double endLongitude,
  }) async {
    try {
      debugPrint('🏁 Ukončuji jízdu $reservationId');

      final response = await _dio.post(
        '/shared-mobility/end-trip',
        data: {
          'reservation_id': reservationId,
          'end_latitude': endLatitude,
          'end_longitude': endLongitude,
          'end_time': DateTime.now().toIso8601String(),
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('❌ Chyba při ukončování jízdy: $e');
      return false;
    }
  }

  /// Získání aktivních rezervací uživatele
  Future<List<VehicleReservation>> getUserReservations(String userId) async {
    try {
      final response = await _dio.get(
        '/shared-mobility/reservations/user/$userId',
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['reservations'] ?? [];
        return data.map((json) => _parseVehicleReservation(json)).toList();
      }
    } catch (e) {
      debugPrint('Chyba při načítání rezervací: $e');
    }

    return [];
  }

  /// Hlášení problému s vozidlem
  Future<bool> reportVehicleIssue({
    required String vehicleId,
    required String issueType,
    required String description,
    String? userId,
    List<String>? photos,
  }) async {
    try {
      await _dio.post(
        '/shared-mobility/report-issue',
        data: {
          'vehicle_id': vehicleId,
          'issue_type': issueType,
          'description': description,
          'timestamp': DateTime.now().toIso8601String(),
          if (userId != null) 'user_id': userId,
          if (photos != null) 'photos': photos,
        },
      );

      return true;
    } catch (e) {
      debugPrint('Chyba při hlášení problému: $e');
      return false;
    }
  }

  /// Získání statistik sdílené mobility
  Future<SharedMobilityStats?> getStats(String cityId) async {
    try {
      final response = await _dio.get('/shared-mobility/statistics/$cityId');

      if (response.statusCode == 200) {
        return SharedMobilityStats.fromJson(response.data);
      }
    } catch (e) {
      debugPrint('Chyba při načítání statistik: $e');
    }

    return _generateMockStats(cityId);
  }

  /// Pokus o získání skutečných dat
  Future<List<SharedVehicle>> _fetchRealVehicleData(
    double latitude,
    double longitude,
    double radiusKm,
  ) async {
    // Zde by byla integrace se skutečnými API:
    // - Bolt (scootery): https://api.bolt.eu/
    // - Lime (scootery): https://api.lime.bike/
    // - Car2Go/ShareNow: https://api.car2go.com/
    // - Nextbike: https://api.nextbike.net/

    // Pro demo vracíme prázdný seznam
    return [];
  }

  /// Generování mock dat
  List<SharedVehicle> _generateMockVehicles(double lat, double lng) {
    final vehicles = <SharedVehicle>[];
    final random = DateTime.now().millisecondsSinceEpoch;

    // Generujeme různé typy vozidel
    final types = [
      VehicleType.bike,
      VehicleType.scooter,
      VehicleType.car,
      VehicleType.electric,
    ];

    for (int i = 0; i < 15; i++) {
      final offsetLat = (random % 2000 - 1000) / 100000.0;
      final offsetLng = (random % 2000 - 1000) / 100000.0;
      final type = types[i % types.length];

      vehicles.add(
        SharedVehicle(
          id: 'vehicle_${lat.toStringAsFixed(3)}_$i',
          licensePlate: _generateLicensePlate(i),
          type: type,
          brand: _getBrandForType(type),
          model: _getModelForType(type, i),
          latitude: lat + offsetLat,
          longitude: lng + offsetLng,
          batteryLevel: type == VehicleType.electric ? 60 + (i * 5) % 40 : 0,
          fuelLevel: type != VehicleType.electric ? 40 + (i * 7) % 60 : 0,
          pricePerMinute: _getPricePerMinute(type),
          pricePerKm: _getPricePerKm(type),
          currency: 'HRK',
          status: i % 8 == 0
              ? VehicleStatus.maintenance
              : VehicleStatus.available,
          features: _getFeaturesForType(type),
          lastUpdated: DateTime.now().subtract(Duration(minutes: i * 2)),
        ),
      );
    }

    return vehicles;
  }

  String _generateLicensePlate(int index) {
    final letters = ['ZG', 'ST', 'RI', 'OS', 'KA', 'PU'];
    final letter = letters[index % letters.length];
    final number = (1000 + index * 123) % 9999;
    return '$letter-$number-XX';
  }

  String _getBrandForType(VehicleType type) {
    switch (type) {
      case VehicleType.bike:
        return 'Nextbike';
      case VehicleType.scooter:
        return 'Bolt';
      case VehicleType.car:
        return 'Volkswagen';
      case VehicleType.electric:
        return 'Tesla';
      case VehicleType.hybrid:
        return 'Toyota';
    }
  }

  String _getModelForType(VehicleType type, int index) {
    switch (type) {
      case VehicleType.bike:
        return 'City Bike';
      case VehicleType.scooter:
        return 'ES200';
      case VehicleType.car:
        return ['Golf', 'Polo', 'Passat'][index % 3];
      case VehicleType.electric:
        return 'Model 3';
      case VehicleType.hybrid:
        return 'Prius';
    }
  }

  double _getPricePerMinute(VehicleType type) {
    switch (type) {
      case VehicleType.bike:
        return 1.0;
      case VehicleType.scooter:
        return 2.0;
      case VehicleType.car:
        return 4.0;
      case VehicleType.electric:
        return 5.0;
      case VehicleType.hybrid:
        return 4.5;
    }
  }

  double _getPricePerKm(VehicleType type) {
    switch (type) {
      case VehicleType.bike:
        return 0.0;
      case VehicleType.scooter:
        return 1.0;
      case VehicleType.car:
        return 3.0;
      case VehicleType.electric:
        return 2.5;
      case VehicleType.hybrid:
        return 2.8;
    }
  }

  List<String> _getFeaturesForType(VehicleType type) {
    switch (type) {
      case VehicleType.bike:
        return ['basket', 'lights', 'gps'];
      case VehicleType.scooter:
        return ['helmet', 'lights', 'gps', 'phone_holder'];
      case VehicleType.car:
        return ['air_conditioning', 'gps', 'bluetooth', 'usb_charging'];
      case VehicleType.electric:
        return ['autopilot', 'supercharging', 'premium_audio', 'heated_seats'];
      case VehicleType.hybrid:
        return ['eco_mode', 'gps', 'bluetooth', 'backup_camera'];
    }
  }

  VehicleReservation _createMockReservation(
    String vehicleId,
    String userId,
    Duration duration,
  ) {
    final startTime = DateTime.now();

    return VehicleReservation(
      id: 'res_${DateTime.now().millisecondsSinceEpoch}',
      vehicleId: vehicleId,
      userId: userId,
      startTime: startTime,
      duration: duration,
      currency: 'HRK',
      status: ReservationStatus.confirmed,
      unlockCode: '${1000 + (vehicleId.hashCode % 9000)}',
      createdAt: DateTime.now(),
    );
  }

  SharedMobilityStats _generateMockStats(String cityId) {
    return SharedMobilityStats(
      cityId: cityId,
      totalVehicles: 450,
      availableVehicles: 320,
      totalTrips: 12500,
      averageTripDuration: const Duration(minutes: 18),
      averageTripDistance: 3.2,
      lastUpdated: DateTime.now(),
    );
  }

  List<SharedVehicle> _filterVehicles(
    List<SharedVehicle> vehicles,
    List<VehicleType>? types,
    bool onlyAvailable,
  ) {
    var filtered = vehicles;

    if (types != null && types.isNotEmpty) {
      filtered = filtered
          .where((vehicle) => types.contains(vehicle.type))
          .toList();
    }

    if (onlyAvailable) {
      filtered = filtered
          .where((vehicle) => vehicle.isAvailable && vehicle.hasEnoughEnergy)
          .toList();
    }

    return filtered;
  }

  bool _isCacheValid(String key) {
    final lastUpdate = _lastUpdate[key];
    if (lastUpdate == null) return false;

    return DateTime.now().difference(lastUpdate) < const Duration(minutes: 3);
  }

  VehicleReservation _parseVehicleReservation(Map<String, dynamic> json) {
    return VehicleReservation(
      id: json['id'] ?? '',
      vehicleId: json['vehicle_id'] ?? '',
      userId: json['user_id'] ?? '',
      startTime: DateTime.parse(json['start_time']),
      endTime: json['end_time'] != null
          ? DateTime.parse(json['end_time'])
          : null,
      duration: json['duration_minutes'] != null
          ? Duration(minutes: json['duration_minutes'])
          : null,
      totalPrice: json['total_price']?.toDouble(),
      currency: json['currency'] ?? 'HRK',
      status: ReservationStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => ReservationStatus.pending,
      ),
      unlockCode: json['unlock_code'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  void clearCache() {
    _cache.clear();
    _lastUpdate.clear();
  }

  void dispose() {
    clearCache();
  }
}

/// Statistiky sdílené mobility
class SharedMobilityStats {
  final String cityId;
  final int totalVehicles;
  final int availableVehicles;
  final int totalTrips;
  final Duration averageTripDuration;
  final double averageTripDistance;
  final DateTime lastUpdated;

  SharedMobilityStats({
    required this.cityId,
    required this.totalVehicles,
    required this.availableVehicles,
    required this.totalTrips,
    required this.averageTripDuration,
    required this.averageTripDistance,
    required this.lastUpdated,
  });

  factory SharedMobilityStats.fromJson(Map<String, dynamic> json) {
    return SharedMobilityStats(
      cityId: json['city_id'] ?? '',
      totalVehicles: json['total_vehicles'] ?? 0,
      availableVehicles: json['available_vehicles'] ?? 0,
      totalTrips: json['total_trips'] ?? 0,
      averageTripDuration: Duration(
        minutes: json['average_trip_duration_minutes'] ?? 0,
      ),
      averageTripDistance: json['average_trip_distance_km']?.toDouble() ?? 0.0,
      lastUpdated: DateTime.parse(json['last_updated']),
    );
  }
}
