// ========== PARKOVÁNÍ ==========

/// Parkovací místo
class ParkingSpot {
  final String id;
  final String name;
  final double latitude;
  final double longitude;
  final String address;
  final ParkingType type;
  final int totalSpaces;
  final int availableSpaces;
  final double hourlyRate;
  final double dailyRate;
  final String currency;
  final List<String> paymentMethods;
  final ParkingFeatures features;
  final bool isActive;
  final DateTime lastUpdated;

  ParkingSpot({
    required this.id,
    required this.name,
    required this.latitude,
    required this.longitude,
    required this.address,
    required this.type,
    required this.totalSpaces,
    required this.availableSpaces,
    required this.hourlyRate,
    required this.dailyRate,
    required this.currency,
    required this.paymentMethods,
    required this.features,
    required this.isActive,
    required this.lastUpdated,
  });

  /// Dostupnost v procentech
  double get occupancyRate => totalSpaces > 0
      ? ((totalSpaces - availableSpaces) / totalSpaces) * 100
      : 0;

  /// Je téměř plné (>90% obsazeno)
  bool get isNearlyFull => occupancyRate > 90;

  /// Je dostupné
  bool get hasAvailableSpaces => availableSpaces > 0 && isActive;

  factory ParkingSpot.fromJson(Map<String, dynamic> json) {
    return ParkingSpot(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      latitude: json['latitude']?.toDouble() ?? 0.0,
      longitude: json['longitude']?.toDouble() ?? 0.0,
      address: json['address'] ?? '',
      type: ParkingType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => ParkingType.street,
      ),
      totalSpaces: json['total_spaces'] ?? 0,
      availableSpaces: json['available_spaces'] ?? 0,
      hourlyRate: json['hourly_rate']?.toDouble() ?? 0.0,
      dailyRate: json['daily_rate']?.toDouble() ?? 0.0,
      currency: json['currency'] ?? 'HRK',
      paymentMethods: List<String>.from(json['payment_methods'] ?? []),
      features: ParkingFeatures.fromJson(json['features'] ?? {}),
      isActive: json['is_active'] ?? true,
      lastUpdated: DateTime.parse(
        json['last_updated'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }
}

/// Typ parkování
enum ParkingType {
  street, // Ulice
  garage, // Garáž
  lot, // Parkoviště
  underground, // Podzemní
  private, // Soukromé
  disabled, // Pro ZTP
}

/// Funkce parkování
class ParkingFeatures {
  final bool hasEVCharging; // Nabíjení elektromobilů
  final bool hasDisabledAccess; // Přístup pro ZTP
  final bool hasCCTV; // Kamerový systém
  final bool isCovered; // Zastřešené
  final bool has24HAccess; // 24h přístup
  final bool hasCarWash; // Myčka aut
  final bool hasToilets; // WC
  final double maxHeight; // Max výška vozidla (m)

  ParkingFeatures({
    required this.hasEVCharging,
    required this.hasDisabledAccess,
    required this.hasCCTV,
    required this.isCovered,
    required this.has24HAccess,
    required this.hasCarWash,
    required this.hasToilets,
    required this.maxHeight,
  });

  factory ParkingFeatures.fromJson(Map<String, dynamic> json) {
    return ParkingFeatures(
      hasEVCharging: json['has_ev_charging'] ?? false,
      hasDisabledAccess: json['has_disabled_access'] ?? false,
      hasCCTV: json['has_cctv'] ?? false,
      isCovered: json['is_covered'] ?? false,
      has24HAccess: json['has_24h_access'] ?? false,
      hasCarWash: json['has_car_wash'] ?? false,
      hasToilets: json['has_toilets'] ?? false,
      maxHeight: json['max_height']?.toDouble() ?? 2.0,
    );
  }
}

/// Parkovací rezervace
class ParkingReservation {
  final String id;
  final String parkingSpotId;
  final String userId;
  final DateTime startTime;
  final DateTime endTime;
  final Duration duration;
  final double totalPrice;
  final String currency;
  final ReservationStatus status;
  final String? qrCode;
  final DateTime createdAt;

  ParkingReservation({
    required this.id,
    required this.parkingSpotId,
    required this.userId,
    required this.startTime,
    required this.endTime,
    required this.duration,
    required this.totalPrice,
    required this.currency,
    required this.status,
    this.qrCode,
    required this.createdAt,
  });

  factory ParkingReservation.fromJson(Map<String, dynamic> json) {
    return ParkingReservation(
      id: json['id'] ?? '',
      parkingSpotId: json['parking_spot_id'] ?? '',
      userId: json['user_id'] ?? '',
      startTime: DateTime.parse(json['start_time']),
      endTime: DateTime.parse(json['end_time']),
      duration: Duration(minutes: json['duration_minutes'] ?? 0),
      totalPrice: json['total_price']?.toDouble() ?? 0.0,
      currency: json['currency'] ?? 'HRK',
      status: ReservationStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => ReservationStatus.pending,
      ),
      qrCode: json['qr_code'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }
}

enum ReservationStatus {
  pending,
  confirmed,
  active,
  completed,
  cancelled,
  expired,
}

// ========== SDÍLENÁ DOPRAVA ==========

/// Sdílené vozidlo
class SharedVehicle {
  final String id;
  final String licensePlate;
  final VehicleType type;
  final String brand;
  final String model;
  final double latitude;
  final double longitude;
  final int batteryLevel; // Pro elektromobily (%)
  final int fuelLevel; // Pro spalovací motory (%)
  final double pricePerMinute;
  final double pricePerKm;
  final String currency;
  final VehicleStatus status;
  final List<String> features;
  final DateTime lastUpdated;

  SharedVehicle({
    required this.id,
    required this.licensePlate,
    required this.type,
    required this.brand,
    required this.model,
    required this.latitude,
    required this.longitude,
    required this.batteryLevel,
    required this.fuelLevel,
    required this.pricePerMinute,
    required this.pricePerKm,
    required this.currency,
    required this.status,
    required this.features,
    required this.lastUpdated,
  });

  /// Je dostupné pro rezervaci
  bool get isAvailable => status == VehicleStatus.available;

  /// Má dostatečnou energii (>20%)
  bool get hasEnoughEnergy =>
      (type == VehicleType.electric && batteryLevel > 20) ||
      (type != VehicleType.electric && fuelLevel > 20);

  factory SharedVehicle.fromJson(Map<String, dynamic> json) {
    return SharedVehicle(
      id: json['id'] ?? '',
      licensePlate: json['license_plate'] ?? '',
      type: VehicleType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => VehicleType.bike,
      ),
      brand: json['brand'] ?? '',
      model: json['model'] ?? '',
      latitude: json['latitude']?.toDouble() ?? 0.0,
      longitude: json['longitude']?.toDouble() ?? 0.0,
      batteryLevel: json['battery_level'] ?? 0,
      fuelLevel: json['fuel_level'] ?? 0,
      pricePerMinute: json['price_per_minute']?.toDouble() ?? 0.0,
      pricePerKm: json['price_per_km']?.toDouble() ?? 0.0,
      currency: json['currency'] ?? 'HRK',
      status: VehicleStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => VehicleStatus.available,
      ),
      features: List<String>.from(json['features'] ?? []),
      lastUpdated: DateTime.parse(
        json['last_updated'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }
}

enum VehicleType { car, bike, scooter, electric, hybrid }

enum VehicleStatus { available, reserved, inUse, maintenance, outOfService }

/// Rezervace sdíleného vozidla
class VehicleReservation {
  final String id;
  final String vehicleId;
  final String userId;
  final DateTime startTime;
  final DateTime? endTime;
  final Duration? duration;
  final double? totalPrice;
  final String currency;
  final ReservationStatus status;
  final String? unlockCode;
  final DateTime createdAt;

  VehicleReservation({
    required this.id,
    required this.vehicleId,
    required this.userId,
    required this.startTime,
    this.endTime,
    this.duration,
    this.totalPrice,
    required this.currency,
    required this.status,
    this.unlockCode,
    required this.createdAt,
  });

  factory VehicleReservation.fromJson(Map<String, dynamic> json) {
    return VehicleReservation(
      id: json['id'] ?? '',
      vehicleId: json['vehicle_id'] ?? '',
      userId: json['user_id'] ?? '',
      startTime: DateTime.parse(json['start_time']),
      endTime: json['end_time'] != null
          ? DateTime.parse(json['end_time'])
          : null,
      duration: json['duration_minutes'] != null
          ? Duration(minutes: json['duration_minutes'])
          : null,
      totalPrice: json['total_price']?.toDouble(),
      currency: json['currency'] ?? 'HRK',
      status: ReservationStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => ReservationStatus.pending,
      ),
      unlockCode: json['unlock_code'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }
}

// ========== MĚSTSKÉ SLUŽBY ==========

/// Městský úřad/služba
class CityService {
  final String id;
  final String name;
  final String description;
  final ServiceCategory category;
  final String address;
  final double latitude;
  final double longitude;
  final String phone;
  final String email;
  final String? website;
  final List<WorkingHours> workingHours;
  final List<String> services;
  final bool hasOnlineServices;
  final bool requiresAppointment;
  final double rating;
  final int reviewCount;

  CityService({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.phone,
    required this.email,
    this.website,
    required this.workingHours,
    required this.services,
    required this.hasOnlineServices,
    required this.requiresAppointment,
    required this.rating,
    required this.reviewCount,
  });

  /// Je aktuálně otevřeno
  bool get isOpenNow {
    final now = DateTime.now();
    final today = now.weekday;

    final todayHours = workingHours.where((h) => h.dayOfWeek == today).toList();
    if (todayHours.isEmpty) return false;

    final currentTime = now.hour * 60 + now.minute;

    return todayHours.any(
      (hours) =>
          currentTime >= hours.openMinutes && currentTime <= hours.closeMinutes,
    );
  }

  factory CityService.fromJson(Map<String, dynamic> json) {
    return CityService(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      category: ServiceCategory.values.firstWhere(
        (c) => c.name == json['category'],
        orElse: () => ServiceCategory.other,
      ),
      address: json['address'] ?? '',
      latitude: json['latitude']?.toDouble() ?? 0.0,
      longitude: json['longitude']?.toDouble() ?? 0.0,
      phone: json['phone'] ?? '',
      email: json['email'] ?? '',
      website: json['website'],
      workingHours:
          (json['working_hours'] as List?)
              ?.map((h) => WorkingHours.fromJson(h))
              .toList() ??
          [],
      services: List<String>.from(json['services'] ?? []),
      hasOnlineServices: json['has_online_services'] ?? false,
      requiresAppointment: json['requires_appointment'] ?? false,
      rating: json['rating']?.toDouble() ?? 0.0,
      reviewCount: json['review_count'] ?? 0,
    );
  }
}

enum ServiceCategory {
  administration, // Administrativa
  health, // Zdravotnictví
  education, // Vzdělávání
  social, // Sociální služby
  transport, // Doprava
  environment, // Životní prostředí
  culture, // Kultura
  sports, // Sport
  emergency, // Pohotovost
  other, // Ostatní
}

/// Pracovní doba
class WorkingHours {
  final int dayOfWeek; // 1 = pondělí, 7 = neděle
  final int openHour;
  final int openMinute;
  final int closeHour;
  final int closeMinute;
  final bool isClosed;

  WorkingHours({
    required this.dayOfWeek,
    required this.openHour,
    required this.openMinute,
    required this.closeHour,
    required this.closeMinute,
    this.isClosed = false,
  });

  int get openMinutes => openHour * 60 + openMinute;
  int get closeMinutes => closeHour * 60 + closeMinute;

  String get timeRange => isClosed
      ? 'Zavřeno'
      : '${openHour.toString().padLeft(2, '0')}:${openMinute.toString().padLeft(2, '0')} - ${closeHour.toString().padLeft(2, '0')}:${closeMinute.toString().padLeft(2, '0')}';

  factory WorkingHours.fromJson(Map<String, dynamic> json) {
    return WorkingHours(
      dayOfWeek: json['day_of_week'] ?? 1,
      openHour: json['open_hour'] ?? 0,
      openMinute: json['open_minute'] ?? 0,
      closeHour: json['close_hour'] ?? 0,
      closeMinute: json['close_minute'] ?? 0,
      isClosed: json['is_closed'] ?? false,
    );
  }
}

// ========== HLÁŠENÍ PROBLÉMŮ ==========

/// Hlášení problému
class IssueReport {
  final String id;
  final String title;
  final String description;
  final IssueCategory category;
  final IssuePriority priority;
  final double latitude;
  final double longitude;
  final String address;
  final List<String> photos;
  final String reporterUserId;
  final IssueStatus status;
  final DateTime createdAt;
  final DateTime? resolvedAt;
  final String? assignedTo;
  final List<IssueUpdate> updates;

  IssueReport({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.priority,
    required this.latitude,
    required this.longitude,
    required this.address,
    required this.photos,
    required this.reporterUserId,
    required this.status,
    required this.createdAt,
    this.resolvedAt,
    this.assignedTo,
    required this.updates,
  });

  /// Doba od nahlášení
  Duration get timeSinceReported => DateTime.now().difference(createdAt);

  /// Je vyřešeno
  bool get isResolved => status == IssueStatus.resolved;

  factory IssueReport.fromJson(Map<String, dynamic> json) {
    return IssueReport(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      category: IssueCategory.values.firstWhere(
        (c) => c.name == json['category'],
        orElse: () => IssueCategory.other,
      ),
      priority: IssuePriority.values.firstWhere(
        (p) => p.name == json['priority'],
        orElse: () => IssuePriority.medium,
      ),
      latitude: json['latitude']?.toDouble() ?? 0.0,
      longitude: json['longitude']?.toDouble() ?? 0.0,
      address: json['address'] ?? '',
      photos: List<String>.from(json['photos'] ?? []),
      reporterUserId: json['reporter_user_id'] ?? '',
      status: IssueStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => IssueStatus.reported,
      ),
      createdAt: DateTime.parse(json['created_at']),
      resolvedAt: json['resolved_at'] != null
          ? DateTime.parse(json['resolved_at'])
          : null,
      assignedTo: json['assigned_to'],
      updates:
          (json['updates'] as List?)
              ?.map((u) => IssueUpdate.fromJson(u))
              .toList() ??
          [],
    );
  }
}

enum IssueCategory {
  roads, // Silnice
  lighting, // Osvětlení
  waste, // Odpad
  water, // Voda
  parks, // Parky
  noise, // Hluk
  safety, // Bezpečnost
  transport, // Doprava
  other, // Ostatní
}

enum IssuePriority { low, medium, high, urgent }

enum IssueStatus { reported, inProgress, resolved, rejected, duplicate }

/// Aktualizace hlášení
class IssueUpdate {
  final String id;
  final String issueId;
  final String message;
  final String authorId;
  final DateTime timestamp;
  final List<String> photos;

  IssueUpdate({
    required this.id,
    required this.issueId,
    required this.message,
    required this.authorId,
    required this.timestamp,
    required this.photos,
  });

  factory IssueUpdate.fromJson(Map<String, dynamic> json) {
    return IssueUpdate(
      id: json['id'] ?? '',
      issueId: json['issue_id'] ?? '',
      message: json['message'] ?? '',
      authorId: json['author_id'] ?? '',
      timestamp: DateTime.parse(json['timestamp']),
      photos: List<String>.from(json['photos'] ?? []),
    );
  }
}

// ========== VEŘEJNÉ SLUŽBY ==========

/// Veřejná služba/zařízení
class PublicFacility {
  final String id;
  final String name;
  final String description;
  final FacilityType type;
  final double latitude;
  final double longitude;
  final String address;
  final List<WorkingHours> workingHours;
  final List<String> amenities;
  final bool isAccessible;
  final bool isFree;
  final double? entryFee;
  final String currency;
  final double rating;
  final int reviewCount;
  final List<String> photos;

  PublicFacility({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.latitude,
    required this.longitude,
    required this.address,
    required this.workingHours,
    required this.amenities,
    required this.isAccessible,
    required this.isFree,
    this.entryFee,
    required this.currency,
    required this.rating,
    required this.reviewCount,
    required this.photos,
  });

  factory PublicFacility.fromJson(Map<String, dynamic> json) {
    return PublicFacility(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      type: FacilityType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => FacilityType.other,
      ),
      latitude: json['latitude']?.toDouble() ?? 0.0,
      longitude: json['longitude']?.toDouble() ?? 0.0,
      address: json['address'] ?? '',
      workingHours:
          (json['working_hours'] as List?)
              ?.map((h) => WorkingHours.fromJson(h))
              .toList() ??
          [],
      amenities: List<String>.from(json['amenities'] ?? []),
      isAccessible: json['is_accessible'] ?? false,
      isFree: json['is_free'] ?? true,
      entryFee: json['entry_fee']?.toDouble(),
      currency: json['currency'] ?? 'HRK',
      rating: json['rating']?.toDouble() ?? 0.0,
      reviewCount: json['review_count'] ?? 0,
      photos: List<String>.from(json['photos'] ?? []),
    );
  }
}

enum FacilityType {
  library, // Knihovna
  museum, // Muzeum
  park, // Park
  playground, // Hřiště
  sportsCenter, // Sportovní centrum
  pool, // Bazén
  toilet, // Veřejné WC
  wifi, // WiFi hotspot
  atm, // Bankomat
  pharmacy, // Lékárna
  hospital, // Nemocnice
  police, // Policie
  fireStation, // Hasiči
  other, // Ostatní
}

// ========== DODATEČNÉ TŘÍDY PRO PARKOVÁNÍ ==========

/// Parkovací session
class ParkingSession {
  final String id;
  final String spotId;
  final String userId;
  final String licensePlate;
  final DateTime startTime;
  final DateTime? endTime;
  final Duration plannedDuration;
  final Duration? actualDuration;
  final double totalCost;
  final String currency;
  final SessionStatus status;
  final List<ParkingExtension> extensions;
  final DateTime createdAt;

  ParkingSession({
    required this.id,
    required this.spotId,
    required this.userId,
    required this.licensePlate,
    required this.startTime,
    this.endTime,
    required this.plannedDuration,
    this.actualDuration,
    required this.totalCost,
    required this.currency,
    required this.status,
    required this.extensions,
    required this.createdAt,
  });

  /// Zbývající čas
  Duration? get remainingTime {
    if (endTime != null) return null;
    final plannedEnd = startTime.add(plannedDuration);
    final now = DateTime.now();
    if (now.isAfter(plannedEnd)) return Duration.zero;
    return plannedEnd.difference(now);
  }

  /// Brzy vyprší (méně než 15 minut)
  bool get isExpiringSoon {
    final remaining = remainingTime;
    return remaining != null && remaining.inMinutes <= 15;
  }

  factory ParkingSession.fromJson(Map<String, dynamic> json) {
    return ParkingSession(
      id: json['id'] ?? '',
      spotId: json['spot_id'] ?? '',
      userId: json['user_id'] ?? '',
      licensePlate: json['license_plate'] ?? '',
      startTime: DateTime.parse(json['start_time']),
      endTime: json['end_time'] != null
          ? DateTime.parse(json['end_time'])
          : null,
      plannedDuration: Duration(minutes: json['planned_duration_minutes'] ?? 0),
      actualDuration: json['actual_duration_minutes'] != null
          ? Duration(minutes: json['actual_duration_minutes'])
          : null,
      totalCost: json['total_cost']?.toDouble() ?? 0.0,
      currency: json['currency'] ?? 'HRK',
      status: SessionStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => SessionStatus.active,
      ),
      extensions:
          (json['extensions'] as List?)
              ?.map((e) => ParkingExtension.fromJson(e))
              .toList() ??
          [],
      createdAt: DateTime.parse(json['created_at']),
    );
  }
}

enum SessionStatus { active, completed, cancelled, expired }

/// Prodloužení parkování
class ParkingExtension {
  final String id;
  final String sessionId;
  final Duration additionalTime;
  final double cost;
  final String currency;
  final DateTime timestamp;

  ParkingExtension({
    required this.id,
    required this.sessionId,
    required this.additionalTime,
    required this.cost,
    required this.currency,
    required this.timestamp,
  });

  factory ParkingExtension.fromJson(Map<String, dynamic> json) {
    return ParkingExtension(
      id: json['id'] ?? '',
      sessionId: json['session_id'] ?? '',
      additionalTime: Duration(minutes: json['additional_minutes'] ?? 0),
      cost: json['cost']?.toDouble() ?? 0.0,
      currency: json['currency'] ?? 'HRK',
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

/// Predikce dostupnosti parkování
class ParkingPrediction {
  final String spotId;
  final DateTime targetTime;
  final double availabilityProbability;
  final int predictedAvailableSpaces;
  final double confidence;
  final List<String> factors;

  ParkingPrediction({
    required this.spotId,
    required this.targetTime,
    required this.availabilityProbability,
    required this.predictedAvailableSpaces,
    required this.confidence,
    required this.factors,
  });

  factory ParkingPrediction.fromJson(Map<String, dynamic> json) {
    return ParkingPrediction(
      spotId: json['spot_id'] ?? '',
      targetTime: DateTime.parse(json['target_time']),
      availabilityProbability:
          json['availability_probability']?.toDouble() ?? 0.0,
      predictedAvailableSpaces: json['predicted_available_spaces'] ?? 0,
      confidence: json['confidence']?.toDouble() ?? 0.0,
      factors: List<String>.from(json['factors'] ?? []),
    );
  }

  factory ParkingPrediction.fallback() {
    return ParkingPrediction(
      spotId: '',
      targetTime: DateTime.now(),
      availabilityProbability: 0.5,
      predictedAvailableSpaces: 10,
      confidence: 0.3,
      factors: ['Nedostatek historických dat'],
    );
  }
}

/// Chytrá rezervace
class SmartReservation {
  final String id;
  final ParkingSpot spot;
  final DateTime arrivalTime;
  final Duration duration;
  final double walkingDistance;
  final double totalCost;
  final String currency;
  final double optimizationScore;
  final List<String> reasons;

  SmartReservation({
    required this.id,
    required this.spot,
    required this.arrivalTime,
    required this.duration,
    required this.walkingDistance,
    required this.totalCost,
    required this.currency,
    required this.optimizationScore,
    required this.reasons,
  });

  factory SmartReservation.fromJson(Map<String, dynamic> json) {
    return SmartReservation(
      id: json['id'] ?? '',
      spot: ParkingSpotExtension.fromJson(json['spot']),
      arrivalTime: DateTime.parse(json['arrival_time']),
      duration: Duration(minutes: json['duration_minutes'] ?? 0),
      walkingDistance: json['walking_distance']?.toDouble() ?? 0.0,
      totalCost: json['total_cost']?.toDouble() ?? 0.0,
      currency: json['currency'] ?? 'HRK',
      optimizationScore: json['optimization_score']?.toDouble() ?? 0.0,
      reasons: List<String>.from(json['reasons'] ?? []),
    );
  }
}

/// Dynamické ceny
class DynamicPricing {
  final String spotId;
  final DateTime timeSlot;
  final double basePrice;
  final double dynamicPrice;
  final double demandMultiplier;
  final String currency;

  DynamicPricing({
    required this.spotId,
    required this.timeSlot,
    required this.basePrice,
    required this.dynamicPrice,
    required this.demandMultiplier,
    required this.currency,
  });

  factory DynamicPricing.fromJson(Map<String, dynamic> json) {
    return DynamicPricing(
      spotId: json['spot_id'] ?? '',
      timeSlot: DateTime.parse(json['time_slot']),
      basePrice: json['base_price']?.toDouble() ?? 0.0,
      dynamicPrice: json['dynamic_price']?.toDouble() ?? 0.0,
      demandMultiplier: json['demand_multiplier']?.toDouble() ?? 1.0,
      currency: json['currency'] ?? 'HRK',
    );
  }
}

enum ParkingSpotStatus { available, occupied, outOfOrder, reserved }

/// Extension pro ParkingSpot s fromJson metodou
class ParkingSpotExtension {
  static ParkingSpot fromJson(Map<String, dynamic> json) {
    return ParkingSpot(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      latitude: json['latitude']?.toDouble() ?? 0.0,
      longitude: json['longitude']?.toDouble() ?? 0.0,
      address: json['address'] ?? '',
      type: ParkingType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => ParkingType.street,
      ),
      totalSpaces: json['total_spaces'] ?? 0,
      availableSpaces: json['available_spaces'] ?? 0,
      hourlyRate: json['hourly_rate']?.toDouble() ?? 0.0,
      dailyRate: json['daily_rate']?.toDouble() ?? 0.0,
      currency: json['currency'] ?? 'HRK',
      paymentMethods: List<String>.from(json['payment_methods'] ?? []),
      features: ParkingFeaturesExtension.fromJson(json['features'] ?? {}),
      isActive: json['is_active'] ?? true,
      lastUpdated: DateTime.parse(
        json['last_updated'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }
}

/// Extension pro ParkingFeatures s fromJson metodou
class ParkingFeaturesExtension {
  static ParkingFeatures fromJson(Map<String, dynamic> json) {
    return ParkingFeatures(
      hasEVCharging: json['has_ev_charging'] ?? false,
      hasDisabledAccess: json['has_disabled_access'] ?? false,
      hasCCTV: json['has_cctv'] ?? false,
      isCovered: json['is_covered'] ?? false,
      has24HAccess: json['has_24h_access'] ?? false,
      hasCarWash: json['has_car_wash'] ?? false,
      hasToilets: json['has_toilets'] ?? false,
      maxHeight: json['max_height']?.toDouble() ?? 2.0,
    );
  }
}

// ========== DODATEČNÉ TŘÍDY PRO KOMPATIBILITU ==========

/// Úřední služba (kompatibilita se starým kódem)
class GovernmentOffice {
  final String id;
  final String name;
  final String type;
  final String address;
  final double latitude;
  final double longitude;
  final String phoneNumber;
  final String email;
  final String? website;
  final List<OfficeHours> openingHours;
  final List<String> services;
  final List<String> languages;
  final bool hasDisabledAccess;
  final bool hasParking;

  GovernmentOffice({
    required this.id,
    required this.name,
    required this.type,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.phoneNumber,
    required this.email,
    this.website,
    required this.openingHours,
    required this.services,
    required this.languages,
    required this.hasDisabledAccess,
    required this.hasParking,
  });

  factory GovernmentOffice.fromJson(Map<String, dynamic> json) {
    return GovernmentOffice(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      type: json['type'] ?? '',
      address: json['address'] ?? '',
      latitude: json['latitude']?.toDouble() ?? 0.0,
      longitude: json['longitude']?.toDouble() ?? 0.0,
      phoneNumber: json['phone_number'] ?? '',
      email: json['email'] ?? '',
      website: json['website'],
      openingHours:
          (json['opening_hours'] as List?)
              ?.map((h) => OfficeHours.fromJson(h))
              .toList() ??
          [],
      services: List<String>.from(json['services'] ?? []),
      languages: List<String>.from(json['languages'] ?? []),
      hasDisabledAccess: json['has_disabled_access'] ?? false,
      hasParking: json['has_parking'] ?? false,
    );
  }
}

/// Úřední hodiny
class OfficeHours {
  final String dayOfWeek;
  final String openTime;
  final String closeTime;
  final bool isClosed;

  OfficeHours({
    required this.dayOfWeek,
    required this.openTime,
    required this.closeTime,
    this.isClosed = false,
  });

  factory OfficeHours.fromJson(Map<String, dynamic> json) {
    return OfficeHours(
      dayOfWeek: json['day_of_week'] ?? '',
      openTime: json['open_time'] ?? '',
      closeTime: json['close_time'] ?? '',
      isClosed: json['is_closed'] ?? false,
    );
  }
}

/// Online formulář
class OnlineForm {
  final String id;
  final String title;
  final String description;
  final FormCategory category;
  final List<FormField> fields;
  final String submitUrl;
  final Duration estimatedTime;
  final DateTime createdAt;

  OnlineForm({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.fields,
    required this.submitUrl,
    required this.estimatedTime,
    required this.createdAt,
  });

  factory OnlineForm.fromJson(Map<String, dynamic> json) {
    return OnlineForm(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      category: FormCategory.values.firstWhere(
        (c) => c.name == json['category'],
        orElse: () => FormCategory.other,
      ),
      fields:
          (json['fields'] as List?)
              ?.map((f) => FormField.fromJson(f))
              .toList() ??
          [],
      submitUrl: json['submit_url'] ?? '',
      estimatedTime: Duration(minutes: json['estimated_time_minutes'] ?? 0),
      createdAt: DateTime.parse(
        json['created_at'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }
}

enum FormCategory { residence, business, health, education, transport, other }

/// Pole formuláře
class FormField {
  final String id;
  final String label;
  final FieldType type;
  final bool isRequired;
  final String? placeholder;
  final List<String>? options;

  FormField({
    required this.id,
    required this.label,
    required this.type,
    required this.isRequired,
    this.placeholder,
    this.options,
  });

  factory FormField.fromJson(Map<String, dynamic> json) {
    return FormField(
      id: json['id'] ?? '',
      label: json['label'] ?? '',
      type: FieldType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => FieldType.text,
      ),
      isRequired: json['is_required'] ?? false,
      placeholder: json['placeholder'],
      options: json['options'] != null
          ? List<String>.from(json['options'])
          : null,
    );
  }
}

enum FieldType { text, email, phone, number, date, select, checkbox, file }

/// Podání formuláře
class FormSubmission {
  final String id;
  final String formId;
  final String userId;
  final Map<String, dynamic> data;
  final List<String> attachments;
  final SubmissionStatus status;
  final DateTime submittedAt;
  final DateTime? processedAt;
  final String? notes;

  FormSubmission({
    required this.id,
    required this.formId,
    required this.userId,
    required this.data,
    required this.attachments,
    required this.status,
    required this.submittedAt,
    this.processedAt,
    this.notes,
  });

  factory FormSubmission.fromJson(Map<String, dynamic> json) {
    return FormSubmission(
      id: json['id'] ?? '',
      formId: json['form_id'] ?? '',
      userId: json['user_id'] ?? '',
      data: Map<String, dynamic>.from(json['data'] ?? {}),
      attachments: List<String>.from(json['attachments'] ?? []),
      status: SubmissionStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => SubmissionStatus.pending,
      ),
      submittedAt: DateTime.parse(json['submitted_at']),
      processedAt: json['processed_at'] != null
          ? DateTime.parse(json['processed_at'])
          : null,
      notes: json['notes'],
    );
  }
}

enum SubmissionStatus { pending, processing, approved, rejected, completed }

/// Městský poplatek
class CityPayment {
  final String id;
  final String title;
  final String description;
  final PaymentCategory category;
  final double amount;
  final String currency;
  final DateTime dueDate;
  final String userId;
  final PaymentStatus status;
  final List<PaymentMethod> acceptedMethods;
  final DateTime? paidAt;

  CityPayment({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.amount,
    required this.currency,
    required this.dueDate,
    required this.userId,
    this.status = PaymentStatus.pending,
    required this.acceptedMethods,
    this.paidAt,
  });

  /// Je po splatnosti
  bool get isOverdue =>
      DateTime.now().isAfter(dueDate) && status != PaymentStatus.paid;

  factory CityPayment.fromJson(Map<String, dynamic> json) {
    return CityPayment(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      category: PaymentCategory.values.firstWhere(
        (c) => c.name == json['category'],
        orElse: () => PaymentCategory.other,
      ),
      amount: json['amount']?.toDouble() ?? 0.0,
      currency: json['currency'] ?? 'HRK',
      dueDate: DateTime.parse(json['due_date']),
      userId: json['user_id'] ?? '',
      status: PaymentStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => PaymentStatus.pending,
      ),
      acceptedMethods:
          (json['accepted_methods'] as List?)
              ?.map(
                (m) => PaymentMethod.values.firstWhere(
                  (pm) => pm.name == m,
                  orElse: () => PaymentMethod.card,
                ),
              )
              .toList() ??
          [],
      paidAt: json['paid_at'] != null ? DateTime.parse(json['paid_at']) : null,
    );
  }
}

enum PaymentCategory {
  communalWaste,
  parking,
  utilities,
  taxes,
  permits,
  other,
}

enum PaymentMethod { card, bankTransfer, cash, mobile }

enum PaymentStatus { pending, processing, paid, failed, refunded }

/// Veřejné WiFi
class PublicWifi {
  final String id;
  final String name;
  final String description;
  final double latitude;
  final double longitude;
  final String address;
  final WifiType type;
  final bool requiresRegistration;
  final double speedMbps;
  final Duration? timeLimit;
  final bool isActive;
  final DateTime lastUpdated;

  PublicWifi({
    required this.id,
    required this.name,
    required this.description,
    required this.latitude,
    required this.longitude,
    required this.address,
    required this.type,
    required this.requiresRegistration,
    required this.speedMbps,
    this.timeLimit,
    required this.isActive,
    required this.lastUpdated,
  });

  factory PublicWifi.fromJson(Map<String, dynamic> json) {
    return PublicWifi(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      latitude: json['latitude']?.toDouble() ?? 0.0,
      longitude: json['longitude']?.toDouble() ?? 0.0,
      address: json['address'] ?? '',
      type: WifiType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => WifiType.municipal,
      ),
      requiresRegistration: json['requires_registration'] ?? false,
      speedMbps: json['speed_mbps']?.toDouble() ?? 0.0,
      timeLimit: json['time_limit_minutes'] != null
          ? Duration(minutes: json['time_limit_minutes'])
          : null,
      isActive: json['is_active'] ?? true,
      lastUpdated: DateTime.parse(
        json['last_updated'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }
}

enum WifiType { municipal, library, transport, commercial, other }

// ========== POKROČILÉ FUNKCE ==========

/// AI asistence pro formuláře
class FormAssistance {
  final List<String> suggestions;
  final Map<String, String> autoFill;
  final List<String> warnings;
  final double completionScore;

  FormAssistance({
    required this.suggestions,
    required this.autoFill,
    required this.warnings,
    required this.completionScore,
  });

  factory FormAssistance.fromJson(Map<String, dynamic> json) {
    return FormAssistance(
      suggestions: List<String>.from(json['suggestions'] ?? []),
      autoFill: Map<String, String>.from(json['auto_fill'] ?? {}),
      warnings: List<String>.from(json['warnings'] ?? []),
      completionScore: json['completion_score']?.toDouble() ?? 0.0,
    );
  }

  factory FormAssistance.empty() {
    return FormAssistance(
      suggestions: [],
      autoFill: {},
      warnings: [],
      completionScore: 0.0,
    );
  }
}

/// Doporučení služby
class ServiceRecommendation {
  final String serviceId;
  final String title;
  final String description;
  final double relevanceScore;
  final List<String> reasons;
  final String category;

  ServiceRecommendation({
    required this.serviceId,
    required this.title,
    required this.description,
    required this.relevanceScore,
    required this.reasons,
    required this.category,
  });

  factory ServiceRecommendation.fromJson(Map<String, dynamic> json) {
    return ServiceRecommendation(
      serviceId: json['service_id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      relevanceScore: json['relevance_score']?.toDouble() ?? 0.0,
      reasons: List<String>.from(json['reasons'] ?? []),
      category: json['category'] ?? '',
    );
  }
}

/// Prediktivní upozornění
class PredictiveAlert {
  final String id;
  final String title;
  final String message;
  final AlertType type;
  final DateTime triggerDate;
  final String actionUrl;
  final bool isRead;

  PredictiveAlert({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.triggerDate,
    required this.actionUrl,
    required this.isRead,
  });

  factory PredictiveAlert.fromJson(Map<String, dynamic> json) {
    return PredictiveAlert(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      type: AlertType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => AlertType.info,
      ),
      triggerDate: DateTime.parse(json['trigger_date']),
      actionUrl: json['action_url'] ?? '',
      isRead: json['is_read'] ?? false,
    );
  }
}

enum AlertType { info, warning, urgent, reminder }

/// Ověření dokumentu
class DocumentVerification {
  final String documentId;
  final bool isValid;
  final String status;
  final DateTime verifiedAt;
  final String? blockchainHash;
  final List<String> errors;

  DocumentVerification({
    required this.documentId,
    required this.isValid,
    required this.status,
    required this.verifiedAt,
    this.blockchainHash,
    required this.errors,
  });

  factory DocumentVerification.fromJson(Map<String, dynamic> json) {
    return DocumentVerification(
      documentId: json['document_id'] ?? '',
      isValid: json['is_valid'] ?? false,
      status: json['status'] ?? '',
      verifiedAt: DateTime.parse(json['verified_at']),
      blockchainHash: json['blockchain_hash'],
      errors: List<String>.from(json['errors'] ?? []),
    );
  }

  factory DocumentVerification.failed() {
    return DocumentVerification(
      documentId: '',
      isValid: false,
      status: 'failed',
      verifiedAt: DateTime.now(),
      errors: ['Ověření se nezdařilo'],
    );
  }
}

enum DocumentType { id, passport, license, certificate, permit }

/// Odpověď chatbota
class ChatbotResponse {
  final String message;
  final List<String> suggestions;
  final List<ChatbotAction> actions;
  final String conversationId;
  final double confidence;

  ChatbotResponse({
    required this.message,
    required this.suggestions,
    required this.actions,
    required this.conversationId,
    required this.confidence,
  });

  factory ChatbotResponse.fromJson(Map<String, dynamic> json) {
    return ChatbotResponse(
      message: json['message'] ?? '',
      suggestions: List<String>.from(json['suggestions'] ?? []),
      actions:
          (json['actions'] as List?)
              ?.map((a) => ChatbotAction.fromJson(a))
              .toList() ??
          [],
      conversationId: json['conversation_id'] ?? '',
      confidence: json['confidence']?.toDouble() ?? 0.0,
    );
  }

  factory ChatbotResponse.error() {
    return ChatbotResponse(
      message: 'Omlouvám se, došlo k chybě. Zkuste to prosím znovu.',
      suggestions: [],
      actions: [],
      conversationId: '',
      confidence: 0.0,
    );
  }
}

/// Akce chatbota
class ChatbotAction {
  final String type;
  final String label;
  final String url;
  final Map<String, dynamic> data;

  ChatbotAction({
    required this.type,
    required this.label,
    required this.url,
    required this.data,
  });

  factory ChatbotAction.fromJson(Map<String, dynamic> json) {
    return ChatbotAction(
      type: json['type'] ?? '',
      label: json['label'] ?? '',
      url: json['url'] ?? '',
      data: Map<String, dynamic>.from(json['data'] ?? {}),
    );
  }
}

/// Analýza sentimentu
class SentimentAnalysis {
  final double positiveScore;
  final double negativeScore;
  final double neutralScore;
  final String overallSentiment;
  final List<String> keywords;
  final List<String> suggestions;

  SentimentAnalysis({
    required this.positiveScore,
    required this.negativeScore,
    required this.neutralScore,
    required this.overallSentiment,
    required this.keywords,
    required this.suggestions,
  });

  factory SentimentAnalysis.fromJson(Map<String, dynamic> json) {
    return SentimentAnalysis(
      positiveScore: json['positive_score']?.toDouble() ?? 0.0,
      negativeScore: json['negative_score']?.toDouble() ?? 0.0,
      neutralScore: json['neutral_score']?.toDouble() ?? 0.0,
      overallSentiment: json['overall_sentiment'] ?? 'neutral',
      keywords: List<String>.from(json['keywords'] ?? []),
      suggestions: List<String>.from(json['suggestions'] ?? []),
    );
  }

  factory SentimentAnalysis.neutral() {
    return SentimentAnalysis(
      positiveScore: 0.0,
      negativeScore: 0.0,
      neutralScore: 1.0,
      overallSentiment: 'neutral',
      keywords: [],
      suggestions: [],
    );
  }
}
