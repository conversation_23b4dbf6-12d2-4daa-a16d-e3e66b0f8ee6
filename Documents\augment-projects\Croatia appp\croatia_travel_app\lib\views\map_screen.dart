import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class MapScreen extends StatelessWidget {
  const MapScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF006994), // Adriatic blue
              Color(0xFF2E8B8B), // Mediterranean turquoise
            ],
          ),
        ),
        child: CustomPaint(
          painter: WatercolorMapPainter(),
          child: Safe<PERSON>rea(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: <PERSON>umn(
                children: [
                  // Header s watercolor efektem
                  Container(
                    padding: const EdgeInsets.all(20),
                    child: CustomPaint(
                      painter: WatercolorHeaderMapPainter(),
                      child: <PERSON>umn(
                        children: [
                          Text(
                            'Mapa Chorvatska',
                            style: GoogleFonts.playfairDisplay(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Objevte krásy Adriatiku',
                            style: GoogleFonts.inter(
                              fontSize: 16,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 40),

                  // Simulace mapy s watercolor markery
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.9),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: CustomPaint(
                        painter: WatercolorMapContentPainter(),
                        child: Column(
                          children: [
                            Text(
                              'Interaktivní mapa',
                              style: GoogleFonts.playfairDisplay(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: const Color(0xFF006994),
                              ),
                            ),
                            const SizedBox(height: 20),

                            // Watercolor markery
                            Expanded(
                              child: Stack(
                                children: [
                                  // Simulace mapy Chorvatska
                                  Center(
                                    child: Container(
                                      width: 200,
                                      height: 300,
                                      decoration: BoxDecoration(
                                        color: const Color(
                                          0xFF2E8B8B,
                                        ).withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(15),
                                      ),
                                      child: CustomPaint(
                                        painter: WatercolorCroatiaMapPainter(),
                                      ),
                                    ),
                                  ),

                                  // Watercolor markery měst
                                  _buildWatercolorMarker(
                                    'Zagreb',
                                    0.3,
                                    0.2,
                                    const Color(0xFFFF6B35),
                                  ),
                                  _buildWatercolorMarker(
                                    'Split',
                                    0.4,
                                    0.6,
                                    const Color(0xFF006994),
                                  ),
                                  _buildWatercolorMarker(
                                    'Dubrovnik',
                                    0.5,
                                    0.8,
                                    const Color(0xFF2E8B8B),
                                  ),
                                  _buildWatercolorMarker(
                                    'Pula',
                                    0.2,
                                    0.3,
                                    const Color(0xFFFF6B35),
                                  ),
                                  _buildWatercolorMarker(
                                    'Zadar',
                                    0.35,
                                    0.45,
                                    const Color(0xFF006994),
                                  ),
                                ],
                              ),
                            ),

                            const SizedBox(height: 20),

                            Text(
                              'Klepněte na marker pro více informací',
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                color: const Color(0xFF666666),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWatercolorMarker(
    String city,
    double left,
    double top,
    Color color,
  ) {
    return Positioned(
      left: left * 200,
      top: top * 300,
      child: GestureDetector(
        onTap: () {
          // Zobrazit info o městě
        },
        child: Container(
          width: 40,
          height: 40,
          child: CustomPaint(
            painter: WatercolorMarkerPainter(color),
            child: Center(
              child: Icon(Icons.location_on, color: Colors.white, size: 20),
            ),
          ),
        ),
      ),
    );
  }
}

// Watercolor painters pro mapu
class WatercolorMapPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Jemné watercolor vlny v pozadí
    final path1 = Path();
    path1.moveTo(0, size.height * 0.3);
    path1.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.1,
      size.width * 0.6,
      size.height * 0.4,
    );
    path1.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.7,
      size.width,
      size.height * 0.2,
    );
    path1.lineTo(size.width, size.height);
    path1.lineTo(0, size.height);
    path1.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.2);
    canvas.drawPath(path1, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorHeaderMapPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor pozadí pro header
    final path = Path();
    path.moveTo(size.width * 0.1, size.height * 0.2);
    path.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.05,
      size.width * 0.9,
      size.height * 0.3,
    );
    path.lineTo(size.width * 0.8, size.height * 0.8);
    path.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.95,
      size.width * 0.2,
      size.height * 0.7,
    );
    path.close();

    paint.color = Colors.white.withValues(alpha: 0.1);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorMapContentPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro obsah mapy
    final path = Path();
    path.moveTo(size.width * 0.05, size.height * 0.1);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.02,
      size.width * 0.7,
      size.height * 0.08,
    );
    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.15,
      size.width * 0.98,
      size.height * 0.9,
    );
    path.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.98,
      size.width * 0.3,
      size.height * 0.92,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.85,
      size.width * 0.05,
      size.height * 0.1,
    );
    path.close();

    paint.color = const Color(0xFF006994).withValues(alpha: 0.05);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorCroatiaMapPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Simulace tvaru Chorvatska watercolor stylem
    final path = Path();
    path.moveTo(size.width * 0.2, size.height * 0.1);
    path.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.05,
      size.width * 0.9,
      size.height * 0.3,
    );
    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.6,
      size.width * 0.7,
      size.height * 0.9,
    );
    path.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.95,
      size.width * 0.1,
      size.height * 0.7,
    );
    path.quadraticBezierTo(
      size.width * 0.05,
      size.height * 0.4,
      size.width * 0.2,
      size.height * 0.1,
    );
    path.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.3);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorMarkerPainter extends CustomPainter {
  final Color color;

  WatercolorMarkerPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor kruh pro marker
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2.5;

    // Organický tvar markeru
    final path = Path();
    for (int i = 0; i < 360; i += 20) {
      final angle = i * 3.14159 / 180;
      final variation = 0.8 + (sin(i * 3.14159 / 90) * 0.2);
      final x = center.dx + (radius * variation) * cos(angle);
      final y = center.dy + (radius * variation) * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    paint.color = color.withValues(alpha: 0.8);
    canvas.drawPath(path, paint);

    // Vnitřní kruh
    paint.color = color.withValues(alpha: 0.6);
    canvas.drawCircle(center, radius * 0.6, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
