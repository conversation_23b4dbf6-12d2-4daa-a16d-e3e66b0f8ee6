// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'traffic.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TrafficIncident _$TrafficIncidentFromJson(Map<String, dynamic> json) =>
    TrafficIncident(
      id: json['id'] as String,
      type: $enumDecode(_$TrafficIncidentTypeEnumMap, json['type']),
      title: json['title'] as String,
      description: json['description'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      severity: $enumDecode(_$TrafficSeverityEnumMap, json['severity']),
      status: $enumDecode(_$TrafficIncidentStatusEnumMap, json['status']),
      reportedAt: DateTime.parse(json['reportedAt'] as String),
      resolvedAt: json['resolvedAt'] == null
          ? null
          : DateTime.parse(json['resolvedAt'] as String),
      reportedBy: json['reportedBy'] as String?,
      affectedRoads: (json['affectedRoads'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      alternativeRoutes: (json['alternativeRoutes'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      estimatedDelay: json['estimatedDelay'] == null
          ? null
          : Duration(microseconds: (json['estimatedDelay'] as num).toInt()),
      images: (json['images'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      upvotes: (json['upvotes'] as num?)?.toInt() ?? 0,
      downvotes: (json['downvotes'] as num?)?.toInt() ?? 0,
      isVerified: json['isVerified'] as bool? ?? false,
    );

Map<String, dynamic> _$TrafficIncidentToJson(TrafficIncident instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$TrafficIncidentTypeEnumMap[instance.type]!,
      'title': instance.title,
      'description': instance.description,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'severity': _$TrafficSeverityEnumMap[instance.severity]!,
      'status': _$TrafficIncidentStatusEnumMap[instance.status]!,
      'reportedAt': instance.reportedAt.toIso8601String(),
      'resolvedAt': instance.resolvedAt?.toIso8601String(),
      'reportedBy': instance.reportedBy,
      'affectedRoads': instance.affectedRoads,
      'alternativeRoutes': instance.alternativeRoutes,
      'estimatedDelay': instance.estimatedDelay?.inMicroseconds,
      'images': instance.images,
      'upvotes': instance.upvotes,
      'downvotes': instance.downvotes,
      'isVerified': instance.isVerified,
    };

const _$TrafficIncidentTypeEnumMap = {
  TrafficIncidentType.accident: 'accident',
  TrafficIncidentType.roadwork: 'roadwork',
  TrafficIncidentType.closure: 'closure',
  TrafficIncidentType.congestion: 'congestion',
  TrafficIncidentType.weather: 'weather',
  TrafficIncidentType.event: 'event',
  TrafficIncidentType.breakdown: 'breakdown',
  TrafficIncidentType.other: 'other',
};

const _$TrafficSeverityEnumMap = {
  TrafficSeverity.low: 'low',
  TrafficSeverity.medium: 'medium',
  TrafficSeverity.high: 'high',
  TrafficSeverity.critical: 'critical',
};

const _$TrafficIncidentStatusEnumMap = {
  TrafficIncidentStatus.active: 'active',
  TrafficIncidentStatus.resolved: 'resolved',
  TrafficIncidentStatus.investigating: 'investigating',
  TrafficIncidentStatus.clearing: 'clearing',
};

TrafficCondition _$TrafficConditionFromJson(Map<String, dynamic> json) =>
    TrafficCondition(
      roadId: json['roadId'] as String,
      roadName: json['roadName'] as String,
      flow: $enumDecode(_$TrafficFlowEnumMap, json['flow']),
      averageSpeed: (json['averageSpeed'] as num).toDouble(),
      freeFlowSpeed: (json['freeFlowSpeed'] as num).toDouble(),
      travelTime: (json['travelTime'] as num).toInt(),
      freeFlowTravelTime: (json['freeFlowTravelTime'] as num).toInt(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      segments: (json['segments'] as List<dynamic>?)
              ?.map((e) => TrafficSegment.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$TrafficConditionToJson(TrafficCondition instance) =>
    <String, dynamic>{
      'roadId': instance.roadId,
      'roadName': instance.roadName,
      'flow': _$TrafficFlowEnumMap[instance.flow]!,
      'averageSpeed': instance.averageSpeed,
      'freeFlowSpeed': instance.freeFlowSpeed,
      'travelTime': instance.travelTime,
      'freeFlowTravelTime': instance.freeFlowTravelTime,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'segments': instance.segments,
    };

const _$TrafficFlowEnumMap = {
  TrafficFlow.free: 'free',
  TrafficFlow.light: 'light',
  TrafficFlow.moderate: 'moderate',
  TrafficFlow.heavy: 'heavy',
  TrafficFlow.congested: 'congested',
};

TrafficSegment _$TrafficSegmentFromJson(Map<String, dynamic> json) =>
    TrafficSegment(
      id: json['id'] as String,
      startLatitude: (json['startLatitude'] as num).toDouble(),
      startLongitude: (json['startLongitude'] as num).toDouble(),
      endLatitude: (json['endLatitude'] as num).toDouble(),
      endLongitude: (json['endLongitude'] as num).toDouble(),
      flow: $enumDecode(_$TrafficFlowEnumMap, json['flow']),
      speed: (json['speed'] as num).toDouble(),
      length: (json['length'] as num).toInt(),
    );

Map<String, dynamic> _$TrafficSegmentToJson(TrafficSegment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'startLatitude': instance.startLatitude,
      'startLongitude': instance.startLongitude,
      'endLatitude': instance.endLatitude,
      'endLongitude': instance.endLongitude,
      'flow': _$TrafficFlowEnumMap[instance.flow]!,
      'speed': instance.speed,
      'length': instance.length,
    };

TrafficCamera _$TrafficCameraFromJson(Map<String, dynamic> json) =>
    TrafficCamera(
      id: json['id'] as String,
      name: json['name'] as String,
      location: json['location'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      imageUrl: json['imageUrl'] as String,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      isActive: json['isActive'] as bool? ?? true,
      description: json['description'] as String?,
      type: $enumDecode(_$CameraTypeEnumMap, json['type']),
    );

Map<String, dynamic> _$TrafficCameraToJson(TrafficCamera instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'location': instance.location,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'imageUrl': instance.imageUrl,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'isActive': instance.isActive,
      'description': instance.description,
      'type': _$CameraTypeEnumMap[instance.type]!,
    };

const _$CameraTypeEnumMap = {
  CameraType.traffic: 'traffic',
  CameraType.weather: 'weather',
  CameraType.security: 'security',
  CameraType.toll: 'toll',
};

RoadClosure _$RoadClosureFromJson(Map<String, dynamic> json) => RoadClosure(
      id: json['id'] as String,
      roadName: json['roadName'] as String,
      reason: json['reason'] as String,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: json['endTime'] == null
          ? null
          : DateTime.parse(json['endTime'] as String),
      isPlanned: json['isPlanned'] as bool? ?? false,
      affectedDirections: (json['affectedDirections'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      alternativeRoutes: (json['alternativeRoutes'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      type: $enumDecode(_$ClosureTypeEnumMap, json['type']),
      contactInfo: json['contactInfo'] as String?,
    );

Map<String, dynamic> _$RoadClosureToJson(RoadClosure instance) =>
    <String, dynamic>{
      'id': instance.id,
      'roadName': instance.roadName,
      'reason': instance.reason,
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime?.toIso8601String(),
      'isPlanned': instance.isPlanned,
      'affectedDirections': instance.affectedDirections,
      'alternativeRoutes': instance.alternativeRoutes,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'type': _$ClosureTypeEnumMap[instance.type]!,
      'contactInfo': instance.contactInfo,
    };

const _$ClosureTypeEnumMap = {
  ClosureType.full: 'full',
  ClosureType.partial: 'partial',
  ClosureType.lane: 'lane',
  ClosureType.temporary: 'temporary',
};

TrafficAlert _$TrafficAlertFromJson(Map<String, dynamic> json) => TrafficAlert(
      id: json['id'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      type: $enumDecode(_$TrafficAlertTypeEnumMap, json['type']),
      severity: $enumDecode(_$TrafficSeverityEnumMap, json['severity']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
      affectedAreas: (json['affectedAreas'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      isActive: json['isActive'] as bool? ?? true,
    );

Map<String, dynamic> _$TrafficAlertToJson(TrafficAlert instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'message': instance.message,
      'type': _$TrafficAlertTypeEnumMap[instance.type]!,
      'severity': _$TrafficSeverityEnumMap[instance.severity]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'expiresAt': instance.expiresAt?.toIso8601String(),
      'affectedAreas': instance.affectedAreas,
      'isActive': instance.isActive,
    };

const _$TrafficAlertTypeEnumMap = {
  TrafficAlertType.general: 'general',
  TrafficAlertType.weather: 'weather',
  TrafficAlertType.emergency: 'emergency',
  TrafficAlertType.event: 'event',
  TrafficAlertType.maintenance: 'maintenance',
};

TrafficPrediction _$TrafficPredictionFromJson(Map<String, dynamic> json) =>
    TrafficPrediction(
      id: json['id'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      targetTime: DateTime.parse(json['targetTime'] as String),
      radiusKm: (json['radiusKm'] as num).toDouble(),
      predictedFlow: $enumDecode(_$TrafficFlowEnumMap, json['predictedFlow']),
      averageSpeed: (json['averageSpeed'] as num).toDouble(),
      confidenceLevel: (json['confidenceLevel'] as num).toDouble(),
      factors: (json['factors'] as List<dynamic>)
          .map((e) => PredictionFactor.fromJson(e as Map<String, dynamic>))
          .toList(),
      estimatedDelay:
          Duration(microseconds: (json['estimatedDelay'] as num).toInt()),
      affectedRoads: (json['affectedRoads'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      generatedAt: DateTime.parse(json['generatedAt'] as String),
    );

Map<String, dynamic> _$TrafficPredictionToJson(TrafficPrediction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'targetTime': instance.targetTime.toIso8601String(),
      'radiusKm': instance.radiusKm,
      'predictedFlow': _$TrafficFlowEnumMap[instance.predictedFlow]!,
      'averageSpeed': instance.averageSpeed,
      'confidenceLevel': instance.confidenceLevel,
      'factors': instance.factors,
      'estimatedDelay': instance.estimatedDelay.inMicroseconds,
      'affectedRoads': instance.affectedRoads,
      'generatedAt': instance.generatedAt.toIso8601String(),
    };

AlternativeRoute _$AlternativeRouteFromJson(Map<String, dynamic> json) =>
    AlternativeRoute(
      id: json['id'] as String,
      name: json['name'] as String,
      path: (json['path'] as List<dynamic>)
          .map((e) => LatLng.fromJson(e as Map<String, dynamic>))
          .toList(),
      estimatedTime:
          Duration(microseconds: (json['estimatedTime'] as num).toInt()),
      distance: (json['distance'] as num).toDouble(),
      expectedFlow: $enumDecode(_$TrafficFlowEnumMap, json['expectedFlow']),
      avoidedIncidents: (json['avoidedIncidents'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      fuelSavings: (json['fuelSavings'] as num).toDouble(),
      timeSavings: (json['timeSavings'] as num).toDouble(),
      quality: $enumDecode(_$RouteQualityEnumMap, json['quality']),
    );

Map<String, dynamic> _$AlternativeRouteToJson(AlternativeRoute instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'path': instance.path,
      'estimatedTime': instance.estimatedTime.inMicroseconds,
      'distance': instance.distance,
      'expectedFlow': _$TrafficFlowEnumMap[instance.expectedFlow]!,
      'avoidedIncidents': instance.avoidedIncidents,
      'fuelSavings': instance.fuelSavings,
      'timeSavings': instance.timeSavings,
      'quality': _$RouteQualityEnumMap[instance.quality]!,
    };

const _$RouteQualityEnumMap = {
  RouteQuality.excellent: 'excellent',
  RouteQuality.good: 'good',
  RouteQuality.fair: 'fair',
  RouteQuality.poor: 'poor',
};

TrafficFlowAnalysis _$TrafficFlowAnalysisFromJson(Map<String, dynamic> json) =>
    TrafficFlowAnalysis(
      roadId: json['roadId'] as String,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: DateTime.parse(json['endTime'] as String),
      averageSpeed: (json['averageSpeed'] as num).toDouble(),
      peakSpeed: (json['peakSpeed'] as num).toDouble(),
      minSpeed: (json['minSpeed'] as num).toDouble(),
      dataPoints: (json['dataPoints'] as List<dynamic>)
          .map((e) => FlowDataPoint.fromJson(e as Map<String, dynamic>))
          .toList(),
      patterns: (json['patterns'] as List<dynamic>)
          .map((e) => TrafficPattern.fromJson(e as Map<String, dynamic>))
          .toList(),
      nextHourPrediction: TrafficPrediction.fromJson(
          json['nextHourPrediction'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TrafficFlowAnalysisToJson(
        TrafficFlowAnalysis instance) =>
    <String, dynamic>{
      'roadId': instance.roadId,
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime.toIso8601String(),
      'averageSpeed': instance.averageSpeed,
      'peakSpeed': instance.peakSpeed,
      'minSpeed': instance.minSpeed,
      'dataPoints': instance.dataPoints,
      'patterns': instance.patterns,
      'nextHourPrediction': instance.nextHourPrediction,
    };

PersonalizedAlert _$PersonalizedAlertFromJson(Map<String, dynamic> json) =>
    PersonalizedAlert(
      id: json['id'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      type: $enumDecode(_$TrafficAlertTypeEnumMap, json['type']),
      severity: $enumDecode(_$TrafficSeverityEnumMap, json['severity']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
      affectedRoutes: (json['affectedRoutes'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      relevanceScore: (json['relevanceScore'] as num).toDouble(),
      actionData: json['actionData'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$PersonalizedAlertToJson(PersonalizedAlert instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'message': instance.message,
      'type': _$TrafficAlertTypeEnumMap[instance.type]!,
      'severity': _$TrafficSeverityEnumMap[instance.severity]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'expiresAt': instance.expiresAt?.toIso8601String(),
      'affectedRoutes': instance.affectedRoutes,
      'relevanceScore': instance.relevanceScore,
      'actionData': instance.actionData,
    };

TrafficLightOptimization _$TrafficLightOptimizationFromJson(
        Map<String, dynamic> json) =>
    TrafficLightOptimization(
      intersectionId: json['intersectionId'] as String,
      optimizedAt: DateTime.parse(json['optimizedAt'] as String),
      timeWindow: Duration(microseconds: (json['timeWindow'] as num).toInt()),
      currentPhases: (json['currentPhases'] as List<dynamic>)
          .map((e) => LightPhase.fromJson(e as Map<String, dynamic>))
          .toList(),
      optimizedPhases: (json['optimizedPhases'] as List<dynamic>)
          .map((e) => LightPhase.fromJson(e as Map<String, dynamic>))
          .toList(),
      efficiencyGain: (json['efficiencyGain'] as num).toDouble(),
      waitTimeReduction: (json['waitTimeReduction'] as num).toDouble(),
      metrics: json['metrics'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$TrafficLightOptimizationToJson(
        TrafficLightOptimization instance) =>
    <String, dynamic>{
      'intersectionId': instance.intersectionId,
      'optimizedAt': instance.optimizedAt.toIso8601String(),
      'timeWindow': instance.timeWindow.inMicroseconds,
      'currentPhases': instance.currentPhases,
      'optimizedPhases': instance.optimizedPhases,
      'efficiencyGain': instance.efficiencyGain,
      'waitTimeReduction': instance.waitTimeReduction,
      'metrics': instance.metrics,
    };

LatLng _$LatLngFromJson(Map<String, dynamic> json) => LatLng(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
    );

Map<String, dynamic> _$LatLngToJson(LatLng instance) => <String, dynamic>{
      'latitude': instance.latitude,
      'longitude': instance.longitude,
    };

PredictionFactor _$PredictionFactorFromJson(Map<String, dynamic> json) =>
    PredictionFactor(
      name: json['name'] as String,
      impact: (json['impact'] as num).toDouble(),
      description: json['description'] as String,
    );

Map<String, dynamic> _$PredictionFactorToJson(PredictionFactor instance) =>
    <String, dynamic>{
      'name': instance.name,
      'impact': instance.impact,
      'description': instance.description,
    };

FlowDataPoint _$FlowDataPointFromJson(Map<String, dynamic> json) =>
    FlowDataPoint(
      timestamp: DateTime.parse(json['timestamp'] as String),
      speed: (json['speed'] as num).toDouble(),
      vehicleCount: (json['vehicleCount'] as num).toInt(),
      flow: $enumDecode(_$TrafficFlowEnumMap, json['flow']),
    );

Map<String, dynamic> _$FlowDataPointToJson(FlowDataPoint instance) =>
    <String, dynamic>{
      'timestamp': instance.timestamp.toIso8601String(),
      'speed': instance.speed,
      'vehicleCount': instance.vehicleCount,
      'flow': _$TrafficFlowEnumMap[instance.flow]!,
    };

TrafficPattern _$TrafficPatternFromJson(Map<String, dynamic> json) =>
    TrafficPattern(
      name: json['name'] as String,
      description: json['description'] as String,
      confidence: (json['confidence'] as num).toDouble(),
      duration: Duration(microseconds: (json['duration'] as num).toInt()),
      triggers:
          (json['triggers'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$TrafficPatternToJson(TrafficPattern instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'confidence': instance.confidence,
      'duration': instance.duration.inMicroseconds,
      'triggers': instance.triggers,
    };

LightPhase _$LightPhaseFromJson(Map<String, dynamic> json) => LightPhase(
      direction: json['direction'] as String,
      greenTime: Duration(microseconds: (json['greenTime'] as num).toInt()),
      redTime: Duration(microseconds: (json['redTime'] as num).toInt()),
      yellowTime: Duration(microseconds: (json['yellowTime'] as num).toInt()),
      priority: (json['priority'] as num).toInt(),
    );

Map<String, dynamic> _$LightPhaseToJson(LightPhase instance) =>
    <String, dynamic>{
      'direction': instance.direction,
      'greenTime': instance.greenTime.inMicroseconds,
      'redTime': instance.redTime.inMicroseconds,
      'yellowTime': instance.yellowTime.inMicroseconds,
      'priority': instance.priority,
    };
