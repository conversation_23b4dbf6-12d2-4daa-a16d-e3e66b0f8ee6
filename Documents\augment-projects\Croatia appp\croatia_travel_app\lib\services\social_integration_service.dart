import 'dart:async';
import 'dart:convert';
// import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:share_plus/share_plus.dart';
// import 'package:url_launcher/url_launcher.dart';

/// 📱 SOCIAL INTEGRATION SERVICE - Kompletní social media integrace
class SocialIntegrationService {
  static final SocialIntegrationService _instance =
      SocialIntegrationService._internal();
  factory SocialIntegrationService() => _instance;
  SocialIntegrationService._internal();

  bool _isInitialized = false;
  final List<SocialPost> _scheduledPosts = [];
  final List<SocialAccount> _connectedAccounts = [];
  final Map<String, SocialMetrics> _socialMetrics = {};
  final StreamController<SocialEvent> _eventController =
      StreamController.broadcast();

  /// Stream social událostí
  Stream<SocialEvent> get socialEvents => _eventController.stream;

  /// Inicializace služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('📱 Inicializuji Social Integration Service...');

      await _loadSocialData();
      await _setupAutoPosting();

      _isInitialized = true;
      debugPrint('✅ Social Integration Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Social Integration: $e');
      rethrow;
    }
  }

  /// Automatické sdílení na Instagram Stories
  Future<bool> shareToInstagramStory({
    required String imageUrl,
    required String caption,
    required List<String> hashtags,
    Map<String, dynamic>? stickers,
  }) async {
    try {
      debugPrint('📸 Sdílím na Instagram Stories...');

      // Vytvoření Instagram Story content
      final storyContent = InstagramStoryContent(
        imageUrl: imageUrl,
        caption: caption,
        hashtags: hashtags,
        stickers: stickers ?? {},
        brandingElements: {
          'watermark': 'Croatia Travel App',
          'logo': 'assets/images/logo_watercolor.png',
          'color_scheme': 'adriatic_blue',
        },
      );

      // Generování story image s branding
      final brandedImageUrl = await _generateBrandedStoryImage(storyContent);

      // Sdílení přes system share
      await Share.shareXFiles(
        [XFile(brandedImageUrl)],
        text:
            '${caption}\n\n${hashtags.join(' ')}\n\nCreated with Croatia Travel App 🇭🇷',
      );

      // Tracking
      await _trackSocialShare(SocialPlatform.instagram, 'story', caption);

      _eventController.add(
        SocialEvent(
          type: SocialEventType.storyShared,
          platform: SocialPlatform.instagram,
          contentType: 'story',
          timestamp: DateTime.now(),
        ),
      );

      return true;
    } catch (e) {
      debugPrint('❌ Chyba při sdílení na Instagram: $e');
      return false;
    }
  }

  /// Automatické sdílení na TikTok
  Future<bool> shareToTikTok({
    required List<String> photoUrls,
    required String caption,
    required List<String> hashtags,
    String? musicUrl,
    TikTokTemplate template = TikTokTemplate.travelSlideshow,
  }) async {
    try {
      debugPrint('🎵 Sdílím na TikTok...');

      // Vytvoření TikTok content
      final tiktokContent = TikTokContent(
        photoUrls: photoUrls,
        caption: caption,
        hashtags: hashtags,
        musicUrl: musicUrl,
        template: template,
        effects: ['watercolor_transition', 'croatia_filter'],
      );

      // Generování TikTok video
      final videoUrl = await _generateTikTokVideo(tiktokContent);

      // Sdílení
      final shareText =
          '${caption}\n\n${hashtags.join(' ')}\n\nMade with Croatia Travel App 🇭🇷';
      await Share.shareXFiles([XFile(videoUrl)], text: shareText);

      await _trackSocialShare(SocialPlatform.tiktok, 'video', caption);

      _eventController.add(
        SocialEvent(
          type: SocialEventType.videoShared,
          platform: SocialPlatform.tiktok,
          contentType: 'video',
          timestamp: DateTime.now(),
        ),
      );

      return true;
    } catch (e) {
      debugPrint('❌ Chyba při sdílení na TikTok: $e');
      return false;
    }
  }

  /// Automatické sdílení na Facebook
  Future<bool> shareToFacebook({
    required String text,
    String? imageUrl,
    String? linkUrl,
    List<String>? hashtags,
  }) async {
    try {
      debugPrint('📘 Sdílím na Facebook...');

      final shareContent =
          '${text}\n\n${hashtags?.join(' ') ?? ''}\n\nShared via Croatia Travel App 🇭🇷';

      if (imageUrl != null) {
        await Share.shareXFiles([XFile(imageUrl)], text: shareContent);
      } else {
        await Share.share(shareContent);
      }

      await _trackSocialShare(SocialPlatform.facebook, 'post', text);

      _eventController.add(
        SocialEvent(
          type: SocialEventType.postShared,
          platform: SocialPlatform.facebook,
          contentType: 'post',
          timestamp: DateTime.now(),
        ),
      );

      return true;
    } catch (e) {
      debugPrint('❌ Chyba při sdílení na Facebook: $e');
      return false;
    }
  }

  /// Automatické sdílení na Twitter/X
  Future<bool> shareToTwitter({
    required String text,
    String? imageUrl,
    List<String>? hashtags,
  }) async {
    try {
      debugPrint('🐦 Sdílím na Twitter/X...');

      // Twitter má limit 280 znaků
      final twitterText = _formatTwitterText(text, hashtags);

      if (imageUrl != null) {
        await Share.shareXFiles([XFile(imageUrl)], text: twitterText);
      } else {
        await Share.share(twitterText);
      }

      await _trackSocialShare(SocialPlatform.twitter, 'tweet', text);

      _eventController.add(
        SocialEvent(
          type: SocialEventType.postShared,
          platform: SocialPlatform.twitter,
          contentType: 'tweet',
          timestamp: DateTime.now(),
        ),
      );

      return true;
    } catch (e) {
      debugPrint('❌ Chyba při sdílení na Twitter: $e');
      return false;
    }
  }

  /// Automatické sdílení na Pinterest
  Future<bool> shareToPinterest({
    required String imageUrl,
    required String title,
    required String description,
    String? boardName,
  }) async {
    try {
      debugPrint('📌 Sdílím na Pinterest...');

      final pinterestContent =
          '${title}\n\n${description}\n\nDiscover more at Croatia Travel App 🇭🇷';

      await Share.shareXFiles([XFile(imageUrl)], text: pinterestContent);

      await _trackSocialShare(SocialPlatform.pinterest, 'pin', title);

      _eventController.add(
        SocialEvent(
          type: SocialEventType.postShared,
          platform: SocialPlatform.pinterest,
          contentType: 'pin',
          timestamp: DateTime.now(),
        ),
      );

      return true;
    } catch (e) {
      debugPrint('❌ Chyba při sdílení na Pinterest: $e');
      return false;
    }
  }

  /// Naplánování automatického postu
  Future<bool> schedulePost({
    required SocialPlatform platform,
    required String content,
    required DateTime scheduledTime,
    String? imageUrl,
    List<String>? hashtags,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final post = SocialPost(
        id: 'post_${DateTime.now().millisecondsSinceEpoch}',
        platform: platform,
        content: content,
        imageUrl: imageUrl,
        hashtags: hashtags ?? [],
        scheduledTime: scheduledTime,
        status: PostStatus.scheduled,
        metadata: metadata ?? {},
        createdAt: DateTime.now(),
      );

      _scheduledPosts.add(post);
      await _saveSocialData();

      _eventController.add(
        SocialEvent(
          type: SocialEventType.postScheduled,
          platform: platform,
          contentType: 'scheduled_post',
          timestamp: DateTime.now(),
        ),
      );

      return true;
    } catch (e) {
      debugPrint('❌ Chyba při plánování postu: $e');
      return false;
    }
  }

  /// Automatické generování social content z deníkových záznamů
  Future<List<SocialContentSuggestion>> generateContentSuggestions({
    required List<Map<String, dynamic>> diaryEntries,
    int maxSuggestions = 5,
  }) async {
    try {
      debugPrint('💡 Generuji social content návrhy...');

      final suggestions = <SocialContentSuggestion>[];

      for (final entry in diaryEntries.take(maxSuggestions)) {
        // Instagram Story suggestion
        suggestions.add(
          SocialContentSuggestion(
            id: 'ig_story_${entry['id']}',
            platform: SocialPlatform.instagram,
            contentType: 'story',
            title: 'Instagram Story',
            description: 'Sdílejte svou vzpomínku jako Instagram Story',
            content: entry['title'] ?? '',
            imageUrl: entry['photos']?.first,
            hashtags: _generateHashtags(
              entry['location'],
              SocialPlatform.instagram,
            ),
            estimatedReach: _estimateReach(SocialPlatform.instagram, 'story'),
            bestTimeToPost: _getBestPostingTime(SocialPlatform.instagram),
          ),
        );

        // TikTok suggestion
        if (entry['photos'] != null && (entry['photos'] as List).length >= 3) {
          suggestions.add(
            SocialContentSuggestion(
              id: 'tiktok_${entry['id']}',
              platform: SocialPlatform.tiktok,
              contentType: 'video',
              title: 'TikTok Slideshow',
              description: 'Vytvořte TikTok slideshow z vašich fotek',
              content: 'Exploring ${entry['location']} 🇭🇷',
              imageUrl: entry['photos']?.first,
              hashtags: _generateHashtags(
                entry['location'],
                SocialPlatform.tiktok,
              ),
              estimatedReach: _estimateReach(SocialPlatform.tiktok, 'video'),
              bestTimeToPost: _getBestPostingTime(SocialPlatform.tiktok),
            ),
          );
        }
      }

      return suggestions;
    } catch (e) {
      debugPrint('❌ Chyba při generování návrhů: $e');
      return [];
    }
  }

  /// Získání social media analytics
  Future<SocialAnalytics> getSocialAnalytics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final start =
        startDate ?? DateTime.now().subtract(const Duration(days: 30));
    final end = endDate ?? DateTime.now();

    final analytics = SocialAnalytics(
      totalShares: _calculateTotalShares(start, end),
      platformBreakdown: _calculatePlatformBreakdown(start, end),
      topPerformingContent: _getTopPerformingContent(start, end),
      engagementRate: _calculateEngagementRate(start, end),
      reachEstimate: _calculateReachEstimate(start, end),
      viralCoefficient: _calculateViralCoefficient(start, end),
      bestPostingTimes: _getBestPostingTimes(),
      hashtagPerformance: _getHashtagPerformance(start, end),
    );

    return analytics;
  }

  /// Automatické hashtag suggestions
  List<String> generateHashtagSuggestions({
    required String location,
    required SocialPlatform platform,
    String? contentType,
    List<String>? customKeywords,
  }) {
    final baseHashtags = ['#Croatia', '#TravelDiary', '#Memories'];
    final locationHashtags = ['#$location', '#${location}Croatia'];
    final platformSpecific = _getPlatformSpecificHashtags(platform);
    final trending = _getTrendingHashtags(platform);

    final allHashtags = [
      ...baseHashtags,
      ...locationHashtags,
      ...platformSpecific,
      ...trending,
      if (customKeywords != null) ...customKeywords.map((k) => '#$k'),
    ];

    // Limit podle platformy
    final limit = _getHashtagLimit(platform);
    return allHashtags.take(limit).toList();
  }

  /// Cross-platform sharing
  Future<Map<SocialPlatform, bool>> shareToMultiplePlatforms({
    required String content,
    required List<SocialPlatform> platforms,
    String? imageUrl,
    List<String>? hashtags,
  }) async {
    final results = <SocialPlatform, bool>{};

    for (final platform in platforms) {
      switch (platform) {
        case SocialPlatform.instagram:
          results[platform] = await shareToInstagramStory(
            imageUrl: imageUrl ?? '',
            caption: content,
            hashtags: hashtags ?? [],
          );
          break;
        case SocialPlatform.facebook:
          results[platform] = await shareToFacebook(
            text: content,
            imageUrl: imageUrl,
            hashtags: hashtags,
          );
          break;
        case SocialPlatform.twitter:
          results[platform] = await shareToTwitter(
            text: content,
            imageUrl: imageUrl,
            hashtags: hashtags,
          );
          break;
        case SocialPlatform.pinterest:
          if (imageUrl != null) {
            results[platform] = await shareToPinterest(
              imageUrl: imageUrl,
              title: content,
              description: content,
            );
          }
          break;
        case SocialPlatform.tiktok:
          // TikTok requires video content
          results[platform] = false;
          break;
      }

      // Delay mezi posty
      await Future.delayed(const Duration(seconds: 2));
    }

    return results;
  }

  /// Pomocné metody
  Future<String> _generateBrandedStoryImage(
    InstagramStoryContent content,
  ) async {
    // Generování branded Instagram Story
    return 'assets/images/branded_story.png';
  }

  Future<String> _generateTikTokVideo(TikTokContent content) async {
    // Generování TikTok video
    return 'assets/videos/tiktok_video.mp4';
  }

  String _formatTwitterText(String text, List<String>? hashtags) {
    final maxLength = 250; // Necháme prostor pro hashtags
    var twitterText = text.length > maxLength
        ? '${text.substring(0, maxLength)}...'
        : text;

    if (hashtags != null && hashtags.isNotEmpty) {
      final hashtagText = hashtags.take(3).join(' ');
      twitterText += '\n\n$hashtagText';
    }

    twitterText += '\n\nvia @CroatiaTravelApp';
    return twitterText;
  }

  List<String> _generateHashtags(String? location, SocialPlatform platform) {
    final base = ['#Croatia', '#TravelDiary'];
    if (location != null) {
      base.addAll(['#$location', '#${location}Croatia']);
    }

    switch (platform) {
      case SocialPlatform.instagram:
        base.addAll(['#Instatravel', '#Wanderlust', '#TravelGram']);
        break;
      case SocialPlatform.tiktok:
        base.addAll(['#Travel', '#Explore', '#Fyp']);
        break;
      case SocialPlatform.twitter:
        base.addAll(['#Travel', '#Croatia2024']);
        break;
      default:
        break;
    }

    return base;
  }

  List<String> _getPlatformSpecificHashtags(SocialPlatform platform) {
    switch (platform) {
      case SocialPlatform.instagram:
        return ['#Instatravel', '#TravelGram', '#Wanderlust'];
      case SocialPlatform.tiktok:
        return ['#Fyp', '#Viral', '#Travel'];
      case SocialPlatform.twitter:
        return ['#TravelTwitter', '#Croatia2024'];
      case SocialPlatform.pinterest:
        return ['#TravelInspiration', '#TravelPlanning'];
      case SocialPlatform.facebook:
        return ['#TravelCommunity', '#CroatiaTravel'];
    }
  }

  List<String> _getTrendingHashtags(SocialPlatform platform) {
    // V produkci by se načítaly skutečné trending hashtags
    return ['#TrendingNow', '#Viral2024'];
  }

  int _getHashtagLimit(SocialPlatform platform) {
    switch (platform) {
      case SocialPlatform.instagram:
        return 30;
      case SocialPlatform.tiktok:
        return 10;
      case SocialPlatform.twitter:
        return 5;
      default:
        return 10;
    }
  }

  DateTime _getBestPostingTime(SocialPlatform platform) {
    // Optimální časy pro posting podle platformy
    final now = DateTime.now();
    switch (platform) {
      case SocialPlatform.instagram:
        return DateTime(now.year, now.month, now.day, 18, 0); // 18:00
      case SocialPlatform.tiktok:
        return DateTime(now.year, now.month, now.day, 20, 0); // 20:00
      case SocialPlatform.facebook:
        return DateTime(now.year, now.month, now.day, 15, 0); // 15:00
      default:
        return DateTime(now.year, now.month, now.day, 12, 0); // 12:00
    }
  }

  int _estimateReach(SocialPlatform platform, String contentType) {
    // Odhad dosahu podle platformy a typu obsahu
    switch (platform) {
      case SocialPlatform.instagram:
        return contentType == 'story' ? 500 : 1000;
      case SocialPlatform.tiktok:
        return 2000;
      case SocialPlatform.facebook:
        return 300;
      default:
        return 500;
    }
  }

  Future<void> _trackSocialShare(
    SocialPlatform platform,
    String contentType,
    String content,
  ) async {
    final key = '${platform.name}_$contentType';
    final current =
        _socialMetrics[key] ??
        SocialMetrics(
          platform: platform,
          contentType: contentType,
          totalShares: 0,
          totalReach: 0,
          totalEngagement: 0,
        );

    _socialMetrics[key] = SocialMetrics(
      platform: platform,
      contentType: contentType,
      totalShares: current.totalShares + 1,
      totalReach: current.totalReach + _estimateReach(platform, contentType),
      totalEngagement: current.totalEngagement + 10, // Placeholder
    );
  }

  // Analytics helper methods
  int _calculateTotalShares(DateTime start, DateTime end) => 150; // Placeholder
  Map<SocialPlatform, int> _calculatePlatformBreakdown(
    DateTime start,
    DateTime end,
  ) => {
    SocialPlatform.instagram: 60,
    SocialPlatform.tiktok: 40,
    SocialPlatform.facebook: 30,
    SocialPlatform.twitter: 20,
  };
  List<String> _getTopPerformingContent(DateTime start, DateTime end) => [
    'Post 1',
    'Post 2',
  ];
  double _calculateEngagementRate(DateTime start, DateTime end) =>
      0.045; // 4.5%
  int _calculateReachEstimate(DateTime start, DateTime end) => 15000;
  double _calculateViralCoefficient(DateTime start, DateTime end) => 1.2;
  Map<SocialPlatform, DateTime> _getBestPostingTimes() => {
    SocialPlatform.instagram: DateTime.now().copyWith(hour: 18),
    SocialPlatform.tiktok: DateTime.now().copyWith(hour: 20),
  };
  Map<String, int> _getHashtagPerformance(DateTime start, DateTime end) => {
    '#Croatia': 1000,
    '#TravelDiary': 800,
  };

  Future<void> _setupAutoPosting() async {
    // Setup automatického postování
  }

  Future<void> _loadSocialData() async {
    // Load social data
  }

  Future<void> _saveSocialData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'scheduled_posts',
        jsonEncode(_scheduledPosts.map((p) => p.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání social dat: $e');
    }
  }

  @override
  void dispose() {
    _eventController.close();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  List<SocialPost> get scheduledPosts => List.unmodifiable(_scheduledPosts);
  Map<String, SocialMetrics> get socialMetrics =>
      Map.unmodifiable(_socialMetrics);
}

/// Modely pro social integration
class InstagramStoryContent {
  final String imageUrl;
  final String caption;
  final List<String> hashtags;
  final Map<String, dynamic> stickers;
  final Map<String, dynamic> brandingElements;

  InstagramStoryContent({
    required this.imageUrl,
    required this.caption,
    required this.hashtags,
    required this.stickers,
    required this.brandingElements,
  });
}

class TikTokContent {
  final List<String> photoUrls;
  final String caption;
  final List<String> hashtags;
  final String? musicUrl;
  final TikTokTemplate template;
  final List<String> effects;

  TikTokContent({
    required this.photoUrls,
    required this.caption,
    required this.hashtags,
    this.musicUrl,
    required this.template,
    required this.effects,
  });
}

class SocialPost {
  final String id;
  final SocialPlatform platform;
  final String content;
  final String? imageUrl;
  final List<String> hashtags;
  final DateTime scheduledTime;
  final PostStatus status;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;

  SocialPost({
    required this.id,
    required this.platform,
    required this.content,
    this.imageUrl,
    required this.hashtags,
    required this.scheduledTime,
    required this.status,
    required this.metadata,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'platform': platform.name,
    'content': content,
    'imageUrl': imageUrl,
    'hashtags': hashtags,
    'scheduledTime': scheduledTime.toIso8601String(),
    'status': status.name,
    'metadata': metadata,
    'createdAt': createdAt.toIso8601String(),
  };
}

class SocialContentSuggestion {
  final String id;
  final SocialPlatform platform;
  final String contentType;
  final String title;
  final String description;
  final String content;
  final String? imageUrl;
  final List<String> hashtags;
  final int estimatedReach;
  final DateTime bestTimeToPost;

  SocialContentSuggestion({
    required this.id,
    required this.platform,
    required this.contentType,
    required this.title,
    required this.description,
    required this.content,
    this.imageUrl,
    required this.hashtags,
    required this.estimatedReach,
    required this.bestTimeToPost,
  });
}

class SocialAnalytics {
  final int totalShares;
  final Map<SocialPlatform, int> platformBreakdown;
  final List<String> topPerformingContent;
  final double engagementRate;
  final int reachEstimate;
  final double viralCoefficient;
  final Map<SocialPlatform, DateTime> bestPostingTimes;
  final Map<String, int> hashtagPerformance;

  SocialAnalytics({
    required this.totalShares,
    required this.platformBreakdown,
    required this.topPerformingContent,
    required this.engagementRate,
    required this.reachEstimate,
    required this.viralCoefficient,
    required this.bestPostingTimes,
    required this.hashtagPerformance,
  });
}

class SocialMetrics {
  final SocialPlatform platform;
  final String contentType;
  final int totalShares;
  final int totalReach;
  final int totalEngagement;

  SocialMetrics({
    required this.platform,
    required this.contentType,
    required this.totalShares,
    required this.totalReach,
    required this.totalEngagement,
  });
}

class SocialAccount {
  final String id;
  final SocialPlatform platform;
  final String username;
  final bool isConnected;
  final DateTime? connectedAt;

  SocialAccount({
    required this.id,
    required this.platform,
    required this.username,
    required this.isConnected,
    this.connectedAt,
  });
}

class SocialEvent {
  final SocialEventType type;
  final SocialPlatform platform;
  final String contentType;
  final DateTime timestamp;

  SocialEvent({
    required this.type,
    required this.platform,
    required this.contentType,
    required this.timestamp,
  });
}

enum SocialPlatform { instagram, tiktok, facebook, twitter, pinterest }

enum TikTokTemplate { travelSlideshow, cityGuide, foodJourney, weeklyRecap }

enum PostStatus { scheduled, posted, failed, cancelled }

enum SocialEventType {
  storyShared,
  postShared,
  videoShared,
  postScheduled,
  accountConnected,
}
