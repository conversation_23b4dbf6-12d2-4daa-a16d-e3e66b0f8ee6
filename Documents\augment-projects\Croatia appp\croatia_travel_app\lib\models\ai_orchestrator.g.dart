// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ai_orchestrator.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserProfile _$UserProfileFromJson(Map<String, dynamic> json) => UserProfile(
      id: json['id'] as String,
      name: json['name'] as String,
      interests: (json['interests'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      preferences: json['preferences'] as Map<String, dynamic>? ?? const {},
      visitedPlaces: (json['visitedPlaces'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      preferredLanguage: json['preferredLanguage'] as String? ?? 'cs',
      travelStyle:
          $enumDecodeNullable(_$TravelStyleEnumMap, json['travelStyle']) ??
              TravelStyle.balanced,
      budget:
          $enumDecodeNullable(_$BudgetEnumMap, json['budget']) ?? Budget.medium,
    );

Map<String, dynamic> _$UserProfileToJson(UserProfile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'interests': instance.interests,
      'preferences': instance.preferences,
      'visitedPlaces': instance.visitedPlaces,
      'preferredLanguage': instance.preferredLanguage,
      'travelStyle': _$TravelStyleEnumMap[instance.travelStyle]!,
      'budget': _$BudgetEnumMap[instance.budget]!,
    };

const _$TravelStyleEnumMap = {
  TravelStyle.luxury: 'luxury',
  TravelStyle.budget: 'budget',
  TravelStyle.adventure: 'adventure',
  TravelStyle.cultural: 'cultural',
  TravelStyle.relaxed: 'relaxed',
  TravelStyle.family: 'family',
  TravelStyle.romantic: 'romantic',
  TravelStyle.business: 'business',
  TravelStyle.balanced: 'balanced',
};

const _$BudgetEnumMap = {
  Budget.low: 'low',
  Budget.medium: 'medium',
  Budget.high: 'high',
  Budget.unlimited: 'unlimited',
};

ConversationMemory _$ConversationMemoryFromJson(Map<String, dynamic> json) =>
    ConversationMemory(
      facts: json['facts'] as Map<String, dynamic>? ?? const {},
      topics: (json['topics'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      preferences: (json['preferences'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, (e as num).toInt()),
          ) ??
          const {},
      interactions: (json['interactions'] as List<dynamic>?)
              ?.map((e) => UserInteraction.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$ConversationMemoryToJson(ConversationMemory instance) =>
    <String, dynamic>{
      'facts': instance.facts,
      'topics': instance.topics,
      'preferences': instance.preferences,
      'interactions': instance.interactions,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };

UserInteraction _$UserInteractionFromJson(Map<String, dynamic> json) =>
    UserInteraction(
      query: json['query'] as String,
      response: json['response'] as String,
      wasHelpful: json['wasHelpful'] as bool,
      confidence: (json['confidence'] as num).toDouble(),
      timestamp: DateTime.parse(json['timestamp'] as String),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$UserInteractionToJson(UserInteraction instance) =>
    <String, dynamic>{
      'query': instance.query,
      'response': instance.response,
      'wasHelpful': instance.wasHelpful,
      'confidence': instance.confidence,
      'timestamp': instance.timestamp.toIso8601String(),
      'metadata': instance.metadata,
    };

AIResponse _$AIResponseFromJson(Map<String, dynamic> json) => AIResponse(
      content: json['content'] as String,
      type: $enumDecode(_$AIResponseTypeEnumMap, json['type']),
      confidence: (json['confidence'] as num).toDouble(),
      source: $enumDecode(_$AIResponseSourceEnumMap, json['source']),
      suggestedActions: (json['suggestedActions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      recommendations: (json['recommendations'] as List<dynamic>?)
              ?.map((e) => AIRecommendation.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$AIResponseToJson(AIResponse instance) =>
    <String, dynamic>{
      'content': instance.content,
      'type': _$AIResponseTypeEnumMap[instance.type]!,
      'confidence': instance.confidence,
      'source': _$AIResponseSourceEnumMap[instance.source]!,
      'suggestedActions': instance.suggestedActions,
      'metadata': instance.metadata,
      'recommendations': instance.recommendations,
      'timestamp': instance.timestamp.toIso8601String(),
    };

const _$AIResponseTypeEnumMap = {
  AIResponseType.text: 'text',
  AIResponseType.recommendation: 'recommendation',
  AIResponseType.action: 'action',
  AIResponseType.error: 'error',
  AIResponseType.clarification: 'clarification',
};

const _$AIResponseSourceEnumMap = {
  AIResponseSource.local: 'local',
  AIResponseSource.cloud: 'cloud',
  AIResponseSource.hybrid: 'hybrid',
  AIResponseSource.cache: 'cache',
  AIResponseSource.fallback: 'fallback',
};

AIRecommendation _$AIRecommendationFromJson(Map<String, dynamic> json) =>
    AIRecommendation(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: $enumDecode(_$RecommendationTypeEnumMap, json['type']),
      score: (json['score'] as num).toDouble(),
      data: json['data'] as Map<String, dynamic>? ?? const {},
      imageUrl: json['imageUrl'] as String?,
      location: json['location'] == null
          ? null
          : LocationData.fromJson(json['location'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AIRecommendationToJson(AIRecommendation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'type': _$RecommendationTypeEnumMap[instance.type]!,
      'score': instance.score,
      'data': instance.data,
      'imageUrl': instance.imageUrl,
      'location': instance.location,
    };

const _$RecommendationTypeEnumMap = {
  RecommendationType.restaurant: 'restaurant',
  RecommendationType.attraction: 'attraction',
  RecommendationType.accommodation: 'accommodation',
  RecommendationType.activity: 'activity',
  RecommendationType.transport: 'transport',
  RecommendationType.event: 'event',
  RecommendationType.route: 'route',
};

LearningResult _$LearningResultFromJson(Map<String, dynamic> json) =>
    LearningResult(
      userId: json['userId'] as String,
      updatedPreferences:
          (json['updatedPreferences'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(k, (e as num).toDouble()),
              ) ??
              const {},
      newInterests: (json['newInterests'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      confidenceImprovement: (json['confidenceImprovement'] as num).toDouble(),
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$LearningResultToJson(LearningResult instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'updatedPreferences': instance.updatedPreferences,
      'newInterests': instance.newInterests,
      'confidenceImprovement': instance.confidenceImprovement,
      'timestamp': instance.timestamp.toIso8601String(),
    };
