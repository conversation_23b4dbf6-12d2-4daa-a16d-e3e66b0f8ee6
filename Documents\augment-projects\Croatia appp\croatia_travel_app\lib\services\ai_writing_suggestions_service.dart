import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/ai_writing_suggestion.dart';
import '../models/diary_entry.dart';

/// 🤖 AI WRITING SUGGESTIONS SERVICE - AI návrhy pro psaní
class AIWritingSuggestionsService {
  static final AIWritingSuggestionsService _instance =
      AIWritingSuggestionsService._internal();
  factory AIWritingSuggestionsService() => _instance;
  AIWritingSuggestionsService._internal();

  bool _isInitialized = false;
  final List<DiaryEntry> _userEntries = [];
  final Map<String, List<WritingSuggestion>> _suggestionCache = {};
  final List<WritingTemplate> _templates = [];
  final UserWritingProfile _writingProfile = UserWritingProfile.empty();

  /// Inicializace služby
  Future<void> initialize(List<DiaryEntry> userEntries) async {
    if (_isInitialized) return;

    try {
      debugPrint('🤖 Inicializuji AI Writing Suggestions Service...');

      _userEntries.clear();
      _userEntries.addAll(userEntries);

      await _loadTemplates();
      await _analyzeWritingProfile();

      _isInitialized = true;
      debugPrint('✅ AI Writing Suggestions Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci AI Writing: $e');
      await _createDefaultTemplates();
      _isInitialized = true;
    }
  }

  /// Získání návrhů pro aktuální text
  Future<List<WritingSuggestion>> getSuggestions({
    required String currentText,
    String? title,
    DiaryMood? mood,
    String? location,
    String? weather,
    List<String>? photos,
  }) async {
    try {
      final context = WritingContext(
        currentText: currentText,
        title: title,
        mood: mood,
        location: location,
        weather: weather,
        photos: photos ?? [],
        timestamp: DateTime.now(),
      );

      final cacheKey = _createCacheKey(context);

      // Kontrola cache
      if (_suggestionCache.containsKey(cacheKey)) {
        return _suggestionCache[cacheKey]!;
      }

      final suggestions = <WritingSuggestion>[];

      // 1. Návrhy na pokračování textu
      suggestions.addAll(await _generateContinuationSuggestions(context));

      // 2. Návrhy na vylepšení stylu
      suggestions.addAll(await _generateStyleSuggestions(context));

      // 3. Návrhy na doplnění detailů
      suggestions.addAll(await _generateDetailSuggestions(context));

      // 4. Návrhy na základě nálady
      suggestions.addAll(await _generateMoodBasedSuggestions(context));

      // 5. Návrhy na základě lokace
      suggestions.addAll(await _generateLocationBasedSuggestions(context));

      // 6. Personalizované návrhy
      suggestions.addAll(await _generatePersonalizedSuggestions(context));

      // Seřazení podle relevance
      suggestions.sort((a, b) => b.confidence.compareTo(a.confidence));

      // Uložení do cache
      _suggestionCache[cacheKey] = suggestions;

      return suggestions.take(10).toList(); // Max 10 návrhů
    } catch (e) {
      debugPrint('❌ Chyba při generování návrhů: $e');
      return [];
    }
  }

  /// Detekce nálady z textu
  Future<MoodDetectionResult> detectMood(String text) async {
    try {
      final words = text.toLowerCase().split(RegExp(r'\W+'));
      final moodScores = <DiaryMood, double>{};

      // Pozitivní slova
      final positiveWords = [
        'skvělý',
        'úžasný',
        'krásný',
        'radost',
        'štěstí',
        'láska',
        'perfektní',
        'báječný',
        'fantastický',
        'nádherný',
        'úsměv',
        'smích',
        'zábava',
      ];

      // Negativní slova
      final negativeWords = [
        'smutný',
        'špatný',
        'zlý',
        'problém',
        'bolest',
        'strach',
        'úzkost',
        'deprese',
        'smutek',
        'pláč',
        'zklamání',
        'frustrace',
        'stres',
      ];

      // Neutrální slova
      final neutralWords = [
        'normální',
        'obyčejný',
        'standardní',
        'běžný',
        'průměrný',
        'poklidný',
      ];

      // Vzrušené slova
      final excitedWords = [
        'vzrušení',
        'nadšení',
        'energie',
        'akce',
        'dobrodružství',
        'překvapení',
      ];

      // Relaxované slova
      final relaxedWords = [
        'klid',
        'relaxace',
        'odpočinek',
        'mír',
        'pohoda',
        'ticho',
        'meditace',
      ];

      // Výpočet skóre pro každou náladu
      for (final word in words) {
        if (positiveWords.contains(word)) {
          moodScores[DiaryMood.veryHappy] =
              (moodScores[DiaryMood.veryHappy] ?? 0) + 1.0;
          moodScores[DiaryMood.happy] =
              (moodScores[DiaryMood.happy] ?? 0) + 0.8;
        }
        if (negativeWords.contains(word)) {
          moodScores[DiaryMood.sad] = (moodScores[DiaryMood.sad] ?? 0) + 1.0;
          moodScores[DiaryMood.angry] =
              (moodScores[DiaryMood.angry] ?? 0) + 0.6;
        }
        if (neutralWords.contains(word)) {
          moodScores[DiaryMood.neutral] =
              (moodScores[DiaryMood.neutral] ?? 0) + 1.0;
        }
        if (excitedWords.contains(word)) {
          moodScores[DiaryMood.excited] =
              (moodScores[DiaryMood.excited] ?? 0) + 1.0;
        }
        if (relaxedWords.contains(word)) {
          moodScores[DiaryMood.relaxed] =
              (moodScores[DiaryMood.relaxed] ?? 0) + 1.0;
        }
      }

      // Normalizace skóre
      final totalWords = words.length;
      if (totalWords > 0) {
        moodScores.updateAll((key, value) => value / totalWords);
      }

      // Nalezení dominantní nálady
      final dominantMood = moodScores.isNotEmpty
          ? moodScores.entries.reduce((a, b) => a.value > b.value ? a : b)
          : null;

      return MoodDetectionResult(
        detectedMood: dominantMood?.key,
        confidence: dominantMood?.value ?? 0.0,
        moodScores: moodScores,
        suggestedMoods: moodScores.entries
            .where((e) => e.value > 0.1)
            .map((e) => e.key)
            .toList(),
      );
    } catch (e) {
      debugPrint('❌ Chyba při detekci nálady: $e');
      return MoodDetectionResult.empty();
    }
  }

  /// Analýza kvality textu
  Future<TextQualityAnalysis> analyzeTextQuality(String text) async {
    try {
      final words = text.split(RegExp(r'\W+'));
      final sentences = text.split(RegExp(r'[.!?]+'));
      final paragraphs = text.split('\n\n');

      // Základní statistiky
      final wordCount = words.where((w) => w.isNotEmpty).length;
      final sentenceCount = sentences.where((s) => s.trim().isNotEmpty).length;
      final paragraphCount = paragraphs
          .where((p) => p.trim().isNotEmpty)
          .length;

      // Průměrná délka věty
      final averageSentenceLength = sentenceCount > 0
          ? wordCount / sentenceCount
          : 0.0;

      // Složitost slov
      final complexWords = words.where((w) => w.length > 6).length;
      final complexityScore = wordCount > 0 ? complexWords / wordCount : 0.0;

      // Čitelnost (zjednodušená verze)
      final readabilityScore = _calculateReadabilityScore(
        wordCount,
        sentenceCount,
        complexWords,
      );

      // Emocionální intenzita
      final emotionalIntensity = await _calculateEmotionalIntensity(text);

      // Detailnost
      final detailScore = _calculateDetailScore(text);

      // Celkové skóre
      final overallScore =
          (readabilityScore + emotionalIntensity + detailScore) / 3;

      return TextQualityAnalysis(
        wordCount: wordCount,
        sentenceCount: sentenceCount,
        paragraphCount: paragraphCount,
        averageSentenceLength: averageSentenceLength,
        complexityScore: complexityScore,
        readabilityScore: readabilityScore,
        emotionalIntensity: emotionalIntensity,
        detailScore: detailScore,
        overallScore: overallScore,
        suggestions: _generateQualityImprovementSuggestions(
          readabilityScore,
          emotionalIntensity,
          detailScore,
        ),
      );
    } catch (e) {
      debugPrint('❌ Chyba při analýze kvality textu: $e');
      return TextQualityAnalysis.empty();
    }
  }

  /// Generování návrhů na pokračování
  Future<List<WritingSuggestion>> _generateContinuationSuggestions(
    WritingContext context,
  ) async {
    final suggestions = <WritingSuggestion>[];

    if (context.currentText.isEmpty) {
      // Návrhy na začátek
      suggestions.addAll([
        WritingSuggestion(
          id: 'start_time',
          type: SuggestionType.continuation,
          text: 'Dnes ráno jsem se probudil/a s pocitem...',
          explanation: 'Začněte popisem svého ranního pocitu',
          confidence: 0.8,
        ),
        WritingSuggestion(
          id: 'start_location',
          type: SuggestionType.continuation,
          text: 'Nacházím se v ${context.location ?? "krásném místě"} a...',
          explanation: 'Začněte popisem místa, kde se nacházíte',
          confidence: 0.7,
        ),
      ]);
    } else {
      // Návrhy na pokračování na základě kontextu
      final lastSentence = context.currentText.split('.').last.trim();

      if (lastSentence.contains('viděl') || lastSentence.contains('spatřil')) {
        suggestions.add(
          WritingSuggestion(
            id: 'continue_sight',
            type: SuggestionType.continuation,
            text: 'Tento pohled ve mně vyvolal...',
            explanation: 'Popište své emoce z toho, co jste viděli',
            confidence: 0.9,
          ),
        );
      }

      if (lastSentence.contains('chuť') || lastSentence.contains('jídlo')) {
        suggestions.add(
          WritingSuggestion(
            id: 'continue_taste',
            type: SuggestionType.continuation,
            text: 'Chuť mi připomněla...',
            explanation: 'Spojte chuť se vzpomínkou',
            confidence: 0.8,
          ),
        );
      }
    }

    return suggestions;
  }

  /// Generování návrhů na styl
  Future<List<WritingSuggestion>> _generateStyleSuggestions(
    WritingContext context,
  ) async {
    final suggestions = <WritingSuggestion>[];

    // Kontrola opakování slov
    final words = context.currentText.toLowerCase().split(RegExp(r'\W+'));
    final wordCounts = <String, int>{};

    for (final word in words) {
      if (word.length > 3) {
        wordCounts[word] = (wordCounts[word] ?? 0) + 1;
      }
    }

    final repeatedWords = wordCounts.entries.where((e) => e.value > 2).toList();

    for (final repeated in repeatedWords) {
      final synonyms = _getSynonyms(repeated.key);
      if (synonyms.isNotEmpty) {
        suggestions.add(
          WritingSuggestion(
            id: 'synonym_${repeated.key}',
            type: SuggestionType.style,
            text:
                'Zkuste nahradit "${repeated.key}" za: ${synonyms.join(", ")}',
            explanation: 'Vyvarujte se opakování slov',
            confidence: 0.6,
          ),
        );
      }
    }

    return suggestions;
  }

  /// Generování návrhů na detaily
  Future<List<WritingSuggestion>> _generateDetailSuggestions(
    WritingContext context,
  ) async {
    final suggestions = <WritingSuggestion>[];

    // Návrhy na smyslové detaily
    if (!context.currentText.contains(
      RegExp(r'(viděl|slyšel|cítil|ochutnal|dotknul)'),
    )) {
      suggestions.add(
        WritingSuggestion(
          id: 'add_senses',
          type: SuggestionType.detail,
          text: 'Přidejte smyslové detaily - co jste viděli, slyšeli, cítili?',
          explanation: 'Smyslové detaily oživí váš zápis',
          confidence: 0.7,
        ),
      );
    }

    // Návrhy na emoce
    if (!context.currentText.contains(
      RegExp(r'(cítil|pocit|emoce|radost|smutek)'),
    )) {
      suggestions.add(
        WritingSuggestion(
          id: 'add_emotions',
          type: SuggestionType.detail,
          text: 'Popište své emoce a pocity z této situace',
          explanation: 'Emoce dodají zápisu hloubku',
          confidence: 0.8,
        ),
      );
    }

    return suggestions;
  }

  /// Generování návrhů na základě nálady
  Future<List<WritingSuggestion>> _generateMoodBasedSuggestions(
    WritingContext context,
  ) async {
    final suggestions = <WritingSuggestion>[];

    if (context.mood != null) {
      switch (context.mood!) {
        case DiaryMood.happy:
        case DiaryMood.veryHappy:
          suggestions.add(
            WritingSuggestion(
              id: 'happy_detail',
              type: SuggestionType.mood,
              text: 'Co konkrétně vás dnes nejvíce potěšilo?',
              explanation: 'Zachyťte detaily své radosti',
              confidence: 0.8,
            ),
          );
          break;
        case DiaryMood.sad:
          suggestions.add(
            WritingSuggestion(
              id: 'sad_reflection',
              type: SuggestionType.mood,
              text: 'Co by vám teď pomohlo cítit se lépe?',
              explanation: 'Reflexe může pomoci zpracovat smutek',
              confidence: 0.7,
            ),
          );
          break;
        case DiaryMood.excited:
          suggestions.add(
            WritingSuggestion(
              id: 'excited_anticipation',
              type: SuggestionType.mood,
              text: 'Na co se nejvíce těšíte?',
              explanation: 'Zachyťte své nadšení a očekávání',
              confidence: 0.9,
            ),
          );
          break;
        default:
          break;
      }
    }

    return suggestions;
  }

  /// Generování návrhů na základě lokace
  Future<List<WritingSuggestion>> _generateLocationBasedSuggestions(
    WritingContext context,
  ) async {
    final suggestions = <WritingSuggestion>[];

    if (context.location != null) {
      final location = context.location!.toLowerCase();

      if (location.contains('pláž') || location.contains('moře')) {
        suggestions.addAll([
          WritingSuggestion(
            id: 'beach_sounds',
            type: SuggestionType.location,
            text: 'Popište zvuky moře a atmosféru pláže',
            explanation: 'Zachyťte jedinečnou atmosféru pobřeží',
            confidence: 0.8,
          ),
          WritingSuggestion(
            id: 'beach_feeling',
            type: SuggestionType.location,
            text: 'Jak se cítíte u moře? Co vám dává?',
            explanation: 'Moře často vyvolává silné emoce',
            confidence: 0.7,
          ),
        ]);
      }

      if (location.contains('hory') || location.contains('vrch')) {
        suggestions.add(
          WritingSuggestion(
            id: 'mountain_view',
            type: SuggestionType.location,
            text: 'Jaký je výhled z tohoto místa? Co vidíte v dálce?',
            explanation: 'Horské výhledy jsou často nezapomenutelné',
            confidence: 0.8,
          ),
        );
      }
    }

    return suggestions;
  }

  /// Generování personalizovaných návrhů
  Future<List<WritingSuggestion>> _generatePersonalizedSuggestions(
    WritingContext context,
  ) async {
    final suggestions = <WritingSuggestion>[];

    // Na základě uživatelského profilu
    if (_writingProfile.favoriteTopics.isNotEmpty) {
      final topic = _writingProfile.favoriteTopics.first;
      suggestions.add(
        WritingSuggestion(
          id: 'personal_topic',
          type: SuggestionType.personalized,
          text: 'Jak se dnešní zážitek vztahuje k vašemu zájmu o $topic?',
          explanation: 'Propojte zážitek s vašimi zájmy',
          confidence: 0.6,
        ),
      );
    }

    return suggestions;
  }

  /// Pomocné metody
  List<String> _getSynonyms(String word) {
    final synonyms = {
      'krásný': ['nádherný', 'úžasný', 'skvělý', 'báječný'],
      'dobrý': ['výborný', 'skvělý', 'perfektní', 'vynikající'],
      'velký': ['obrovský', 'ohromný', 'gigantický', 'masivní'],
      'malý': ['drobný', 'miniaturní', 'nepatrný', 'titěrný'],
    };

    return synonyms[word] ?? [];
  }

  double _calculateReadabilityScore(
    int words,
    int sentences,
    int complexWords,
  ) {
    if (sentences == 0) return 0.0;

    final avgSentenceLength = words / sentences;
    final complexWordRatio = words > 0 ? complexWords / words : 0.0;

    // Zjednodušená verze Flesch Reading Ease
    final score =
        206.835 - (1.015 * avgSentenceLength) - (84.6 * complexWordRatio);
    return (score / 100).clamp(0.0, 1.0);
  }

  Future<double> _calculateEmotionalIntensity(String text) async {
    final moodResult = await detectMood(text);
    return moodResult.confidence;
  }

  double _calculateDetailScore(String text) {
    final detailWords = [
      'viděl',
      'slyšel',
      'cítil',
      'ochutnal',
      'dotknul',
      'barva',
      'zvuk',
      'vůně',
      'chuť',
      'textura',
      'teplý',
      'studený',
      'měkký',
      'tvrdý',
      'hladký',
    ];

    final words = text.toLowerCase().split(RegExp(r'\W+'));
    final detailCount = words.where((w) => detailWords.contains(w)).length;

    return words.isNotEmpty
        ? (detailCount / words.length * 10).clamp(0.0, 1.0)
        : 0.0;
  }

  List<String> _generateQualityImprovementSuggestions(
    double readability,
    double emotional,
    double detail,
  ) {
    final suggestions = <String>[];

    if (readability < 0.5) {
      suggestions.add('Zkuste používat kratší věty pro lepší čitelnost');
    }

    if (emotional < 0.3) {
      suggestions.add('Přidejte více emocí a osobních pocitů');
    }

    if (detail < 0.3) {
      suggestions.add('Obohaťte text o smyslové detaily');
    }

    return suggestions;
  }

  Future<void> _analyzeWritingProfile() async {
    // Analýza uživatelského stylu psaní na základě historie
    // Pro demo používáme prázdný profil
  }

  Future<void> _loadTemplates() async {
    await _createDefaultTemplates();
  }

  Future<void> _createDefaultTemplates() async {
    _templates.addAll([
      WritingTemplate(
        id: 'daily_reflection',
        name: 'Denní reflexe',
        description: 'Šablona pro denní zamyšlení',
        template: 'Dnes jsem... Cítil/a jsem se... Naučil/a jsem se...',
        category: 'reflexe',
        usageCount: 0,
      ),
      WritingTemplate(
        id: 'travel_experience',
        name: 'Cestovní zážitek',
        description: 'Šablona pro popis cestovních zážitků',
        template:
            'Navštívil/a jsem... Nejvíce mě překvapilo... Rád/a bych se vrátil/a...',
        category: 'cestování',
        usageCount: 0,
      ),
    ]);
  }

  String _createCacheKey(WritingContext context) {
    return '${context.currentText.hashCode}_${context.mood?.name ?? ""}_${context.location ?? ""}';
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  UserWritingProfile get writingProfile => _writingProfile;
  List<WritingTemplate> get templates => List.unmodifiable(_templates);
}
