import 'package:json_annotation/json_annotation.dart';

part 'user_models.g.dart';

/// Typ cest<PERSON>í
enum TravelType {
  culture,
  nature,
  beach,
  adventure,
  food,
  nightlife,
  family,
  romantic,
  business,
  backpacking,
}

/// Věková skupina
enum AgeGroup {
  young, // 18-25
  adult, // 26-40
  middleAge, // 41-60
  senior, // 60+
}

/// Rozpočtová kategorie
enum BudgetCategory {
  budget, // do 50€/den
  mid, // 50-100€/den
  luxury, // 100€+/den
}

/// Model uživatelského profilu
@JsonSerializable()
class UserProfile {
  final String id;
  final String? email;
  final String? displayName;
  final String? photoUrl;
  final List<TravelType> travelPreferences;
  final AgeGroup? ageGroup;
  final BudgetCategory? budgetCategory;
  final List<String> languages;
  final List<String> visitedPlaces;
  final List<String> wishlist;
  final Map<String, dynamic> settings;
  final DateTime createdAt;
  final DateTime lastActiveAt;
  final bool isVerified;

  const UserProfile({
    required this.id,
    this.email,
    this.displayName,
    this.photoUrl,
    this.travelPreferences = const [],
    this.ageGroup,
    this.budgetCategory,
    this.languages = const ['cs'],
    this.visitedPlaces = const [],
    this.wishlist = const [],
    this.settings = const {},
    required this.createdAt,
    required this.lastActiveAt,
    this.isVerified = false,
  });

  /// Zkontroluje, zda je profil kompletní
  bool get isComplete {
    return displayName != null &&
           travelPreferences.isNotEmpty &&
           ageGroup != null &&
           budgetCategory != null;
  }

  /// Získá hlavní cestovní preference
  TravelType? get primaryTravelType {
    return travelPreferences.isNotEmpty ? travelPreferences.first : null;
  }

  /// Název věkové skupiny v češtině
  String get ageGroupName {
    switch (ageGroup) {
      case AgeGroup.young:
        return 'Mladí (18-25)';
      case AgeGroup.adult:
        return 'Dospělí (26-40)';
      case AgeGroup.middleAge:
        return 'Střední věk (41-60)';
      case AgeGroup.senior:
        return 'Senioři (60+)';
      case null:
        return 'Neuvedeno';
    }
  }

  /// Název rozpočtu v češtině
  String get budgetCategoryName {
    switch (budgetCategory) {
      case BudgetCategory.budget:
        return 'Rozpočtový (do 50€/den)';
      case BudgetCategory.mid:
        return 'Střední (50-100€/den)';
      case BudgetCategory.luxury:
        return 'Luxusní (100€+/den)';
      case null:
        return 'Neuvedeno';
    }
  }

  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);

  Map<String, dynamic> toJson() => _$UserProfileToJson(this);

  UserProfile copyWith({
    String? id,
    String? email,
    String? displayName,
    String? photoUrl,
    List<TravelType>? travelPreferences,
    AgeGroup? ageGroup,
    BudgetCategory? budgetCategory,
    List<String>? languages,
    List<String>? visitedPlaces,
    List<String>? wishlist,
    Map<String, dynamic>? settings,
    DateTime? createdAt,
    DateTime? lastActiveAt,
    bool? isVerified,
  }) {
    return UserProfile(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoUrl: photoUrl ?? this.photoUrl,
      travelPreferences: travelPreferences ?? this.travelPreferences,
      ageGroup: ageGroup ?? this.ageGroup,
      budgetCategory: budgetCategory ?? this.budgetCategory,
      languages: languages ?? this.languages,
      visitedPlaces: visitedPlaces ?? this.visitedPlaces,
      wishlist: wishlist ?? this.wishlist,
      settings: settings ?? this.settings,
      createdAt: createdAt ?? this.createdAt,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
      isVerified: isVerified ?? this.isVerified,
    );
  }
}

/// Model pro personalizované doporučení
@JsonSerializable()
class PersonalizedRecommendation {
  final String id;
  final String title;
  final String description;
  final String type; // place, restaurant, activity, event
  final String? imageUrl;
  final double score;
  final List<String> reasons;
  final Map<String, dynamic> data;
  final DateTime createdAt;
  final bool isViewed;
  final bool isLiked;
  final bool isBookmarked;

  const PersonalizedRecommendation({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    this.imageUrl,
    required this.score,
    this.reasons = const [],
    this.data = const {},
    required this.createdAt,
    this.isViewed = false,
    this.isLiked = false,
    this.isBookmarked = false,
  });

  /// Formátované skóre v procentech
  String get scorePercentage => '${(score * 100).round()}%';

  /// Hlavní důvod doporučení
  String get primaryReason => reasons.isNotEmpty ? reasons.first : 'Doporučeno pro vás';

  factory PersonalizedRecommendation.fromJson(Map<String, dynamic> json) =>
      _$PersonalizedRecommendationFromJson(json);

  Map<String, dynamic> toJson() => _$PersonalizedRecommendationToJson(this);

  PersonalizedRecommendation copyWith({
    String? id,
    String? title,
    String? description,
    String? type,
    String? imageUrl,
    double? score,
    List<String>? reasons,
    Map<String, dynamic>? data,
    DateTime? createdAt,
    bool? isViewed,
    bool? isLiked,
    bool? isBookmarked,
  }) {
    return PersonalizedRecommendation(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      imageUrl: imageUrl ?? this.imageUrl,
      score: score ?? this.score,
      reasons: reasons ?? this.reasons,
      data: data ?? this.data,
      createdAt: createdAt ?? this.createdAt,
      isViewed: isViewed ?? this.isViewed,
      isLiked: isLiked ?? this.isLiked,
      isBookmarked: isBookmarked ?? this.isBookmarked,
    );
  }
}

/// Model pro cestovní itinerář
@JsonSerializable()
class TravelItinerary {
  final String id;
  final String title;
  final String description;
  final DateTime startDate;
  final DateTime endDate;
  final List<ItineraryDay> days;
  final List<String> tags;
  final double? totalBudget;
  final String? coverImageUrl;
  final bool isPublic;
  final bool isGenerated;
  final DateTime createdAt;
  final DateTime updatedAt;

  const TravelItinerary({
    required this.id,
    required this.title,
    required this.description,
    required this.startDate,
    required this.endDate,
    this.days = const [],
    this.tags = const [],
    this.totalBudget,
    this.coverImageUrl,
    this.isPublic = false,
    this.isGenerated = false,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Počet dní itineráře
  int get dayCount => endDate.difference(startDate).inDays + 1;

  /// Zkontroluje, zda je itinerář aktuální
  bool get isCurrent {
    final now = DateTime.now();
    return now.isAfter(startDate) && now.isBefore(endDate);
  }

  /// Zkontroluje, zda je itinerář v budoucnosti
  bool get isFuture => DateTime.now().isBefore(startDate);

  factory TravelItinerary.fromJson(Map<String, dynamic> json) =>
      _$TravelItineraryFromJson(json);

  Map<String, dynamic> toJson() => _$TravelItineraryToJson(this);
}

/// Model pro den v itineráři
@JsonSerializable()
class ItineraryDay {
  final String id;
  final DateTime date;
  final String? title;
  final List<ItineraryActivity> activities;
  final String? notes;

  const ItineraryDay({
    required this.id,
    required this.date,
    this.title,
    this.activities = const [],
    this.notes,
  });

  /// Název dne (např. "Den 1")
  String get dayName {
    if (title != null && title!.isNotEmpty) return title!;
    return 'Den ${date.day}.${date.month}';
  }

  factory ItineraryDay.fromJson(Map<String, dynamic> json) =>
      _$ItineraryDayFromJson(json);

  Map<String, dynamic> toJson() => _$ItineraryDayToJson(this);
}

/// Model pro aktivitu v itineráři
@JsonSerializable()
class ItineraryActivity {
  final String id;
  final String title;
  final String? description;
  final DateTime startTime;
  final DateTime? endTime;
  final String? location;
  final double? latitude;
  final double? longitude;
  final String type; // sightseeing, restaurant, transport, etc.
  final double? estimatedCost;
  final String? bookingUrl;
  final bool isCompleted;

  const ItineraryActivity({
    required this.id,
    required this.title,
    this.description,
    required this.startTime,
    this.endTime,
    this.location,
    this.latitude,
    this.longitude,
    required this.type,
    this.estimatedCost,
    this.bookingUrl,
    this.isCompleted = false,
  });

  /// Formátovaný čas
  String get timeRange {
    final start = '${startTime.hour.toString().padLeft(2, '0')}:${startTime.minute.toString().padLeft(2, '0')}';
    if (endTime != null) {
      final end = '${endTime!.hour.toString().padLeft(2, '0')}:${endTime!.minute.toString().padLeft(2, '0')}';
      return '$start - $end';
    }
    return start;
  }

  /// Zkontroluje, zda má aktivita lokaci
  bool get hasLocation => latitude != null && longitude != null;

  factory ItineraryActivity.fromJson(Map<String, dynamic> json) =>
      _$ItineraryActivityFromJson(json);

  Map<String, dynamic> toJson() => _$ItineraryActivityToJson(this);
}

/// Rozšíření pro názvy typů cestování v češtině
extension TravelTypeExtension on TravelType {
  String get czechName {
    switch (this) {
      case TravelType.culture:
        return 'Kultura';
      case TravelType.nature:
        return 'Příroda';
      case TravelType.beach:
        return 'Pláže';
      case TravelType.adventure:
        return 'Dobrodružství';
      case TravelType.food:
        return 'Gastronomie';
      case TravelType.nightlife:
        return 'Noční život';
      case TravelType.family:
        return 'Rodinné';
      case TravelType.romantic:
        return 'Romantické';
      case TravelType.business:
        return 'Obchodní';
      case TravelType.backpacking:
        return 'Backpacking';
    }
  }
}
