/// 🔒 COMPLIANCE MONITORING MODELS - Modely pro monitoring compliance
library;

/// Compliance framework enum
enum ComplianceFramework { gdpr, soc2, iso27001, pci, hipaa, custom }

/// Rule severity enum
enum RuleSeverity { low, medium, high, critical }

/// Compliance status enum
enum ComplianceStatus { compliant, nonCompliant, pending, unknown, inProgress }

/// Framework status enum
enum FrameworkStatus {
  compliant,
  partiallyCompliant,
  nonCompliant,
  notApplicable,
}

/// Compliance rule model
class ComplianceRule {
  final String id;
  final String name;
  final String description;
  final ComplianceFramework framework;
  final RuleSeverity severity;
  final Duration checkInterval;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? lastChecked;
  final Map<String, dynamic>? metadata;

  const ComplianceRule({
    required this.id,
    required this.name,
    required this.description,
    required this.framework,
    required this.severity,
    required this.checkInterval,
    this.isActive = true,
    required this.createdAt,
    this.lastChecked,
    this.metadata,
  });

  ComplianceRule copyWith({
    String? id,
    String? name,
    String? description,
    ComplianceFramework? framework,
    RuleSeverity? severity,
    Duration? checkInterval,
    bool? isActive,
    DateTime? createdAt,
    DateTime? lastChecked,
    Map<String, dynamic>? metadata,
  }) {
    return ComplianceRule(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      framework: framework ?? this.framework,
      severity: severity ?? this.severity,
      checkInterval: checkInterval ?? this.checkInterval,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      lastChecked: lastChecked ?? this.lastChecked,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Compliance check result model
class ComplianceCheckResult {
  final String ruleId;
  final bool isCompliant;
  final String? message;
  final Map<String, dynamic>? details;
  final DateTime checkedAt;
  final String? evidence;
  final List<String>? recommendations;

  const ComplianceCheckResult({
    required this.ruleId,
    required this.isCompliant,
    this.message,
    this.details,
    required this.checkedAt,
    this.evidence,
    this.recommendations,
  });
}

/// Compliance violation model
class ComplianceViolation {
  final String id;
  final String ruleId;
  final String title;
  final String description;
  final RuleSeverity severity;
  final DateTime detectedAt;
  final DateTime? resolvedAt;
  final String? resolution;
  final Map<String, dynamic>? context;
  final bool isResolved;

  const ComplianceViolation({
    required this.id,
    required this.ruleId,
    required this.title,
    required this.description,
    required this.severity,
    required this.detectedAt,
    this.resolvedAt,
    this.resolution,
    this.context,
    this.isResolved = false,
  });
}

/// Compliance event model
class ComplianceEvent {
  final String id;
  final String type;
  final String title;
  final String description;
  final RuleSeverity severity;
  final DateTime timestamp;
  final Map<String, dynamic>? data;
  final String? ruleId;
  final String? violationId;

  const ComplianceEvent({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.severity,
    required this.timestamp,
    this.data,
    this.ruleId,
    this.violationId,
  });
}

/// Compliance report model
class ComplianceReport {
  final String id;
  final DateTime generatedAt;
  final DateTime periodStart;
  final DateTime periodEnd;
  final int totalRules;
  final int compliantRules;
  final int nonCompliantRules;
  final int pendingRules;
  final double complianceScore;
  final List<ComplianceCheckResult> results;
  final List<ComplianceViolation> violations;
  final Map<ComplianceFramework, FrameworkStatus> frameworkStatus;
  final Map<String, dynamic>? summary;

  const ComplianceReport({
    required this.id,
    required this.generatedAt,
    required this.periodStart,
    required this.periodEnd,
    required this.totalRules,
    required this.compliantRules,
    required this.nonCompliantRules,
    required this.pendingRules,
    required this.complianceScore,
    required this.results,
    required this.violations,
    required this.frameworkStatus,
    this.summary,
  });
}

/// Compliance dashboard model
class ComplianceDashboard {
  final DateTime lastUpdated;
  final double overallScore;
  final int totalViolations;
  final int criticalViolations;
  final int resolvedViolations;
  final Map<ComplianceFramework, FrameworkStatus> frameworkStatus;
  final List<ComplianceEvent> recentEvents;
  final Map<String, dynamic> trends;
  final Map<String, int> violationsByCategory;

  const ComplianceDashboard({
    required this.lastUpdated,
    required this.overallScore,
    required this.totalViolations,
    required this.criticalViolations,
    required this.resolvedViolations,
    required this.frameworkStatus,
    required this.recentEvents,
    required this.trends,
    required this.violationsByCategory,
  });
}
