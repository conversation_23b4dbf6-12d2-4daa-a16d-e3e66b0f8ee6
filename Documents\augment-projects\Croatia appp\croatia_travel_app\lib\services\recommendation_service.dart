import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/place.dart';
import '../models/event.dart';
import '../models/cuisine.dart';
import '../data/local_database.dart';
import 'location_service.dart';

class RecommendationService {
  static final RecommendationService _instance =
      RecommendationService._internal();
  factory RecommendationService() => _instance;
  RecommendationService._internal();

  final LocalDatabase _database = LocalDatabase();
  final LocationService _locationService = LocationService();

  /// Získání personalizovaných doporučení
  Future<List<Recommendation>> getPersonalizedRecommendations({
    bool batterySaverMode = false,
    int maxRecommendations = 10,
  }) async {
    try {
      final userPreferences = await _getUserPreferences();
      final visitedPlaces = await _database.getVisitedPlaces();
      final allPlaces = await _database.getAllPlaces();
      final upcomingEvents = await _database.getUpcomingEvents();
      final cuisineItems = (await _database.getAllCuisineItems())
          .cast<CuisineItem>();

      List<Recommendation> recommendations = [];

      // Doporučení míst na základě preferencí a historie
      final placeRecommendations = await _getPlaceRecommendations(
        userPreferences,
        visitedPlaces,
        allPlaces,
        batterySaverMode,
      );
      recommendations.addAll(placeRecommendations);

      // Doporučení událostí
      final eventRecommendations = await _getEventRecommendations(
        userPreferences,
        upcomingEvents,
        batterySaverMode,
      );
      recommendations.addAll(eventRecommendations);

      // Doporučení kuchyně
      final cuisineRecommendations = await _getCuisineRecommendations(
        userPreferences,
        cuisineItems,
        visitedPlaces,
      );
      recommendations.addAll(cuisineRecommendations);

      // Seřazení podle relevance a omezení počtu
      recommendations.sort(
        (a, b) => (b.confidence ?? 0).compareTo(a.confidence ?? 0),
      );

      return recommendations.take(maxRecommendations).toList();
    } catch (e) {
      debugPrint('Chyba při generování doporučení: $e');
      return [];
    }
  }

  /// Doporučení míst
  Future<List<Recommendation>> _getPlaceRecommendations(
    UserPreferences preferences,
    List<Place> visitedPlaces,
    List<Place> allPlaces,
    bool batterySaverMode,
  ) async {
    List<Recommendation> recommendations = [];

    // Analýza preferencí na základě navštívených míst
    final preferredTypes = _analyzePreferredPlaceTypes(visitedPlaces);
    final preferredRegions = _analyzePreferredRegions(visitedPlaces);

    for (final place in allPlaces) {
      if (place.isVisited) continue;

      double confidence = 0.0;

      // Skóre na základě typu místa
      if (preferredTypes.containsKey(place.type)) {
        confidence += preferredTypes[place.type]! * 0.3;
      }

      // Skóre na základě regionu
      if (preferredRegions.containsKey(place.region)) {
        confidence += preferredRegions[place.region]! * 0.2;
      }

      // Skóre na základě hodnocení
      if (place.rating != null) {
        confidence += (place.rating! / 5.0) * 0.2;
      }

      // Skóre na základě vzdálenosti (pokud není úsporný režim)
      if (!batterySaverMode) {
        try {
          final currentPosition = await _locationService.getCurrentPosition();
          final distance = _locationService.calculateDistance(
            currentPosition.latitude,
            currentPosition.longitude,
            place.latitude,
            place.longitude,
          );

          // Blízká místa mají vyšší skóre
          if (distance < 10000) {
            // Do 10 km
            confidence += 0.3 * (1 - distance / 10000);
          }
        } catch (e) {
          // Pokud nelze získat polohu, nepenalizujeme
        }
      }

      // Skóre na základě sezóny a počasí
      confidence += _getSeasonalScore(place) * 0.1;

      if (confidence > 0.3) {
        recommendations.add(
          Recommendation(
            id: place.id,
            type: RecommendationType.place,
            title: place.name,
            description: place.description,
            confidence: confidence,
            metadata: {
              'place_type': place.type.name,
              'region': place.region,
              'rating': place.rating,
            },
          ),
        );
      }
    }

    return recommendations;
  }

  /// Doporučení událostí
  Future<List<Recommendation>> _getEventRecommendations(
    UserPreferences preferences,
    List<Event> upcomingEvents,
    bool batterySaverMode,
  ) async {
    List<Recommendation> recommendations = [];

    for (final event in upcomingEvents) {
      double confidence = 0.0;

      // Skóre na základě typu události
      if (preferences.preferredEventTypes.contains(event.type)) {
        confidence += 0.4;
      }

      // Skóre na základě regionu
      if (preferences.preferredRegions.contains(event.region)) {
        confidence += 0.2;
      }

      // Skóre na základě ceny (zdarma = vyšší skóre)
      if (event.isFree) {
        confidence += 0.2;
      } else if (event.price != null &&
          event.price! <= preferences.maxEventPrice) {
        confidence += 0.1;
      }

      // Skóre na základě času do začátku
      final daysUntilEvent = event.startDate.difference(DateTime.now()).inDays;
      if (daysUntilEvent >= 3 && daysUntilEvent <= 30) {
        confidence += 0.2;
      }

      if (confidence > 0.3) {
        recommendations.add(
          Recommendation(
            id: event.id,
            type: RecommendationType.event,
            title: event.name,
            description: event.description,
            confidence: confidence,
            metadata: {
              'event_type': event.type.name,
              'start_date': event.startDate.toIso8601String(),
              'price': event.price,
              'is_free': event.isFree,
            },
          ),
        );
      }
    }

    return recommendations;
  }

  /// Doporučení kuchyně
  Future<List<Recommendation>> _getCuisineRecommendations(
    UserPreferences preferences,
    List<CuisineItem> cuisineItems,
    List<Place> visitedPlaces,
  ) async {
    List<Recommendation> recommendations = [];

    // Analýza regionů, které uživatel navštívil
    final visitedRegions = visitedPlaces.map((p) => p.region).toSet();

    for (final item in cuisineItems) {
      double confidence = 0.0;

      // Skóre na základě navštívených regionů
      if (visitedRegions.contains(item.region)) {
        confidence += 0.3;
      }

      // Skóre na základě dietních preferencí
      if (preferences.isVegetarian && item.isVegetarian) {
        confidence += 0.3;
      }
      if (preferences.isVegan && item.isVegan) {
        confidence += 0.3;
      }
      if (preferences.isGlutenFree && item.isGlutenFree) {
        confidence += 0.2;
      }

      // Skóre na základě hodnocení
      if (item.rating != null) {
        confidence += (item.rating! / 5.0) * 0.2;
      }

      // Skóre na základě ceny
      if (item.averagePrice != null &&
          item.averagePrice! <= preferences.maxMealPrice) {
        confidence += 0.1;
      }

      if (confidence > 0.2) {
        recommendations.add(
          Recommendation(
            id: item.id,
            type: RecommendationType.cuisine,
            title: item.name,
            description: item.description,
            confidence: confidence,
            metadata: {
              'cuisine_type': item.type.name,
              'region': item.region,
              'is_vegetarian': item.isVegetarian,
              'is_vegan': item.isVegan,
              'average_price': item.averagePrice,
            },
          ),
        );
      }
    }

    return recommendations;
  }

  /// Analýza preferovaných typů míst
  Map<PlaceType, double> _analyzePreferredPlaceTypes(
    List<Place> visitedPlaces,
  ) {
    final typeCounts = <PlaceType, int>{};

    for (final place in visitedPlaces) {
      typeCounts[place.type] = (typeCounts[place.type] ?? 0) + 1;
    }

    final total = visitedPlaces.length;
    if (total == 0) return {};

    return typeCounts.map((type, count) => MapEntry(type, count / total));
  }

  /// Analýza preferovaných regionů
  Map<String, double> _analyzePreferredRegions(List<Place> visitedPlaces) {
    final regionCounts = <String, int>{};

    for (final place in visitedPlaces) {
      regionCounts[place.region] = (regionCounts[place.region] ?? 0) + 1;
    }

    final total = visitedPlaces.length;
    if (total == 0) return {};

    return regionCounts.map((region, count) => MapEntry(region, count / total));
  }

  /// Sezónní skóre pro místa
  double _getSeasonalScore(Place place) {
    final now = DateTime.now();
    final month = now.month;

    // Letní místa (pláže) - vyšší skóre v létě
    if (place.type == PlaceType.beach) {
      if (month >= 6 && month <= 8) return 1.0;
      if (month == 5 || month == 9) return 0.7;
      return 0.3;
    }

    // Zimní aktivity - vyšší skóre v zimě
    if (place.tags.contains('zimní')) {
      if (month >= 12 || month <= 2) return 1.0;
      return 0.3;
    }

    // Výchozí skóre
    return 0.5;
  }

  /// Získání uživatelských preferencí
  Future<UserPreferences> _getUserPreferences() async {
    final prefs = await SharedPreferences.getInstance();

    return UserPreferences(
      preferredRegions:
          prefs.getStringList('preferred_regions') ?? ['dalmatia'],
      preferredEventTypes:
          (prefs.getStringList('preferred_event_types') ?? ['cultural'])
              .map((e) => EventType.values.firstWhere((type) => type.name == e))
              .toList(),
      isVegetarian: prefs.getBool('is_vegetarian') ?? false,
      isVegan: prefs.getBool('is_vegan') ?? false,
      isGlutenFree: prefs.getBool('is_gluten_free') ?? false,
      maxEventPrice: prefs.getDouble('max_event_price') ?? 100.0,
      maxMealPrice: prefs.getDouble('max_meal_price') ?? 30.0,
    );
  }

  /// Zaznamenání interakce s doporučením
  Future<void> recordInteraction(
    String recommendationId,
    RecommendationInteraction interaction,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final key = 'interaction_${recommendationId}_${interaction.name}';
    final count = prefs.getInt(key) ?? 0;
    await prefs.setInt(key, count + 1);
  }
}

class Recommendation {
  final String id;
  final RecommendationType type;
  final String title;
  final String description;
  final double? confidence;
  final Map<String, dynamic>? metadata;

  Recommendation({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    this.confidence,
    this.metadata,
  });
}

enum RecommendationType { place, event, cuisine, route, activity }

enum RecommendationInteraction { tap, bookmark, share, dismiss }

class UserPreferences {
  final List<String> preferredRegions;
  final List<EventType> preferredEventTypes;
  final bool isVegetarian;
  final bool isVegan;
  final bool isGlutenFree;
  final double maxEventPrice;
  final double maxMealPrice;

  UserPreferences({
    required this.preferredRegions,
    required this.preferredEventTypes,
    required this.isVegetarian,
    required this.isVegan,
    required this.isGlutenFree,
    required this.maxEventPrice,
    required this.maxMealPrice,
  });
}
