import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/restaurant.dart';
import '../services/restaurant_discovery_service.dart';

class RestaurantDiscoveryScreen extends StatefulWidget {
  const RestaurantDiscoveryScreen({super.key});

  @override
  State<RestaurantDiscoveryScreen> createState() =>
      _RestaurantDiscoveryScreenState();
}

class _RestaurantDiscoveryScreenState extends State<RestaurantDiscoveryScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Restaurant Discovery stav
  String _selectedRegion = 'all';
  String _selectedCuisineType = 'all';
  String _selectedPriceRange = 'all';
  String _searchQuery = '';
  bool _isLoading = false;

  // Data
  List<Restaurant> _allRestaurants = [];
  List<Restaurant> _filteredRestaurants = [];

  // Statistiky
  int _totalRestaurants = 0;
  int _openNow = 0;
  int _topRated = 0;
  int _traditionalCroatian = 0;

  final RestaurantDiscoveryService _restaurantService =
      RestaurantDiscoveryService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadRestaurantData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadRestaurantData() async {
    setState(() => _isLoading = true);

    try {
      _allRestaurants = await _restaurantService.getAllRestaurants();
      _applyFilters();
      _updateStatistics();
    } catch (e) {
      debugPrint('Chyba při načítání restaurací: $e');
    }

    setState(() => _isLoading = false);
  }

  void _applyFilters() {
    _filteredRestaurants = _allRestaurants.where((restaurant) {
      // Region filter
      if (_selectedRegion != 'all' && restaurant.region != _selectedRegion) {
        return false;
      }

      // Cuisine type filter
      if (_selectedCuisineType != 'all' &&
          restaurant.cuisineType != _selectedCuisineType) {
        return false;
      }

      // Price range filter
      if (_selectedPriceRange != 'all' &&
          restaurant.priceRange != _selectedPriceRange) {
        return false;
      }

      // Search query
      if (_searchQuery.isNotEmpty) {
        return restaurant.name.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            restaurant.description.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            restaurant.specialties.any(
              (s) => s.toLowerCase().contains(_searchQuery.toLowerCase()),
            );
      }

      return true;
    }).toList();
  }

  void _updateStatistics() {
    _totalRestaurants = _allRestaurants.length;
    _openNow = _allRestaurants.where((r) => r.isOpenNow).length;
    _topRated = _allRestaurants.where((r) => r.rating >= 4.5).length;
    _traditionalCroatian = _allRestaurants
        .where((r) => r.cuisineType == 'traditional')
        .length;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Chorvatské restaurace',
          style: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFFFF6B35).withValues(alpha: 0.9),
                const Color(0xFF4CAF50).withValues(alpha: 0.8),
                const Color(0xFF006994).withValues(alpha: 0.7),
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: CustomPaint(
            painter: WatercolorRestaurantHeaderPainter(),
            size: Size.infinite,
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: _loadRestaurantData,
              icon: const Icon(Icons.refresh),
              tooltip: 'Aktualizovat restaurace',
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          indicator: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Colors.white.withValues(alpha: 0.3),
          ),
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Přehled'),
            Tab(icon: Icon(Icons.restaurant), text: 'Všechny'),
            Tab(icon: Icon(Icons.star), text: 'Top hodnocené'),
            Tab(icon: Icon(Icons.local_dining), text: 'Tradiční'),
            Tab(icon: Icon(Icons.map), text: 'Mapa'),
          ],
        ),
      ),
      body: Container(
        child: CustomPaint(
          painter: WatercolorRestaurantBackgroundPainter(),
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(),
              _buildAllRestaurantsTab(),
              _buildTopRatedTab(),
              _buildTraditionalTab(),
              _buildMapTab(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Hlavní karta
          Container(
            child: CustomPaint(
              painter: WatercolorRestaurantMainCardPainter(
                const Color(0xFFFF6B35),
              ),
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Column(
                  children: [
                    Text(
                      'Objevte chorvatskou gastronomii',
                      style: GoogleFonts.playfairDisplay(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFFFF6B35),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Najděte nejlepší restaurace, konobe a taverne v Chorvatsku',
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        color: const Color(0xFF666666),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Vyhledávání
          _buildSearchSection(),

          const SizedBox(height: 20),

          // Statistiky
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            children: [
              _buildStatCard(
                'Celkem',
                '$_totalRestaurants',
                'restaurací',
                Icons.restaurant,
                const Color(0xFFFF6B35),
              ),
              _buildStatCard(
                'Otevřeno',
                '$_openNow',
                'právě teď',
                Icons.access_time,
                const Color(0xFF4CAF50),
              ),
              _buildStatCard(
                'Top hodnocené',
                '$_topRated',
                '4.5+ hvězdiček',
                Icons.star,
                const Color(0xFF006994),
              ),
              _buildStatCard(
                'Tradiční',
                '$_traditionalCroatian',
                'chorvatské',
                Icons.local_dining,
                const Color(0xFF2E8B8B),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Doporučené restaurace
          _buildRecommendedSection(),
        ],
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      child: CustomPaint(
        painter: WatercolorRestaurantSearchPainter(const Color(0xFF4CAF50)),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Vyhledávání restaurací',
                style: GoogleFonts.playfairDisplay(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF4CAF50),
                ),
              ),
              const SizedBox(height: 16),

              // Search field
              TextField(
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                    _applyFilters();
                  });
                },
                decoration: InputDecoration(
                  hintText: 'Hledat restaurace, jídla, speciality...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                  fillColor: Colors.grey[50],
                ),
              ),

              const SizedBox(height: 16),

              // Filters
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildFilterChip(
                    'Region',
                    _selectedRegion,
                    ['all', 'dalmatia', 'istria', 'slavonia', 'zagreb', 'lika'],
                    (value) => setState(() {
                      _selectedRegion = value;
                      _applyFilters();
                    }),
                  ),
                  _buildFilterChip(
                    'Kuchyně',
                    _selectedCuisineType,
                    [
                      'all',
                      'traditional',
                      'seafood',
                      'mediterranean',
                      'international',
                    ],
                    (value) => setState(() {
                      _selectedCuisineType = value;
                      _applyFilters();
                    }),
                  ),
                  _buildFilterChip(
                    'Cena',
                    _selectedPriceRange,
                    ['all', 'budget', 'mid', 'upscale', 'fine'],
                    (value) => setState(() {
                      _selectedPriceRange = value;
                      _applyFilters();
                    }),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Container(
      child: CustomPaint(
        painter: WatercolorRestaurantStatCardPainter(color),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                child: CustomPaint(
                  painter: WatercolorRestaurantIconPainter(color),
                  child: Icon(icon, size: 32, color: color),
                ),
              ),
              const SizedBox(height: 12),
              Text(
                value,
                style: GoogleFonts.playfairDisplay(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: GoogleFonts.inter(
                  fontSize: 12,
                  color: const Color(0xFF666666),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF2C2C2C),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterChip(
    String label,
    String currentValue,
    List<String> options,
    Function(String) onChanged,
  ) {
    return Container(
      child: CustomPaint(
        painter: WatercolorRestaurantFilterChipPainter(const Color(0xFF006994)),
        child: PopupMenuButton<String>(
          onSelected: onChanged,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: const Color(0xFF006994).withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '$label: ${_getFilterLabel(currentValue)}',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF006994),
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  Icons.arrow_drop_down,
                  size: 16,
                  color: const Color(0xFF006994),
                ),
              ],
            ),
          ),
          itemBuilder: (context) => options
              .map(
                (option) => PopupMenuItem(
                  value: option,
                  child: Text(_getFilterLabel(option)),
                ),
              )
              .toList(),
        ),
      ),
    );
  }

  String _getFilterLabel(String value) {
    switch (value) {
      case 'all':
        return 'Vše';
      case 'dalmatia':
        return 'Dalmácie';
      case 'istria':
        return 'Istrie';
      case 'slavonia':
        return 'Slavonie';
      case 'zagreb':
        return 'Zagreb';
      case 'lika':
        return 'Lika';
      case 'traditional':
        return 'Tradiční';
      case 'seafood':
        return 'Mořské plody';
      case 'mediterranean':
        return 'Středomořská';
      case 'international':
        return 'Mezinárodní';
      case 'budget':
        return 'Levné (€)';
      case 'mid':
        return 'Střední (€€)';
      case 'upscale':
        return 'Dražší (€€€)';
      case 'fine':
        return 'Fine dining (€€€€)';
      default:
        return value;
    }
  }

  Widget _buildRecommendedSection() {
    final recommended = _allRestaurants
        .where((r) => r.rating >= 4.5)
        .take(3)
        .toList();

    return Container(
      child: CustomPaint(
        painter: WatercolorRestaurantRecommendedPainter(
          const Color(0xFF2E8B8B),
        ),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Doporučené restaurace',
                style: GoogleFonts.playfairDisplay(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2E8B8B),
                ),
              ),
              const SizedBox(height: 16),
              ...recommended.map(
                (restaurant) =>
                    _buildRestaurantCard(restaurant, isCompact: true),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAllRestaurantsTab() {
    return Column(
      children: [
        // Search bar
        Container(
          padding: const EdgeInsets.all(16),
          child: TextField(
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
                _applyFilters();
              });
            },
            decoration: InputDecoration(
              hintText: 'Hledat restaurace...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
          ),
        ),

        // Results
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _filteredRestaurants.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.restaurant, size: 64, color: Colors.grey[400]),
                      const SizedBox(height: 16),
                      Text(
                        'Žádné restaurace nenalezeny',
                        style: GoogleFonts.inter(
                          fontSize: 18,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _filteredRestaurants.length,
                  itemBuilder: (context, index) {
                    return _buildRestaurantCard(_filteredRestaurants[index]);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildTopRatedTab() {
    final topRated = _allRestaurants.where((r) => r.rating >= 4.5).toList();
    topRated.sort((a, b) => b.rating.compareTo(a.rating));

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: topRated.length,
      itemBuilder: (context, index) {
        return _buildRestaurantCard(topRated[index], showRank: index + 1);
      },
    );
  }

  Widget _buildTraditionalTab() {
    final traditional = _allRestaurants
        .where((r) => r.cuisineType == 'traditional')
        .toList();

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: traditional.length,
      itemBuilder: (context, index) {
        return _buildRestaurantCard(
          traditional[index],
          showTraditionalBadge: true,
        );
      },
    );
  }

  Widget _buildMapTab() {
    return Container(
      child: CustomPaint(
        painter: WatercolorRestaurantMapPainter(),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.map, size: 64, color: const Color(0xFF006994)),
              const SizedBox(height: 16),
              Text(
                'Mapa restaurací',
                style: GoogleFonts.playfairDisplay(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF006994),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Připravujeme interaktivní mapu\ns watercolor designem',
                style: GoogleFonts.inter(
                  fontSize: 16,
                  color: const Color(0xFF666666),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRestaurantCard(
    Restaurant restaurant, {
    bool isCompact = false,
    int? showRank,
    bool showTraditionalBadge = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: CustomPaint(
        painter: WatercolorRestaurantCardPainter(
          _getCuisineColor(restaurant.cuisineType),
        ),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  if (showRank != null) ...[
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: const Color(0xFF006994),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          '$showRank',
                          style: GoogleFonts.inter(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                  ],

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                restaurant.name,
                                style: GoogleFonts.playfairDisplay(
                                  fontSize: isCompact ? 16 : 18,
                                  fontWeight: FontWeight.bold,
                                  color: const Color(0xFF2C2C2C),
                                ),
                              ),
                            ),
                            if (showTraditionalBadge)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(
                                    0xFF2E8B8B,
                                  ).withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'Tradiční',
                                  style: GoogleFonts.inter(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFF2E8B8B),
                                  ),
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(Icons.star, size: 16, color: Colors.amber),
                            const SizedBox(width: 4),
                            Text(
                              restaurant.rating.toStringAsFixed(1),
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF2C2C2C),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '(${restaurant.reviewCount} recenzí)',
                              style: GoogleFonts.inter(
                                fontSize: 12,
                                color: const Color(0xFF666666),
                              ),
                            ),
                            const Spacer(),
                            Text(
                              _getPriceRangeLabel(restaurant.priceRange),
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF4CAF50),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: restaurant.isOpenNow
                          ? const Color(0xFF4CAF50)
                          : const Color(0xFFFF6B35),
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ),

              if (!isCompact) ...[
                const SizedBox(height: 12),

                // Description
                Text(
                  restaurant.description,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    color: const Color(0xFF666666),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 12),

                // Specialties
                if (restaurant.specialties.isNotEmpty) ...[
                  Wrap(
                    spacing: 6,
                    runSpacing: 6,
                    children: restaurant.specialties
                        .take(3)
                        .map(
                          (specialty) => Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: _getCuisineColor(
                                restaurant.cuisineType,
                              ).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              specialty,
                              style: GoogleFonts.inter(
                                fontSize: 12,
                                color: _getCuisineColor(restaurant.cuisineType),
                              ),
                            ),
                          ),
                        )
                        .toList(),
                  ),
                  const SizedBox(height: 12),
                ],

                // Contact info
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 16,
                      color: const Color(0xFF666666),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        restaurant.address,
                        style: GoogleFonts.inter(
                          fontSize: 12,
                          color: const Color(0xFF666666),
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => _callRestaurant(restaurant.phone),
                      icon: const Icon(Icons.phone, size: 20),
                      color: const Color(0xFF4CAF50),
                    ),
                    IconButton(
                      onPressed: () => _openWebsite(restaurant.website),
                      icon: const Icon(Icons.web, size: 20),
                      color: const Color(0xFF006994),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getCuisineColor(String cuisineType) {
    switch (cuisineType) {
      case 'traditional':
        return const Color(0xFF2E8B8B);
      case 'seafood':
        return const Color(0xFF006994);
      case 'mediterranean':
        return const Color(0xFF4CAF50);
      case 'international':
        return const Color(0xFFFF6B35);
      default:
        return const Color(0xFF666666);
    }
  }

  String _getPriceRangeLabel(String priceRange) {
    switch (priceRange) {
      case 'budget':
        return '€';
      case 'mid':
        return '€€';
      case 'upscale':
        return '€€€';
      case 'fine':
        return '€€€€';
      default:
        return '€€';
    }
  }

  void _callRestaurant(String phone) {
    // Implementace volání
    debugPrint('Volání: $phone');
  }

  void _openWebsite(String? website) {
    if (website != null) {
      // Implementace otevření webu
      debugPrint('Otevírání: $website');
    }
  }
}

// Watercolor painters pro Restaurant Discovery Screen
class WatercolorRestaurantHeaderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor vlny pro Restaurant header
    final path1 = Path();
    path1.moveTo(0, size.height * 0.3);
    path1.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.1,
      size.width * 0.5,
      size.height * 0.4,
    );
    path1.quadraticBezierTo(
      size.width * 0.75,
      size.height * 0.7,
      size.width,
      size.height * 0.2,
    );
    path1.lineTo(size.width, size.height);
    path1.lineTo(0, size.height);
    path1.close();

    paint.color = const Color(0xFFFF6B35).withValues(alpha: 0.2);
    canvas.drawPath(path1, paint);

    // Druhá vrstva
    final path2 = Path();
    path2.moveTo(0, size.height * 0.5);
    path2.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.2,
      size.width * 0.7,
      size.height * 0.6,
    );
    path2.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.8,
      size.width,
      size.height * 0.4,
    );
    path2.lineTo(size.width, size.height);
    path2.lineTo(0, size.height);
    path2.close();

    paint.color = const Color(0xFF4CAF50).withValues(alpha: 0.15);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorRestaurantBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Jemný watercolor efekt pro pozadí restaurant discovery
    final path = Path();
    path.moveTo(size.width * 0.1, size.height * 0.05);
    path.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.02,
      size.width * 0.9,
      size.height * 0.08,
    );
    path.lineTo(size.width * 0.95, size.height * 0.95);
    path.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.98,
      size.width * 0.05,
      size.height * 0.92,
    );
    path.close();

    paint.color = const Color(0xFFFF6B35).withValues(alpha: 0.03);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorRestaurantMainCardPainter extends CustomPainter {
  final Color color;

  WatercolorRestaurantMainCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro hlavní restaurant kartu
    final path = Path();
    path.moveTo(size.width * 0.05, size.height * 0.1);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.02,
      size.width * 0.7,
      size.height * 0.08,
    );
    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.15,
      size.width * 0.98,
      size.height * 0.9,
    );
    path.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.98,
      size.width * 0.3,
      size.height * 0.92,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.85,
      size.width * 0.05,
      size.height * 0.1,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.1);
    canvas.drawPath(path, paint);

    // Druhá vrstva pro hloubku
    final path2 = Path();
    path2.moveTo(size.width * 0.1, size.height * 0.2);
    path2.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.05,
      size.width * 0.9,
      size.height * 0.15,
    );
    path2.lineTo(size.width * 0.85, size.height * 0.8);
    path2.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.95,
      size.width * 0.15,
      size.height * 0.85,
    );
    path2.close();

    paint.color = color.withValues(alpha: 0.05);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorRestaurantSearchPainter extends CustomPainter {
  final Color color;

  WatercolorRestaurantSearchPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro search sekci
    final path = Path();
    path.moveTo(size.width * 0.04, size.height * 0.15);
    path.quadraticBezierTo(
      size.width * 0.35,
      size.height * 0.03,
      size.width * 0.75,
      size.height * 0.12,
    );
    path.quadraticBezierTo(
      size.width * 0.96,
      size.height * 0.21,
      size.width * 0.94,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.65,
      size.height * 0.97,
      size.width * 0.25,
      size.height * 0.88,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.79,
      size.width * 0.04,
      size.height * 0.15,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorRestaurantStatCardPainter extends CustomPainter {
  final Color color;

  WatercolorRestaurantStatCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro stat karty
    final path = Path();
    path.moveTo(size.width * 0.08, size.height * 0.15);
    path.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.05,
      size.width * 0.8,
      size.height * 0.12,
    );
    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.2,
      size.width * 0.92,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.95,
      size.width * 0.2,
      size.height * 0.88,
    );
    path.quadraticBezierTo(
      size.width * 0.05,
      size.height * 0.8,
      size.width * 0.08,
      size.height * 0.15,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.12);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorRestaurantIconPainter extends CustomPainter {
  final Color color;

  WatercolorRestaurantIconPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor kruh kolem restaurant ikony
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2.5;

    final path = Path();
    for (int i = 0; i < 360; i += 20) {
      final angle = i * pi / 180;
      final variation = 0.8 + (sin(i * pi / 60) * 0.2);
      final x = center.dx + (radius * variation) * cos(angle);
      final y = center.dy + (radius * variation) * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    paint.color = color.withValues(alpha: 0.2);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorRestaurantFilterChipPainter extends CustomPainter {
  final Color color;

  WatercolorRestaurantFilterChipPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro filter chipy
    final path = Path();
    path.moveTo(size.width * 0.1, size.height * 0.2);
    path.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.05,
      size.width * 0.8,
      size.height * 0.15,
    );
    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.25,
      size.width * 0.9,
      size.height * 0.8,
    );
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.95,
      size.width * 0.2,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.05,
      size.height * 0.75,
      size.width * 0.1,
      size.height * 0.2,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.1);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorRestaurantRecommendedPainter extends CustomPainter {
  final Color color;

  WatercolorRestaurantRecommendedPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro recommended sekci
    final path = Path();
    path.moveTo(size.width * 0.03, size.height * 0.12);
    path.quadraticBezierTo(
      size.width * 0.32,
      size.height * 0.04,
      size.width * 0.72,
      size.height * 0.1,
    );
    path.quadraticBezierTo(
      size.width * 0.97,
      size.height * 0.18,
      size.width * 0.95,
      size.height * 0.88,
    );
    path.quadraticBezierTo(
      size.width * 0.68,
      size.height * 0.96,
      size.width * 0.28,
      size.height * 0.9,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.82,
      size.width * 0.03,
      size.height * 0.12,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorRestaurantCardPainter extends CustomPainter {
  final Color color;

  WatercolorRestaurantCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro restaurant karty
    final path = Path();
    path.moveTo(size.width * 0.02, size.height * 0.18);
    path.quadraticBezierTo(
      size.width * 0.35,
      size.height * 0.06,
      size.width * 0.78,
      size.height * 0.14,
    );
    path.quadraticBezierTo(
      size.width * 0.98,
      size.height * 0.22,
      size.width * 0.96,
      size.height * 0.82,
    );
    path.quadraticBezierTo(
      size.width * 0.65,
      size.height * 0.94,
      size.width * 0.22,
      size.height * 0.86,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.54,
      size.width * 0.02,
      size.height * 0.18,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.1);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorRestaurantMapPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor efekt pro mapu restaurací
    final path1 = Path();
    path1.moveTo(size.width * 0.1, size.height * 0.1);
    path1.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.05,
      size.width * 0.9,
      size.height * 0.15,
    );
    path1.lineTo(size.width * 0.85, size.height * 0.9);
    path1.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.95,
      size.width * 0.15,
      size.height * 0.85,
    );
    path1.close();

    paint.color = const Color(0xFF006994).withValues(alpha: 0.05);
    canvas.drawPath(path1, paint);

    // Druhá vrstva
    final path2 = Path();
    path2.moveTo(size.width * 0.2, size.height * 0.2);
    path2.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.15,
      size.width * 0.8,
      size.height * 0.25,
    );
    path2.lineTo(size.width * 0.75, size.height * 0.8);
    path2.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.85,
      size.width * 0.25,
      size.height * 0.75,
    );
    path2.close();

    paint.color = const Color(0xFF4CAF50).withValues(alpha: 0.03);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
