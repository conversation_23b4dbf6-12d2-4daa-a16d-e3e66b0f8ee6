import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 🎬 INFLUENCER MARKETING SERVICE - Content Creator Economy
class InfluencerMarketingService {
  static final InfluencerMarketingService _instance =
      InfluencerMarketingService._internal();
  factory InfluencerMarketingService() => _instance;
  InfluencerMarketingService._internal();

  bool _isInitialized = false;
  final List<InfluencerProfile> _influencers = [];
  final List<Campaign> _campaigns = [];
  final List<ContentTemplate> _templates = [];
  final Map<String, InfluencerMetrics> _metrics = {};
  final StreamController<InfluencerEvent> _eventController =
      StreamController.broadcast();

  /// Stream influencer událostí
  Stream<InfluencerEvent> get influencerEvents => _eventController.stream;

  /// Inicializace služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🎬 Inicializuji Influencer Marketing Service...');

      await _loadInfluencerData();
      await _setupDefaultTemplates();
      await _setupDefaultCampaigns();

      _isInitialized = true;
      debugPrint('✅ Influencer Marketing Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Influencer Marketing: $e');
      rethrow;
    }
  }

  /// Registrace influencera
  Future<InfluencerProfile> registerInfluencer({
    required String userId,
    required String username,
    required String email,
    required List<SocialPlatform> platforms,
    required Map<SocialPlatform, SocialStats> socialStats,
    required List<String> niches,
    String? bio,
    String? location,
    String? website,
  }) async {
    try {
      debugPrint('🌟 Registruji influencera: $username');

      // Výpočet influencer tier podle follower count
      final tier = _calculateInfluencerTier(socialStats);

      final profile = InfluencerProfile(
        id: 'inf_${DateTime.now().millisecondsSinceEpoch}',
        userId: userId,
        username: username,
        email: email,
        bio: bio,
        location: location,
        website: website,
        platforms: platforms,
        socialStats: socialStats,
        niches: niches,
        tier: tier,
        isVerified: false,
        isActive: true,
        joinedAt: DateTime.now(),
        lastActiveAt: DateTime.now(),
      );

      _influencers.add(profile);

      // Inicializace metrik
      _metrics[profile.id] = InfluencerMetrics(
        influencerId: profile.id,
        totalCampaigns: 0,
        completedCampaigns: 0,
        totalEarnings: 0.0,
        averageEngagementRate: _calculateAverageEngagement(socialStats),
        totalReach: _calculateTotalReach(socialStats),
        contentCreated: 0,
        rating: 5.0,
      );

      await _saveInfluencerData();

      _eventController.add(
        InfluencerEvent(
          type: InfluencerEventType.influencerRegistered,
          influencerId: profile.id,
          timestamp: DateTime.now(),
        ),
      );

      debugPrint('✅ Influencer registrován: ${profile.username}');
      return profile;
    } catch (e) {
      debugPrint('❌ Chyba při registraci influencera: $e');
      rethrow;
    }
  }

  /// Vytvoření influencer kampaně
  Future<Campaign> createCampaign({
    required String title,
    required String description,
    required CampaignType type,
    required List<String> requirements,
    required Map<String, dynamic> compensation,
    required DateTime startDate,
    required DateTime endDate,
    required List<String> targetNiches,
    required InfluencerTier minTier,
    String? location,
    int? maxParticipants,
  }) async {
    try {
      debugPrint('📢 Vytvářím kampaň: $title');

      final campaign = Campaign(
        id: 'camp_${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        description: description,
        type: type,
        requirements: requirements,
        compensation: compensation,
        startDate: startDate,
        endDate: endDate,
        targetNiches: targetNiches,
        minTier: minTier,
        location: location,
        maxParticipants: maxParticipants,
        status: CampaignStatus.active,
        participants: [],
        createdAt: DateTime.now(),
      );

      _campaigns.add(campaign);
      await _saveInfluencerData();

      // Notifikace vhodným influencerům
      await _notifyEligibleInfluencers(campaign);

      _eventController.add(
        InfluencerEvent(
          type: InfluencerEventType.campaignCreated,
          campaignId: campaign.id,
          timestamp: DateTime.now(),
        ),
      );

      debugPrint('✅ Kampaň vytvořena: ${campaign.title}');
      return campaign;
    } catch (e) {
      debugPrint('❌ Chyba při vytváření kampaně: $e');
      rethrow;
    }
  }

  /// Přihlášení do kampaně
  Future<bool> applyToCampaign({
    required String influencerId,
    required String campaignId,
    String? message,
    Map<String, dynamic>? proposedContent,
  }) async {
    try {
      debugPrint('📝 Influencer $influencerId se hlásí do kampaně $campaignId');

      final campaignIndex = _campaigns.indexWhere((c) => c.id == campaignId);
      if (campaignIndex == -1) return false;

      final campaign = _campaigns[campaignIndex];
      final influencer = _influencers.firstWhere((i) => i.id == influencerId);

      // Kontrola oprávnění
      if (!_isEligibleForCampaign(influencer, campaign)) {
        debugPrint('❌ Influencer nesplňuje požadavky kampaně');
        return false;
      }

      // Kontrola kapacity
      if (campaign.maxParticipants != null &&
          campaign.participants.length >= campaign.maxParticipants!) {
        debugPrint('❌ Kampaň je plná');
        return false;
      }

      final application = CampaignApplication(
        influencerId: influencerId,
        campaignId: campaignId,
        message: message,
        proposedContent: proposedContent,
        status: ApplicationStatus.pending,
        appliedAt: DateTime.now(),
      );

      // Přidání do participants
      _campaigns[campaignIndex] = campaign.copyWith(
        participants: [...campaign.participants, application],
      );

      await _saveInfluencerData();

      _eventController.add(
        InfluencerEvent(
          type: InfluencerEventType.campaignApplicationSubmitted,
          influencerId: influencerId,
          campaignId: campaignId,
          timestamp: DateTime.now(),
        ),
      );

      return true;
    } catch (e) {
      debugPrint('❌ Chyba při přihlašování do kampaně: $e');
      return false;
    }
  }

  /// Schválení/zamítnutí přihlášky
  Future<bool> reviewApplication({
    required String campaignId,
    required String influencerId,
    required ApplicationStatus newStatus,
    String? feedback,
  }) async {
    try {
      final campaignIndex = _campaigns.indexWhere((c) => c.id == campaignId);
      if (campaignIndex == -1) return false;

      final campaign = _campaigns[campaignIndex];
      final applicationIndex = campaign.participants.indexWhere(
        (p) => p.influencerId == influencerId,
      );
      if (applicationIndex == -1) return false;

      final updatedParticipants = [...campaign.participants];
      updatedParticipants[applicationIndex] =
          updatedParticipants[applicationIndex].copyWith(
            status: newStatus,
            feedback: feedback,
            reviewedAt: DateTime.now(),
          );

      _campaigns[campaignIndex] = campaign.copyWith(
        participants: updatedParticipants,
      );

      await _saveInfluencerData();

      _eventController.add(
        InfluencerEvent(
          type: newStatus == ApplicationStatus.approved
              ? InfluencerEventType.campaignApplicationApproved
              : InfluencerEventType.campaignApplicationRejected,
          influencerId: influencerId,
          campaignId: campaignId,
          timestamp: DateTime.now(),
        ),
      );

      return true;
    } catch (e) {
      debugPrint('❌ Chyba při review aplikace: $e');
      return false;
    }
  }

  /// Vytvoření content template
  Future<ContentTemplate> createContentTemplate({
    required String name,
    required String description,
    required ContentType contentType,
    required List<SocialPlatform> platforms,
    required Map<String, dynamic> templateData,
    List<String>? requiredElements,
    List<String>? suggestedHashtags,
  }) async {
    try {
      final template = ContentTemplate(
        id: 'template_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        description: description,
        contentType: contentType,
        platforms: platforms,
        templateData: templateData,
        requiredElements: requiredElements ?? [],
        suggestedHashtags: suggestedHashtags ?? [],
        isActive: true,
        createdAt: DateTime.now(),
      );

      _templates.add(template);
      await _saveInfluencerData();

      return template;
    } catch (e) {
      debugPrint('❌ Chyba při vytváření template: $e');
      rethrow;
    }
  }

  /// Generování content brief pro influencera
  Future<ContentBrief> generateContentBrief({
    required String campaignId,
    required String influencerId,
    String? templateId,
  }) async {
    try {
      final campaign = _campaigns.firstWhere((c) => c.id == campaignId);
      final influencer = _influencers.firstWhere((i) => i.id == influencerId);

      ContentTemplate? template;
      if (templateId != null) {
        template = _templates.firstWhere((t) => t.id == templateId);
      }

      final brief = ContentBrief(
        id: 'brief_${DateTime.now().millisecondsSinceEpoch}',
        campaignId: campaignId,
        influencerId: influencerId,
        templateId: templateId,
        title: 'Content Brief - ${campaign.title}',
        description: campaign.description,
        requirements: campaign.requirements,
        suggestedContent: _generateContentSuggestions(campaign, influencer),
        hashtags: _generateCampaignHashtags(campaign),
        deadline: campaign.endDate,
        compensation: campaign.compensation,
        brandGuidelines: _getBrandGuidelines(),
        createdAt: DateTime.now(),
      );

      return brief;
    } catch (e) {
      debugPrint('❌ Chyba při generování content brief: $e');
      rethrow;
    }
  }

  /// Tracking výkonu influencera
  Future<InfluencerPerformance> trackInfluencerPerformance({
    required String influencerId,
    required String campaignId,
    required Map<String, dynamic> performanceData,
  }) async {
    try {
      final performance = InfluencerPerformance(
        id: 'perf_${DateTime.now().millisecondsSinceEpoch}',
        influencerId: influencerId,
        campaignId: campaignId,
        contentUrl: performanceData['contentUrl'],
        platform: performanceData['platform'],
        publishedAt: DateTime.parse(performanceData['publishedAt']),
        metrics: PerformanceMetrics(
          views: performanceData['views'] ?? 0,
          likes: performanceData['likes'] ?? 0,
          comments: performanceData['comments'] ?? 0,
          shares: performanceData['shares'] ?? 0,
          saves: performanceData['saves'] ?? 0,
          clickThroughs: performanceData['clickThroughs'] ?? 0,
          engagementRate: performanceData['engagementRate'] ?? 0.0,
        ),
        createdAt: DateTime.now(),
      );

      // Aktualizace influencer metrik
      await _updateInfluencerMetrics(influencerId, performance);

      _eventController.add(
        InfluencerEvent(
          type: InfluencerEventType.contentPerformanceTracked,
          influencerId: influencerId,
          campaignId: campaignId,
          timestamp: DateTime.now(),
        ),
      );

      return performance;
    } catch (e) {
      debugPrint('❌ Chyba při tracking výkonu: $e');
      rethrow;
    }
  }

  /// Získání vhodných influencerů pro kampaň
  Future<List<InfluencerMatch>> findInfluencersForCampaign({
    required String campaignId,
    int limit = 20,
  }) async {
    try {
      final campaign = _campaigns.firstWhere((c) => c.id == campaignId);
      final matches = <InfluencerMatch>[];

      for (final influencer in _influencers) {
        if (_isEligibleForCampaign(influencer, campaign)) {
          final score = _calculateMatchScore(influencer, campaign);
          matches.add(
            InfluencerMatch(
              influencer: influencer,
              campaign: campaign,
              matchScore: score,
              reasons: _getMatchReasons(influencer, campaign),
              estimatedReach: _estimateReach(influencer),
              estimatedEngagement: _estimateEngagement(influencer),
            ),
          );
        }
      }

      // Seřazení podle match score
      matches.sort((a, b) => b.matchScore.compareTo(a.matchScore));

      return matches.take(limit).toList();
    } catch (e) {
      debugPrint('❌ Chyba při hledání influencerů: $e');
      return [];
    }
  }

  /// Získání influencer leaderboard
  Future<List<InfluencerLeaderboardEntry>> getInfluencerLeaderboard({
    LeaderboardMetric metric = LeaderboardMetric.totalEarnings,
    int limit = 10,
  }) async {
    final entries = <InfluencerLeaderboardEntry>[];

    for (final influencer in _influencers) {
      final metrics = _metrics[influencer.id];
      if (metrics != null) {
        entries.add(
          InfluencerLeaderboardEntry(
            influencer: influencer,
            metrics: metrics,
            rank: 0, // Bude vypočítáno po seřazení
          ),
        );
      }
    }

    // Seřazení podle metriky
    switch (metric) {
      case LeaderboardMetric.totalEarnings:
        entries.sort(
          (a, b) => b.metrics.totalEarnings.compareTo(a.metrics.totalEarnings),
        );
        break;
      case LeaderboardMetric.completedCampaigns:
        entries.sort(
          (a, b) => b.metrics.completedCampaigns.compareTo(
            a.metrics.completedCampaigns,
          ),
        );
        break;
      case LeaderboardMetric.engagementRate:
        entries.sort(
          (a, b) => b.metrics.averageEngagementRate.compareTo(
            a.metrics.averageEngagementRate,
          ),
        );
        break;
      case LeaderboardMetric.rating:
        entries.sort((a, b) => b.metrics.rating.compareTo(a.metrics.rating));
        break;
    }

    // Přiřazení ranků
    for (int i = 0; i < entries.length; i++) {
      entries[i] = entries[i].copyWith(rank: i + 1);
    }

    return entries.take(limit).toList();
  }

  /// Výpočet kompenzace pro influencera
  Map<String, dynamic> calculateCompensation({
    required InfluencerProfile influencer,
    required Campaign campaign,
    required ContentType contentType,
  }) {
    final baseRate = _getBaseRate(influencer.tier, contentType);
    final reachMultiplier = _getReachMultiplier(influencer);
    final engagementBonus = _getEngagementBonus(influencer);
    final nicheBonus = _getNicheBonus(influencer, campaign);

    final totalAmount =
        baseRate * reachMultiplier * (1 + engagementBonus + nicheBonus);

    return {
      'baseRate': baseRate,
      'reachMultiplier': reachMultiplier,
      'engagementBonus': engagementBonus,
      'nicheBonus': nicheBonus,
      'totalAmount': totalAmount.round(),
      'currency': 'HRK',
      'paymentTerms': 'Net 30',
    };
  }

  /// Pomocné metody
  InfluencerTier _calculateInfluencerTier(
    Map<SocialPlatform, SocialStats> socialStats,
  ) {
    final maxFollowers = socialStats.values
        .map((stats) => stats.followers)
        .reduce((a, b) => a > b ? a : b);

    if (maxFollowers >= 1000000) return InfluencerTier.mega;
    if (maxFollowers >= 100000) return InfluencerTier.macro;
    if (maxFollowers >= 10000) return InfluencerTier.micro;
    if (maxFollowers >= 1000) return InfluencerTier.nano;
    return InfluencerTier.nano;
  }

  double _calculateAverageEngagement(
    Map<SocialPlatform, SocialStats> socialStats,
  ) {
    if (socialStats.isEmpty) return 0.0;

    final engagementRates = socialStats.values.map(
      (stats) => stats.engagementRate,
    );
    return engagementRates.reduce((a, b) => a + b) / engagementRates.length;
  }

  int _calculateTotalReach(Map<SocialPlatform, SocialStats> socialStats) {
    return socialStats.values
        .map((stats) => stats.followers)
        .reduce((a, b) => a + b);
  }

  bool _isEligibleForCampaign(InfluencerProfile influencer, Campaign campaign) {
    // Kontrola tier
    if (influencer.tier.index < campaign.minTier.index) return false;

    // Kontrola niche
    final hasMatchingNiche = influencer.niches.any(
      (niche) => campaign.targetNiches.contains(niche),
    );
    if (!hasMatchingNiche) return false;

    // Kontrola lokace
    if (campaign.location != null && influencer.location != campaign.location)
      return false;

    return true;
  }

  double _calculateMatchScore(InfluencerProfile influencer, Campaign campaign) {
    double score = 0.0;

    // Niche match (40%)
    final nicheMatches = influencer.niches
        .where((niche) => campaign.targetNiches.contains(niche))
        .length;
    score += (nicheMatches / campaign.targetNiches.length) * 0.4;

    // Engagement rate (30%)
    final metrics = _metrics[influencer.id];
    if (metrics != null) {
      score +=
          (metrics.averageEngagementRate / 0.1) * 0.3; // Normalizováno na 10%
    }

    // Tier match (20%)
    score += (influencer.tier.index / InfluencerTier.values.length) * 0.2;

    // Location match (10%)
    if (campaign.location == null || influencer.location == campaign.location) {
      score += 0.1;
    }

    return score.clamp(0.0, 1.0);
  }

  List<String> _getMatchReasons(
    InfluencerProfile influencer,
    Campaign campaign,
  ) {
    final reasons = <String>[];

    final nicheMatches = influencer.niches.where(
      (niche) => campaign.targetNiches.contains(niche),
    );
    if (nicheMatches.isNotEmpty) {
      reasons.add('Matching niches: ${nicheMatches.join(', ')}');
    }

    if (campaign.location == null || influencer.location == campaign.location) {
      reasons.add('Location match');
    }

    final metrics = _metrics[influencer.id];
    if (metrics != null && metrics.averageEngagementRate > 0.05) {
      reasons.add(
        'High engagement rate (${(metrics.averageEngagementRate * 100).toStringAsFixed(1)}%)',
      );
    }

    return reasons;
  }

  int _estimateReach(InfluencerProfile influencer) {
    return influencer.socialStats.values
        .map((stats) => (stats.followers * 0.3).round()) // 30% reach estimate
        .reduce((a, b) => a + b);
  }

  int _estimateEngagement(InfluencerProfile influencer) {
    final metrics = _metrics[influencer.id];
    if (metrics == null) return 0;

    return (_estimateReach(influencer) * metrics.averageEngagementRate).round();
  }

  double _getBaseRate(InfluencerTier tier, ContentType contentType) {
    final baseRates = {
      InfluencerTier.nano: 500.0,
      InfluencerTier.micro: 2000.0,
      InfluencerTier.macro: 10000.0,
      InfluencerTier.mega: 50000.0,
    };

    final multipliers = {
      ContentType.instagramPost: 1.0,
      ContentType.instagramStory: 0.5,
      ContentType.tiktokVideo: 1.2,
      ContentType.youtubeVideo: 2.0,
      ContentType.blogPost: 1.5,
    };

    return (baseRates[tier] ?? 500.0) * (multipliers[contentType] ?? 1.0);
  }

  double _getReachMultiplier(InfluencerProfile influencer) {
    final totalReach = _calculateTotalReach(influencer.socialStats);
    return 1.0 + (totalReach / 100000) * 0.1; // 10% bonus per 100k followers
  }

  double _getEngagementBonus(InfluencerProfile influencer) {
    final metrics = _metrics[influencer.id];
    if (metrics == null) return 0.0;

    // Bonus za vysoký engagement
    if (metrics.averageEngagementRate > 0.1) return 0.5; // 50% bonus
    if (metrics.averageEngagementRate > 0.05) return 0.25; // 25% bonus
    return 0.0;
  }

  double _getNicheBonus(InfluencerProfile influencer, Campaign campaign) {
    final nicheMatches = influencer.niches
        .where((niche) => campaign.targetNiches.contains(niche))
        .length;
    return nicheMatches * 0.1; // 10% bonus per matching niche
  }

  List<String> _generateContentSuggestions(
    Campaign campaign,
    InfluencerProfile influencer,
  ) {
    return [
      'Share your experience with Croatia Travel App',
      'Create a day-in-the-life content featuring the app',
      'Show how the app helps with travel planning',
      'Highlight your favorite app features',
    ];
  }

  List<String> _generateCampaignHashtags(Campaign campaign) {
    return [
      '#CroatiaTravel',
      '#TravelApp',
      '#Croatia',
      '#TravelDiary',
      '#Sponsored',
      '#Ad',
    ];
  }

  Map<String, dynamic> _getBrandGuidelines() {
    return {
      'colors': ['#006994', '#FF6B35', '#8FBC8F'],
      'fonts': ['Inter', 'Playfair Display'],
      'tone': 'Friendly, authentic, inspiring',
      'doNots': ['No competitor mentions', 'No negative content'],
      'mustInclude': ['App logo', 'Watercolor aesthetic'],
    };
  }

  Future<void> _notifyEligibleInfluencers(Campaign campaign) async {
    // Notifikace vhodným influencerům
    debugPrint('📧 Notifikuji vhodné influencery o nové kampani');
  }

  Future<void> _updateInfluencerMetrics(
    String influencerId,
    InfluencerPerformance performance,
  ) async {
    final current = _metrics[influencerId];
    if (current == null) return;

    _metrics[influencerId] = InfluencerMetrics(
      influencerId: influencerId,
      totalCampaigns: current.totalCampaigns,
      completedCampaigns: current.completedCampaigns + 1,
      totalEarnings: current.totalEarnings,
      averageEngagementRate:
          (current.averageEngagementRate + performance.metrics.engagementRate) /
          2,
      totalReach: current.totalReach + performance.metrics.views,
      contentCreated: current.contentCreated + 1,
      rating: current.rating,
    );
  }

  Future<void> _setupDefaultTemplates() async {
    if (_templates.isNotEmpty) return;

    _templates.addAll([
      ContentTemplate(
        id: 'template_ig_post',
        name: 'Instagram Post Template',
        description: 'Standard Instagram post for Croatia Travel App',
        contentType: ContentType.instagramPost,
        platforms: [SocialPlatform.instagram],
        templateData: {
          'aspectRatio': '1:1',
          'minResolution': '1080x1080',
          'requiredElements': ['App screenshot', 'Watercolor overlay'],
        },
        requiredElements: ['App logo', 'Hashtags', 'Call to action'],
        suggestedHashtags: ['#CroatiaTravel', '#TravelApp', '#Croatia'],
        isActive: true,
        createdAt: DateTime.now(),
      ),
    ]);
  }

  Future<void> _setupDefaultCampaigns() async {
    if (_campaigns.isNotEmpty) return;

    // Setup default campaigns
  }

  Future<void> _loadInfluencerData() async {
    // Load influencer data
  }

  Future<void> _saveInfluencerData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'influencers',
        jsonEncode(_influencers.map((i) => i.toJson()).toList()),
      );
      await prefs.setString(
        'campaigns',
        jsonEncode(_campaigns.map((c) => c.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání influencer dat: $e');
    }
  }

  @override
  void dispose() {
    _eventController.close();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  List<InfluencerProfile> get influencers => List.unmodifiable(_influencers);
  List<Campaign> get campaigns => List.unmodifiable(_campaigns);
  List<ContentTemplate> get templates => List.unmodifiable(_templates);
}

/// Modely pro influencer marketing
class InfluencerProfile {
  final String id;
  final String userId;
  final String username;
  final String email;
  final String? bio;
  final String? location;
  final String? website;
  final List<SocialPlatform> platforms;
  final Map<SocialPlatform, SocialStats> socialStats;
  final List<String> niches;
  final InfluencerTier tier;
  final bool isVerified;
  final bool isActive;
  final DateTime joinedAt;
  final DateTime lastActiveAt;

  InfluencerProfile({
    required this.id,
    required this.userId,
    required this.username,
    required this.email,
    this.bio,
    this.location,
    this.website,
    required this.platforms,
    required this.socialStats,
    required this.niches,
    required this.tier,
    required this.isVerified,
    required this.isActive,
    required this.joinedAt,
    required this.lastActiveAt,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'userId': userId,
    'username': username,
    'email': email,
    'bio': bio,
    'location': location,
    'website': website,
    'platforms': platforms.map((p) => p.name).toList(),
    'socialStats': socialStats.map((k, v) => MapEntry(k.name, v.toJson())),
    'niches': niches,
    'tier': tier.name,
    'isVerified': isVerified,
    'isActive': isActive,
    'joinedAt': joinedAt.toIso8601String(),
    'lastActiveAt': lastActiveAt.toIso8601String(),
  };
}

class Campaign {
  final String id;
  final String title;
  final String description;
  final CampaignType type;
  final List<String> requirements;
  final Map<String, dynamic> compensation;
  final DateTime startDate;
  final DateTime endDate;
  final List<String> targetNiches;
  final InfluencerTier minTier;
  final String? location;
  final int? maxParticipants;
  final CampaignStatus status;
  final List<CampaignApplication> participants;
  final DateTime createdAt;

  Campaign({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.requirements,
    required this.compensation,
    required this.startDate,
    required this.endDate,
    required this.targetNiches,
    required this.minTier,
    this.location,
    this.maxParticipants,
    required this.status,
    required this.participants,
    required this.createdAt,
  });

  Campaign copyWith({
    String? id,
    String? title,
    String? description,
    CampaignType? type,
    List<String>? requirements,
    Map<String, dynamic>? compensation,
    DateTime? startDate,
    DateTime? endDate,
    List<String>? targetNiches,
    InfluencerTier? minTier,
    String? location,
    int? maxParticipants,
    CampaignStatus? status,
    List<CampaignApplication>? participants,
    DateTime? createdAt,
  }) {
    return Campaign(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      requirements: requirements ?? this.requirements,
      compensation: compensation ?? this.compensation,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      targetNiches: targetNiches ?? this.targetNiches,
      minTier: minTier ?? this.minTier,
      location: location ?? this.location,
      maxParticipants: maxParticipants ?? this.maxParticipants,
      status: status ?? this.status,
      participants: participants ?? this.participants,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'description': description,
    'type': type.name,
    'requirements': requirements,
    'compensation': compensation,
    'startDate': startDate.toIso8601String(),
    'endDate': endDate.toIso8601String(),
    'targetNiches': targetNiches,
    'minTier': minTier.name,
    'location': location,
    'maxParticipants': maxParticipants,
    'status': status.name,
    'participants': participants.map((p) => p.toJson()).toList(),
    'createdAt': createdAt.toIso8601String(),
  };
}

class CampaignApplication {
  final String influencerId;
  final String campaignId;
  final String? message;
  final Map<String, dynamic>? proposedContent;
  final ApplicationStatus status;
  final DateTime appliedAt;
  final String? feedback;
  final DateTime? reviewedAt;

  CampaignApplication({
    required this.influencerId,
    required this.campaignId,
    this.message,
    this.proposedContent,
    required this.status,
    required this.appliedAt,
    this.feedback,
    this.reviewedAt,
  });

  CampaignApplication copyWith({
    String? influencerId,
    String? campaignId,
    String? message,
    Map<String, dynamic>? proposedContent,
    ApplicationStatus? status,
    DateTime? appliedAt,
    String? feedback,
    DateTime? reviewedAt,
  }) {
    return CampaignApplication(
      influencerId: influencerId ?? this.influencerId,
      campaignId: campaignId ?? this.campaignId,
      message: message ?? this.message,
      proposedContent: proposedContent ?? this.proposedContent,
      status: status ?? this.status,
      appliedAt: appliedAt ?? this.appliedAt,
      feedback: feedback ?? this.feedback,
      reviewedAt: reviewedAt ?? this.reviewedAt,
    );
  }

  Map<String, dynamic> toJson() => {
    'influencerId': influencerId,
    'campaignId': campaignId,
    'message': message,
    'proposedContent': proposedContent,
    'status': status.name,
    'appliedAt': appliedAt.toIso8601String(),
    'feedback': feedback,
    'reviewedAt': reviewedAt?.toIso8601String(),
  };
}

class ContentTemplate {
  final String id;
  final String name;
  final String description;
  final ContentType contentType;
  final List<SocialPlatform> platforms;
  final Map<String, dynamic> templateData;
  final List<String> requiredElements;
  final List<String> suggestedHashtags;
  final bool isActive;
  final DateTime createdAt;

  ContentTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.contentType,
    required this.platforms,
    required this.templateData,
    required this.requiredElements,
    required this.suggestedHashtags,
    required this.isActive,
    required this.createdAt,
  });
}

class ContentBrief {
  final String id;
  final String campaignId;
  final String influencerId;
  final String? templateId;
  final String title;
  final String description;
  final List<String> requirements;
  final List<String> suggestedContent;
  final List<String> hashtags;
  final DateTime deadline;
  final Map<String, dynamic> compensation;
  final Map<String, dynamic> brandGuidelines;
  final DateTime createdAt;

  ContentBrief({
    required this.id,
    required this.campaignId,
    required this.influencerId,
    this.templateId,
    required this.title,
    required this.description,
    required this.requirements,
    required this.suggestedContent,
    required this.hashtags,
    required this.deadline,
    required this.compensation,
    required this.brandGuidelines,
    required this.createdAt,
  });
}

class InfluencerPerformance {
  final String id;
  final String influencerId;
  final String campaignId;
  final String? contentUrl;
  final SocialPlatform platform;
  final DateTime publishedAt;
  final PerformanceMetrics metrics;
  final DateTime createdAt;

  InfluencerPerformance({
    required this.id,
    required this.influencerId,
    required this.campaignId,
    this.contentUrl,
    required this.platform,
    required this.publishedAt,
    required this.metrics,
    required this.createdAt,
  });
}

class PerformanceMetrics {
  final int views;
  final int likes;
  final int comments;
  final int shares;
  final int saves;
  final int clickThroughs;
  final double engagementRate;

  PerformanceMetrics({
    required this.views,
    required this.likes,
    required this.comments,
    required this.shares,
    required this.saves,
    required this.clickThroughs,
    required this.engagementRate,
  });
}

class InfluencerMetrics {
  final String influencerId;
  final int totalCampaigns;
  final int completedCampaigns;
  final double totalEarnings;
  final double averageEngagementRate;
  final int totalReach;
  final int contentCreated;
  final double rating;

  InfluencerMetrics({
    required this.influencerId,
    required this.totalCampaigns,
    required this.completedCampaigns,
    required this.totalEarnings,
    required this.averageEngagementRate,
    required this.totalReach,
    required this.contentCreated,
    required this.rating,
  });
}

class InfluencerMatch {
  final InfluencerProfile influencer;
  final Campaign campaign;
  final double matchScore;
  final List<String> reasons;
  final int estimatedReach;
  final int estimatedEngagement;

  InfluencerMatch({
    required this.influencer,
    required this.campaign,
    required this.matchScore,
    required this.reasons,
    required this.estimatedReach,
    required this.estimatedEngagement,
  });
}

class InfluencerLeaderboardEntry {
  final InfluencerProfile influencer;
  final InfluencerMetrics metrics;
  final int rank;

  InfluencerLeaderboardEntry({
    required this.influencer,
    required this.metrics,
    required this.rank,
  });

  InfluencerLeaderboardEntry copyWith({
    InfluencerProfile? influencer,
    InfluencerMetrics? metrics,
    int? rank,
  }) {
    return InfluencerLeaderboardEntry(
      influencer: influencer ?? this.influencer,
      metrics: metrics ?? this.metrics,
      rank: rank ?? this.rank,
    );
  }
}

class SocialStats {
  final int followers;
  final double engagementRate;
  final int avgLikes;
  final int avgComments;

  SocialStats({
    required this.followers,
    required this.engagementRate,
    required this.avgLikes,
    required this.avgComments,
  });

  Map<String, dynamic> toJson() => {
    'followers': followers,
    'engagementRate': engagementRate,
    'avgLikes': avgLikes,
    'avgComments': avgComments,
  };
}

class InfluencerEvent {
  final InfluencerEventType type;
  final String? influencerId;
  final String? campaignId;
  final String? rewardId;
  final DateTime timestamp;

  InfluencerEvent({
    required this.type,
    this.influencerId,
    this.campaignId,
    this.rewardId,
    required this.timestamp,
  });
}

enum SocialPlatform { instagram, tiktok, youtube, facebook, twitter }

enum InfluencerTier { nano, micro, macro, mega }

enum CampaignType {
  productLaunch,
  brandAwareness,
  userGenerated,
  eventPromotion,
}

enum CampaignStatus { draft, active, paused, completed, cancelled }

enum ApplicationStatus { pending, approved, rejected, completed }

enum ContentType {
  instagramPost,
  instagramStory,
  tiktokVideo,
  youtubeVideo,
  blogPost,
}

enum LeaderboardMetric {
  totalEarnings,
  completedCampaigns,
  engagementRate,
  rating,
}

enum InfluencerEventType {
  influencerRegistered,
  campaignCreated,
  campaignApplicationSubmitted,
  campaignApplicationApproved,
  campaignApplicationRejected,
  contentPerformanceTracked,
}
