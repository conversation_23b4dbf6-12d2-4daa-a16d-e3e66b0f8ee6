import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/transportation_hub.dart';

/// Služba pro Transportation Hub - kompletní dopravní služby
/// Právně bezpečné řešení bez API závislostí
class TransportationHubService {
  static final TransportationHubService _instance = TransportationHubService._internal();
  factory TransportationHubService() => _instance;
  TransportationHubService._internal();

  // Cache pro dopravní služby
  List<CarRental>? _cachedCarRentals;
  List<ParkingLot>? _cachedParkingLots;
  List<TaxiService>? _cachedTaxiServices;
  List<TrafficInfo>? _cachedTrafficInfo;
  DateTime? _lastCacheUpdate;
  final Duration _cacheValidDuration = const Duration(hours: 2);

  /// Získá všechny autopůjčovny
  Future<List<CarRental>> getAllCarRentals() async {
    if (_isCacheValid() && _cachedCarRentals != null) {
      return _cachedCarRentals!;
    }

    try {
      await Future.delayed(const Duration(milliseconds: 600));
      
      _cachedCarRentals = _generateSampleCarRentals();
      _lastCacheUpdate = DateTime.now();
      
      return _cachedCarRentals!;
    } catch (e) {
      debugPrint('Chyba při načítání autopůjčoven: $e');
      return [];
    }
  }

  /// Získá všechna parkoviště
  Future<List<ParkingLot>> getAllParkingLots() async {
    if (_isCacheValid() && _cachedParkingLots != null) {
      return _cachedParkingLots!;
    }

    try {
      await Future.delayed(const Duration(milliseconds: 500));
      
      _cachedParkingLots = _generateSampleParkingLots();
      _lastCacheUpdate = DateTime.now();
      
      return _cachedParkingLots!;
    } catch (e) {
      debugPrint('Chyba při načítání parkovišť: $e');
      return [];
    }
  }

  /// Získá všechny taxi služby
  Future<List<TaxiService>> getAllTaxiServices() async {
    if (_isCacheValid() && _cachedTaxiServices != null) {
      return _cachedTaxiServices!;
    }

    try {
      await Future.delayed(const Duration(milliseconds: 400));
      
      _cachedTaxiServices = _generateSampleTaxiServices();
      _lastCacheUpdate = DateTime.now();
      
      return _cachedTaxiServices!;
    } catch (e) {
      debugPrint('Chyba při načítání taxi služeb: $e');
      return [];
    }
  }

  /// Získá aktuální dopravní informace
  Future<List<TrafficInfo>> getCurrentTrafficInfo() async {
    if (_isCacheValid() && _cachedTrafficInfo != null) {
      return _cachedTrafficInfo!;
    }

    try {
      await Future.delayed(const Duration(milliseconds: 300));
      
      _cachedTrafficInfo = _generateSampleTrafficInfo();
      _lastCacheUpdate = DateTime.now();
      
      return _cachedTrafficInfo!;
    } catch (e) {
      debugPrint('Chyba při načítání dopravních informací: $e');
      return [];
    }
  }

  /// Vyhledá autopůjčovny podle kritérií
  Future<List<CarRental>> searchCarRentals({
    String? query,
    String? region,
    bool? hasAirportPickup,
    bool? hasDelivery,
    double? latitude,
    double? longitude,
    double? maxDistance,
    double? minRating,
  }) async {
    final allRentals = await getAllCarRentals();
    
    return allRentals.where((rental) {
      if (query != null && query.isNotEmpty) {
        final searchLower = query.toLowerCase();
        if (!rental.name.toLowerCase().contains(searchLower) &&
            !rental.description.toLowerCase().contains(searchLower)) {
          return false;
        }
      }

      if (region != null && region != 'all' && rental.region != region) {
        return false;
      }

      if (hasAirportPickup == true && !rental.hasAirportPickup) return false;
      if (hasDelivery == true && !rental.hasDelivery) return false;

      if (latitude != null && longitude != null && maxDistance != null) {
        final distance = rental.distanceFrom(latitude, longitude);
        if (distance > maxDistance) return false;
      }

      if (minRating != null && rental.rating < minRating) return false;

      return true;
    }).toList();
  }

  /// Vyhledá parkoviště podle kritérií
  Future<List<ParkingLot>> searchParkingLots({
    String? query,
    String? region,
    ParkingType? parkingType,
    bool? hasElectricCharging,
    bool? hasDisabledAccess,
    double? latitude,
    double? longitude,
    double? maxDistance,
    double? maxHourlyRate,
  }) async {
    final allParkingLots = await getAllParkingLots();
    
    return allParkingLots.where((parking) {
      if (query != null && query.isNotEmpty) {
        final searchLower = query.toLowerCase();
        if (!parking.name.toLowerCase().contains(searchLower) &&
            !parking.description.toLowerCase().contains(searchLower)) {
          return false;
        }
      }

      if (region != null && region != 'all' && parking.region != region) {
        return false;
      }

      if (parkingType != null && parking.parkingType != parkingType) {
        return false;
      }

      if (hasElectricCharging == true && !parking.hasElectricCharging) return false;
      if (hasDisabledAccess == true && !parking.hasDisabledAccess) return false;

      if (latitude != null && longitude != null && maxDistance != null) {
        final distance = parking.distanceFrom(latitude, longitude);
        if (distance > maxDistance) return false;
      }

      if (maxHourlyRate != null && parking.hourlyRate > maxHourlyRate) return false;

      return true;
    }).toList();
  }

  /// Získá nejbližší dopravní služby
  Future<List<TransportationService>> getNearbyServices(
    double latitude,
    double longitude, {
    double maxDistance = 10.0,
    TransportationServiceType? serviceType,
    int limit = 20,
  }) async {
    final services = <TransportationService>[];
    
    // Převede všechny služby na jednotný formát
    final carRentals = await getAllCarRentals();
    final parkingLots = await getAllParkingLots();
    final taxiServices = await getAllTaxiServices();

    // Přidá autopůjčovny
    for (final rental in carRentals) {
      if (serviceType == null || serviceType == TransportationServiceType.carRental) {
        final distance = rental.distanceFrom(latitude, longitude);
        if (distance <= maxDistance) {
          services.add(_carRentalToTransportationService(rental));
        }
      }
    }

    // Přidá parkoviště
    for (final parking in parkingLots) {
      if (serviceType == null || serviceType == TransportationServiceType.parking) {
        final distance = parking.distanceFrom(latitude, longitude);
        if (distance <= maxDistance) {
          services.add(_parkingLotToTransportationService(parking));
        }
      }
    }

    // Seřadí podle vzdálenosti
    services.sort((a, b) {
      final distanceA = a.distanceFrom(latitude, longitude);
      final distanceB = b.distanceFrom(latitude, longitude);
      return distanceA.compareTo(distanceB);
    });

    return services.take(limit).toList();
  }

  /// Získá statistiky dopravních služeb
  Future<TransportationStatistics> getTransportationStatistics() async {
    final carRentals = await getAllCarRentals();
    final parkingLots = await getAllParkingLots();
    final taxiServices = await getAllTaxiServices();
    final trafficInfo = await getCurrentTrafficInfo();
    
    return TransportationStatistics(
      totalCarRentals: carRentals.length,
      totalParkingLots: parkingLots.length,
      totalTaxiServices: taxiServices.length,
      activeTrafficAlerts: trafficInfo.where((t) => t.isActive).length,
      topRatedCarRentals: carRentals.where((c) => c.rating >= 4.0).length,
      availableParkingSpaces: parkingLots.fold(0, (sum, p) => sum + (p.availableSpaces ?? 0)),
      averageHourlyParkingRate: parkingLots.isNotEmpty 
          ? parkingLots.map((p) => p.hourlyRate).reduce((a, b) => a + b) / parkingLots.length
          : 0.0,
    );
  }

  /// Kontroluje platnost cache
  bool _isCacheValid() {
    return _lastCacheUpdate != null &&
           DateTime.now().difference(_lastCacheUpdate!) < _cacheValidDuration;
  }

  /// Generuje ukázková data autopůjčoven
  List<CarRental> _generateSampleCarRentals() {
    final rentals = <CarRental>[];

    rentals.addAll([
      _createCarRental(
        'Hertz Croatia',
        'Mezinárodní autopůjčovna s širokou nabídkou vozidel',
        'Ulica grada Vukovara 269, Zagreb',
        45.8131, 15.9775, 'zagreb',
        '+385 1 484 6777', 'https://www.hertz.hr',
        4.2, 156, hasAirportPickup: true, hasDelivery: true,
      ),
      _createCarRental(
        'Avis Croatia',
        'Prémiová autopůjčovna s luxusními vozidly',
        'Savska cesta 32, Zagreb',
        45.8058, 15.9553, 'zagreb',
        '+385 1 455 5555', 'https://www.avis.hr',
        4.4, 203, hasAirportPickup: true, hasDelivery: false,
      ),
      _createCarRental(
        'Budget Rent a Car',
        'Ekonomická autopůjčovna s výhodnými cenami',
        'Branimirova 29, Zagreb',
        45.8069, 15.9614, 'zagreb',
        '+385 1 455 4936', 'https://www.budget.hr',
        4.0, 89, hasAirportPickup: false, hasDelivery: true,
      ),
    ]);

    // Split
    rentals.addAll([
      _createCarRental(
        'Europcar Split',
        'Autopůjčovna v centru Splitu s moderními vozidly',
        'Obala Hrvatskog narodnog preporoda 10, Split',
        43.5081, 16.4402, 'dalmatia',
        '+385 21 399 000', 'https://www.europcar.hr',
        4.3, 134, hasAirportPickup: true, hasDelivery: true,
      ),
    ]);

    // Dubrovník
    rentals.addAll([
      _createCarRental(
        'Sixt Dubrovnik',
        'Prémiová autopůjčovna s výhledem na Staré město',
        'Frana Supila 12, Dubrovnik',
        42.6405, 18.1094, 'dubrovnik',
        '+385 20 436 666', 'https://www.sixt.hr',
        4.5, 178, hasAirportPickup: true, hasDelivery: false,
      ),
    ]);

    return rentals;
  }

  CarRental _createCarRental(
    String name, String description, String address,
    double latitude, double longitude, String region,
    String phone, String website,
    double rating, int reviewCount, {
    bool hasAirportPickup = false,
    bool hasDelivery = false,
  }) {
    return CarRental(
      id: 'rental_${name.toLowerCase().replaceAll(' ', '_')}',
      name: name,
      description: description,
      address: address,
      latitude: latitude,
      longitude: longitude,
      region: region,
      phone: phone,
      website: website,
      rating: rating,
      reviewCount: reviewCount,
      availableCategories: _generateCarCategories(),
      operatingHours: '8:00 - 18:00',
      hasAirportPickup: hasAirportPickup,
      hasDelivery: hasDelivery,
      hasGPS: true,
      hasInsurance: true,
      minAge: 21,
      requiredDocuments: ['Řidičský průkaz', 'Pas/občanský průkaz', 'Kreditní karta'],
      lastUpdated: DateTime.now(),
    );
  }

  List<CarCategory> _generateCarCategories() {
    return [
      CarCategory(
        id: 'economy',
        name: 'Ekonomická',
        description: 'Malé a úsporné vozidlo',
        carType: CarType.economy,
        seats: 4,
        doors: 4,
        hasAirConditioning: true,
        hasAutomaticTransmission: false,
        fuelType: 'Benzín',
        dailyPrice: 150,
        weeklyPrice: 900,
        monthlyPrice: 3500,
        currency: 'HRK',
        includedFeatures: ['Klimatizace', 'Rádio', 'Centrální zamykání'],
      ),
      CarCategory(
        id: 'compact',
        name: 'Kompaktní',
        description: 'Pohodlné vozidlo pro město i výlety',
        carType: CarType.compact,
        seats: 5,
        doors: 4,
        hasAirConditioning: true,
        hasAutomaticTransmission: true,
        fuelType: 'Benzín',
        dailyPrice: 220,
        weeklyPrice: 1400,
        monthlyPrice: 5200,
        currency: 'HRK',
        includedFeatures: ['Klimatizace', 'Automat', 'GPS', 'Bluetooth'],
      ),
    ];
  }

  /// Generuje ukázková data parkovišť
  List<ParkingLot> _generateSampleParkingLots() {
    final parkingLots = <ParkingLot>[];

    // Zagreb
    parkingLots.addAll([
      _createParkingLot(
        'Cvjetni trg',
        'Centrální parkoviště v srdci Záhřebu',
        'Cvjetni trg, Zagreb',
        45.8131, 15.9775, 'zagreb',
        ParkingType.street, 50, 12, 8.0, 60.0,
        hasElectricCharging: false, hasSecurity: true,
      ),
      _createParkingLot(
        'Garáže Importanne',
        'Podzemní garáže v nákupním centru',
        'Starčevićev trg 7, Zagreb',
        45.8108, 15.9758, 'zagreb',
        ParkingType.underground, 200, 45, 12.0, 80.0,
        hasElectricCharging: true, hasSecurity: true,
      ),
    ]);

    // Split
    parkingLots.addAll([
      _createParkingLot(
        'Riva Split',
        'Parkoviště u promenády',
        'Obala Hrvatskog narodnog preporoda, Split',
        43.5081, 16.4402, 'dalmatia',
        ParkingType.street, 30, 8, 10.0, 70.0,
        hasElectricCharging: false, hasSecurity: false,
      ),
    ]);

    return parkingLots;
  }

  ParkingLot _createParkingLot(
    String name, String description, String address,
    double latitude, double longitude, String region,
    ParkingType parkingType, int totalSpaces, int availableSpaces,
    double hourlyRate, double dailyRate, {
    bool hasElectricCharging = false,
    bool hasSecurity = false,
  }) {
    return ParkingLot(
      id: 'parking_${name.toLowerCase().replaceAll(' ', '_')}',
      name: name,
      description: description,
      address: address,
      latitude: latitude,
      longitude: longitude,
      region: region,
      parkingType: parkingType,
      totalSpaces: totalSpaces,
      availableSpaces: availableSpaces,
      hourlyRate: hourlyRate,
      dailyRate: dailyRate,
      currency: 'HRK',
      operatingHours: '24/7',
      hasElectricCharging: hasElectricCharging,
      hasDisabledAccess: true,
      hasSecurity: hasSecurity,
      hasReservation: false,
      rating: 3.5 + Random().nextDouble() * 1.5,
      reviewCount: 20 + Random().nextInt(100),
      lastUpdated: DateTime.now(),
    );
  }

  /// Generuje ukázková data taxi služeb
  List<TaxiService> _generateSampleTaxiServices() {
    return [
      TaxiService(
        id: 'taxi_zagreb_1',
        name: 'Radio Taxi Zagreb',
        description: 'Největší taxi služba v Záhřebu',
        phone: '+385 1 1777',
        website: 'https://www.radio-taxi-zagreb.hr',
        region: 'zagreb',
        rating: 4.2,
        reviewCount: 567,
        hasOnlineBooking: true,
        hasAppBooking: false,
        acceptsCreditCards: true,
        hasAirportService: true,
        has24hService: true,
        estimatedWaitTime: '5-10 min',
        baseRate: 25.0,
        perKmRate: 7.0,
        currency: 'HRK',
        serviceTypes: ['Standardní', 'Komfort', 'Van'],
        lastUpdated: DateTime.now(),
      ),
      TaxiService(
        id: 'uber_croatia',
        name: 'Uber Croatia',
        description: 'Moderní ride-sharing služba',
        phone: 'Aplikace',
        mobileApp: 'Uber',
        region: 'all',
        rating: 4.5,
        reviewCount: 1234,
        hasOnlineBooking: false,
        hasAppBooking: true,
        acceptsCreditCards: true,
        hasAirportService: true,
        has24hService: true,
        estimatedWaitTime: '3-8 min',
        serviceTypes: ['UberX', 'UberXL', 'Uber Comfort'],
        lastUpdated: DateTime.now(),
      ),
    ];
  }

  /// Generuje ukázková data dopravních informací
  List<TrafficInfo> _generateSampleTrafficInfo() {
    final now = DateTime.now();
    
    return [
      TrafficInfo(
        id: 'traffic_1',
        title: 'Uzavírka A1 - Zagreb-Split',
        description: 'Částečná uzavírka kvůli údržbě mostu',
        infoType: TrafficInfoType.maintenance,
        severity: TrafficSeverity.medium,
        location: 'A1, km 45',
        latitude: 45.5,
        longitude: 16.0,
        startTime: now.subtract(const Duration(hours: 2)),
        endTime: now.add(const Duration(hours: 6)),
        affectedRoutes: ['A1'],
        alternativeRoute: 'Objížďka přes D1',
        lastUpdated: now,
      ),
      TrafficInfo(
        id: 'traffic_2',
        title: 'Nehoda na Slavonské',
        description: 'Dopravní nehoda blokuje pravý pruh',
        infoType: TrafficInfoType.accident,
        severity: TrafficSeverity.high,
        location: 'Slavonska avenija, Zagreb',
        latitude: 45.8131,
        longitude: 15.9775,
        startTime: now.subtract(const Duration(minutes: 30)),
        affectedRoutes: ['Slavonska avenija'],
        lastUpdated: now,
      ),
    ];
  }

  TransportationService _carRentalToTransportationService(CarRental rental) {
    return TransportationService(
      id: rental.id,
      name: rental.name,
      description: rental.description,
      serviceType: TransportationServiceType.carRental,
      phone: rental.phone,
      website: rental.website,
      email: rental.email,
      address: rental.address,
      latitude: rental.latitude,
      longitude: rental.longitude,
      region: rental.region,
      rating: rental.rating,
      reviewCount: rental.reviewCount,
      features: ['GPS', 'Pojištění', 'Letištní pickup'],
      operatingHours: rental.operatingHours,
      priceRange: '150-500 HRK/den',
      isActive: true,
      hasOnlineBooking: true,
      acceptsCreditCards: true,
      hasCustomerSupport: true,
      supportedLanguages: ['hr', 'en', 'de'],
      lastUpdated: rental.lastUpdated,
    );
  }

  TransportationService _parkingLotToTransportationService(ParkingLot parking) {
    return TransportationService(
      id: parking.id,
      name: parking.name,
      description: parking.description,
      serviceType: TransportationServiceType.parking,
      phone: parking.phone,
      website: parking.website,
      address: parking.address,
      latitude: parking.latitude,
      longitude: parking.longitude,
      region: parking.region,
      rating: parking.rating,
      reviewCount: parking.reviewCount,
      features: parking.hasElectricCharging ? ['Elektrické nabíjení'] : [],
      operatingHours: parking.operatingHours,
      priceRange: '${parking.hourlyRate} HRK/hod',
      isActive: true,
      hasOnlineBooking: parking.hasReservation,
      acceptsCreditCards: true,
      hasCustomerSupport: false,
      supportedLanguages: ['hr'],
      lastUpdated: parking.lastUpdated,
    );
  }
}

/// Statistiky dopravních služeb
class TransportationStatistics {
  final int totalCarRentals;
  final int totalParkingLots;
  final int totalTaxiServices;
  final int activeTrafficAlerts;
  final int topRatedCarRentals;
  final int availableParkingSpaces;
  final double averageHourlyParkingRate;

  TransportationStatistics({
    required this.totalCarRentals,
    required this.totalParkingLots,
    required this.totalTaxiServices,
    required this.activeTrafficAlerts,
    required this.topRatedCarRentals,
    required this.availableParkingSpaces,
    required this.averageHourlyParkingRate,
  });
}
