import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import '../models/city_services.dart';
import '../services/city_services_service.dart';

class GovernmentServicesWidget extends StatefulWidget {
  const GovernmentServicesWidget({super.key});

  @override
  State<GovernmentServicesWidget> createState() =>
      _GovernmentServicesWidgetState();
}

class _GovernmentServicesWidgetState extends State<GovernmentServicesWidget> {
  final CityServicesService _cityService = CityServicesService();

  List<GovernmentOffice> _offices = [];
  List<OnlineForm> _forms = [];
  List<FormSubmission> _submissions = [];
  bool _isLoading = false;
  Position? _currentPosition;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      _currentPosition = await Geolocator.getCurrentPosition();

      // Načtení <PERSON>
      final offices = await _cityService.getNearbyOffices(
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
      );

      // Načtení formulářů
      final forms = await _cityService.getAvailableForms();

      // Načtení podání uživatele
      final submissions = await _cityService.getUserSubmissions();

      setState(() {
        _offices = offices;
        _forms = forms;
        _submissions = submissions;
      });
    } catch (e) {
      _showError('Chyba při načítání dat: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildQuickActions(),
            const SizedBox(height: 24),
            if (_submissions.isNotEmpty) ...[
              _buildMySubmissions(),
              const SizedBox(height: 24),
            ],
            _buildNearbyOffices(),
            const SizedBox(height: 24),
            _buildOnlineForms(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Rychlé akce',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _findNearestOffice,
                    icon: const Icon(Icons.location_on),
                    label: const Text('Nejbližší úřad'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _browseOnlineForms,
                    icon: const Icon(Icons.description),
                    label: const Text('Online formuláře'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMySubmissions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Moje podání',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _submissions.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final submission = _submissions[index];
                return _buildSubmissionTile(submission);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubmissionTile(FormSubmission submission) {
    return ListTile(
      leading: Icon(
        _getSubmissionStatusIcon(submission.status),
        color: _getSubmissionStatusColor(submission.status),
      ),
      title: Text('Podání #${submission.referenceNumber ?? submission.id}'),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Stav: ${_getSubmissionStatusText(submission.status)}'),
          Text('Podáno: ${_formatDate(submission.submittedAt)}'),
        ],
      ),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () => _showSubmissionDetails(submission),
    );
  }

  Widget _buildNearbyOffices() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Úřady v okolí',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else if (_offices.isEmpty)
              const Text('Žádné úřady v okolí')
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _offices.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final office = _offices[index];
                  return _buildOfficeTile(office);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildOfficeTile(GovernmentOffice office) {
    return ListTile(
      leading: Icon(
        Icons.account_balance,
        color: office.isOpenNow ? Colors.green : Colors.grey,
      ),
      title: Text(office.name),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(office.address),
          Text(
            office.isOpenNow ? 'Otevřeno' : 'Zavřeno',
            style: TextStyle(
              color: office.isOpenNow ? Colors.green : Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            'Služby: ${office.services.take(2).join(', ')}${office.services.length > 2 ? '...' : ''}',
          ),
        ],
      ),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () => _showOfficeDetails(office),
    );
  }

  Widget _buildOnlineForms() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Online formuláře',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (_forms.isEmpty)
              const Text('Žádné dostupné formuláře')
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _forms.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final form = _forms[index];
                  return _buildFormTile(form);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormTile(OnlineForm form) {
    return ListTile(
      leading: Icon(
        _getFormCategoryIcon(form.category),
        color: Theme.of(context).colorScheme.primary,
      ),
      title: Text(form.title),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(form.description),
          Text('Odhadovaný čas: ${form.estimatedTime.inMinutes} min'),
          if (form.fee != null) Text('Poplatek: ${form.fee} ${form.currency}'),
        ],
      ),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () => _showFormDetails(form),
    );
  }

  IconData _getSubmissionStatusIcon(SubmissionStatus status) {
    switch (status) {
      case SubmissionStatus.submitted:
        return Icons.send;
      case SubmissionStatus.inReview:
        return Icons.hourglass_empty;
      case SubmissionStatus.approved:
        return Icons.check_circle;
      case SubmissionStatus.rejected:
        return Icons.cancel;
      case SubmissionStatus.completed:
        return Icons.done_all;
    }
  }

  Color _getSubmissionStatusColor(SubmissionStatus status) {
    switch (status) {
      case SubmissionStatus.submitted:
        return Colors.blue;
      case SubmissionStatus.inReview:
        return Colors.orange;
      case SubmissionStatus.approved:
        return Colors.green;
      case SubmissionStatus.rejected:
        return Colors.red;
      case SubmissionStatus.completed:
        return Colors.green;
    }
  }

  String _getSubmissionStatusText(SubmissionStatus status) {
    switch (status) {
      case SubmissionStatus.submitted:
        return 'Podáno';
      case SubmissionStatus.inReview:
        return 'V posouzení';
      case SubmissionStatus.approved:
        return 'Schváleno';
      case SubmissionStatus.rejected:
        return 'Zamítnuto';
      case SubmissionStatus.completed:
        return 'Dokončeno';
    }
  }

  IconData _getFormCategoryIcon(FormCategory category) {
    switch (category) {
      case FormCategory.residence:
        return Icons.home;
      case FormCategory.business:
        return Icons.business;
      case FormCategory.construction:
        return Icons.construction;
      case FormCategory.social:
        return Icons.people;
      case FormCategory.tax:
        return Icons.receipt;
      case FormCategory.other:
        return Icons.description;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}.${date.month}.${date.year}';
  }

  void _findNearestOffice() {
    if (_offices.isNotEmpty) {
      _showOfficeDetails(_offices.first);
    } else {
      _showError('Žádné úřady v okolí');
    }
  }

  void _browseOnlineForms() {
    // Implementace procházení formulářů
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Otevírám seznam formulářů...')),
    );
  }

  void _showSubmissionDetails(FormSubmission submission) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Podání #${submission.referenceNumber ?? submission.id}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Stav: ${_getSubmissionStatusText(submission.status)}'),
            Text('Podáno: ${_formatDate(submission.submittedAt)}'),
            if (submission.processedAt != null)
              Text('Zpracováno: ${_formatDate(submission.processedAt!)}'),
            if (submission.notes != null) Text('Poznámky: ${submission.notes}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zavřít'),
          ),
        ],
      ),
    );
  }

  void _showOfficeDetails(GovernmentOffice office) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(office.name),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Adresa: ${office.address}'),
              Text('Telefon: ${office.phoneNumber}'),
              Text('E-mail: ${office.email}'),
              if (office.website != null) Text('Web: ${office.website}'),
              const SizedBox(height: 8),
              const Text(
                'Otevírací doba:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              ...office.openingHours.map(
                (hours) => Text(
                  '${_getDayName(hours.dayOfWeek)}: ${hours.isClosed ? 'Zavřeno' : '${hours.openTime} - ${hours.closeTime}'}',
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Služby:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              ...office.services.map((service) => Text('• $service')),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zavřít'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Implementace navigace k úřadu
            },
            child: const Text('Navigovat'),
          ),
        ],
      ),
    );
  }

  void _showFormDetails(OnlineForm form) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(form.title),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(form.description),
              const SizedBox(height: 8),
              Text('Odhadovaný čas: ${form.estimatedTime.inMinutes} minut'),
              if (form.fee != null)
                Text('Poplatek: ${form.fee} ${form.currency}'),
              if (form.requiredDocuments.isNotEmpty) ...[
                const SizedBox(height: 8),
                const Text(
                  'Požadované dokumenty:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                ...form.requiredDocuments.map((doc) => Text('• $doc')),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zavřít'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _fillForm(form);
            },
            child: const Text('Vyplnit'),
          ),
        ],
      ),
    );
  }

  void _fillForm(OnlineForm form) {
    // Implementace vyplnění formuláře
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Otevírám formulář: ${form.title}')));
  }

  String _getDayName(String dayOfWeek) {
    const days = {
      'monday': 'Pondělí',
      'tuesday': 'Úterý',
      'wednesday': 'Středa',
      'thursday': 'Čtvrtek',
      'friday': 'Pátek',
      'saturday': 'Sobota',
      'sunday': 'Neděle',
    };
    return days[dayOfWeek] ?? dayOfWeek;
  }
}
