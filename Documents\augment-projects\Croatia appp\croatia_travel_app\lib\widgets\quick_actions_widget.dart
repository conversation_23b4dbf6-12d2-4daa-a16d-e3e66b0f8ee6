import 'package:flutter/material.dart';

class QuickActionsWidget extends StatelessWidget {
  const QuickActionsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildQuickAction(
                  context,
                  icon: Icons.map,
                  label: 'Mapa',
                  onTap: () => _navigateToMap(context),
                ),
                _buildQuickAction(
                  context,
                  icon: Icons.camera_alt,
                  label: 'Foto',
                  onTap: () => _openCamera(context),
                ),
                _buildQuickAction(
                  context,
                  icon: Icons.mic,
                  label: 'Audio',
                  onTap: () => _startAudioRecording(context),
                ),
                _buildQuickAction(
                  context,
                  icon: Icons.translate,
                  label: 'Slovník',
                  onTap: () => _openDictionary(context),
                ),
              ],
            ),
            const Sized<PERSON>ox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildQuickAction(
                  context,
                  icon: Icons.directions,
                  label: 'Navigace',
                  onTap: () => _openNavigation(context),
                ),
                _buildQuickAction(
                  context,
                  icon: Icons.event,
                  label: 'Události',
                  onTap: () => _openEvents(context),
                ),
                _buildQuickAction(
                  context,
                  icon: Icons.restaurant,
                  label: 'Kuchyně',
                  onTap: () => _openCuisine(context),
                ),
                _buildQuickAction(
                  context,
                  icon: Icons.account_balance_wallet,
                  label: 'Rozpočet',
                  onTap: () => _openBudget(context),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickAction(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: 70,
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                size: 24,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToMap(BuildContext context) {
    // Navigace na mapu
    DefaultTabController.of(context).animateTo(1);
  }

  void _openCamera(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Přidat fotografii',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildCameraOption(
                  context,
                  icon: Icons.camera_alt,
                  label: 'Fotoaparát',
                  onTap: () {
                    Navigator.pop(context);
                    // Otevřít fotoaparát
                  },
                ),
                _buildCameraOption(
                  context,
                  icon: Icons.photo_library,
                  label: 'Galerie',
                  onTap: () {
                    Navigator.pop(context);
                    // Otevřít galerii
                  },
                ),
                _buildCameraOption(
                  context,
                  icon: Icons.view_in_ar,
                  label: 'AR sken',
                  onTap: () {
                    Navigator.pop(context);
                    // Otevřít AR rozpoznávání památek
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCameraOption(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, size: 32),
            const SizedBox(height: 8),
            Text(label),
          ],
        ),
      ),
    );
  }

  void _startAudioRecording(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Audio nahrávání'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.mic, size: 48, color: Colors.red),
            SizedBox(height: 16),
            Text('Nahrávání audio poznámky...'),
            SizedBox(height: 16),
            LinearProgressIndicator(),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Zastavit'),
          ),
        ],
      ),
    );
  }

  void _openDictionary(BuildContext context) {
    // Navigace na slovník
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Text(
              'Rychlý překlad',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                hintText: 'Zadejte slovo nebo frázi...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.translate),
              ),
              onSubmitted: (value) {
                // Vyhledat překlad
              },
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ListView(
                children: const [
                  ListTile(
                    title: Text('Dobrý den'),
                    subtitle: Text('Dobar dan'),
                    trailing: Icon(Icons.volume_up),
                  ),
                  ListTile(
                    title: Text('Děkuji'),
                    subtitle: Text('Hvala'),
                    trailing: Icon(Icons.volume_up),
                  ),
                  ListTile(
                    title: Text('Promiňte'),
                    subtitle: Text('Oprostite'),
                    trailing: Icon(Icons.volume_up),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _openNavigation(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Navigace',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.near_me),
              title: const Text('Nejbližší místa'),
              onTap: () {
                Navigator.pop(context);
                // Zobrazit nejbližší místa
              },
            ),
            ListTile(
              leading: const Icon(Icons.route),
              title: const Text('Naplánovat trasu'),
              onTap: () {
                Navigator.pop(context);
                // Otevřít plánovač tras
              },
            ),
            ListTile(
              leading: const Icon(Icons.bookmark),
              title: const Text('Uložené trasy'),
              onTap: () {
                Navigator.pop(context);
                // Zobrazit uložené trasy
              },
            ),
          ],
        ),
      ),
    );
  }

  void _openEvents(BuildContext context) {
    // Navigace na události
    DefaultTabController.of(context).animateTo(3);
  }

  void _openCuisine(BuildContext context) {
    // Navigace na kuchyni
    DefaultTabController.of(context).animateTo(4);
  }

  void _openBudget(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Rychlé přidání výdaje',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      hintText: 'Částka',
                      border: OutlineInputBorder(),
                      prefixText: '€ ',
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Kategorie',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'food', child: Text('Jídlo')),
                      DropdownMenuItem(
                        value: 'transport',
                        child: Text('Doprava'),
                      ),
                      DropdownMenuItem(
                        value: 'accommodation',
                        child: Text('Ubytování'),
                      ),
                      DropdownMenuItem(
                        value: 'entertainment',
                        child: Text('Zábava'),
                      ),
                    ],
                    onChanged: (value) {},
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // Uložit výdaj
              },
              child: const Text('Přidat výdaj'),
            ),
          ],
        ),
      ),
    );
  }
}
