import 'package:flutter/material.dart';
import '../models/monument.dart';

class AROverlayWidget extends StatefulWidget {
  final MonumentInfo monument;

  const AROverlayWidget({super.key, required this.monument});

  @override
  State<AROverlayWidget> createState() => _AROverlayWidgetState();
}

class _AROverlayWidgetState extends State<AROverlayWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _slideController;
  late Animation<double> _pulseAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, -1), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.elasticOut),
        );

    _slideController.forward();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Hlavní informační panel
        SlideTransition(
          position: _slideAnimation,
          child: Positioned(
            top: 120,
            left: 16,
            right: 16,
            child: _buildMainInfoPanel(),
          ),
        ),

        // Pulsující indikátor na památce
        Positioned(
          top: MediaQuery.of(context).size.height * 0.4,
          left: MediaQuery.of(context).size.width * 0.5 - 25,
          child: _buildPulsingIndicator(),
        ),

        // Boční informační panely
        Positioned(right: 16, top: 200, child: _buildSideInfoPanel()),

        // Spodní akční panel
        Positioned(
          bottom: 120,
          left: 16,
          right: 16,
          child: _buildActionPanel(),
        ),

        // AR hotspoty
        ..._buildARHotspots(),
      ],
    );
  }

  Widget _buildMainInfoPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue, width: 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Text(
                widget.monument.category.icon,
                style: const TextStyle(fontSize: 24),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  widget.monument.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  widget.monument.category.displayName,
                  style: const TextStyle(color: Colors.white, fontSize: 10),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            widget.monument.description,
            style: const TextStyle(color: Colors.white70, fontSize: 14),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              const Icon(Icons.access_time, color: Colors.white70, size: 16),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  widget.monument.visitingHours,
                  style: const TextStyle(color: Colors.white70, fontSize: 12),
                ),
              ),
              if (widget.monument.ticketPrice > 0)
                Text(
                  '${widget.monument.ticketPrice.round()} HRK',
                  style: const TextStyle(
                    color: Colors.green,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPulsingIndicator() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.3),
              shape: BoxShape.circle,
              border: Border.all(color: Colors.blue, width: 2),
            ),
            child: const Icon(Icons.location_on, color: Colors.blue, size: 30),
          ),
        );
      },
    );
  }

  Widget _buildSideInfoPanel() {
    return Column(
      children: [
        _buildInfoBubble(
          icon: Icons.history,
          text: widget.monument.buildingPeriod,
          color: Colors.orange,
        ),
        const SizedBox(height: 8),
        _buildInfoBubble(
          icon: Icons.architecture,
          text: widget.monument.materials.join(', '),
          color: Colors.purple,
        ),
        const SizedBox(height: 8),
        if (widget.monument.architect != null)
          _buildInfoBubble(
            icon: Icons.person,
            text: widget.monument.architect!,
            color: Colors.green,
          ),
      ],
    );
  }

  Widget _buildInfoBubble({
    required IconData icon,
    required String text,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: Colors.white, size: 20),
          const SizedBox(height: 4),
          SizedBox(
            width: 80,
            child: Text(
              text,
              style: const TextStyle(color: Colors.white, fontSize: 10),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionPanel() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.5)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildActionButton(
            icon: Icons.info_outline,
            label: 'Detail',
            onTap: () => _showDetailInfo(),
          ),
          _buildActionButton(
            icon: Icons.volume_up,
            label: 'Audio',
            onTap: () => _playAudioGuide(),
          ),
          _buildActionButton(
            icon: Icons.camera_alt,
            label: 'Foto',
            onTap: () => _takePhoto(),
          ),
          _buildActionButton(
            icon: Icons.quiz,
            label: 'Kvíz',
            onTap: () => _startQuiz(),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: Colors.blue, size: 20),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(color: Colors.white, fontSize: 10),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildARHotspots() {
    // Simulace AR hotspotů na různých místech památky
    return [
      Positioned(
        top: MediaQuery.of(context).size.height * 0.3,
        left: MediaQuery.of(context).size.width * 0.2,
        child: _buildHotspot(
          'Hlavní vchod',
          'Původní vstupní brána z 14. století',
          Icons.door_front_door,
        ),
      ),
      Positioned(
        top: MediaQuery.of(context).size.height * 0.5,
        right: MediaQuery.of(context).size.width * 0.2,
        child: _buildHotspot(
          'Věž',
          'Obranná věž vysoká 25 metrů',
          Icons.castle,
        ),
      ),
      Positioned(
        bottom: MediaQuery.of(context).size.height * 0.3,
        left: MediaQuery.of(context).size.width * 0.3,
        child: _buildHotspot(
          'Nádvoří',
          'Centrální prostor paláce',
          Icons.landscape,
        ),
      ),
    ];
  }

  Widget _buildHotspot(String title, String description, IconData icon) {
    return GestureDetector(
      onTap: () => _showHotspotInfo(title, description),
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.red.withValues(alpha: 0.8),
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white, width: 2),
        ),
        child: Icon(icon, color: Colors.white, size: 20),
      ),
    );
  }

  void _showDetailInfo() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.monument.name,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Historické informace:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(widget.monument.historicalInfo),
              const SizedBox(height: 16),
              const Text(
                'Praktické informace:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text('Otevírací doba: ${widget.monument.visitingHours}'),
              Text(
                'Vstupné: ${widget.monument.ticketPrice > 0 ? '${widget.monument.ticketPrice.round()} HRK' : 'Zdarma'}',
              ),
              Text('Přístupnost: ${widget.monument.accessibility}'),
            ],
          ),
        ),
      ),
    );
  }

  void _playAudioGuide() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🎵 Přehrávám audio průvodce...'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _takePhoto() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('📸 Foto uloženo do galerie!'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _startQuiz() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Kvíz: ${widget.monument.name}'),
        content: const Text('Chcete si otestovat své znalosti o této památce?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Později'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showQuiz();
            },
            child: const Text('Začít kvíz'),
          ),
        ],
      ),
    );
  }

  void _showQuiz() {
    // Simulace kvízu
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Kvíz'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Ve kterém století byla postavena tato památka?'),
            SizedBox(height: 16),
            Text('A) 12. století'),
            Text('B) 13. století'),
            Text('C) 14. století'),
            Text('D) 15. století'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Ukončit'),
          ),
        ],
      ),
    );
  }

  void _showHotspotInfo(String title, String description) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(description),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
