import 'diary_entry.dart';

/// 🤖 AI WRITING SUGGESTION MODELS - Modely pro AI návrhy psaní

/// Návrh pro psaní
class WritingSuggestion {
  final String id;
  final SuggestionType type;
  final String text;
  final String explanation;
  final double confidence;
  final Map<String, dynamic>? metadata;

  const WritingSuggestion({
    required this.id,
    required this.type,
    required this.text,
    required this.explanation,
    required this.confidence,
    this.metadata,
  });

  WritingSuggestion copyWith({
    String? id,
    SuggestionType? type,
    String? text,
    String? explanation,
    double? confidence,
    Map<String, dynamic>? metadata,
  }) {
    return WritingSuggestion(
      id: id ?? this.id,
      type: type ?? this.type,
      text: text ?? this.text,
      explanation: explanation ?? this.explanation,
      confidence: confidence ?? this.confidence,
      metadata: metadata ?? this.metadata,
    );
  }

  bool get isHighConfidence => confidence >= 0.8;
  bool get isMediumConfidence => confidence >= 0.5 && confidence < 0.8;
  bool get isLowConfidence => confidence < 0.5;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'text': text,
      'explanation': explanation,
      'confidence': confidence,
      'metadata': metadata,
    };
  }

  factory WritingSuggestion.fromJson(Map<String, dynamic> json) {
    return WritingSuggestion(
      id: json['id'] as String,
      type: SuggestionType.values.firstWhere((t) => t.name == json['type']),
      text: json['text'] as String,
      explanation: json['explanation'] as String,
      confidence: (json['confidence'] as num).toDouble(),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }
}

/// Typ návrhu
enum SuggestionType {
  continuation,    // Pokračování textu
  style,          // Vylepšení stylu
  detail,         // Přidání detailů
  mood,           // Na základě nálady
  location,       // Na základě lokace
  personalized,   // Personalizované
  grammar,        // Gramatika
  structure,      // Struktura
}

extension SuggestionTypeExtension on SuggestionType {
  String get displayName {
    switch (this) {
      case SuggestionType.continuation:
        return 'Pokračování';
      case SuggestionType.style:
        return 'Styl';
      case SuggestionType.detail:
        return 'Detaily';
      case SuggestionType.mood:
        return 'Nálada';
      case SuggestionType.location:
        return 'Lokace';
      case SuggestionType.personalized:
        return 'Personalizované';
      case SuggestionType.grammar:
        return 'Gramatika';
      case SuggestionType.structure:
        return 'Struktura';
    }
  }

  String get icon {
    switch (this) {
      case SuggestionType.continuation:
        return '➡️';
      case SuggestionType.style:
        return '✨';
      case SuggestionType.detail:
        return '🔍';
      case SuggestionType.mood:
        return '😊';
      case SuggestionType.location:
        return '📍';
      case SuggestionType.personalized:
        return '👤';
      case SuggestionType.grammar:
        return '📝';
      case SuggestionType.structure:
        return '🏗️';
    }
  }

  String get color {
    switch (this) {
      case SuggestionType.continuation:
        return '#4CAF50';
      case SuggestionType.style:
        return '#9C27B0';
      case SuggestionType.detail:
        return '#FF9800';
      case SuggestionType.mood:
        return '#E91E63';
      case SuggestionType.location:
        return '#2196F3';
      case SuggestionType.personalized:
        return '#607D8B';
      case SuggestionType.grammar:
        return '#795548';
      case SuggestionType.structure:
        return '#009688';
    }
  }
}

/// Kontext pro psaní
class WritingContext {
  final String currentText;
  final String? title;
  final DiaryMood? mood;
  final String? location;
  final String? weather;
  final List<String> photos;
  final DateTime timestamp;

  const WritingContext({
    required this.currentText,
    this.title,
    this.mood,
    this.location,
    this.weather,
    this.photos = const [],
    required this.timestamp,
  });

  int get wordCount => currentText.split(RegExp(r'\W+')).where((w) => w.isNotEmpty).length;
  int get characterCount => currentText.length;
  bool get hasPhotos => photos.isNotEmpty;
  bool get isEmpty => currentText.trim().isEmpty;

  Map<String, dynamic> toJson() {
    return {
      'currentText': currentText,
      'title': title,
      'mood': mood?.name,
      'location': location,
      'weather': weather,
      'photos': photos,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

/// Výsledek detekce nálady
class MoodDetectionResult {
  final DiaryMood? detectedMood;
  final double confidence;
  final Map<DiaryMood, double> moodScores;
  final List<DiaryMood> suggestedMoods;

  const MoodDetectionResult({
    this.detectedMood,
    required this.confidence,
    this.moodScores = const {},
    this.suggestedMoods = const [],
  });

  bool get hasDetectedMood => detectedMood != null;
  bool get isHighConfidence => confidence >= 0.7;
  bool get hasSuggestions => suggestedMoods.isNotEmpty;

  factory MoodDetectionResult.empty() {
    return const MoodDetectionResult(
      confidence: 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'detectedMood': detectedMood?.name,
      'confidence': confidence,
      'moodScores': moodScores.map((k, v) => MapEntry(k.name, v)),
      'suggestedMoods': suggestedMoods.map((m) => m.name).toList(),
    };
  }
}

/// Analýza kvality textu
class TextQualityAnalysis {
  final int wordCount;
  final int sentenceCount;
  final int paragraphCount;
  final double averageSentenceLength;
  final double complexityScore;
  final double readabilityScore;
  final double emotionalIntensity;
  final double detailScore;
  final double overallScore;
  final List<String> suggestions;

  const TextQualityAnalysis({
    required this.wordCount,
    required this.sentenceCount,
    required this.paragraphCount,
    required this.averageSentenceLength,
    required this.complexityScore,
    required this.readabilityScore,
    required this.emotionalIntensity,
    required this.detailScore,
    required this.overallScore,
    this.suggestions = const [],
  });

  String get qualityLevel {
    if (overallScore >= 0.8) return 'Výborná';
    if (overallScore >= 0.6) return 'Dobrá';
    if (overallScore >= 0.4) return 'Průměrná';
    return 'Potřebuje zlepšení';
  }

  String get qualityColor {
    if (overallScore >= 0.8) return '#4CAF50';
    if (overallScore >= 0.6) return '#FF9800';
    if (overallScore >= 0.4) return '#FFC107';
    return '#F44336';
  }

  bool get needsImprovement => overallScore < 0.6;
  bool get isExcellent => overallScore >= 0.8;

  factory TextQualityAnalysis.empty() {
    return const TextQualityAnalysis(
      wordCount: 0,
      sentenceCount: 0,
      paragraphCount: 0,
      averageSentenceLength: 0.0,
      complexityScore: 0.0,
      readabilityScore: 0.0,
      emotionalIntensity: 0.0,
      detailScore: 0.0,
      overallScore: 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'wordCount': wordCount,
      'sentenceCount': sentenceCount,
      'paragraphCount': paragraphCount,
      'averageSentenceLength': averageSentenceLength,
      'complexityScore': complexityScore,
      'readabilityScore': readabilityScore,
      'emotionalIntensity': emotionalIntensity,
      'detailScore': detailScore,
      'overallScore': overallScore,
      'suggestions': suggestions,
    };
  }
}

/// Šablona pro psaní
class WritingTemplate {
  final String id;
  final String name;
  final String description;
  final String template;
  final String category;
  final List<String> tags;
  final int usageCount;
  final DateTime? lastUsed;

  const WritingTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.template,
    required this.category,
    this.tags = const [],
    this.usageCount = 0,
    this.lastUsed,
  });

  WritingTemplate copyWith({
    String? id,
    String? name,
    String? description,
    String? template,
    String? category,
    List<String>? tags,
    int? usageCount,
    DateTime? lastUsed,
  }) {
    return WritingTemplate(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      template: template ?? this.template,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      usageCount: usageCount ?? this.usageCount,
      lastUsed: lastUsed ?? this.lastUsed,
    );
  }

  bool get isPopular => usageCount > 10;
  bool get isRecentlyUsed => lastUsed != null && 
      DateTime.now().difference(lastUsed!).inDays <= 7;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'template': template,
      'category': category,
      'tags': tags,
      'usageCount': usageCount,
      'lastUsed': lastUsed?.toIso8601String(),
    };
  }

  factory WritingTemplate.fromJson(Map<String, dynamic> json) {
    return WritingTemplate(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      template: json['template'] as String,
      category: json['category'] as String,
      tags: (json['tags'] as List<dynamic>).cast<String>(),
      usageCount: json['usageCount'] as int? ?? 0,
      lastUsed: json['lastUsed'] != null 
          ? DateTime.parse(json['lastUsed'] as String)
          : null,
    );
  }
}

/// Profil uživatelského stylu psaní
class UserWritingProfile {
  final double averageWordCount;
  final double averageSentenceLength;
  final List<String> favoriteWords;
  final List<String> favoriteTopics;
  final DiaryMood? dominantMood;
  final Map<String, int> categoryUsage;
  final double emotionalIntensity;
  final double detailLevel;
  final List<String> writingPatterns;

  const UserWritingProfile({
    required this.averageWordCount,
    required this.averageSentenceLength,
    this.favoriteWords = const [],
    this.favoriteTopics = const [],
    this.dominantMood,
    this.categoryUsage = const {},
    required this.emotionalIntensity,
    required this.detailLevel,
    this.writingPatterns = const [],
  });

  factory UserWritingProfile.empty() {
    return const UserWritingProfile(
      averageWordCount: 0.0,
      averageSentenceLength: 0.0,
      emotionalIntensity: 0.0,
      detailLevel: 0.0,
    );
  }

  String get writingStyle {
    if (averageWordCount > 200 && detailLevel > 0.7) return 'Detailní vypravěč';
    if (emotionalIntensity > 0.8) return 'Emocionální pisatel';
    if (averageSentenceLength < 10) return 'Stručný komunikátor';
    if (averageWordCount > 150) return 'Rozvláčný pisatel';
    return 'Vyvážený pisatel';
  }

  Map<String, dynamic> toJson() {
    return {
      'averageWordCount': averageWordCount,
      'averageSentenceLength': averageSentenceLength,
      'favoriteWords': favoriteWords,
      'favoriteTopics': favoriteTopics,
      'dominantMood': dominantMood?.name,
      'categoryUsage': categoryUsage,
      'emotionalIntensity': emotionalIntensity,
      'detailLevel': detailLevel,
      'writingPatterns': writingPatterns,
    };
  }
}
