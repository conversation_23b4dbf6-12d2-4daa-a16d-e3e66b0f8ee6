import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'smart_city_screen.dart';
import 'transportation_hub_screen.dart';
import 'emergency_services_screen.dart';
import 'restaurant_discovery_screen.dart';
import 'entertainment_activities_screen.dart';
import 'neighborhood_help_screen.dart';
import 'contacts_directory_screen.dart';

/// 🗺️ ŽIVOT V CHORVATSKU - Hlavní hub pro praktický život
class CroatianLifeScreen extends StatefulWidget {
  const CroatianLifeScreen({super.key});

  @override
  State<CroatianLifeScreen> createState() => _CroatianLifeScreenState();
}

class _CroatianLifeScreenState extends State<CroatianLifeScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF006994), // Adriatic Blue
              Color(0xFF2E8B8B), // Teal
              Color(0xFFFF6B35), // Sunset Orange
            ],
            stops: [0.0, 0.6, 1.0],
          ),
        ),
        child: CustomPaint(
          painter: WatercolorLifeBackgroundPainter(),
          child: SafeArea(
            child: Column(
              children: [
                // Header
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      Text(
                        '🗺️ Život u Hrvatskoj',
                        style: GoogleFonts.inter(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ).animate().fadeIn().slideY(begin: -0.3),
                      const SizedBox(height: 8),
                      Text(
                        'Sve što trebate za svakodnevni život',
                        style: GoogleFonts.inter(
                          fontSize: 16,
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                      ).animate().fadeIn(delay: 200.ms).slideY(begin: -0.3),
                    ],
                  ),
                ),

                // Main content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      children: [
                        // Quick actions grid
                        _buildQuickActionsGrid(),

                        const SizedBox(height: 24),

                        // Main categories
                        _buildMainCategories(),

                        const SizedBox(height: 24),

                        // Neighborhood help section
                        _buildNeighborhoodHelpSection(),

                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActionsGrid() {
    final quickActions = [
      {'icon': '🚌', 'title': 'Prijevoz', 'subtitle': 'Autobusi uživo'},
      {'icon': '🅿️', 'title': 'Parkiranje', 'subtitle': 'Slobodna mjesta'},
      {'icon': '🏥', 'title': 'Hitna', 'subtitle': 'Službe 24/7'},
      {'icon': '🍽️', 'title': 'Hrana', 'subtitle': 'Dostava'},
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '⚡ Brze akcije',
            style: GoogleFonts.inter(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 2.5,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: quickActions.length,
            itemBuilder: (context, index) {
              final action = quickActions[index];
              return Container(
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(12),
                    onTap: () => _handleQuickAction(index),
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Row(
                        children: [
                          Text(
                            action['icon']!,
                            style: const TextStyle(fontSize: 24),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  action['title']!,
                                  style: GoogleFonts.inter(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                  ),
                                ),
                                Text(
                                  action['subtitle']!,
                                  style: GoogleFonts.inter(
                                    fontSize: 12,
                                    color: Colors.white.withValues(alpha: 0.7),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    ).animate().fadeIn(delay: 300.ms).slideY(begin: 0.3);
  }

  Widget _buildMainCategories() {
    final categories = [
      {
        'icon': '🚌',
        'title': 'Prijevoz',
        'subtitle': 'Autobusi, tramvaji, parkiranje',
        'color': const Color(0xFF006994),
        'screen': const TransportationHubScreen(),
      },
      {
        'icon': '🏛️',
        'title': 'Gradske usluge',
        'subtitle': 'Uredi, institucije, obrasci',
        'color': const Color(0xFF2E8B8B),
        'screen': const SmartCityScreen(),
      },
      {
        'icon': '🏥',
        'title': 'Zdravlje i hitne službe',
        'subtitle': 'Bolnice, ljekarne, hitni kontakti',
        'color': const Color(0xFFFF6B35),
        'screen': const EmergencyServicesScreen(),
      },
      {
        'icon': '🍽️',
        'title': 'Hrana i piće',
        'subtitle': 'Restorani, dostava, lokalni proizvodi',
        'color': const Color(0xFF8FBC8F),
        'screen': const RestaurantDiscoveryScreen(),
      },
      {
        'icon': '🎭',
        'title': 'Kultura i zabava',
        'subtitle': 'Kazališta, koncerti, festivali',
        'color': const Color(0xFFE6E6FA),
        'screen': const EntertainmentActivitiesScreen(),
      },
      {
        'icon': '📞',
        'title': 'Kontakti i hitne službe',
        'subtitle': 'Uredi, bolnice, divadla, emergency',
        'color': const Color(0xFF006994),
        'screen': const ContactsDirectoryScreen(),
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '📋 Glavne kategorije',
          style: GoogleFonts.inter(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ).animate().fadeIn(delay: 500.ms),
        const SizedBox(height: 16),
        ...categories.asMap().entries.map((entry) {
          final index = entry.key;
          final category = entry.value;

          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(16),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => category['screen'] as Widget,
                    ),
                  );
                },
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: (category['color'] as Color).withValues(
                            alpha: 0.2,
                          ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Center(
                          child: Text(
                            category['icon'] as String,
                            style: const TextStyle(fontSize: 24),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              category['title'] as String,
                              style: GoogleFonts.inter(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              category['subtitle'] as String,
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                color: Colors.white.withValues(alpha: 0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Icon(
                        Icons.arrow_forward_ios,
                        color: Colors.white.withValues(alpha: 0.5),
                        size: 16,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ).animate().fadeIn(delay: (600 + index * 100).ms).slideX(begin: 0.3);
        }),
      ],
    );
  }

  Widget _buildNeighborhoodHelpSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.15),
            Colors.white.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text('🤝', style: const TextStyle(fontSize: 24)),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Susjedska pomoć',
                      style: GoogleFonts.inter(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      'Ponuda i potražnja usluga u susjedstvu',
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildNeighborhoodButton(
                  '📢 Tražim',
                  'Objavite što trebate',
                  () => _openNeighborhoodHelp('request'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildNeighborhoodButton(
                  '💡 Nudim',
                  'Ponudite svoje usluge',
                  () => _openNeighborhoodHelp('offer'),
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate().fadeIn(delay: 1000.ms).slideY(begin: 0.3);
  }

  Widget _buildNeighborhoodButton(
    String title,
    String subtitle,
    VoidCallback onTap,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleQuickAction(int index) {
    switch (index) {
      case 0: // Transport
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const TransportationHubScreen(),
          ),
        );
        break;
      case 1: // Parking
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const SmartCityScreen()),
        );
        break;
      case 2: // Emergency
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const EmergencyServicesScreen(),
          ),
        );
        break;
      case 3: // Food
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const RestaurantDiscoveryScreen(),
          ),
        );
        break;
    }
  }

  void _openNeighborhoodHelp(String type) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NeighborhoodHelpScreen(initialTab: type),
      ),
    );
  }
}

/// Custom painter pro watercolor pozadí
class WatercolorLifeBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor spots
    final spots = [
      {
        'x': size.width * 0.15,
        'y': size.height * 0.25,
        'radius': 90.0,
        'color': Colors.white.withValues(alpha: 0.08),
      },
      {
        'x': size.width * 0.85,
        'y': size.height * 0.4,
        'radius': 70.0,
        'color': Colors.white.withValues(alpha: 0.06),
      },
      {
        'x': size.width * 0.25,
        'y': size.height * 0.75,
        'radius': 110.0,
        'color': Colors.white.withValues(alpha: 0.05),
      },
      {
        'x': size.width * 0.9,
        'y': size.height * 0.85,
        'radius': 60.0,
        'color': Colors.white.withValues(alpha: 0.08),
      },
    ];

    for (final spot in spots) {
      paint.color = spot['color'] as Color;
      canvas.drawCircle(
        Offset(spot['x'] as double, spot['y'] as double),
        spot['radius'] as double,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
