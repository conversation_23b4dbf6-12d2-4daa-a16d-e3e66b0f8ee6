import 'package:json_annotation/json_annotation.dart';

part 'ticket.g.dart';

/// Vstupenka pro památky, muzea, akce
@JsonSerializable()
class Ticket {
  final String id;
  final String title;
  final String description;
  final TicketType type;
  final String venueId;
  final String venueName;
  final String location;
  final double? latitude;
  final double? longitude;
  final String region;
  final TicketPricing pricing;
  final List<TicketAvailability> availability;
  final TicketProvider provider;
  final List<String> images;
  final List<String> tags;
  final TicketFeatures features;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;

  Ticket({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.venueId,
    required this.venueName,
    required this.location,
    this.latitude,
    this.longitude,
    required this.region,
    required this.pricing,
    required this.availability,
    required this.provider,
    this.images = const [],
    this.tags = const [],
    required this.features,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
  });

  factory Ticket.fromJson(Map<String, dynamic> json) => _$TicketFromJson(json);
  Map<String, dynamic> toJson() => _$TicketToJson(this);

  /// Je vstupenka dostupná pro dané datum?
  bool isAvailableForDate(DateTime date) {
    return availability.any((avail) => 
      date.isAfter(avail.startDate.subtract(const Duration(days: 1))) &&
      date.isBefore(avail.endDate.add(const Duration(days: 1)))
    );
  }

  /// Získá cenu pro daný typ a počet osob
  double? getPriceForGroup(TicketCategory category, int count) {
    final categoryPricing = pricing.categories
        .where((cat) => cat.category == category)
        .firstOrNull;
    
    if (categoryPricing == null) return null;
    
    double basePrice = categoryPricing.price * count;
    
    // Aplikuj skupinové slevy
    for (final discount in pricing.groupDiscounts) {
      if (count >= discount.minPeople) {
        basePrice *= (1 - discount.discountPercentage / 100);
        break;
      }
    }
    
    return basePrice;
  }

  /// Má sezónní slevy?
  bool get hasSeasonalDiscounts => pricing.seasonalDiscounts.isNotEmpty;

  /// Má skupinové slevy?
  bool get hasGroupDiscounts => pricing.groupDiscounts.isNotEmpty;
}

/// Typ vstupenky
enum TicketType {
  @JsonValue('museum')
  museum, // Muzeum
  @JsonValue('monument')
  monument, // Památka
  @JsonValue('park')
  park, // Park
  @JsonValue('event')
  event, // Akce
  @JsonValue('tour')
  tour, // Prohlídka
  @JsonValue('activity')
  activity, // Aktivita
  @JsonValue('transport')
  transport, // Doprava
  @JsonValue('combo')
  combo, // Kombinovaná
}

/// Cenové informace
@JsonSerializable()
class TicketPricing {
  final List<TicketCategoryPricing> categories;
  final List<GroupDiscount> groupDiscounts;
  final List<SeasonalDiscount> seasonalDiscounts;
  final String currency;
  final bool includesVAT;
  final String? notes;

  TicketPricing({
    required this.categories,
    this.groupDiscounts = const [],
    this.seasonalDiscounts = const [],
    this.currency = 'EUR',
    this.includesVAT = true,
    this.notes,
  });

  factory TicketPricing.fromJson(Map<String, dynamic> json) => 
      _$TicketPricingFromJson(json);
  Map<String, dynamic> toJson() => _$TicketPricingToJson(this);
}

/// Cenová kategorie
@JsonSerializable()
class TicketCategoryPricing {
  final TicketCategory category;
  final double price;
  final String description;
  final int? minAge;
  final int? maxAge;
  final bool requiresProof;

  TicketCategoryPricing({
    required this.category,
    required this.price,
    required this.description,
    this.minAge,
    this.maxAge,
    this.requiresProof = false,
  });

  factory TicketCategoryPricing.fromJson(Map<String, dynamic> json) => 
      _$TicketCategoryPricingFromJson(json);
  Map<String, dynamic> toJson() => _$TicketCategoryPricingToJson(this);
}

/// Kategorie vstupenky
enum TicketCategory {
  @JsonValue('adult')
  adult, // Dospělý
  @JsonValue('child')
  child, // Dítě
  @JsonValue('student')
  student, // Student
  @JsonValue('senior')
  senior, // Senior
  @JsonValue('family')
  family, // Rodinná
  @JsonValue('group')
  group, // Skupinová
  @JsonValue('disabled')
  disabled, // ZTP
}

/// Skupinová sleva
@JsonSerializable()
class GroupDiscount {
  final int minPeople;
  final double discountPercentage;
  final String description;

  GroupDiscount({
    required this.minPeople,
    required this.discountPercentage,
    required this.description,
  });

  factory GroupDiscount.fromJson(Map<String, dynamic> json) => 
      _$GroupDiscountFromJson(json);
  Map<String, dynamic> toJson() => _$GroupDiscountToJson(this);
}

/// Sezónní sleva
@JsonSerializable()
class SeasonalDiscount {
  final DateTime startDate;
  final DateTime endDate;
  final double discountPercentage;
  final String description;
  final List<TicketCategory> applicableCategories;

  SeasonalDiscount({
    required this.startDate,
    required this.endDate,
    required this.discountPercentage,
    required this.description,
    this.applicableCategories = const [],
  });

  factory SeasonalDiscount.fromJson(Map<String, dynamic> json) => 
      _$SeasonalDiscountFromJson(json);
  Map<String, dynamic> toJson() => _$SeasonalDiscountToJson(this);

  /// Je sleva aktivní pro dané datum?
  bool isActiveForDate(DateTime date) {
    return date.isAfter(startDate.subtract(const Duration(days: 1))) &&
           date.isBefore(endDate.add(const Duration(days: 1)));
  }
}

/// Dostupnost vstupenky
@JsonSerializable()
class TicketAvailability {
  final DateTime startDate;
  final DateTime endDate;
  final List<int> availableDays; // 1-7 (pondělí-neděle)
  final String? openingHours;
  final int? maxCapacity;
  final bool requiresReservation;
  final String? notes;

  TicketAvailability({
    required this.startDate,
    required this.endDate,
    this.availableDays = const [1, 2, 3, 4, 5, 6, 7],
    this.openingHours,
    this.maxCapacity,
    this.requiresReservation = false,
    this.notes,
  });

  factory TicketAvailability.fromJson(Map<String, dynamic> json) => 
      _$TicketAvailabilityFromJson(json);
  Map<String, dynamic> toJson() => _$TicketAvailabilityToJson(this);
}

/// Poskytovatel vstupenek
@JsonSerializable()
class TicketProvider {
  final String name;
  final String officialWebsite;
  final String? bookingUrl;
  final String? phoneNumber;
  final String? email;
  final ProviderType type;
  final bool isVerified;
  final double rating;
  final String? notes;

  TicketProvider({
    required this.name,
    required this.officialWebsite,
    this.bookingUrl,
    this.phoneNumber,
    this.email,
    required this.type,
    this.isVerified = false,
    this.rating = 0.0,
    this.notes,
  });

  factory TicketProvider.fromJson(Map<String, dynamic> json) => 
      _$TicketProviderFromJson(json);
  Map<String, dynamic> toJson() => _$TicketProviderToJson(this);
}

/// Typ poskytovatele
enum ProviderType {
  @JsonValue('official')
  official, // Oficiální
  @JsonValue('authorized')
  authorized, // Autorizovaný
  @JsonValue('partner')
  partner, // Partner
}

/// Funkce vstupenky
@JsonSerializable()
class TicketFeatures {
  final bool hasQRCode;
  final bool isMobileTicket;
  final bool allowsCancellation;
  final bool allowsRescheduling;
  final bool includesAudioGuide;
  final bool includesMap;
  final bool hasGroupGuide;
  final bool isSkipTheLine;
  final List<String> includedServices;
  final List<String> restrictions;

  TicketFeatures({
    this.hasQRCode = true,
    this.isMobileTicket = true,
    this.allowsCancellation = false,
    this.allowsRescheduling = false,
    this.includesAudioGuide = false,
    this.includesMap = false,
    this.hasGroupGuide = false,
    this.isSkipTheLine = false,
    this.includedServices = const [],
    this.restrictions = const [],
  });

  factory TicketFeatures.fromJson(Map<String, dynamic> json) => 
      _$TicketFeaturesFromJson(json);
  Map<String, dynamic> toJson() => _$TicketFeaturesToJson(this);
}
