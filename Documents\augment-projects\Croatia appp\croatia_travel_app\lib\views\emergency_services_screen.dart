import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:geolocator/geolocator.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/emergency_services.dart';
import '../services/emergency_services_service.dart';

class EmergencyServicesScreen extends StatefulWidget {
  const EmergencyServicesScreen({super.key});

  @override
  State<EmergencyServicesScreen> createState() => _EmergencyServicesScreenState();
}

class _EmergencyServicesScreenState extends State<EmergencyServicesScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final EmergencyServicesService _emergencyService = EmergencyServicesService();

  Position? _currentPosition;
  bool _isLoading = true;
  
  List<EmergencyContact> _emergencyContacts = [];
  List<HealthcareProvider> _healthcareProviders = [];
  List<TouristAssistance> _touristAssistance = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeServices();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeServices() async {
    try {
      await _emergencyService.initialize();
      await _getCurrentLocation();
      await _loadAllData();
    } catch (e) {
      debugPrint('Chyba při inicializaci nouzových služeb: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      final permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        await Geolocator.requestPermission();
      }

      _currentPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
    } catch (e) {
      debugPrint('Chyba při získávání lokace: $e');
      // Fallback na Zagreb
      _currentPosition = Position(
        latitude: 45.815,
        longitude: 15.982,
        timestamp: DateTime.now(),
        accuracy: 0,
        altitude: 0,
        altitudeAccuracy: 0,
        heading: 0,
        headingAccuracy: 0,
        speed: 0,
        speedAccuracy: 0,
      );
    }
  }

  Future<void> _loadAllData() async {
    if (_currentPosition == null) return;

    try {
      final results = await Future.wait([
        _emergencyService.getNearbyEmergencyContacts(
          latitude: _currentPosition!.latitude,
          longitude: _currentPosition!.longitude,
        ),
        _emergencyService.getNearbyHealthcareProviders(
          latitude: _currentPosition!.latitude,
          longitude: _currentPosition!.longitude,
        ),
        _emergencyService.getNearbyTouristAssistance(
          latitude: _currentPosition!.latitude,
          longitude: _currentPosition!.longitude,
        ),
      ]);

      if (mounted) {
        setState(() {
          _emergencyContacts = results[0] as List<EmergencyContact>;
          _healthcareProviders = results[1] as List<HealthcareProvider>;
          _touristAssistance = results[2] as List<TouristAssistance>;
        });
      }
    } catch (e) {
      debugPrint('Chyba při načítání dat: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'NOUZOVÉ SLUŽBY',
          style: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFFDC143C).withValues(alpha: 0.9), // Červená pro nouzové služby
                const Color(0xFFB22222).withValues(alpha: 0.8),
                const Color(0xFFDC143C).withValues(alpha: 0.9),
              ],
            ),
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.emergency), text: 'Nouzové'),
            Tab(icon: Icon(Icons.local_hospital), text: 'Zdravotní'),
            Tab(icon: Icon(Icons.info), text: 'Turistické'),
            Tab(icon: Icon(Icons.phone), text: 'Rychlé volání'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildEmergencyContactsTab(),
                _buildHealthcareTab(),
                _buildTouristAssistanceTab(),
                _buildQuickCallTab(),
              ],
            ),
    );
  }

  Widget _buildEmergencyContactsTab() {
    if (_emergencyContacts.isEmpty) {
      return const Center(
        child: Text('Načítání nouzových kontaktů...'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _emergencyContacts.length,
      itemBuilder: (context, index) {
        final contact = _emergencyContacts[index];
        return _buildEmergencyContactCard(contact);
      },
    );
  }

  Widget _buildHealthcareTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _healthcareProviders.length,
      itemBuilder: (context, index) {
        final provider = _healthcareProviders[index];
        return _buildHealthcareCard(provider);
      },
    );
  }

  Widget _buildTouristAssistanceTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _touristAssistance.length,
      itemBuilder: (context, index) {
        final assistance = _touristAssistance[index];
        return _buildTouristAssistanceCard(assistance);
      },
    );
  }

  Widget _buildQuickCallTab() {
    final quickNumbers = _emergencyService.getEmergencyNumbers();
    
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Varování
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                const Icon(Icons.warning, color: Colors.red),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Pouze pro skutečné nouzové situace!',
                    style: GoogleFonts.inter(
                      fontWeight: FontWeight.w600,
                      color: Colors.red[700],
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          
          // Rychlá tlačítka
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 1.2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: quickNumbers.length,
              itemBuilder: (context, index) {
                final contact = quickNumbers[index];
                return _buildQuickCallButton(contact);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmergencyContactCard(EmergencyContact contact) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(_getEmergencyIcon(contact.type), color: Colors.red),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    contact.nameEn,
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (contact.isOfficial)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'OFICIÁLNÍ',
                      style: GoogleFonts.inter(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: Colors.green[700],
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              contact.descriptionEn,
              style: GoogleFonts.inter(color: Colors.grey[600]),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _makePhoneCall(contact.phoneNumber),
                    icon: const Icon(Icons.phone),
                    label: Text(contact.phoneNumber),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHealthcareCard(HealthcareProvider provider) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  provider.isEmergency ? Icons.emergency : Icons.local_hospital,
                  color: provider.isEmergency ? Colors.red : Colors.blue,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    provider.nameEn,
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              provider.type.toUpperCase(),
              style: GoogleFonts.inter(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              provider.addressEn,
              style: GoogleFonts.inter(color: Colors.grey[600]),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _makePhoneCall(provider.phoneNumber),
                    icon: const Icon(Icons.phone),
                    label: Text(provider.phoneNumber),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: provider.isEmergency ? Colors.red : Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTouristAssistanceCard(TouristAssistance assistance) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info, color: Colors.blue),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    assistance.nameEn,
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              assistance.addressEn,
              style: GoogleFonts.inter(color: Colors.grey[600]),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _makePhoneCall(assistance.phoneNumber),
                    icon: const Icon(Icons.phone),
                    label: Text(assistance.phoneNumber),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickCallButton(EmergencyContact contact) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.red.withValues(alpha: 0.8),
            Colors.red.withValues(alpha: 0.9),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => _makePhoneCall(contact.phoneNumber),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _getEmergencyIcon(contact.type),
                  size: 40,
                  color: Colors.white,
                ),
                const SizedBox(height: 12),
                Text(
                  contact.phoneNumber,
                  style: GoogleFonts.inter(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  contact.nameEn,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getEmergencyIcon(EmergencyType type) {
    switch (type) {
      case EmergencyType.police:
        return Icons.local_police;
      case EmergencyType.fire:
        return Icons.local_fire_department;
      case EmergencyType.medical:
        return Icons.medical_services;
      case EmergencyType.mountain:
        return Icons.terrain;
      case EmergencyType.sea:
        return Icons.sailing;
      case EmergencyType.tourist:
        return Icons.info;
      case EmergencyType.consulate:
        return Icons.account_balance;
      case EmergencyType.roadside:
        return Icons.car_repair;
    }
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final uri = Uri.parse('tel:$phoneNumber');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Nelze volat číslo: $phoneNumber')),
        );
      }
    }
  }
}
