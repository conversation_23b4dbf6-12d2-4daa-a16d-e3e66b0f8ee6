// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_profile.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserProfile _$UserProfileFromJson(Map<String, dynamic> json) => UserProfile(
      id: json['id'] as String,
      username: json['username'] as String,
      email: json['email'] as String,
      displayName: json['displayName'] as String?,
      avatarUrl: json['avatarUrl'] as String?,
      bio: json['bio'] as String?,
      location: json['location'] as String?,
      birthDate: json['birthDate'] == null
          ? null
          : DateTime.parse(json['birthDate'] as String),
      interests: (json['interests'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      languages: (json['languages'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      preferences:
          UserPreferences.fromJson(json['preferences'] as Map<String, dynamic>),
      stats: UserStats.fromJson(json['stats'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isPublic: json['isPublic'] as bool? ?? false,
      isVerified: json['isVerified'] as bool? ?? false,
    );

Map<String, dynamic> _$UserProfileToJson(UserProfile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'email': instance.email,
      'displayName': instance.displayName,
      'avatarUrl': instance.avatarUrl,
      'bio': instance.bio,
      'location': instance.location,
      'birthDate': instance.birthDate?.toIso8601String(),
      'interests': instance.interests,
      'languages': instance.languages,
      'preferences': instance.preferences,
      'stats': instance.stats,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isPublic': instance.isPublic,
      'isVerified': instance.isVerified,
    };

UserPreferences _$UserPreferencesFromJson(Map<String, dynamic> json) =>
    UserPreferences(
      language: json['language'] as String? ?? 'cs',
      currency: json['currency'] as String? ?? 'EUR',
      dateFormat: json['dateFormat'] as String? ?? 'dd.MM.yyyy',
      timeFormat: json['timeFormat'] as String? ?? '24h',
      notificationsEnabled: json['notificationsEnabled'] as bool? ?? true,
      locationSharingEnabled: json['locationSharingEnabled'] as bool? ?? false,
      publicProfile: json['publicProfile'] as bool? ?? false,
      autoSync: json['autoSync'] as bool? ?? true,
      batterySaverMode: json['batterySaverMode'] as bool? ?? false,
      voiceSettings:
          VoiceSettings.fromJson(json['voiceSettings'] as Map<String, dynamic>),
      privacySettings: PrivacySettings.fromJson(
          json['privacySettings'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$UserPreferencesToJson(UserPreferences instance) =>
    <String, dynamic>{
      'language': instance.language,
      'currency': instance.currency,
      'dateFormat': instance.dateFormat,
      'timeFormat': instance.timeFormat,
      'notificationsEnabled': instance.notificationsEnabled,
      'locationSharingEnabled': instance.locationSharingEnabled,
      'publicProfile': instance.publicProfile,
      'autoSync': instance.autoSync,
      'batterySaverMode': instance.batterySaverMode,
      'voiceSettings': instance.voiceSettings,
      'privacySettings': instance.privacySettings,
    };

VoiceSettings _$VoiceSettingsFromJson(Map<String, dynamic> json) =>
    VoiceSettings(
      voiceNotesEnabled: json['voiceNotesEnabled'] as bool? ?? true,
      speechToTextEnabled: json['speechToTextEnabled'] as bool? ?? true,
      textToSpeechEnabled: json['textToSpeechEnabled'] as bool? ?? true,
      preferredLanguage: json['preferredLanguage'] as String? ?? 'cs-CZ',
      speechRate: (json['speechRate'] as num?)?.toDouble() ?? 0.8,
      volume: (json['volume'] as num?)?.toDouble() ?? 1.0,
      pitch: (json['pitch'] as num?)?.toDouble() ?? 1.0,
      autoTranscription: json['autoTranscription'] as bool? ?? false,
    );

Map<String, dynamic> _$VoiceSettingsToJson(VoiceSettings instance) =>
    <String, dynamic>{
      'voiceNotesEnabled': instance.voiceNotesEnabled,
      'speechToTextEnabled': instance.speechToTextEnabled,
      'textToSpeechEnabled': instance.textToSpeechEnabled,
      'preferredLanguage': instance.preferredLanguage,
      'speechRate': instance.speechRate,
      'volume': instance.volume,
      'pitch': instance.pitch,
      'autoTranscription': instance.autoTranscription,
    };

PrivacySettings _$PrivacySettingsFromJson(Map<String, dynamic> json) =>
    PrivacySettings(
      shareLocation: json['shareLocation'] as bool? ?? false,
      shareTrips: json['shareTrips'] as bool? ?? true,
      sharePhotos: json['sharePhotos'] as bool? ?? true,
      shareVoiceNotes: json['shareVoiceNotes'] as bool? ?? false,
      allowInvitations: json['allowInvitations'] as bool? ?? true,
      showOnlineStatus: json['showOnlineStatus'] as bool? ?? true,
      blockedUsers: (json['blockedUsers'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$PrivacySettingsToJson(PrivacySettings instance) =>
    <String, dynamic>{
      'shareLocation': instance.shareLocation,
      'shareTrips': instance.shareTrips,
      'sharePhotos': instance.sharePhotos,
      'shareVoiceNotes': instance.shareVoiceNotes,
      'allowInvitations': instance.allowInvitations,
      'showOnlineStatus': instance.showOnlineStatus,
      'blockedUsers': instance.blockedUsers,
    };

UserStats _$UserStatsFromJson(Map<String, dynamic> json) => UserStats(
      totalTrips: (json['totalTrips'] as num?)?.toInt() ?? 0,
      placesVisited: (json['placesVisited'] as num?)?.toInt() ?? 0,
      diaryEntries: (json['diaryEntries'] as num?)?.toInt() ?? 0,
      voiceNotes: (json['voiceNotes'] as num?)?.toInt() ?? 0,
      photosShared: (json['photosShared'] as num?)?.toInt() ?? 0,
      collaborativeTrips: (json['collaborativeTrips'] as num?)?.toInt() ?? 0,
      friendsCount: (json['friendsCount'] as num?)?.toInt() ?? 0,
      totalTravelTime: json['totalTravelTime'] == null
          ? Duration.zero
          : Duration(microseconds: (json['totalTravelTime'] as num).toInt()),
      totalDistance: (json['totalDistance'] as num?)?.toDouble() ?? 0.0,
      lastActivity: json['lastActivity'] == null
          ? null
          : DateTime.parse(json['lastActivity'] as String),
    );

Map<String, dynamic> _$UserStatsToJson(UserStats instance) => <String, dynamic>{
      'totalTrips': instance.totalTrips,
      'placesVisited': instance.placesVisited,
      'diaryEntries': instance.diaryEntries,
      'voiceNotes': instance.voiceNotes,
      'photosShared': instance.photosShared,
      'collaborativeTrips': instance.collaborativeTrips,
      'friendsCount': instance.friendsCount,
      'totalTravelTime': instance.totalTravelTime.inMicroseconds,
      'totalDistance': instance.totalDistance,
      'lastActivity': instance.lastActivity?.toIso8601String(),
    };
