import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/ticket.dart';

/// Služba pro správu vstupenek s doporučujícími od<PERSON>zy
class TicketService {
  static final TicketService _instance = TicketService._internal();
  factory TicketService() => _instance;
  TicketService._internal();

  // Cache a stav
  List<Ticket> _tickets = [];
  List<Ticket> _favoriteTickets = [];
  bool _isLoading = false;

  /// Inicializace služby
  Future<void> initialize() async {
    try {
      await _loadTickets();
      await _loadFavorites();
      debugPrint('TicketService inicializován úspěšně');
    } catch (e) {
      debugPrint('Chyba při inicializaci TicketService: $e');
    }
  }

  /// Načte vstupenky (oficiální data bez scrapování)
  Future<void> _loadTickets() async {
    _isLoading = true;

    try {
      // Oficiální vstupenky pro chorvatské památky a muzea
      _tickets = [
        // Dubrovník
        _createDubrovnikCityWallsTicket(),
        _createDubrovnikPalaceTicket(),
        _createDubrovnikMuseumTicket(),

        // Split
        _createDiocletianPalaceTicket(),

        // Zagreb
        _createZagrebMuseumTicket(),
        _createZagrebCathedralTicket(),

        // Plitvice
        _createPlitviceTicket(),

        // Krka
        _createKrkaTicket(),

        // Hvar
        _createHvarFortressTicket(),
      ];
    } catch (e) {
      debugPrint('Chyba při načítání vstupenek: $e');
    } finally {
      _isLoading = false;
    }
  }

  /// Vytvoří vstupenku pro Dubrovnické hradby
  Ticket _createDubrovnikCityWallsTicket() {
    return Ticket(
      id: 'dubrovnik_walls',
      title: 'Dubrovnické hradby',
      description:
          'Procházka po slavných hradbách Starého města Dubrovníku s úžasným výhledem na Jaderské moře.',
      type: TicketType.monument,
      venueId: 'dubrovnik_old_town',
      venueName: 'Staré město Dubrovník',
      location: 'Dubrovník, Chorvatsko',
      latitude: 42.6407,
      longitude: 18.1077,
      region: 'Dubrovnicko-neretvanská',
      pricing: TicketPricing(
        categories: [
          TicketCategoryPricing(
            category: TicketCategory.adult,
            price: 35.0,
            description: 'Dospělý (18+ let)',
          ),
          TicketCategoryPricing(
            category: TicketCategory.child,
            price: 15.0,
            description: 'Dítě (7-17 let)',
            minAge: 7,
            maxAge: 17,
          ),
          TicketCategoryPricing(
            category: TicketCategory.student,
            price: 25.0,
            description: 'Student s průkazem',
            requiresProof: true,
          ),
        ],
        groupDiscounts: [
          GroupDiscount(
            minPeople: 10,
            discountPercentage: 10.0,
            description: '10% sleva pro skupiny 10+ osob',
          ),
          GroupDiscount(
            minPeople: 20,
            discountPercentage: 15.0,
            description: '15% sleva pro skupiny 20+ osob',
          ),
        ],
        seasonalDiscounts: [
          SeasonalDiscount(
            startDate: DateTime(2024, 11, 1),
            endDate: DateTime(2025, 3, 31),
            discountPercentage: 20.0,
            description: 'Zimní sleva 20%',
          ),
        ],
        currency: 'EUR',
      ),
      availability: [
        TicketAvailability(
          startDate: DateTime(2024, 1, 1),
          endDate: DateTime(2024, 12, 31),
          availableDays: [1, 2, 3, 4, 5, 6, 7],
          openingHours: '8:00 - 19:00 (léto), 9:00 - 15:00 (zima)',
          requiresReservation: false,
        ),
      ],
      provider: TicketProvider(
        name: 'Dubrovnik Tourist Board',
        officialWebsite: 'https://www.dubrovnik.hr',
        bookingUrl: 'https://www.dubrovnik.hr/en/visit/city-walls',
        phoneNumber: '+385 20 312 011',
        type: ProviderType.official,
        isVerified: true,
        rating: 4.8,
      ),
      images: [
        'assets/images/dubrovnik_walls_1.jpg',
        'assets/images/dubrovnik_walls_2.jpg',
      ],
      tags: ['UNESCO', 'hradby', 'výhled', 'historie'],
      features: TicketFeatures(
        hasQRCode: true,
        isMobileTicket: true,
        includesMap: true,
        isSkipTheLine: false,
        includedServices: ['Mapa', 'Informační brožura'],
        restrictions: ['Není vhodné pro vozíčkáře', 'Pohodlná obuv doporučena'],
      ),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Vytvoří vstupenku pro Diokleciánův palác
  Ticket _createDiocletianPalaceTicket() {
    return Ticket(
      id: 'diocletian_palace',
      title: 'Diokleciánův palác - Prohlídka',
      description:
          'Guidovaná prohlídka nejlépe zachovaného římského paláce na světě.',
      type: TicketType.tour,
      venueId: 'split_palace',
      venueName: 'Diokleciánův palác',
      location: 'Split, Chorvatsko',
      latitude: 43.5081,
      longitude: 16.4402,
      region: 'Splitsko-dalmatinská',
      pricing: TicketPricing(
        categories: [
          TicketCategoryPricing(
            category: TicketCategory.adult,
            price: 25.0,
            description: 'Dospělý (18+ let)',
          ),
          TicketCategoryPricing(
            category: TicketCategory.child,
            price: 12.0,
            description: 'Dítě (6-17 let)',
            minAge: 6,
            maxAge: 17,
          ),
          TicketCategoryPricing(
            category: TicketCategory.family,
            price: 60.0,
            description: 'Rodinná (2 dospělí + 2 děti)',
          ),
        ],
        groupDiscounts: [
          GroupDiscount(
            minPeople: 8,
            discountPercentage: 12.0,
            description: '12% sleva pro skupiny 8+ osob',
          ),
        ],
      ),
      availability: [
        TicketAvailability(
          startDate: DateTime(2024, 1, 1),
          endDate: DateTime(2024, 12, 31),
          availableDays: [1, 2, 3, 4, 5, 6, 7],
          openingHours: '9:00, 11:00, 14:00, 16:00',
          requiresReservation: true,
        ),
      ],
      provider: TicketProvider(
        name: 'Split Tourist Board',
        officialWebsite: 'https://www.visitsplit.com',
        bookingUrl: 'https://www.visitsplit.com/en/diocletians-palace-tours',
        phoneNumber: '+385 21 345 606',
        type: ProviderType.official,
        isVerified: true,
        rating: 4.7,
      ),
      images: [
        'assets/images/diocletian_palace_1.jpg',
        'assets/images/diocletian_palace_2.jpg',
      ],
      tags: ['UNESCO', 'římské', 'palác', 'prohlídka'],
      features: TicketFeatures(
        hasQRCode: true,
        isMobileTicket: true,
        hasGroupGuide: true,
        includesAudioGuide: true,
        allowsCancellation: true,
        includedServices: ['Průvodce', 'Audio guide', 'Mapa'],
      ),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Vytvoří vstupenku pro Plitvická jezera
  Ticket _createPlitviceTicket() {
    return Ticket(
      id: 'plitvice_lakes',
      title: 'Plitvická jezera - Národní park',
      description:
          'Vstup do nejkrásnějšího národního parku Chorvatska s kaskádami jezer a vodopádů.',
      type: TicketType.park,
      venueId: 'plitvice_np',
      venueName: 'Národní park Plitvická jezera',
      location: 'Plitvička jezera, Chorvatsko',
      latitude: 44.8654,
      longitude: 15.5820,
      region: 'Ličko-senjská',
      pricing: TicketPricing(
        categories: [
          TicketCategoryPricing(
            category: TicketCategory.adult,
            price: 40.0, // Vysoká sezóna
            description: 'Dospělý (18+ let)',
          ),
          TicketCategoryPricing(
            category: TicketCategory.child,
            price: 25.0,
            description: 'Dítě (7-17 let)',
            minAge: 7,
            maxAge: 17,
          ),
          TicketCategoryPricing(
            category: TicketCategory.student,
            price: 30.0,
            description: 'Student s průkazem',
            requiresProof: true,
          ),
        ],
        seasonalDiscounts: [
          SeasonalDiscount(
            startDate: DateTime(2024, 11, 1),
            endDate: DateTime(2025, 3, 31),
            discountPercentage: 50.0,
            description: 'Zimní ceny - 50% sleva',
          ),
          SeasonalDiscount(
            startDate: DateTime(2024, 4, 1),
            endDate: DateTime(2024, 6, 30),
            discountPercentage: 25.0,
            description: 'Jarní sleva - 25% sleva',
          ),
        ],
      ),
      availability: [
        TicketAvailability(
          startDate: DateTime(2024, 1, 1),
          endDate: DateTime(2024, 12, 31),
          availableDays: [1, 2, 3, 4, 5, 6, 7],
          openingHours: '7:00 - 20:00 (léto), 8:00 - 16:00 (zima)',
          maxCapacity: 4000,
          requiresReservation: true,
          notes: 'Rezervace povinná v hlavní sezóně',
        ),
      ],
      provider: TicketProvider(
        name: 'Národní park Plitvická jezera',
        officialWebsite: 'https://np-plitvicka-jezera.hr',
        bookingUrl: 'https://np-plitvicka-jezera.hr/en/plan-your-visit/',
        phoneNumber: '+385 53 751 015',
        type: ProviderType.official,
        isVerified: true,
        rating: 4.9,
      ),
      images: ['assets/images/plitvice_1.jpg', 'assets/images/plitvice_2.jpg'],
      tags: ['UNESCO', 'národní park', 'jezera', 'vodopády', 'příroda'],
      features: TicketFeatures(
        hasQRCode: true,
        isMobileTicket: true,
        includesMap: true,
        allowsCancellation: true,
        allowsRescheduling: true,
        includedServices: [
          'Mapa tras',
          'Informační brožura',
          'Panoramický vlak',
        ],
        restrictions: [
          'Zákaz koupání',
          'Zákaz pikniků',
          'Pouze značené stezky',
        ],
      ),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Otevře oficiální stránku pro rezervaci
  Future<void> openBookingPage(Ticket ticket) async {
    try {
      final url = ticket.provider.bookingUrl ?? ticket.provider.officialWebsite;
      final uri = Uri.parse(url);

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        debugPrint('Nelze otevřít URL: $url');
      }
    } catch (e) {
      debugPrint('Chyba při otevírání rezervační stránky: $e');
    }
  }

  /// Přidá vstupenku do oblíbených
  Future<void> addToFavorites(Ticket ticket) async {
    if (!_favoriteTickets.any((t) => t.id == ticket.id)) {
      _favoriteTickets.add(ticket);
      await _saveFavorites();
    }
  }

  /// Odebere vstupenku z oblíbených
  Future<void> removeFromFavorites(String ticketId) async {
    _favoriteTickets.removeWhere((t) => t.id == ticketId);
    await _saveFavorites();
  }

  /// Je vstupenka v oblíbených?
  bool isFavorite(String ticketId) {
    return _favoriteTickets.any((t) => t.id == ticketId);
  }

  /// Vyhledá vstupenky podle kritérií
  List<Ticket> searchTickets({
    String? query,
    TicketType? type,
    String? region,
    double? maxPrice,
    bool? hasGroupDiscounts,
    bool? hasSeasonalDiscounts,
  }) {
    return _tickets.where((ticket) {
      if (query != null && query.isNotEmpty) {
        final searchLower = query.toLowerCase();
        if (!ticket.title.toLowerCase().contains(searchLower) &&
            !ticket.description.toLowerCase().contains(searchLower) &&
            !ticket.tags.any(
              (tag) => tag.toLowerCase().contains(searchLower),
            )) {
          return false;
        }
      }

      if (type != null && ticket.type != type) return false;
      if (region != null && ticket.region != region) return false;

      if (maxPrice != null) {
        final adultPrice = ticket.pricing.categories
            .where((cat) => cat.category == TicketCategory.adult)
            .firstOrNull
            ?.price;
        if (adultPrice != null && adultPrice > maxPrice) return false;
      }

      if (hasGroupDiscounts == true && !ticket.hasGroupDiscounts) return false;
      if (hasSeasonalDiscounts == true && !ticket.hasSeasonalDiscounts) {
        return false;
      }

      return true;
    }).toList();
  }

  /// Načte oblíbené vstupenky
  Future<void> _loadFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoriteIds = prefs.getStringList('favorite_tickets') ?? [];
      _favoriteTickets = _tickets
          .where((t) => favoriteIds.contains(t.id))
          .toList();
    } catch (e) {
      debugPrint('Chyba při načítání oblíbených vstupenek: $e');
    }
  }

  /// Uloží oblíbené vstupenky
  Future<void> _saveFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoriteIds = _favoriteTickets.map((t) => t.id).toList();
      await prefs.setStringList('favorite_tickets', favoriteIds);
    } catch (e) {
      debugPrint('Chyba při ukládání oblíbených vstupenek: $e');
    }
  }

  // Gettery
  List<Ticket> get tickets => _tickets;
  List<Ticket> get favoriteTickets => _favoriteTickets;
  bool get isLoading => _isLoading;

  /// Získá vstupenky podle typu
  List<Ticket> getTicketsByType(TicketType type) {
    return _tickets.where((t) => t.type == type).toList();
  }

  /// Získá vstupenky podle regionu
  List<Ticket> getTicketsByRegion(String region) {
    return _tickets.where((t) => t.region == region).toList();
  }

  /// Vytvoří vstupenku pro Záhřebské muzeum
  Ticket _createZagrebMuseumTicket() {
    return Ticket(
      id: 'zagreb_museum',
      title: 'Muzeum města Záhřebu',
      description: 'Objevte bohatou historii hlavního města Chorvatska.',
      type: TicketType.museum,
      venueId: 'zagreb_city_museum',
      venueName: 'Muzeum města Záhřebu',
      location: 'Záhřeb, Chorvatsko',
      latitude: 45.8150,
      longitude: 15.9819,
      region: 'Záhřebská',
      pricing: TicketPricing(
        categories: [
          TicketCategoryPricing(
            category: TicketCategory.adult,
            price: 8.0,
            description: 'Dospělý',
          ),
          TicketCategoryPricing(
            category: TicketCategory.child,
            price: 4.0,
            description: 'Dítě (6-17 let)',
            minAge: 6,
            maxAge: 17,
          ),
        ],
      ),
      availability: [
        TicketAvailability(
          startDate: DateTime(2024, 1, 1),
          endDate: DateTime(2024, 12, 31),
          availableDays: [2, 3, 4, 5, 6, 7], // Pondělí zavřeno
          openingHours: '10:00 - 18:00',
        ),
      ],
      provider: TicketProvider(
        name: 'Zagreb Tourist Board',
        officialWebsite: 'https://www.infozagreb.hr',
        type: ProviderType.official,
        isVerified: true,
        rating: 4.5,
      ),
      images: ['assets/images/zagreb_museum.jpg'],
      tags: ['muzeum', 'historie', 'záhřeb'],
      features: TicketFeatures(
        hasQRCode: true,
        isMobileTicket: true,
        includesMap: true,
      ),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Vytvoří vstupenku pro Záhřebskou katedrálu
  Ticket _createZagrebCathedralTicket() {
    return Ticket(
      id: 'zagreb_cathedral',
      title: 'Záhřebská katedrála',
      description: 'Návštěva nejznámější sakrální stavby Chorvatska.',
      type: TicketType.monument,
      venueId: 'zagreb_cathedral',
      venueName: 'Katedrála Nanebevzetí Panny Marie',
      location: 'Záhřeb, Chorvatsko',
      latitude: 45.8144,
      longitude: 15.9798,
      region: 'Záhřebská',
      pricing: TicketPricing(
        categories: [
          TicketCategoryPricing(
            category: TicketCategory.adult,
            price: 0.0, // Vstup zdarma
            description: 'Vstup zdarma',
          ),
        ],
      ),
      availability: [
        TicketAvailability(
          startDate: DateTime(2024, 1, 1),
          endDate: DateTime(2024, 12, 31),
          availableDays: [1, 2, 3, 4, 5, 6, 7],
          openingHours: '6:30 - 20:00',
        ),
      ],
      provider: TicketProvider(
        name: 'Záhřebská arcidiecéze',
        officialWebsite: 'https://www.zg-nadbiskupija.hr',
        type: ProviderType.official,
        isVerified: true,
        rating: 4.6,
      ),
      images: ['assets/images/zagreb_cathedral.jpg'],
      tags: ['katedrála', 'gotika', 'zdarma'],
      features: TicketFeatures(
        hasQRCode: false,
        isMobileTicket: false,
        restrictions: ['Tichý chod během bohoslužeb', 'Vhodné oblečení'],
      ),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Vytvoří vstupenku pro Krka
  Ticket _createKrkaTicket() {
    return Ticket(
      id: 'krka_national_park',
      title: 'Národní park Krka',
      description: 'Vodopády a přírodní krásy řeky Krka.',
      type: TicketType.park,
      venueId: 'krka_np',
      venueName: 'Národní park Krka',
      location: 'Krka, Chorvatsko',
      latitude: 43.8069,
      longitude: 15.9614,
      region: 'Šibenicko-kninská',
      pricing: TicketPricing(
        categories: [
          TicketCategoryPricing(
            category: TicketCategory.adult,
            price: 30.0,
            description: 'Dospělý (18+ let)',
          ),
          TicketCategoryPricing(
            category: TicketCategory.child,
            price: 20.0,
            description: 'Dítě (7-17 let)',
            minAge: 7,
            maxAge: 17,
          ),
        ],
        seasonalDiscounts: [
          SeasonalDiscount(
            startDate: DateTime(2024, 11, 1),
            endDate: DateTime(2025, 3, 31),
            discountPercentage: 40.0,
            description: 'Zimní sleva 40%',
          ),
        ],
      ),
      availability: [
        TicketAvailability(
          startDate: DateTime(2024, 1, 1),
          endDate: DateTime(2024, 12, 31),
          availableDays: [1, 2, 3, 4, 5, 6, 7],
          openingHours: '8:00 - 20:00 (léto), 9:00 - 16:00 (zima)',
        ),
      ],
      provider: TicketProvider(
        name: 'Národní park Krka',
        officialWebsite: 'https://www.npkrka.hr',
        bookingUrl: 'https://www.npkrka.hr/en/plan-your-visit/',
        type: ProviderType.official,
        isVerified: true,
        rating: 4.8,
      ),
      images: ['assets/images/krka_1.jpg'],
      tags: ['národní park', 'vodopády', 'příroda', 'koupání'],
      features: TicketFeatures(
        hasQRCode: true,
        isMobileTicket: true,
        includesMap: true,
        includedServices: ['Mapa', 'Informační brožura'],
        restrictions: [
          'Označené stezky pouze',
          'Koupání povoleno na vybraných místech',
        ],
      ),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Vytvoří vstupenku pro Hvarskou pevnost
  Ticket _createHvarFortressTicket() {
    return Ticket(
      id: 'hvar_fortress',
      title: 'Španělská pevnost Hvar',
      description: 'Historická pevnost s úžasným výhledem na ostrov Hvar.',
      type: TicketType.monument,
      venueId: 'hvar_fortress',
      venueName: 'Španělská pevnost',
      location: 'Hvar, Chorvatsko',
      latitude: 43.1729,
      longitude: 16.4414,
      region: 'Splitsko-dalmatinská',
      pricing: TicketPricing(
        categories: [
          TicketCategoryPricing(
            category: TicketCategory.adult,
            price: 15.0,
            description: 'Dospělý',
          ),
          TicketCategoryPricing(
            category: TicketCategory.child,
            price: 8.0,
            description: 'Dítě (6-17 let)',
            minAge: 6,
            maxAge: 17,
          ),
        ],
      ),
      availability: [
        TicketAvailability(
          startDate: DateTime(2024, 4, 1),
          endDate: DateTime(2024, 10, 31),
          availableDays: [1, 2, 3, 4, 5, 6, 7],
          openingHours: '9:00 - 19:00',
        ),
      ],
      provider: TicketProvider(
        name: 'Hvar Tourist Board',
        officialWebsite: 'https://www.tzhvar.hr',
        type: ProviderType.official,
        isVerified: true,
        rating: 4.4,
      ),
      images: ['assets/images/hvar_fortress.jpg'],
      tags: ['pevnost', 'výhled', 'historie'],
      features: TicketFeatures(
        hasQRCode: true,
        isMobileTicket: true,
        restrictions: ['Pohodlná obuv doporučena', 'Strmý výstup'],
      ),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Vytvoří vstupenku pro Knížecí palác Dubrovník
  Ticket _createDubrovnikPalaceTicket() {
    return Ticket(
      id: 'dubrovnik_palace',
      title: 'Knížecí palác Dubrovník',
      description:
          'Historický palác a muzeum s bohatou historií Dubrovnické republiky.',
      type: TicketType.museum,
      venueId: 'dubrovnik_palace',
      venueName: 'Knížecí palác',
      location: 'Dubrovník, Chorvatsko',
      latitude: 42.6407,
      longitude: 18.1077,
      region: 'Dubrovnicko-neretvanská',
      pricing: TicketPricing(
        categories: [
          TicketCategoryPricing(
            category: TicketCategory.adult,
            price: 12.0,
            description: 'Dospělý',
          ),
          TicketCategoryPricing(
            category: TicketCategory.child,
            price: 6.0,
            description: 'Dítě (6-17 let)',
            minAge: 6,
            maxAge: 17,
          ),
        ],
      ),
      availability: [
        TicketAvailability(
          startDate: DateTime(2024, 1, 1),
          endDate: DateTime(2024, 12, 31),
          availableDays: [1, 2, 3, 4, 5, 6, 7],
          openingHours: '9:00 - 18:00',
        ),
      ],
      provider: TicketProvider(
        name: 'Dubrovnik Museums',
        officialWebsite: 'https://www.dumus.hr',
        type: ProviderType.official,
        isVerified: true,
        rating: 4.6,
      ),
      images: ['assets/images/dubrovnik_palace.jpg'],
      tags: ['muzeum', 'palác', 'historie'],
      features: TicketFeatures(
        hasQRCode: true,
        isMobileTicket: true,
        includesMap: true,
      ),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Vytvoří vstupenku pro Dubrovnické muzeum
  Ticket _createDubrovnikMuseumTicket() {
    return Ticket(
      id: 'dubrovnik_museum',
      title: 'Dubrovnické muzeum',
      description: 'Kulturní dědictví a historie Dubrovníku.',
      type: TicketType.museum,
      venueId: 'dubrovnik_museum',
      venueName: 'Dubrovnické muzeum',
      location: 'Dubrovník, Chorvatsko',
      latitude: 42.6407,
      longitude: 18.1077,
      region: 'Dubrovnicko-neretvanská',
      pricing: TicketPricing(
        categories: [
          TicketCategoryPricing(
            category: TicketCategory.adult,
            price: 10.0,
            description: 'Dospělý',
          ),
          TicketCategoryPricing(
            category: TicketCategory.child,
            price: 5.0,
            description: 'Dítě (6-17 let)',
            minAge: 6,
            maxAge: 17,
          ),
        ],
      ),
      availability: [
        TicketAvailability(
          startDate: DateTime(2024, 1, 1),
          endDate: DateTime(2024, 12, 31),
          availableDays: [1, 2, 3, 4, 5, 6, 7],
          openingHours: '9:00 - 17:00',
        ),
      ],
      provider: TicketProvider(
        name: 'Dubrovnik Museums',
        officialWebsite: 'https://www.dumus.hr',
        type: ProviderType.official,
        isVerified: true,
        rating: 4.5,
      ),
      images: ['assets/images/dubrovnik_museum.jpg'],
      tags: ['muzeum', 'kultura', 'historie'],
      features: TicketFeatures(hasQRCode: true, isMobileTicket: true),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
}
