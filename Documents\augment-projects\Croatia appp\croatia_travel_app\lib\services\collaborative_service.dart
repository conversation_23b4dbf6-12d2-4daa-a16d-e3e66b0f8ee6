import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/place.dart';
import '../models/collaborative_trip.dart';

class CollaborativeService {
  static final CollaborativeService _instance =
      CollaborativeService._internal();
  factory CollaborativeService() => _instance;
  CollaborativeService._internal();

  StreamController<CollaborativeEvent>? _eventController;
  final Map<String, CollaborativeTrip> _activeTrips = {};
  final Map<String, List<TripInvitation>> _invitations = {};

  /// Stream událostí kolaborace
  Stream<CollaborativeEvent> get eventStream {
    _eventController ??= StreamController<CollaborativeEvent>.broadcast();
    return _eventController!.stream;
  }

  /// Vytvoření sdílené cesty
  Future<CollaborativeTrip> createSharedTrip({
    required String name,
    required String description,
    required DateTime startDate,
    required DateTime endDate,
    required String creatorId,
    List<String> invitedUserIds = const [],
  }) async {
    try {
      final trip = CollaborativeTrip(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        description: description,
        startDate: startDate,
        endDate: endDate,
        creatorId: creatorId,
        participants: [creatorId],
        invitedUsers: invitedUserIds,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      _activeTrips[trip.id] = trip;

      // Odeslání pozvánek
      for (final userId in invitedUserIds) {
        await _sendInvitation(trip, userId);
      }

      _eventController?.add(
        CollaborativeEvent(
          type: CollaborativeEventType.tripCreated,
          trip: trip,
        ),
      );

      return trip;
    } catch (e) {
      _eventController?.add(
        CollaborativeEvent(
          type: CollaborativeEventType.error,
          error: 'Chyba při vytváření sdílené cesty: $e',
        ),
      );
      rethrow;
    }
  }

  /// Pozvání uživatele na cestu
  Future<void> inviteUserToTrip(String tripId, String userId) async {
    try {
      final trip = _activeTrips[tripId];
      if (trip == null) {
        throw Exception('Cesta nebyla nalezena');
      }

      if (trip.participants.contains(userId) ||
          trip.invitedUsers.contains(userId)) {
        throw Exception('Uživatel je již pozván nebo účastní');
      }

      final updatedTrip = trip.copyWith(
        invitedUsers: [...trip.invitedUsers, userId],
        updatedAt: DateTime.now(),
      );

      _activeTrips[tripId] = updatedTrip;

      await _sendInvitation(updatedTrip, userId);

      _eventController?.add(
        CollaborativeEvent(
          type: CollaborativeEventType.userInvited,
          trip: updatedTrip,
          userId: userId,
        ),
      );
    } catch (e) {
      _eventController?.add(
        CollaborativeEvent(
          type: CollaborativeEventType.error,
          error: 'Chyba při pozývání uživatele: $e',
        ),
      );
    }
  }

  /// Přijetí pozvánky
  Future<void> acceptInvitation(String tripId, String userId) async {
    try {
      final trip = _activeTrips[tripId];
      if (trip == null) {
        throw Exception('Cesta nebyla nalezena');
      }

      if (!trip.invitedUsers.contains(userId)) {
        throw Exception('Uživatel není pozván');
      }

      final updatedInvitedUsers = trip.invitedUsers
          .where((id) => id != userId)
          .toList();
      final updatedParticipants = [...trip.participants, userId];

      final updatedTrip = trip.copyWith(
        participants: updatedParticipants,
        invitedUsers: updatedInvitedUsers,
        updatedAt: DateTime.now(),
      );

      _activeTrips[tripId] = updatedTrip;

      // Odstranění pozvánky
      _invitations[userId]?.removeWhere((inv) => inv.tripId == tripId);

      _eventController?.add(
        CollaborativeEvent(
          type: CollaborativeEventType.invitationAccepted,
          trip: updatedTrip,
          userId: userId,
        ),
      );
    } catch (e) {
      _eventController?.add(
        CollaborativeEvent(
          type: CollaborativeEventType.error,
          error: 'Chyba při přijímání pozvánky: $e',
        ),
      );
    }
  }

  /// Odmítnutí pozvánky
  Future<void> declineInvitation(String tripId, String userId) async {
    try {
      final trip = _activeTrips[tripId];
      if (trip == null) {
        throw Exception('Cesta nebyla nalezena');
      }

      final updatedInvitedUsers = trip.invitedUsers
          .where((id) => id != userId)
          .toList();

      final updatedTrip = trip.copyWith(
        invitedUsers: updatedInvitedUsers,
        updatedAt: DateTime.now(),
      );

      _activeTrips[tripId] = updatedTrip;

      // Odstranění pozvánky
      _invitations[userId]?.removeWhere((inv) => inv.tripId == tripId);

      _eventController?.add(
        CollaborativeEvent(
          type: CollaborativeEventType.invitationDeclined,
          trip: updatedTrip,
          userId: userId,
        ),
      );
    } catch (e) {
      _eventController?.add(
        CollaborativeEvent(
          type: CollaborativeEventType.error,
          error: 'Chyba při odmítání pozvánky: $e',
        ),
      );
    }
  }

  /// Přidání místa do sdílené cesty
  Future<void> addPlaceToTrip(String tripId, Place place, String userId) async {
    try {
      final trip = _activeTrips[tripId];
      if (trip == null) {
        throw Exception('Cesta nebyla nalezena');
      }

      if (!trip.participants.contains(userId)) {
        throw Exception('Uživatel není účastníkem cesty');
      }

      final suggestion = PlaceSuggestion(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        place: place,
        suggestedBy: userId,
        suggestedAt: DateTime.now(),
        votes: [userId], // Autor automaticky hlasuje pro
        status: SuggestionStatus.pending,
      );

      final updatedSuggestions = [...trip.placeSuggestions, suggestion];

      final updatedTrip = trip.copyWith(
        placeSuggestions: updatedSuggestions,
        updatedAt: DateTime.now(),
      );

      _activeTrips[tripId] = updatedTrip;

      _eventController?.add(
        CollaborativeEvent(
          type: CollaborativeEventType.placeSuggested,
          trip: updatedTrip,
          suggestion: suggestion,
          userId: userId,
        ),
      );
    } catch (e) {
      _eventController?.add(
        CollaborativeEvent(
          type: CollaborativeEventType.error,
          error: 'Chyba při přidávání místa: $e',
        ),
      );
    }
  }

  /// Hlasování pro návrh místa
  Future<void> voteForSuggestion(
    String tripId,
    String suggestionId,
    String userId,
    bool isUpvote,
  ) async {
    try {
      final trip = _activeTrips[tripId];
      if (trip == null) {
        throw Exception('Cesta nebyla nalezena');
      }

      if (!trip.participants.contains(userId)) {
        throw Exception('Uživatel není účastníkem cesty');
      }

      final suggestionIndex = trip.placeSuggestions.indexWhere(
        (s) => s.id == suggestionId,
      );
      if (suggestionIndex == -1) {
        throw Exception('Návrh nebyl nalezen');
      }

      final suggestion = trip.placeSuggestions[suggestionIndex];
      List<String> updatedVotes = List.from(suggestion.votes);
      List<String> updatedDownvotes = List.from(suggestion.downvotes);

      if (isUpvote) {
        if (!updatedVotes.contains(userId)) {
          updatedVotes.add(userId);
        }
        updatedDownvotes.remove(userId);
      } else {
        if (!updatedDownvotes.contains(userId)) {
          updatedDownvotes.add(userId);
        }
        updatedVotes.remove(userId);
      }

      // Automatické schválení při většině hlasů
      SuggestionStatus newStatus = suggestion.status;
      final totalParticipants = trip.participants.length;
      final majorityThreshold = (totalParticipants / 2).ceil();

      if (updatedVotes.length >= majorityThreshold) {
        newStatus = SuggestionStatus.approved;
      } else if (updatedDownvotes.length >= majorityThreshold) {
        newStatus = SuggestionStatus.rejected;
      }

      final updatedSuggestion = suggestion.copyWith(
        votes: updatedVotes,
        downvotes: updatedDownvotes,
        status: newStatus,
      );

      final updatedSuggestions = List<PlaceSuggestion>.from(
        trip.placeSuggestions,
      );
      updatedSuggestions[suggestionIndex] = updatedSuggestion;

      final updatedTrip = trip.copyWith(
        placeSuggestions: updatedSuggestions,
        updatedAt: DateTime.now(),
      );

      _activeTrips[tripId] = updatedTrip;

      _eventController?.add(
        CollaborativeEvent(
          type: CollaborativeEventType.suggestionVoted,
          trip: updatedTrip,
          suggestion: updatedSuggestion,
          userId: userId,
        ),
      );

      if (newStatus == SuggestionStatus.approved) {
        _eventController?.add(
          CollaborativeEvent(
            type: CollaborativeEventType.suggestionApproved,
            trip: updatedTrip,
            suggestion: updatedSuggestion,
          ),
        );
      }
    } catch (e) {
      _eventController?.add(
        CollaborativeEvent(
          type: CollaborativeEventType.error,
          error: 'Chyba při hlasování: $e',
        ),
      );
    }
  }

  /// Přidání komentáře k návrhu
  Future<void> addCommentToSuggestion(
    String tripId,
    String suggestionId,
    String userId,
    String comment,
  ) async {
    try {
      final trip = _activeTrips[tripId];
      if (trip == null) {
        throw Exception('Cesta nebyla nalezena');
      }

      final suggestionIndex = trip.placeSuggestions.indexWhere(
        (s) => s.id == suggestionId,
      );
      if (suggestionIndex == -1) {
        throw Exception('Návrh nebyl nalezen');
      }

      final newComment = SuggestionComment(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        comment: comment,
        createdAt: DateTime.now(),
      );

      final suggestion = trip.placeSuggestions[suggestionIndex];
      final updatedComments = [...suggestion.comments, newComment];

      final updatedSuggestion = suggestion.copyWith(comments: updatedComments);

      final updatedSuggestions = List<PlaceSuggestion>.from(
        trip.placeSuggestions,
      );
      updatedSuggestions[suggestionIndex] = updatedSuggestion;

      final updatedTrip = trip.copyWith(
        placeSuggestions: updatedSuggestions,
        updatedAt: DateTime.now(),
      );

      _activeTrips[tripId] = updatedTrip;

      _eventController?.add(
        CollaborativeEvent(
          type: CollaborativeEventType.commentAdded,
          trip: updatedTrip,
          suggestion: updatedSuggestion,
          comment: newComment,
          userId: userId,
        ),
      );
    } catch (e) {
      _eventController?.add(
        CollaborativeEvent(
          type: CollaborativeEventType.error,
          error: 'Chyba při přidávání komentáře: $e',
        ),
      );
    }
  }

  /// Odeslání pozvánky
  Future<void> _sendInvitation(CollaborativeTrip trip, String userId) async {
    final invitation = TripInvitation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      tripId: trip.id,
      tripName: trip.name,
      invitedBy: trip.creatorId,
      invitedAt: DateTime.now(),
      status: InvitationStatus.pending,
    );

    _invitations[userId] ??= [];
    _invitations[userId]!.add(invitation);

    // Simulace odeslání push notifikace
    debugPrint('Pozvánka odeslána uživateli $userId pro cestu ${trip.name}');
  }

  /// Získání pozvánek pro uživatele
  List<TripInvitation> getInvitationsForUser(String userId) {
    return _invitations[userId] ?? [];
  }

  /// Získání aktivních cest pro uživatele
  List<CollaborativeTrip> getTripsForUser(String userId) {
    return _activeTrips.values
        .where((trip) => trip.participants.contains(userId))
        .toList();
  }

  /// Získání cesty podle ID
  CollaborativeTrip? getTrip(String tripId) {
    return _activeTrips[tripId];
  }

  /// Opuštění cesty
  Future<void> leaveTrip(String tripId, String userId) async {
    try {
      final trip = _activeTrips[tripId];
      if (trip == null) {
        throw Exception('Cesta nebyla nalezena');
      }

      if (trip.creatorId == userId) {
        throw Exception('Tvůrce cesty nemůže cestu opustit');
      }

      final updatedParticipants = trip.participants
          .where((id) => id != userId)
          .toList();

      final updatedTrip = trip.copyWith(
        participants: updatedParticipants,
        updatedAt: DateTime.now(),
      );

      _activeTrips[tripId] = updatedTrip;

      _eventController?.add(
        CollaborativeEvent(
          type: CollaborativeEventType.userLeft,
          trip: updatedTrip,
          userId: userId,
        ),
      );
    } catch (e) {
      _eventController?.add(
        CollaborativeEvent(
          type: CollaborativeEventType.error,
          error: 'Chyba při opouštění cesty: $e',
        ),
      );
    }
  }

  void dispose() {
    _eventController?.close();
  }
}

// Události kolaborace
class CollaborativeEvent {
  final CollaborativeEventType type;
  final CollaborativeTrip? trip;
  final PlaceSuggestion? suggestion;
  final SuggestionComment? comment;
  final String? userId;
  final String? error;

  CollaborativeEvent({
    required this.type,
    this.trip,
    this.suggestion,
    this.comment,
    this.userId,
    this.error,
  });
}

enum CollaborativeEventType {
  tripCreated,
  userInvited,
  invitationAccepted,
  invitationDeclined,
  placeSuggested,
  suggestionVoted,
  suggestionApproved,
  commentAdded,
  userLeft,
  error,
}
