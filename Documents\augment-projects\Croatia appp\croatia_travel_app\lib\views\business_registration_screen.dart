import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/business.dart';
import '../services/business_service.dart';
import '../widgets/watercolor_painters.dart';
import 'business_dashboard_screen.dart';

/// 🏢 BUSINESS REGISTRATION - Registrace nového podniku
class BusinessRegistrationScreen extends StatefulWidget {
  const BusinessRegistrationScreen({super.key});

  @override
  State<BusinessRegistrationScreen> createState() => _BusinessRegistrationScreenState();
}

class _BusinessRegistrationScreenState extends State<BusinessRegistrationScreen> {
  final BusinessService _businessService = BusinessService();
  final _formKey = GlobalKey<FormState>();
  final PageController _pageController = PageController();

  // Form controllers
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _addressController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _websiteController = TextEditingController();
  final _ownerFirstNameController = TextEditingController();
  final _ownerLastNameController = TextEditingController();
  final _ownerEmailController = TextEditingController();
  final _ownerPhoneController = TextEditingController();

  // Form data
  BusinessType _selectedType = BusinessType.restaurant;
  BusinessCategory _selectedCategory = BusinessCategory.food;
  double _latitude = 45.815;
  double _longitude = 15.982;
  List<String> _selectedAmenities = [];
  Map<String, DayHours> _openingHours = {};

  int _currentStep = 0;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeOpeningHours();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _websiteController.dispose();
    _ownerFirstNameController.dispose();
    _ownerLastNameController.dispose();
    _ownerEmailController.dispose();
    _ownerPhoneController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  void _initializeOpeningHours() {
    final days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    for (final day in days) {
      _openingHours[day] = const DayHours(
        isOpen: true,
        openTime: '09:00',
        closeTime: '22:00',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        child: CustomPaint(
          painter: WatercolorBusinessBackgroundPainter(),
          child: SafeArea(
            child: Column(
              children: [
                _buildHeader(),
                _buildProgressIndicator(),
                Expanded(
                  child: PageView(
                    controller: _pageController,
                    onPageChanged: (index) => setState(() => _currentStep = index),
                    children: [
                      _buildBasicInfoStep(),
                      _buildContactInfoStep(),
                      _buildBusinessDetailsStep(),
                      _buildOwnerInfoStep(),
                      _buildConfirmationStep(),
                    ],
                  ),
                ),
                _buildNavigationButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back, color: Colors.white),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Registrace podniku',
                  style: GoogleFonts.playfairDisplay(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  'Krok ${_currentStep + 1} z 5',
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: List.generate(5, (index) {
          final isActive = index <= _currentStep;
          return Expanded(
            child: Container(
              height: 4,
              margin: EdgeInsets.only(right: index < 4 ? 8 : 0),
              decoration: BoxDecoration(
                color: isActive ? Colors.white : Colors.white.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildBasicInfoStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Container(
        child: CustomPaint(
          painter: WatercolorBusinessCardPainter(const Color(0xFF006994)),
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.95),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Základní informace',
                    style: GoogleFonts.playfairDisplay(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF2C2C2C),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Název podniku
                  TextFormField(
                    controller: _nameController,
                    decoration: InputDecoration(
                      labelText: 'Název podniku *',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(Icons.business),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Zadejte název podniku';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Popis
                  TextFormField(
                    controller: _descriptionController,
                    maxLines: 3,
                    decoration: InputDecoration(
                      labelText: 'Popis podniku *',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(Icons.description),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Zadejte popis podniku';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Typ podniku
                  DropdownButtonFormField<BusinessType>(
                    value: _selectedType,
                    decoration: InputDecoration(
                      labelText: 'Typ podniku *',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(Icons.category),
                    ),
                    items: BusinessType.values.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(_getBusinessTypeLabel(type)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _selectedType = value);
                      }
                    },
                  ),
                  const SizedBox(height: 16),

                  // Kategorie
                  DropdownButtonFormField<BusinessCategory>(
                    value: _selectedCategory,
                    decoration: InputDecoration(
                      labelText: 'Kategorie *',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(Icons.local_offer),
                    ),
                    items: BusinessCategory.values.map((category) {
                      return DropdownMenuItem(
                        value: category,
                        child: Text(_getBusinessCategoryLabel(category)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _selectedCategory = value);
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContactInfoStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Container(
        child: CustomPaint(
          painter: WatercolorBusinessCardPainter(const Color(0xFF4CAF50)),
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.95),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Kontaktní údaje',
                  style: GoogleFonts.playfairDisplay(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF2C2C2C),
                  ),
                ),
                const SizedBox(height: 24),

                // Adresa
                TextFormField(
                  controller: _addressController,
                  decoration: InputDecoration(
                    labelText: 'Adresa *',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    prefixIcon: const Icon(Icons.location_on),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Zadejte adresu';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Telefon
                TextFormField(
                  controller: _phoneController,
                  decoration: InputDecoration(
                    labelText: 'Telefon *',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    prefixIcon: const Icon(Icons.phone),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Zadejte telefon';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Email
                TextFormField(
                  controller: _emailController,
                  decoration: InputDecoration(
                    labelText: 'Email *',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    prefixIcon: const Icon(Icons.email),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Zadejte email';
                    }
                    if (!value.contains('@')) {
                      return 'Zadejte platný email';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Website
                TextFormField(
                  controller: _websiteController,
                  decoration: InputDecoration(
                    labelText: 'Webové stránky',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    prefixIcon: const Icon(Icons.web),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBusinessDetailsStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Container(
        child: CustomPaint(
          painter: WatercolorBusinessCardPainter(const Color(0xFFFF9800)),
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.95),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Detaily podniku',
                  style: GoogleFonts.playfairDisplay(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF2C2C2C),
                  ),
                ),
                const SizedBox(height: 24),

                // Otevírací hodiny
                Text(
                  'Otevírací hodiny',
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF2C2C2C),
                  ),
                ),
                const SizedBox(height: 12),
                ..._buildOpeningHoursInputs(),

                const SizedBox(height: 24),

                // Vybavení
                Text(
                  'Vybavení a služby',
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF2C2C2C),
                  ),
                ),
                const SizedBox(height: 12),
                _buildAmenitiesSelector(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOwnerInfoStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Container(
        child: CustomPaint(
          painter: WatercolorBusinessCardPainter(const Color(0xFF9C27B0)),
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.95),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Údaje majitele',
                  style: GoogleFonts.playfairDisplay(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF2C2C2C),
                  ),
                ),
                const SizedBox(height: 24),

                // Jméno majitele
                TextFormField(
                  controller: _ownerFirstNameController,
                  decoration: InputDecoration(
                    labelText: 'Jméno *',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    prefixIcon: const Icon(Icons.person),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Zadejte jméno';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Příjmení majitele
                TextFormField(
                  controller: _ownerLastNameController,
                  decoration: InputDecoration(
                    labelText: 'Příjmení *',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    prefixIcon: const Icon(Icons.person_outline),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Zadejte příjmení';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Email majitele
                TextFormField(
                  controller: _ownerEmailController,
                  decoration: InputDecoration(
                    labelText: 'Email majitele *',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    prefixIcon: const Icon(Icons.email),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Zadejte email majitele';
                    }
                    if (!value.contains('@')) {
                      return 'Zadejte platný email';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Telefon majitele
                TextFormField(
                  controller: _ownerPhoneController,
                  decoration: InputDecoration(
                    labelText: 'Telefon majitele *',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    prefixIcon: const Icon(Icons.phone),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Zadejte telefon majitele';
                    }
                    return null;
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildConfirmationStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Container(
        child: CustomPaint(
          painter: WatercolorBusinessCardPainter(const Color(0xFF2196F3)),
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.95),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Potvrzení registrace',
                  style: GoogleFonts.playfairDisplay(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF2C2C2C),
                  ),
                ),
                const SizedBox(height: 24),

                _buildSummaryItem('Název', _nameController.text),
                _buildSummaryItem('Typ', _getBusinessTypeLabel(_selectedType)),
                _buildSummaryItem('Kategorie', _getBusinessCategoryLabel(_selectedCategory)),
                _buildSummaryItem('Adresa', _addressController.text),
                _buildSummaryItem('Telefon', _phoneController.text),
                _buildSummaryItem('Email', _emailController.text),
                _buildSummaryItem('Majitel', '${_ownerFirstNameController.text} ${_ownerLastNameController.text}'),

                const SizedBox(height: 24),

                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF5F5F5),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: const Color(0xFF006994),
                        size: 32,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Po registraci bude váš profil odeslán k ověření. Proces ověření může trvat 1-3 pracovní dny.',
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          color: const Color(0xFF666666),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF666666),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value.isEmpty ? 'Nezadáno' : value,
              style: GoogleFonts.inter(
                fontSize: 14,
                color: const Color(0xFF2C2C2C),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildOpeningHoursInputs() {
    final dayLabels = {
      'monday': 'Pondělí',
      'tuesday': 'Úterý',
      'wednesday': 'Středa',
      'thursday': 'Čtvrtek',
      'friday': 'Pátek',
      'saturday': 'Sobota',
      'sunday': 'Neděle',
    };

    return dayLabels.entries.map((entry) {
      final day = entry.key;
      final label = entry.value;
      final hours = _openingHours[day]!;

      return Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: const Color(0xFFF5F5F5),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            SizedBox(
              width: 80,
              child: Text(
                label,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Switch(
              value: hours.isOpen,
              onChanged: (value) {
                setState(() {
                  _openingHours[day] = DayHours(
                    isOpen: value,
                    openTime: value ? '09:00' : null,
                    closeTime: value ? '22:00' : null,
                  );
                });
              },
            ),
            if (hours.isOpen) ...[
              const SizedBox(width: 16),
              Text('${hours.openTime} - ${hours.closeTime}'),
            ] else
              const Text('Zavřeno'),
          ],
        ),
      );
    }).toList();
  }

  Widget _buildAmenitiesSelector() {
    final availableAmenities = [
      'WiFi', 'Klimatizace', 'Parkování', 'Terasa', 'Platební karty',
      'Bezbariérový přístup', 'Dětský koutek', 'Rozvoz', 'Rezervace online'
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: availableAmenities.map((amenity) {
        final isSelected = _selectedAmenities.contains(amenity);
        return FilterChip(
          label: Text(amenity),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              if (selected) {
                _selectedAmenities.add(amenity);
              } else {
                _selectedAmenities.remove(amenity);
              }
            });
          },
        );
      }).toList(),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousStep,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: const BorderSide(color: Colors.white),
                ),
                child: Text(
                  'Zpět',
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _isLoading ? null : _nextStep,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isLoading
                  ? const CircularProgressIndicator()
                  : Text(
                      _currentStep == 4 ? 'Registrovat' : 'Další',
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF006994),
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  void _previousStep() {
    if (_currentStep > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _nextStep() {
    if (_currentStep < 4) {
      if (_validateCurrentStep()) {
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    } else {
      _submitRegistration();
    }
  }

  bool _validateCurrentStep() {
    switch (_currentStep) {
      case 0:
        return _nameController.text.isNotEmpty && _descriptionController.text.isNotEmpty;
      case 1:
        return _addressController.text.isNotEmpty && 
               _phoneController.text.isNotEmpty && 
               _emailController.text.isNotEmpty;
      case 3:
        return _ownerFirstNameController.text.isNotEmpty && 
               _ownerLastNameController.text.isNotEmpty &&
               _ownerEmailController.text.isNotEmpty &&
               _ownerPhoneController.text.isNotEmpty;
      default:
        return true;
    }
  }

  Future<void> _submitRegistration() async {
    setState(() => _isLoading = true);

    try {
      await _businessService.initialize();

      final owner = BusinessOwner(
        id: 'owner_${DateTime.now().millisecondsSinceEpoch}',
        firstName: _ownerFirstNameController.text,
        lastName: _ownerLastNameController.text,
        email: _ownerEmailController.text,
        phone: _ownerPhoneController.text,
        registeredAt: DateTime.now(),
      );

      final business = await _businessService.registerBusiness(
        name: _nameController.text,
        description: _descriptionController.text,
        type: _selectedType,
        category: _selectedCategory,
        address: _addressController.text,
        latitude: _latitude,
        longitude: _longitude,
        phone: _phoneController.text,
        email: _emailController.text,
        website: _websiteController.text,
        owner: owner,
        openingHours: BusinessHours(schedule: _openingHours),
        amenities: _selectedAmenities,
      );

      if (business != null) {
        // Úspěšná registrace
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => BusinessDashboardScreen(businessId: business.id),
          ),
        );
      } else {
        _showErrorDialog('Registrace se nezdařila. Zkuste to prosím znovu.');
      }
    } catch (e) {
      _showErrorDialog('Došlo k chybě: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Chyba'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  String _getBusinessTypeLabel(BusinessType type) {
    switch (type) {
      case BusinessType.restaurant:
        return 'Restaurace';
      case BusinessType.hotel:
        return 'Hotel';
      case BusinessType.attraction:
        return 'Atrakce';
      case BusinessType.shop:
        return 'Obchod';
      case BusinessType.service:
        return 'Služba';
      case BusinessType.transport:
        return 'Doprava';
      case BusinessType.entertainment:
        return 'Zábava';
      case BusinessType.healthcare:
        return 'Zdravotnictví';
      case BusinessType.education:
        return 'Vzdělávání';
      case BusinessType.other:
        return 'Ostatní';
    }
  }

  String _getBusinessCategoryLabel(BusinessCategory category) {
    switch (category) {
      case BusinessCategory.food:
        return 'Jídlo a pití';
      case BusinessCategory.accommodation:
        return 'Ubytování';
      case BusinessCategory.tourism:
        return 'Turistika';
      case BusinessCategory.retail:
        return 'Maloobchod';
      case BusinessCategory.automotive:
        return 'Automotive';
      case BusinessCategory.beauty:
        return 'Krása a wellness';
      case BusinessCategory.fitness:
        return 'Fitness';
      case BusinessCategory.technology:
        return 'Technologie';
      case BusinessCategory.finance:
        return 'Finance';
      case BusinessCategory.consulting:
        return 'Poradenství';
      case BusinessCategory.marketing:
        return 'Marketing';
      case BusinessCategory.events:
        return 'Události';
    }
  }
}
