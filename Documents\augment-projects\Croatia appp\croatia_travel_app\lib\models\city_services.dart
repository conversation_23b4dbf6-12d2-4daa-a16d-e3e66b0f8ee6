import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'city_services.g.dart';

// Enums
enum FormCategory { residence, business, construction, social, tax, other }

enum FormStatus { active, inactive, archived }

enum FieldType {
  text,
  email,
  phone,
  number,
  date,
  select,
  multiSelect,
  checkbox,
  file,
  textarea,
}

enum SubmissionStatus { submitted, inReview, approved, rejected, completed }

enum PaymentCategory {
  communalWaste,
  propertyTax,
  businessLicense,
  parking,
  utilities,
  fines,
  other,
}

enum PaymentStatus { pending, paid, overdue, cancelled, refunded }

enum PaymentMethod { card, bankTransfer, cash, mobilePay, crypto }

enum IssueCategory {
  roads,
  lighting,
  waste,
  water,
  parks,
  noise,
  safety,
  other,
}

enum IssuePriority { low, medium, high, urgent }

enum IssueStatus { reported, assigned, inProgress, resolved, closed }

enum WifiType { municipal, library, park, transport, tourist, emergency }

enum ServiceCategory {
  government,
  utilities,
  transport,
  health,
  education,
  emergency,
  other,
}

enum ServicePriority { low, medium, high, urgent }

enum AlertType { info, warning, emergency, maintenance, event }

enum AlertPriority { low, medium, high }

enum DocumentType {
  passport,
  id,
  drivingLicense,
  birthCertificate,
  marriageCertificate,
  residencePermit,
  other,
}

enum VerificationStatus { pending, verified, rejected, expired }

enum SentimentType { positive, negative, neutral, mixed }

// Models
@JsonSerializable()
class GovernmentOffice {
  final String id;
  final String name;
  final String type;
  final String address;
  final double latitude;
  final double longitude;
  final String phoneNumber;
  final String email;
  final String? website;
  final List<OfficeHours> openingHours;
  final List<String> services;
  final List<String> languages;
  final bool hasDisabledAccess;
  final bool hasParking;
  final String? description;
  final List<String> requiredDocuments;

  const GovernmentOffice({
    required this.id,
    required this.name,
    required this.type,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.phoneNumber,
    required this.email,
    this.website,
    required this.openingHours,
    required this.services,
    this.languages = const ['hr'],
    this.hasDisabledAccess = false,
    this.hasParking = false,
    this.description,
    this.requiredDocuments = const [],
  });

  factory GovernmentOffice.fromJson(Map<String, dynamic> json) =>
      _$GovernmentOfficeFromJson(json);

  Map<String, dynamic> toJson() => _$GovernmentOfficeToJson(this);

  // Additional getters
  bool get isOpenNow {
    final now = DateTime.now();
    final currentDay = _getCurrentDayName();

    final todayHours = openingHours
        .where((h) => h.dayOfWeek == currentDay)
        .firstOrNull;
    if (todayHours == null || todayHours.isClosed) return false;

    final currentTime = TimeOfDay.fromDateTime(now);
    final openTime = _parseTime(todayHours.openTime);
    final closeTime = _parseTime(todayHours.closeTime);

    return _isTimeBetween(currentTime, openTime, closeTime);
  }

  String _getCurrentDayName() {
    final days = [
      'Neděle',
      'Pondělí',
      'Úterý',
      'Středa',
      'Čtvrtek',
      'Pátek',
      'Sobota',
    ];
    return days[DateTime.now().weekday % 7];
  }

  TimeOfDay _parseTime(String timeStr) {
    final parts = timeStr.split(':');
    return TimeOfDay(hour: int.parse(parts[0]), minute: int.parse(parts[1]));
  }

  bool _isTimeBetween(TimeOfDay current, TimeOfDay start, TimeOfDay end) {
    final currentMinutes = current.hour * 60 + current.minute;
    final startMinutes = start.hour * 60 + start.minute;
    final endMinutes = end.hour * 60 + end.minute;

    return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
  }
}

@JsonSerializable()
class OfficeHours {
  final String dayOfWeek;
  final String openTime;
  final String closeTime;
  final bool isClosed;

  const OfficeHours({
    required this.dayOfWeek,
    required this.openTime,
    required this.closeTime,
    this.isClosed = false,
  });

  factory OfficeHours.fromJson(Map<String, dynamic> json) =>
      _$OfficeHoursFromJson(json);

  Map<String, dynamic> toJson() => _$OfficeHoursToJson(this);
}

@JsonSerializable()
class OnlineForm {
  final String id;
  final String title;
  final String description;
  final FormCategory category;
  final List<FormField> fields;
  final List<String> requiredDocuments;
  final String submitUrl;
  final Duration estimatedTime;
  final double? fee;
  final String? currency;
  final FormStatus status;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool requiresAuthentication;
  final List<String> supportedLanguages;

  const OnlineForm({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.fields,
    this.requiredDocuments = const [],
    required this.submitUrl,
    required this.estimatedTime,
    this.fee,
    this.currency,
    this.status = FormStatus.active,
    required this.createdAt,
    this.updatedAt,
    this.requiresAuthentication = true,
    this.supportedLanguages = const ['hr', 'en'],
  });

  factory OnlineForm.fromJson(Map<String, dynamic> json) =>
      _$OnlineFormFromJson(json);

  Map<String, dynamic> toJson() => _$OnlineFormToJson(this);
}

@JsonSerializable()
class FormField {
  final String id;
  final String label;
  final String? placeholder;
  final FieldType type;
  final bool isRequired;
  final List<String>? options;
  final String? validationPattern;
  final String? helpText;

  const FormField({
    required this.id,
    required this.label,
    this.placeholder,
    required this.type,
    this.isRequired = false,
    this.options,
    this.validationPattern,
    this.helpText,
  });

  factory FormField.fromJson(Map<String, dynamic> json) =>
      _$FormFieldFromJson(json);

  Map<String, dynamic> toJson() => _$FormFieldToJson(this);
}

@JsonSerializable()
class FormSubmission {
  final String id;
  final String formId;
  final String userId;
  final Map<String, dynamic> data;
  final List<String> attachments;
  final SubmissionStatus status;
  final DateTime submittedAt;
  final DateTime? processedAt;
  final String? referenceNumber;
  final String? notes;

  const FormSubmission({
    required this.id,
    required this.formId,
    required this.userId,
    required this.data,
    this.attachments = const [],
    this.status = SubmissionStatus.submitted,
    required this.submittedAt,
    this.processedAt,
    this.referenceNumber,
    this.notes,
  });

  factory FormSubmission.fromJson(Map<String, dynamic> json) =>
      _$FormSubmissionFromJson(json);

  Map<String, dynamic> toJson() => _$FormSubmissionToJson(this);
}

@JsonSerializable()
class CityPayment {
  final String id;
  final String title;
  final String description;
  final PaymentCategory category;
  final double amount;
  final String currency;
  final DateTime dueDate;
  final PaymentStatus status;
  final String? userId;
  final String? referenceNumber;
  final List<PaymentMethod> acceptedMethods;
  final bool isRecurring;
  final Duration? recurringPeriod;

  const CityPayment({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.amount,
    required this.currency,
    required this.dueDate,
    this.status = PaymentStatus.pending,
    this.userId,
    this.referenceNumber,
    this.acceptedMethods = const [],
    this.isRecurring = false,
    this.recurringPeriod,
  });

  factory CityPayment.fromJson(Map<String, dynamic> json) =>
      _$CityPaymentFromJson(json);

  Map<String, dynamic> toJson() => _$CityPaymentToJson(this);

  // Additional getters
  bool get isOverdue =>
      DateTime.now().isAfter(dueDate) && status == PaymentStatus.pending;
}

@JsonSerializable()
class IssueReport {
  final String id;
  final String title;
  final String description;
  final IssueCategory category;
  final IssuePriority priority;
  final double latitude;
  final double longitude;
  final String address;
  final List<String> photos;
  final String? reporterName;
  final String? reporterEmail;
  final String? reporterPhone;
  final IssueStatus status;
  final DateTime reportedAt;
  final DateTime? assignedAt;
  final DateTime? resolvedAt;
  final String? assignedTo;
  final List<IssueUpdate> updates;

  const IssueReport({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    this.priority = IssuePriority.medium,
    required this.latitude,
    required this.longitude,
    required this.address,
    this.photos = const [],
    this.reporterName,
    this.reporterEmail,
    this.reporterPhone,
    this.status = IssueStatus.reported,
    required this.reportedAt,
    this.assignedAt,
    this.resolvedAt,
    this.assignedTo,
    this.updates = const [],
  });

  factory IssueReport.fromJson(Map<String, dynamic> json) =>
      _$IssueReportFromJson(json);

  Map<String, dynamic> toJson() => _$IssueReportToJson(this);
}

@JsonSerializable()
class IssueUpdate {
  final String id;
  final String message;
  final DateTime timestamp;
  final String? updatedBy;
  final IssueStatus? newStatus;

  const IssueUpdate({
    required this.id,
    required this.message,
    required this.timestamp,
    this.updatedBy,
    this.newStatus,
  });

  factory IssueUpdate.fromJson(Map<String, dynamic> json) =>
      _$IssueUpdateFromJson(json);

  Map<String, dynamic> toJson() => _$IssueUpdateToJson(this);
}

@JsonSerializable()
class PublicWifi {
  final String id;
  final String name;
  final String? description;
  final double latitude;
  final double longitude;
  final String address;
  final WifiType type;
  final bool requiresPassword;
  final bool requiresRegistration;
  final String? password;
  final double? speedMbps;
  final int? userLimit;
  final Duration? timeLimit;
  final bool isActive;
  final DateTime lastUpdated;

  const PublicWifi({
    required this.id,
    required this.name,
    this.description,
    required this.latitude,
    required this.longitude,
    required this.address,
    required this.type,
    this.requiresPassword = false,
    this.requiresRegistration = false,
    this.password,
    this.speedMbps,
    this.userLimit,
    this.timeLimit,
    this.isActive = true,
    required this.lastUpdated,
  });

  factory PublicWifi.fromJson(Map<String, dynamic> json) =>
      _$PublicWifiFromJson(json);

  Map<String, dynamic> toJson() => _$PublicWifiToJson(this);
}

@JsonSerializable()
class FormAssistance {
  final String formId;
  final Map<String, dynamic> suggestions;
  final List<String> missingFields;
  final List<FormValidationError> validationErrors;
  final List<String> autoFillRecommendations;
  final double completionPercentage;
  final Duration estimatedTimeToComplete;

  const FormAssistance({
    required this.formId,
    required this.suggestions,
    required this.missingFields,
    required this.validationErrors,
    required this.autoFillRecommendations,
    required this.completionPercentage,
    required this.estimatedTimeToComplete,
  });

  factory FormAssistance.fromJson(Map<String, dynamic> json) =>
      _$FormAssistanceFromJson(json);

  Map<String, dynamic> toJson() => _$FormAssistanceToJson(this);
}

@JsonSerializable()
class FormValidationError {
  final String fieldId;
  final String errorType;
  final String message;
  final String? suggestion;

  const FormValidationError({
    required this.fieldId,
    required this.errorType,
    required this.message,
    this.suggestion,
  });

  factory FormValidationError.fromJson(Map<String, dynamic> json) =>
      _$FormValidationErrorFromJson(json);

  Map<String, dynamic> toJson() => _$FormValidationErrorToJson(this);
}

@JsonSerializable()
class ServiceRecommendation {
  final String id;
  final String title;
  final String description;
  final ServiceCategory category;
  final double relevanceScore;
  final List<String> reasons;
  final DateTime deadline;
  final ServicePriority priority;
  final Map<String, dynamic> actionData;

  const ServiceRecommendation({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.relevanceScore,
    required this.reasons,
    required this.deadline,
    required this.priority,
    required this.actionData,
  });

  factory ServiceRecommendation.fromJson(Map<String, dynamic> json) =>
      _$ServiceRecommendationFromJson(json);

  Map<String, dynamic> toJson() => _$ServiceRecommendationToJson(this);
}

@JsonSerializable()
class PredictiveAlert {
  final String id;
  final String title;
  final String message;
  final AlertType type;
  final AlertPriority priority;
  final DateTime triggerTime;
  final DateTime? expiresAt;
  final Map<String, dynamic> metadata;
  final List<String> affectedServices;
  final bool isActive;

  const PredictiveAlert({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.priority,
    required this.triggerTime,
    this.expiresAt,
    required this.metadata,
    required this.affectedServices,
    this.isActive = true,
  });

  factory PredictiveAlert.fromJson(Map<String, dynamic> json) =>
      _$PredictiveAlertFromJson(json);

  Map<String, dynamic> toJson() => _$PredictiveAlertToJson(this);
}

@JsonSerializable()
class DocumentVerification {
  final String id;
  final String userId;
  final DocumentType documentType;
  final VerificationStatus status;
  final List<VerificationCheck> checks;
  final DateTime submittedAt;
  final DateTime? verifiedAt;
  final String? rejectionReason;

  const DocumentVerification({
    required this.id,
    required this.userId,
    required this.documentType,
    required this.status,
    required this.checks,
    required this.submittedAt,
    this.verifiedAt,
    this.rejectionReason,
  });

  factory DocumentVerification.fromJson(Map<String, dynamic> json) =>
      _$DocumentVerificationFromJson(json);

  Map<String, dynamic> toJson() => _$DocumentVerificationToJson(this);
}

@JsonSerializable()
class VerificationCheck {
  final String name;
  final bool passed;
  final String? details;

  const VerificationCheck({
    required this.name,
    required this.passed,
    this.details,
  });

  factory VerificationCheck.fromJson(Map<String, dynamic> json) =>
      _$VerificationCheckFromJson(json);

  Map<String, dynamic> toJson() => _$VerificationCheckToJson(this);
}

@JsonSerializable()
class ChatbotResponse {
  final String id;
  final String query;
  final String response;
  final double confidence;
  final List<String> suggestedActions;
  final Map<String, dynamic> context;
  final DateTime timestamp;
  final String? sessionId;
  final List<String> relatedServices;
  final SentimentAnalysis? sentiment;

  const ChatbotResponse({
    required this.id,
    required this.query,
    required this.response,
    required this.confidence,
    required this.suggestedActions,
    required this.context,
    required this.timestamp,
    this.sessionId,
    required this.relatedServices,
    this.sentiment,
  });

  factory ChatbotResponse.fromJson(Map<String, dynamic> json) =>
      _$ChatbotResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ChatbotResponseToJson(this);
}

@JsonSerializable()
class SentimentAnalysis {
  final SentimentType type;
  final double confidence;
  final Map<String, double> scores;

  const SentimentAnalysis({
    required this.type,
    required this.confidence,
    required this.scores,
  });

  factory SentimentAnalysis.fromJson(Map<String, dynamic> json) =>
      _$SentimentAnalysisFromJson(json);

  Map<String, dynamic> toJson() => _$SentimentAnalysisToJson(this);
}
