import 'dart:async';
import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/place.dart';
import '../models/event.dart';

// Simple Memory class for database operations
class Memory {
  final String id;
  final String title;
  final String description;
  final DateTime date;
  final String? location;
  final List<String> photos;

  const Memory({
    required this.id,
    required this.title,
    required this.description,
    required this.date,
    this.location,
    this.photos = const [],
  });
}

// Simple Photo class for database operations
class Photo {
  final String id;
  final String path;
  final DateTime? takenAt;
  final String? location;
  final List<String> tags;

  const Photo({
    required this.id,
    required this.path,
    this.takenAt,
    this.location,
    this.tags = const [],
  });
}

class LocalDatabase {
  static final LocalDatabase _instance = LocalDatabase._internal();
  factory LocalDatabase() => _instance;
  LocalDatabase._internal();

  static Database? _database;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'croatia_travel.db');

    return await openDatabase(path, version: 1, onCreate: _onCreate);
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create tables
    await db.execute('''
      CREATE TABLE places (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        latitude REAL NOT NULL,
        longitude REAL NOT NULL,
        category TEXT NOT NULL,
        rating REAL,
        photos TEXT,
        is_synced INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE events (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        start_date TEXT NOT NULL,
        end_date TEXT,
        location TEXT,
        latitude REAL,
        longitude REAL,
        category TEXT,
        price REAL,
        is_synced INTEGER DEFAULT 0,
        created_at TEXT NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE diary_entries (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        date TEXT NOT NULL,
        location TEXT,
        photos TEXT,
        voice_notes TEXT,
        is_synced INTEGER DEFAULT 0,
        created_at TEXT NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE user_profiles (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT,
        preferences TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE memories (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        date TEXT NOT NULL,
        location TEXT,
        photos TEXT,
        created_at TEXT NOT NULL
      )
    ''');
  }

  // Place operations
  Future<void> insertPlace(Place place) async {
    final db = await database;
    await db.insert('places', {
      'id': place.id,
      'name': place.name,
      'description': place.description,
      'latitude': place.latitude,
      'longitude': place.longitude,
      'category': place.type.toString(),
      'rating': place.rating,
      'photos': jsonEncode(place.images),
      'created_at': DateTime.now().toIso8601String(),
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<List<Place>> getAllPlaces() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('places');
    return List.generate(maps.length, (i) {
      return Place(
        id: maps[i]['id'],
        name: maps[i]['name'],
        description: maps[i]['description'],
        latitude: maps[i]['latitude'],
        longitude: maps[i]['longitude'],
        region: maps[i]['region'] ?? 'Unknown',
        type: PlaceType.other, // Default value
        rating: maps[i]['rating'],
      );
    });
  }

  // Event operations
  Future<void> insertEvent(Event event) async {
    final db = await database;
    await db.insert('events', {
      'id': event.id,
      'title': event.name,
      'description': event.description,
      'start_date': event.startDate.toIso8601String(),
      'end_date': event.endDate.toIso8601String(),
      'location': event.location,
      'latitude': event.latitude,
      'longitude': event.longitude,
      'category': event.type.toString(),
      'price': event.price,
      'created_at': DateTime.now().toIso8601String(),
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<void> insertOrUpdateEvent(Event event) async {
    await insertEvent(event);
  }

  // Memory operations
  Future<void> insertMemory(Memory memory) async {
    final db = await database;
    await db.insert('memories', {
      'id': memory.id,
      'title': memory.title,
      'description': memory.description,
      'date': memory.date.toIso8601String(),
      'location': memory.location,
      'photos': jsonEncode(memory.photos),
      'created_at': DateTime.now().toIso8601String(),
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<List<Memory>> getAllMemories() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('memories');
    return List.generate(maps.length, (i) {
      return Memory(
        id: maps[i]['id'],
        title: maps[i]['title'],
        description: maps[i]['description'],
        date: DateTime.parse(maps[i]['date']),
        location: maps[i]['location'],
        photos: List<String>.from(jsonDecode(maps[i]['photos'] ?? '[]')),
      );
    });
  }

  // Sync operations
  Future<List<Map<String, dynamic>>> getUnsyncedUserData() async {
    final db = await database;
    final List<Map<String, dynamic>> unsyncedData = [];

    // Get unsynced places
    final places = await db.query(
      'places',
      where: 'is_synced = ?',
      whereArgs: [0],
    );
    unsyncedData.addAll(places.map((p) => {'type': 'place', 'data': p}));

    // Get unsynced events
    final events = await db.query(
      'events',
      where: 'is_synced = ?',
      whereArgs: [0],
    );
    unsyncedData.addAll(events.map((e) => {'type': 'event', 'data': e}));

    return unsyncedData;
  }

  Future<void> markAsSynced(String table, String id) async {
    final db = await database;
    await db.update(table, {'is_synced': 1}, where: 'id = ?', whereArgs: [id]);
  }

  // Emergency alerts
  Future<void> saveEmergencyAlerts(List<dynamic> alerts) async {
    // Implementation for emergency alerts
    // This would typically save to a dedicated table
  }

  // Additional methods needed by services
  Future<void> saveMemory(Memory memory) async {
    await insertMemory(memory);
  }

  Future<void> saveVideoMemory(Memory memory) async {
    await insertMemory(memory);
  }

  Future<void> updateParkingAvailability(String spotId, int available) async {
    // Implementation for parking availability updates
  }

  Future<void> savePhoto(Photo photo) async {
    // Implementation for photo saving
  }

  Future<void> saveAlbum(Map<String, dynamic> album) async {
    // Implementation for album saving
  }

  Future<List<Photo>> getAllPhotos() async {
    // Implementation for getting all photos
    return [];
  }

  Future<List<Place>> getVisitedPlaces() async {
    return await getAllPlaces();
  }

  Future<List<Event>> getUpcomingEvents() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('events');
    return List.generate(maps.length, (i) {
      return Event(
        id: maps[i]['id'],
        name: maps[i]['title'],
        description: maps[i]['description'],
        startDate: DateTime.parse(maps[i]['start_date']),
        endDate: DateTime.parse(maps[i]['end_date']),
        location: maps[i]['location'],
        latitude: maps[i]['latitude'],
        longitude: maps[i]['longitude'],
        region: 'Unknown',
        type: EventType.other,
        price: maps[i]['price'],
      );
    });
  }

  Future<List<Event>> getAllEvents() async {
    return await getUpcomingEvents();
  }

  Future<List<dynamic>> getAllCuisineItems() async {
    // Implementation for cuisine items
    return [];
  }

  Future<void> insertOrUpdatePlace(Place place) async {
    await insertPlace(place);
  }

  Future<void> insertOrUpdateCuisineItem(dynamic item) async {
    // Implementation for cuisine item updates
  }

  Future<void> markDataAsSynced(String table, String id) async {
    await markAsSynced(table, id);
  }

  Future<void> saveOfflineMap(Map<String, dynamic> mapData) async {
    // Implementation for offline map saving
  }

  Future<void> savePackageProgress(Map<String, dynamic> progress) async {
    // Implementation for package progress saving
  }

  Future<void> saveReward(Map<String, dynamic> reward) async {
    // Implementation for reward saving
  }

  Future<void> saveCustomPackage(Map<String, dynamic> package) async {
    // Implementation for custom package saving
  }

  Future<void> saveTrafficIncident(Map<String, dynamic> incident) async {
    // Implementation for traffic incident saving
  }

  Future<void> clearTrafficCache() async {
    // Implementation for traffic cache clearing
  }

  Future<void> saveTicket(dynamic ticket) async {
    // Implementation for ticket saving
  }

  Future<List<dynamic>> getUserTickets() async {
    // Implementation for getting user tickets
    return [];
  }

  Future<void> updateTicketStatus(String ticketId, String status) async {
    // Implementation for ticket status updates
  }

  Future<void> clearTransportCache() async {
    // Implementation for transport cache clearing
  }

  Future<List<dynamic>> getAllVoiceNotes() async {
    // Implementation for getting voice notes
    return [];
  }

  // Close database
  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
