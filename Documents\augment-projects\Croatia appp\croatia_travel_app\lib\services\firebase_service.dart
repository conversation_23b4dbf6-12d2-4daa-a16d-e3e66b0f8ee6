import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

import '../models/user_models.dart';
import '../models/camera_models.dart';

/// Služba pro Firebase backend
class FirebaseService extends ChangeNotifier {
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() => _instance;
  FirebaseService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final FirebaseMessaging _messaging = FirebaseMessaging.instance;

  User? _currentUser;
  UserProfile? _userProfile;
  bool _isInitialized = false;
  StreamSubscription<User?>? _authSubscription;

  // Gettery
  User? get currentUser => _currentUser;
  UserProfile? get userProfile => _userProfile;
  bool get isInitialized => _isInitialized;
  bool get isLoggedIn => _currentUser != null;
  String? get userId => _currentUser?.uid;

  /// Inicializuje Firebase službu
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await Firebase.initializeApp();
      await _setupMessaging();
      _setupAuthListener();
      
      _isInitialized = true;
      debugPrint('Firebase služba inicializována');
    } catch (e) {
      debugPrint('Chyba při inicializaci Firebase: $e');
      throw Exception('Nepodařilo se inicializovat Firebase');
    }
  }

  /// Nastaví listener pro změny autentifikace
  void _setupAuthListener() {
    _authSubscription = _auth.authStateChanges().listen((User? user) async {
      _currentUser = user;
      
      if (user != null) {
        await _loadUserProfile();
        debugPrint('Uživatel přihlášen: ${user.email}');
      } else {
        _userProfile = null;
        debugPrint('Uživatel odhlášen');
      }
      
      notifyListeners();
    });
  }

  /// Nastaví Firebase Messaging
  Future<void> _setupMessaging() async {
    try {
      // Požádá o povolení notifikací
      final settings = await _messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        debugPrint('Notifikace povoleny');
        
        // Získá FCM token
        final token = await _messaging.getToken();
        debugPrint('FCM Token: $token');
        
        // Nastaví handlery pro notifikace
        FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
        FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);
      }
    } catch (e) {
      debugPrint('Chyba při nastavování notifikací: $e');
    }
  }

  /// Zpracuje notifikaci v popředí
  void _handleForegroundMessage(RemoteMessage message) {
    debugPrint('Notifikace v popředí: ${message.notification?.title}');
    // TODO: Zobrazit in-app notifikaci
  }

  /// Zpracuje kliknutí na notifikaci
  void _handleMessageOpenedApp(RemoteMessage message) {
    debugPrint('Kliknuto na notifikaci: ${message.data}');
    // TODO: Navigovat na příslušnou obrazovku
  }

  /// Registrace nového uživatele
  Future<UserCredential> registerWithEmail(String email, String password) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        await _createUserProfile(credential.user!);
      }

      return credential;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  /// Přihlášení existujícího uživatele
  Future<UserCredential> signInWithEmail(String email, String password) async {
    try {
      return await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  /// Odhlášení uživatele
  Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      debugPrint('Chyba při odhlašování: $e');
      rethrow;
    }
  }

  /// Resetování hesla
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  /// Vytvoří uživatelský profil v Firestore
  Future<void> _createUserProfile(User user) async {
    try {
      final profile = UserProfile(
        id: user.uid,
        email: user.email,
        displayName: user.displayName,
        photoUrl: user.photoURL,
        createdAt: DateTime.now(),
        lastActiveAt: DateTime.now(),
      );

      await _firestore
          .collection('users')
          .doc(user.uid)
          .set(profile.toJson());

      _userProfile = profile;
    } catch (e) {
      debugPrint('Chyba při vytváření profilu: $e');
      rethrow;
    }
  }

  /// Načte uživatelský profil z Firestore
  Future<void> _loadUserProfile() async {
    if (_currentUser == null) return;

    try {
      final doc = await _firestore
          .collection('users')
          .doc(_currentUser!.uid)
          .get();

      if (doc.exists) {
        _userProfile = UserProfile.fromJson(doc.data()!);
        
        // Aktualizuje lastActiveAt
        await _updateLastActive();
      }
    } catch (e) {
      debugPrint('Chyba při načítání profilu: $e');
    }
  }

  /// Aktualizuje uživatelský profil
  Future<void> updateUserProfile(UserProfile profile) async {
    if (_currentUser == null) return;

    try {
      await _firestore
          .collection('users')
          .doc(_currentUser!.uid)
          .update(profile.toJson());

      _userProfile = profile;
      notifyListeners();
    } catch (e) {
      debugPrint('Chyba při aktualizaci profilu: $e');
      rethrow;
    }
  }

  /// Aktualizuje čas poslední aktivity
  Future<void> _updateLastActive() async {
    if (_currentUser == null) return;

    try {
      await _firestore
          .collection('users')
          .doc(_currentUser!.uid)
          .update({'lastActiveAt': FieldValue.serverTimestamp()});
    } catch (e) {
      debugPrint('Chyba při aktualizaci aktivity: $e');
    }
  }

  /// Nahraje fotografii do Firebase Storage
  Future<String> uploadPhoto(File file, String fileName) async {
    if (_currentUser == null) throw Exception('Uživatel není přihlášen');

    try {
      final ref = _storage
          .ref()
          .child('users')
          .child(_currentUser!.uid)
          .child('photos')
          .child(fileName);

      final uploadTask = ref.putFile(file);
      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();

      return downloadUrl;
    } catch (e) {
      debugPrint('Chyba při nahrávání fotky: $e');
      rethrow;
    }
  }

  /// Uloží cestovní fotografii do Firestore
  Future<void> saveTravelPhoto(TravelPhoto photo) async {
    if (_currentUser == null) return;

    try {
      await _firestore
          .collection('users')
          .doc(_currentUser!.uid)
          .collection('photos')
          .doc(photo.id)
          .set(photo.toJson());
    } catch (e) {
      debugPrint('Chyba při ukládání fotky: $e');
      rethrow;
    }
  }

  /// Načte cestovní fotografie z Firestore
  Future<List<TravelPhoto>> loadTravelPhotos() async {
    if (_currentUser == null) return [];

    try {
      final snapshot = await _firestore
          .collection('users')
          .doc(_currentUser!.uid)
          .collection('photos')
          .orderBy('takenAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => TravelPhoto.fromJson(doc.data()))
          .toList();
    } catch (e) {
      debugPrint('Chyba při načítání fotek: $e');
      return [];
    }
  }

  /// Uloží doporučení do Firestore
  Future<void> saveRecommendations(List<PersonalizedRecommendation> recommendations) async {
    if (_currentUser == null) return;

    try {
      final batch = _firestore.batch();
      
      for (final recommendation in recommendations) {
        final ref = _firestore
            .collection('users')
            .doc(_currentUser!.uid)
            .collection('recommendations')
            .doc(recommendation.id);
        
        batch.set(ref, recommendation.toJson());
      }
      
      await batch.commit();
    } catch (e) {
      debugPrint('Chyba při ukládání doporučení: $e');
      rethrow;
    }
  }

  /// Načte doporučení z Firestore
  Future<List<PersonalizedRecommendation>> loadRecommendations() async {
    if (_currentUser == null) return [];

    try {
      final snapshot = await _firestore
          .collection('users')
          .doc(_currentUser!.uid)
          .collection('recommendations')
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => PersonalizedRecommendation.fromJson(doc.data()))
          .toList();
    } catch (e) {
      debugPrint('Chyba při načítání doporučení: $e');
      return [];
    }
  }

  /// Uloží itinerář do Firestore
  Future<void> saveItinerary(TravelItinerary itinerary) async {
    if (_currentUser == null) return;

    try {
      await _firestore
          .collection('users')
          .doc(_currentUser!.uid)
          .collection('itineraries')
          .doc(itinerary.id)
          .set(itinerary.toJson());
    } catch (e) {
      debugPrint('Chyba při ukládání itineráře: $e');
      rethrow;
    }
  }

  /// Načte itineráře z Firestore
  Future<List<TravelItinerary>> loadItineraries() async {
    if (_currentUser == null) return [];

    try {
      final snapshot = await _firestore
          .collection('users')
          .doc(_currentUser!.uid)
          .collection('itineraries')
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => TravelItinerary.fromJson(doc.data()))
          .toList();
    } catch (e) {
      debugPrint('Chyba při načítání itinerářů: $e');
      return [];
    }
  }

  /// Odešle push notifikaci
  Future<void> sendNotification({
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    if (_currentUser == null) return;

    try {
      // TODO: Implementovat server-side funkci pro odesílání notifikací
      debugPrint('Odesílání notifikace: $title');
    } catch (e) {
      debugPrint('Chyba při odesílání notifikace: $e');
    }
  }

  /// Zpracuje Firebase Auth výjimky
  Exception _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return Exception('Uživatel s tímto emailem neexistuje');
      case 'wrong-password':
        return Exception('Nesprávné heslo');
      case 'email-already-in-use':
        return Exception('Email je již používán');
      case 'weak-password':
        return Exception('Heslo je příliš slabé');
      case 'invalid-email':
        return Exception('Neplatný email');
      case 'user-disabled':
        return Exception('Uživatelský účet je deaktivován');
      case 'too-many-requests':
        return Exception('Příliš mnoho pokusů, zkuste to později');
      default:
        return Exception('Chyba autentifikace: ${e.message}');
    }
  }

  @override
  void dispose() {
    _authSubscription?.cancel();
    super.dispose();
  }
}
