import 'package:flutter_test/flutter_test.dart';
import 'package:croatia_travel_app/models/offline_data.dart';
import 'package:croatia_travel_app/services/offline_ai_service.dart';
import 'package:croatia_travel_app/services/offline_manager_service.dart';

void main() {
  group('Offline Functionality Tests', () {
    late OfflineAIService aiService;
    late OfflineManagerService managerService;

    setUp(() {
      aiService = OfflineAIService();
      managerService = OfflineManagerService();
    });

    group('OfflineDataPackage Tests', () {
      test('OfflineDataPackage creation and properties', () {
        final package = OfflineDataPackage(
          id: 'test_package',
          name: 'Test Package',
          description: 'Test description',
          type: OfflineDataType.places,
          status: OfflineDataStatus.downloaded,
          sizeBytes: 1024 * 1024, // 1 MB
          version: 1,
          region: 'croatia',
          downloadedAt: DateTime.now(),
          lastUpdated: DateTime.now(),
        );

        expect(package.id, equals('test_package'));
        expect(package.name, equals('Test Package'));
        expect(package.type, equals(OfflineDataType.places));
        expect(package.status, equals(OfflineDataStatus.downloaded));
        expect(package.sizeBytes, equals(1024 * 1024));
        expect(package.sizeMB, equals(1.0));
        expect(package.sizeDescription, equals('1.0 MB'));
      });

      test('OfflineDataPackage isUpToDate check', () {
        final recentPackage = OfflineDataPackage(
          id: 'recent',
          name: 'Recent Package',
          description: 'Recently downloaded',
          type: OfflineDataType.places,
          status: OfflineDataStatus.downloaded,
          sizeBytes: 1024,
          version: 1,
          region: 'croatia',
          downloadedAt: DateTime.now(),
          lastUpdated: DateTime.now(),
        );

        final oldPackage = OfflineDataPackage(
          id: 'old',
          name: 'Old Package',
          description: 'Old download',
          type: OfflineDataType.places,
          status: OfflineDataStatus.downloaded,
          sizeBytes: 1024,
          version: 1,
          region: 'croatia',
          downloadedAt: DateTime.now().subtract(const Duration(days: 10)),
          lastUpdated: DateTime.now().subtract(const Duration(days: 10)),
        );

        expect(recentPackage.isUpToDate, isTrue);
        expect(oldPackage.isUpToDate, isFalse);
      });

      test('OfflineDataPackage isAvailable check', () {
        final availablePackage = OfflineDataPackage(
          id: 'available',
          name: 'Available Package',
          description: 'Available for use',
          type: OfflineDataType.places,
          status: OfflineDataStatus.downloaded,
          sizeBytes: 1024,
          version: 1,
          region: 'croatia',
          downloadedAt: DateTime.now(),
          lastUpdated: DateTime.now(),
        );

        final unavailablePackage = OfflineDataPackage(
          id: 'unavailable',
          name: 'Unavailable Package',
          description: 'Not available',
          type: OfflineDataType.places,
          status: OfflineDataStatus.notDownloaded,
          sizeBytes: 1024,
          version: 1,
          region: 'croatia',
        );

        expect(availablePackage.isAvailable, isTrue);
        expect(unavailablePackage.isAvailable, isFalse);
      });

      test('OfflineDataPackage status descriptions in Czech', () {
        final statuses = [
          (OfflineDataStatus.notDownloaded, 'Nestaženo'),
          (OfflineDataStatus.downloading, 'Stahování...'),
          (OfflineDataStatus.downloaded, 'Aktuální'),
          (OfflineDataStatus.outdated, 'Zastaralé'),
          (OfflineDataStatus.error, 'Chyba'),
        ];

        for (final (status, expectedDescription) in statuses) {
          final package = OfflineDataPackage(
            id: 'test',
            name: 'Test',
            description: 'Test',
            type: OfflineDataType.places,
            status: status,
            sizeBytes: 1024,
            version: 1,
            region: 'croatia',
            downloadedAt: status == OfflineDataStatus.downloaded
                ? DateTime.now()
                : null,
            lastUpdated: status == OfflineDataStatus.downloaded
                ? DateTime.now()
                : null,
          );

          expect(package.statusDescription, equals(expectedDescription));
        }
      });

      test('OfflineDataPackage copyWith functionality', () {
        final original = OfflineDataPackage(
          id: 'original',
          name: 'Original',
          description: 'Original description',
          type: OfflineDataType.places,
          status: OfflineDataStatus.notDownloaded,
          sizeBytes: 1024,
          version: 1,
          region: 'croatia',
        );

        final updated = original.copyWith(
          status: OfflineDataStatus.downloaded,
          downloadedAt: DateTime.now(),
        );

        expect(updated.id, equals(original.id));
        expect(updated.name, equals(original.name));
        expect(updated.status, equals(OfflineDataStatus.downloaded));
        expect(updated.downloadedAt, isNotNull);
        expect(original.status, equals(OfflineDataStatus.notDownloaded));
      });
    });

    group('OfflineAIResponse Tests', () {
      test('OfflineAIResponse creation and matching', () {
        final response = OfflineAIResponse(
          id: 'test_response',
          question: 'Jak se dostanu do Chorvatska?',
          answer:
              'Do Chorvatska se můžete dostat letadlem, autem nebo autobusem.',
          keywords: ['doprava', 'letadlo', 'auto', 'autobus', 'chorvatsko'],
          category: 'doprava',
          createdAt: DateTime.now(),
        );

        expect(response.id, equals('test_response'));
        expect(response.category, equals('doprava'));
        expect(response.keywords.length, equals(5));
        expect(response.useCount, equals(0));
        expect(response.relevanceScore, equals(1.0));
      });

      test('OfflineAIResponse query matching', () {
        final response = OfflineAIResponse(
          id: 'transport',
          question: 'Jak se dostanu do Chorvatska?',
          answer: 'Můžete použít letadlo, auto nebo autobus.',
          keywords: ['doprava', 'letadlo', 'auto', 'autobus'],
          category: 'doprava',
          createdAt: DateTime.now(),
        );

        // Test přímé shody
        expect(response.matchesQuery('jak se dostanu'), isTrue);
        expect(response.matchesQuery('do chorvatska'), isTrue);

        // Test shody klíčových slov
        expect(response.matchesQuery('doprava'), isTrue);
        expect(response.matchesQuery('letadlo'), isTrue);
        expect(response.matchesQuery('auto'), isTrue);

        // Test neshody
        expect(response.matchesQuery('ubytování'), isFalse);
        expect(response.matchesQuery('jídlo'), isFalse);
      });

      test('OfflineAIResponse copyWith for use count', () {
        final original = OfflineAIResponse(
          id: 'test',
          question: 'Test question',
          answer: 'Test answer',
          keywords: ['test'],
          category: 'test',
          createdAt: DateTime.now(),
          useCount: 5,
        );

        final updated = original.copyWith(useCount: 10);

        expect(updated.useCount, equals(10));
        expect(original.useCount, equals(5));
        expect(updated.id, equals(original.id));
      });
    });

    group('OfflineStats Tests', () {
      test('OfflineStats calculations', () {
        final stats = OfflineStats(
          totalPackages: 4,
          downloadedPackages: 2,
          totalSizeBytes: 8 * 1024 * 1024, // 8 MB
          downloadedSizeBytes: 3 * 1024 * 1024, // 3 MB
          lastSyncAt: DateTime.now(),
          syncCount: 5,
          packagesByType: {
            OfflineDataType.places: 1,
            OfflineDataType.tickets: 1,
            OfflineDataType.aiResponses: 1,
            OfflineDataType.emergency: 1,
          },
        );

        expect(stats.downloadProgress, equals(0.5)); // 2/4 = 0.5
        expect(stats.storageProgress, equals(0.375)); // 3/8 = 0.375
        expect(stats.totalSizeDescription, equals('8.0 MB'));
        expect(stats.downloadedSizeDescription, equals('3.0 MB'));
      });

      test('OfflineStats size formatting', () {
        final testCases = [
          (512, '512 B'),
          (1024, '1.0 KB'),
          (1024 * 1024, '1.0 MB'),
          (1024 * 1024 * 1024, '1.0 GB'),
        ];

        for (final (bytes, expectedFormat) in testCases) {
          final stats = OfflineStats(
            totalPackages: 1,
            downloadedPackages: 1,
            totalSizeBytes: bytes,
            downloadedSizeBytes: bytes,
            lastSyncAt: DateTime.now(),
            syncCount: 1,
            packagesByType: {OfflineDataType.places: 1},
          );

          expect(stats.totalSizeDescription, equals(expectedFormat));
        }
      });
    });

    group('OfflineMapArea Tests', () {
      test('OfflineMapArea position containment', () {
        final croatiaArea = OfflineMapArea(
          id: 'croatia',
          name: 'Chorvatsko',
          description: 'Celé území Chorvatska',
          northLatitude: 46.5,
          southLatitude: 42.4,
          eastLongitude: 19.4,
          westLongitude: 13.5,
          zoomLevel: 8,
          status: OfflineDataStatus.downloaded,
          sizeBytes: 50 * 1024 * 1024, // 50 MB
          tileCount: 1000,
        );

        // Zagreb coordinates
        expect(croatiaArea.containsPosition(45.8150, 15.9819), isTrue);

        // Split coordinates
        expect(croatiaArea.containsPosition(43.5081, 16.4402), isTrue);

        // Outside Croatia (Vienna)
        expect(croatiaArea.containsPosition(48.2082, 16.3738), isFalse);

        // Outside Croatia (Rome)
        expect(croatiaArea.containsPosition(41.9028, 12.4964), isFalse);
      });

      test('OfflineMapArea area calculation', () {
        final smallArea = OfflineMapArea(
          id: 'small',
          name: 'Small Area',
          description: 'Small test area',
          northLatitude: 46.0,
          southLatitude: 45.0,
          eastLongitude: 16.0,
          westLongitude: 15.0,
          zoomLevel: 10,
          status: OfflineDataStatus.downloaded,
          sizeBytes: 1024,
          tileCount: 100,
        );

        final area = smallArea.areaSqKm;
        expect(area, greaterThan(0));
        expect(area, lessThan(20000)); // Reasonable upper bound
      });
    });

    group('Offline AI Service Tests', () {
      test('AI service initialization', () {
        expect(aiService, isNotNull);
        expect(aiService.isInitialized, isFalse);
        expect(aiService.responses, isEmpty);
      });

      test('AI service predefined responses', () async {
        await aiService.initialize();

        expect(aiService.isInitialized, isTrue);
        expect(aiService.responses, isNotEmpty);

        // Should have responses in different categories
        final categories = aiService.responses.map((r) => r.category).toSet();
        expect(categories, contains('obecné'));
        expect(categories, contains('doprava'));
        expect(categories, contains('gastronomie'));
        expect(categories, contains('památky'));
      });

      test('AI service query answering', () async {
        await aiService.initialize();

        // Test basic queries
        final transportAnswer = await aiService.findAnswer(
          'jak se dostanu do chorvatska',
        );
        expect(transportAnswer, isNotNull);
        expect(transportAnswer!.toLowerCase(), contains('letadl'));

        final foodAnswer = await aiService.findAnswer('co ochutnat');
        expect(foodAnswer, isNotNull);
        expect(foodAnswer!.toLowerCase(), contains('čevapčiči'));

        final unknownAnswer = await aiService.findAnswer(
          'něco úplně neznámého',
        );
        expect(unknownAnswer, isNotNull);
        expect(unknownAnswer!.toLowerCase(), contains('omlouváme'));
      });

      test('AI service response statistics', () async {
        await aiService.initialize();

        final stats = aiService.getResponseStats();
        expect(stats, isNotEmpty);
        expect(stats.keys, contains('obecné'));
        expect(stats.keys, contains('doprava'));

        final mostUsed = aiService.getMostUsedResponses(limit: 5);
        expect(mostUsed.length, lessThanOrEqualTo(5));
      });
    });

    group('Offline Manager Service Tests', () {
      test('Manager service initialization', () {
        expect(managerService, isNotNull);
        expect(managerService.isInitialized, isFalse);
        expect(managerService.packages, isEmpty);
      });

      test('Manager service default packages', () async {
        await managerService.initialize();

        expect(managerService.isInitialized, isTrue);
        expect(managerService.packages, isNotEmpty);

        // Should have all required package types
        final types = managerService.packages.map((p) => p.type).toSet();
        expect(types, contains(OfflineDataType.places));
        expect(types, contains(OfflineDataType.tickets));
        expect(types, contains(OfflineDataType.aiResponses));
        expect(types, contains(OfflineDataType.emergency));
      });

      test('Manager service offline stats', () async {
        await managerService.initialize();

        final stats = await managerService.getOfflineStats();
        expect(stats.totalPackages, greaterThan(0));
        expect(stats.totalSizeBytes, greaterThan(0));
        expect(stats.packagesByType, isNotEmpty);
      });

      test('Manager service data availability check', () async {
        await managerService.initialize();

        // AI responses should be available by default
        final aiAvailable = await managerService.isDataAvailableOffline(
          OfflineDataType.aiResponses,
        );
        expect(aiAvailable, isTrue);

        // Other types should not be available initially
        final placesAvailable = await managerService.isDataAvailableOffline(
          OfflineDataType.places,
        );
        expect(placesAvailable, isFalse);
      });
    });

    group('Integration Tests', () {
      test('Full offline workflow simulation', () async {
        // Initialize all services
        await managerService.initialize();
        await aiService.initialize();

        // Check initial state
        expect(managerService.isInitialized, isTrue);
        expect(aiService.isInitialized, isTrue);

        // Test AI functionality
        final answer = await aiService.findAnswer('chorvatsko');
        expect(answer, isNotNull);
        expect(answer!.contains('Chorvatsko'), isTrue);

        // Check offline stats
        final stats = await managerService.getOfflineStats();
        expect(stats.totalPackages, greaterThan(0));

        // Verify AI responses are available
        final aiAvailable = await managerService.isDataAvailableOffline(
          OfflineDataType.aiResponses,
        );
        expect(aiAvailable, isTrue);
      });

      test('Offline data types coverage', () {
        final allTypes = OfflineDataType.values;
        final allStatuses = OfflineDataStatus.values;

        // Ensure all types are covered
        expect(allTypes, contains(OfflineDataType.places));
        expect(allTypes, contains(OfflineDataType.tickets));
        expect(allTypes, contains(OfflineDataType.aiResponses));
        expect(allTypes, contains(OfflineDataType.emergency));

        // Ensure all statuses are covered
        expect(allStatuses, contains(OfflineDataStatus.notDownloaded));
        expect(allStatuses, contains(OfflineDataStatus.downloading));
        expect(allStatuses, contains(OfflineDataStatus.downloaded));
        expect(allStatuses, contains(OfflineDataStatus.outdated));
        expect(allStatuses, contains(OfflineDataStatus.error));
      });

      test('Croatian language support in offline responses', () async {
        await aiService.initialize();

        // Test Czech/Croatian language queries
        final czechQueries = [
          'kde najdu restauraci',
          'jak se dostanu',
          'co vidět',
          'kolik stojí',
          'nouzové číslo',
        ];

        for (final query in czechQueries) {
          final answer = await aiService.findAnswer(query);
          expect(answer, isNotNull);
          expect(answer!.isNotEmpty, isTrue);
        }
      });
    });
  });
}
