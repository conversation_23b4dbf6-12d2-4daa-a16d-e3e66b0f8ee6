import 'package:flutter/material.dart';
import '../services/recommendation_service.dart';

class RecommendationsWidget extends StatefulWidget {
  final bool batterySaverMode;

  const RecommendationsWidget({super.key, this.batterySaverMode = false});

  @override
  State<RecommendationsWidget> createState() => _RecommendationsWidgetState();
}

class _RecommendationsWidgetState extends State<RecommendationsWidget> {
  final RecommendationService _recommendationService = RecommendationService();
  List<Recommendation> _recommendations = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadRecommendations();
  }

  Future<void> _loadRecommendations() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final recommendations = await _recommendationService
          .getPersonalizedRecommendations(
            batterySaverMode: widget.batterySaverMode,
          );

      setState(() {
        _recommendations = recommendations;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      debugPrint('Chyba při načítání doporučení: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    if (_recommendations.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(Icons.lightbulb_outline, size: 48, color: Colors.grey[400]),
              const SizedBox(height: 8),
              Text(
                'Žádná doporučení',
                style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              ),
              const SizedBox(height: 4),
              Text(
                'Prozkoumejte více míst pro personalizovaná doporučení',
                style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Icon(Icons.recommend, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'Doporučeno pro vás',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                if (widget.batterySaverMode)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.amber.shade100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'Úsporný režim',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.amber.shade700,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _recommendations.length,
              itemBuilder: (context, index) {
                final recommendation = _recommendations[index];
                return Container(
                  width: 280,
                  margin: const EdgeInsets.only(right: 12),
                  child: _buildRecommendationCard(recommendation),
                );
              },
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildRecommendationCard(Recommendation recommendation) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => _onRecommendationTap(recommendation),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Obrázek nebo ikona
            Container(
              height: 100,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
                gradient: LinearGradient(
                  colors: _getGradientColors(recommendation.type),
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Center(
                child: Icon(
                  _getRecommendationIcon(recommendation.type),
                  size: 48,
                  color: Colors.white,
                ),
              ),
            ),

            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      recommendation.title,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      recommendation.description,
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const Spacer(),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: _getTypeColor(
                              recommendation.type,
                            ).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            _getTypeLabel(recommendation.type),
                            style: TextStyle(
                              fontSize: 10,
                              color: _getTypeColor(recommendation.type),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const Spacer(),
                        if (recommendation.confidence != null)
                          Row(
                            children: [
                              Icon(
                                Icons.star,
                                size: 12,
                                color: Colors.amber[600],
                              ),
                              Text(
                                '${(recommendation.confidence! * 100).round()}%',
                                style: const TextStyle(fontSize: 10),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Color> _getGradientColors(RecommendationType type) {
    switch (type) {
      case RecommendationType.place:
        return [Colors.blue.shade400, Colors.blue.shade600];
      case RecommendationType.event:
        return [Colors.purple.shade400, Colors.purple.shade600];
      case RecommendationType.cuisine:
        return [Colors.orange.shade400, Colors.orange.shade600];
      case RecommendationType.route:
        return [Colors.green.shade400, Colors.green.shade600];
      case RecommendationType.activity:
        return [Colors.teal.shade400, Colors.teal.shade600];
    }
  }

  IconData _getRecommendationIcon(RecommendationType type) {
    switch (type) {
      case RecommendationType.place:
        return Icons.place;
      case RecommendationType.event:
        return Icons.event;
      case RecommendationType.cuisine:
        return Icons.restaurant;
      case RecommendationType.route:
        return Icons.route;
      case RecommendationType.activity:
        return Icons.local_activity;
    }
  }

  Color _getTypeColor(RecommendationType type) {
    switch (type) {
      case RecommendationType.place:
        return Colors.blue;
      case RecommendationType.event:
        return Colors.purple;
      case RecommendationType.cuisine:
        return Colors.orange;
      case RecommendationType.route:
        return Colors.green;
      case RecommendationType.activity:
        return Colors.teal;
    }
  }

  String _getTypeLabel(RecommendationType type) {
    switch (type) {
      case RecommendationType.place:
        return 'Místo';
      case RecommendationType.event:
        return 'Událost';
      case RecommendationType.cuisine:
        return 'Kuchyně';
      case RecommendationType.route:
        return 'Trasa';
      case RecommendationType.activity:
        return 'Aktivita';
    }
  }

  void _onRecommendationTap(Recommendation recommendation) {
    switch (recommendation.type) {
      case RecommendationType.place:
        Navigator.pushNamed(context, '/places/${recommendation.id}');
        break;
      case RecommendationType.event:
        Navigator.pushNamed(context, '/events/${recommendation.id}');
        break;
      case RecommendationType.cuisine:
        // Otevřít detail pokrmu
        break;
      case RecommendationType.route:
        Navigator.pushNamed(context, '/routes/${recommendation.id}');
        break;
      case RecommendationType.activity:
        // Otevřít detail aktivity
        break;
    }

    // Zaznamenat interakci pro zlepšení doporučení
    _recommendationService.recordInteraction(
      recommendation.id,
      RecommendationInteraction.tap,
    );
  }
}
