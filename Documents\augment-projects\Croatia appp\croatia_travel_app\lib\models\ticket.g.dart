// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ticket.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Ticket _$TicketFromJson(Map<String, dynamic> json) => Ticket(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: $enumDecode(_$TicketTypeEnumMap, json['type']),
      venueId: json['venueId'] as String,
      venueName: json['venueName'] as String,
      location: json['location'] as String,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      region: json['region'] as String,
      pricing: TicketPricing.fromJson(json['pricing'] as Map<String, dynamic>),
      availability: (json['availability'] as List<dynamic>)
          .map((e) => TicketAvailability.fromJson(e as Map<String, dynamic>))
          .toList(),
      provider:
          TicketProvider.fromJson(json['provider'] as Map<String, dynamic>),
      images: (json['images'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      features:
          TicketFeatures.fromJson(json['features'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isActive: json['isActive'] as bool? ?? true,
    );

Map<String, dynamic> _$TicketToJson(Ticket instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'type': _$TicketTypeEnumMap[instance.type]!,
      'venueId': instance.venueId,
      'venueName': instance.venueName,
      'location': instance.location,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'region': instance.region,
      'pricing': instance.pricing,
      'availability': instance.availability,
      'provider': instance.provider,
      'images': instance.images,
      'tags': instance.tags,
      'features': instance.features,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isActive': instance.isActive,
    };

const _$TicketTypeEnumMap = {
  TicketType.museum: 'museum',
  TicketType.monument: 'monument',
  TicketType.park: 'park',
  TicketType.event: 'event',
  TicketType.tour: 'tour',
  TicketType.activity: 'activity',
  TicketType.transport: 'transport',
  TicketType.combo: 'combo',
};

TicketPricing _$TicketPricingFromJson(Map<String, dynamic> json) =>
    TicketPricing(
      categories: (json['categories'] as List<dynamic>)
          .map((e) => TicketCategoryPricing.fromJson(e as Map<String, dynamic>))
          .toList(),
      groupDiscounts: (json['groupDiscounts'] as List<dynamic>?)
              ?.map((e) => GroupDiscount.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      seasonalDiscounts: (json['seasonalDiscounts'] as List<dynamic>?)
              ?.map((e) => SeasonalDiscount.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      currency: json['currency'] as String? ?? 'EUR',
      includesVAT: json['includesVAT'] as bool? ?? true,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$TicketPricingToJson(TicketPricing instance) =>
    <String, dynamic>{
      'categories': instance.categories,
      'groupDiscounts': instance.groupDiscounts,
      'seasonalDiscounts': instance.seasonalDiscounts,
      'currency': instance.currency,
      'includesVAT': instance.includesVAT,
      'notes': instance.notes,
    };

TicketCategoryPricing _$TicketCategoryPricingFromJson(
        Map<String, dynamic> json) =>
    TicketCategoryPricing(
      category: $enumDecode(_$TicketCategoryEnumMap, json['category']),
      price: (json['price'] as num).toDouble(),
      description: json['description'] as String,
      minAge: (json['minAge'] as num?)?.toInt(),
      maxAge: (json['maxAge'] as num?)?.toInt(),
      requiresProof: json['requiresProof'] as bool? ?? false,
    );

Map<String, dynamic> _$TicketCategoryPricingToJson(
        TicketCategoryPricing instance) =>
    <String, dynamic>{
      'category': _$TicketCategoryEnumMap[instance.category]!,
      'price': instance.price,
      'description': instance.description,
      'minAge': instance.minAge,
      'maxAge': instance.maxAge,
      'requiresProof': instance.requiresProof,
    };

const _$TicketCategoryEnumMap = {
  TicketCategory.adult: 'adult',
  TicketCategory.child: 'child',
  TicketCategory.student: 'student',
  TicketCategory.senior: 'senior',
  TicketCategory.family: 'family',
  TicketCategory.group: 'group',
  TicketCategory.disabled: 'disabled',
};

GroupDiscount _$GroupDiscountFromJson(Map<String, dynamic> json) =>
    GroupDiscount(
      minPeople: (json['minPeople'] as num).toInt(),
      discountPercentage: (json['discountPercentage'] as num).toDouble(),
      description: json['description'] as String,
    );

Map<String, dynamic> _$GroupDiscountToJson(GroupDiscount instance) =>
    <String, dynamic>{
      'minPeople': instance.minPeople,
      'discountPercentage': instance.discountPercentage,
      'description': instance.description,
    };

SeasonalDiscount _$SeasonalDiscountFromJson(Map<String, dynamic> json) =>
    SeasonalDiscount(
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      discountPercentage: (json['discountPercentage'] as num).toDouble(),
      description: json['description'] as String,
      applicableCategories: (json['applicableCategories'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$TicketCategoryEnumMap, e))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$SeasonalDiscountToJson(SeasonalDiscount instance) =>
    <String, dynamic>{
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'discountPercentage': instance.discountPercentage,
      'description': instance.description,
      'applicableCategories': instance.applicableCategories
          .map((e) => _$TicketCategoryEnumMap[e]!)
          .toList(),
    };

TicketAvailability _$TicketAvailabilityFromJson(Map<String, dynamic> json) =>
    TicketAvailability(
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      availableDays: (json['availableDays'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [1, 2, 3, 4, 5, 6, 7],
      openingHours: json['openingHours'] as String?,
      maxCapacity: (json['maxCapacity'] as num?)?.toInt(),
      requiresReservation: json['requiresReservation'] as bool? ?? false,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$TicketAvailabilityToJson(TicketAvailability instance) =>
    <String, dynamic>{
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'availableDays': instance.availableDays,
      'openingHours': instance.openingHours,
      'maxCapacity': instance.maxCapacity,
      'requiresReservation': instance.requiresReservation,
      'notes': instance.notes,
    };

TicketProvider _$TicketProviderFromJson(Map<String, dynamic> json) =>
    TicketProvider(
      name: json['name'] as String,
      officialWebsite: json['officialWebsite'] as String,
      bookingUrl: json['bookingUrl'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      email: json['email'] as String?,
      type: $enumDecode(_$ProviderTypeEnumMap, json['type']),
      isVerified: json['isVerified'] as bool? ?? false,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$TicketProviderToJson(TicketProvider instance) =>
    <String, dynamic>{
      'name': instance.name,
      'officialWebsite': instance.officialWebsite,
      'bookingUrl': instance.bookingUrl,
      'phoneNumber': instance.phoneNumber,
      'email': instance.email,
      'type': _$ProviderTypeEnumMap[instance.type]!,
      'isVerified': instance.isVerified,
      'rating': instance.rating,
      'notes': instance.notes,
    };

const _$ProviderTypeEnumMap = {
  ProviderType.official: 'official',
  ProviderType.authorized: 'authorized',
  ProviderType.partner: 'partner',
};

TicketFeatures _$TicketFeaturesFromJson(Map<String, dynamic> json) =>
    TicketFeatures(
      hasQRCode: json['hasQRCode'] as bool? ?? true,
      isMobileTicket: json['isMobileTicket'] as bool? ?? true,
      allowsCancellation: json['allowsCancellation'] as bool? ?? false,
      allowsRescheduling: json['allowsRescheduling'] as bool? ?? false,
      includesAudioGuide: json['includesAudioGuide'] as bool? ?? false,
      includesMap: json['includesMap'] as bool? ?? false,
      hasGroupGuide: json['hasGroupGuide'] as bool? ?? false,
      isSkipTheLine: json['isSkipTheLine'] as bool? ?? false,
      includedServices: (json['includedServices'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      restrictions: (json['restrictions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$TicketFeaturesToJson(TicketFeatures instance) =>
    <String, dynamic>{
      'hasQRCode': instance.hasQRCode,
      'isMobileTicket': instance.isMobileTicket,
      'allowsCancellation': instance.allowsCancellation,
      'allowsRescheduling': instance.allowsRescheduling,
      'includesAudioGuide': instance.includesAudioGuide,
      'includesMap': instance.includesMap,
      'hasGroupGuide': instance.hasGroupGuide,
      'isSkipTheLine': instance.isSkipTheLine,
      'includedServices': instance.includedServices,
      'restrictions': instance.restrictions,
    };
