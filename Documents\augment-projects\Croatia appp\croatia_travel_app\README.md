# 🇭🇷 Croatia Travel App

Komprehenzivní cestovní aplikace pro Chorvatsko s pokročilými dopravními funkcemi a 100% legálními zdroji dat.

## 🌟 Klíčové funkce

### 🚌 Dopravní systém
- **100% legální zdroje dat** - Open Data, OpenStreetMap, partnerské API
- **Real-time informace** - Crowdsourcing od uživatelů
- **Plánování tras** - Multimodální doprava
- **Online jízdenky** - Integrace s dopravními společnostmi
- **Offline podpora** - Funguje bez internetu

### 🗺️ Mapy a navigace
- **Detailní mapy Chorvatska** - Offline dostupné
- **Označování navštívených míst** - Osobní cestovní deník
- **AR rozpoznávání památek** - Rozšířená realita
- **Audio průvodci** - Profesionální komentáře

### 🎯 Cestovní funkce
- **Regionální <PERSON>** - Dalmácie, Istrie, Slavonie
- **Chorvatská kuchyně** - Restaurace a recepty
- **Rozpočet** - Správa cestovních výdajů
- **Cestovní slovník** - Chorvatsko-český
- **Tematické balíčky** - Připravené itineráře

### 🤖 AI a pokročilé funkce
- **AI web scraping** - Inteligentní získávání dat
- **Hlasové nahrávky** - Voice-to-text integrace
- **Personalizované doporučení** - ML algoritmy
- **Chytrá města** - Veřejná doprava, služby

## 🏗️ Architektura

### Legální dopravní služby
```
LegalTransportService
├── OpenDataService (data.gov.hr, data.zagreb.hr)
├── CrowdsourcingService (uživatelské reporty)
├── PartnerServices (Google Transit, OSM, Moovit)
└── Fallback strategies (mock data)
```

### AI alternativy (volitelné)
```
HybridTransportService
├── AITransportScraper (inteligentní web scraping)
├── LegalTransportService (legální fallback)
└── Adaptive algorithms (přizpůsobení změnám)
```

## 🚀 Rychlý start

### Požadavky
- Flutter 3.16.0+
- Dart 3.2.0+
- Android Studio / VS Code
- Git

### Instalace

```bash
# Klonování repozitáře
git clone https://github.com/your-username/croatia-travel-app.git
cd croatia-travel-app

# Instalace dependencies
flutter pub get

# Spuštění aplikace
flutter run
```

### Konfigurace (volitelné)

Vytvořte `.env` soubor pro API klíče:

```env
# Google Maps (volitelné)
GOOGLE_MAPS_API_KEY=your_key_here

# Partner API (volitelné)
MOOVIT_API_KEY=your_key_here
CITYMAPPER_API_KEY=your_key_here

# Backend pro crowdsourcing
CROWDSOURCING_API_URL=https://your-backend.com/api
```

## 📱 Použití

### Základní dopravní funkce

```dart
// Inicializace legální služby
final legalService = LegalTransportService();
await legalService.initialize();

// Získání zastávek (Open Data + OSM + partneri)
final stops = await legalService.getStopsForCity('zagreb');

// Real-time data (crowdsourcing)
final arrivals = await legalService.getRealTimeArrivals('stop_001', 'zagreb');

// Plánování tras
final routes = await legalService.planRoute(
  fromLat: 45.815,
  fromLng: 15.982,
  toLat: 45.813,
  toLng: 15.977,
);
```

### Crowdsourcing

```dart
// Reportování zpoždění
await legalService.reportUserData(
  type: UserReportType.delay,
  data: {
    'stopId': 'stop_001',
    'routeNumber': '6',
    'delay': Duration(minutes: 5),
  },
  userId: 'user123',
);
```

### AI scraping (volitelné)

```dart
// Hybridní přístup (legální + AI)
final hybridService = HybridTransportService();
await hybridService.initialize();

// Automaticky vybere nejlepší zdroj
final stops = await hybridService.getStopsForCity('zagreb');
```

## 🔒 Legální aspekty

### ✅ 100% legální zdroje
- **Open Data portály** - Oficiální vládní data
- **OpenStreetMap** - Community-driven mapa
- **Partnerské API** - Oficiální smlouvy
- **Crowdsourcing** - Uživatelská data s consent

### ⚠️ AI scraping (volitelné)
- Může porušovat Terms of Service
- Pouze pro testování a development
- Doporučujeme kombinovat s legálními zdroji

## 📊 Podporovaná města

| Město | Open Data | OSM | Real-time | Status |
|-------|-----------|-----|-----------|---------|
| **Zagreb** | ✅ GTFS | ✅ Kompletní | ✅ Crowdsourcing | Plně podporováno |
| **Split** | ⚠️ Omezené | ✅ Základní | ✅ Crowdsourcing | Částečně podporováno |
| **Rijeka** | ⚠️ Minimální | ✅ Základní | ✅ Crowdsourcing | Základní podpora |
| **Dubrovnik** | ❌ Nedostupné | ✅ Základní | ✅ Crowdsourcing | Základní podpora |

## 🧪 Testování

```bash
# Unit testy
flutter test

# Integration testy
flutter test integration_test/

# Coverage report
flutter test --coverage

# Specifické testy
flutter test test/services/legal_transport_service_test.dart
```

## 📚 Dokumentace

- [🔒 Legální alternativy](docs/LEGAL_ALTERNATIVES.md) - Kompletní průvodce legálními zdroji
- [🤖 AI Scraping Guide](docs/AI_SCRAPING_GUIDE.md) - AI web scraping implementace
- [🚀 Deployment Guide](docs/DEPLOYMENT_GUIDE.md) - Nasazení do produkce
- [🔗 Real API Integration](docs/REAL_API_INTEGRATION.md) - Oficiální API integrace

## 🤝 Přispívání

### Crowdsourcing data
Pomozte vylepšit dopravní data:
- Nahlašujte zpoždění vozidel
- Reportujte obsazenost
- Přidávejte nové zastávky
- Hlasujte o kvalitě dat

### Development
```bash
# Fork repozitář
git fork https://github.com/your-username/croatia-travel-app.git

# Vytvořte feature branch
git checkout -b feature/amazing-feature

# Commit změny
git commit -m 'Add amazing feature'

# Push do branch
git push origin feature/amazing-feature

# Otevřete Pull Request
```

## 📄 Licence

Tento projekt je licencován pod MIT licencí - viz [LICENSE](LICENSE) soubor.

## 🆘 Podpora

### Kontakt
- **Email**: <EMAIL>
- **GitHub Issues**: [Nahlásit problém](https://github.com/your-username/croatia-travel-app/issues)
- **Discord**: [Komunitní chat](https://discord.gg/croatia-travel)

### FAQ

**Q: Je aplikace zdarma?**
A: Ano, základní funkce jsou zdarma. Premium funkce vyžadují předplatné.

**Q: Funguje offline?**
A: Ano, mapy a základní dopravní data jsou dostupné offline.

**Q: Jsou data aktuální?**
A: Ano, používáme oficiální Open Data zdroje a crowdsourcing pro aktuálnost.

**Q: Je web scraping legální?**
A: AI scraping je volitelná funkce. Doporučujeme používat pouze legální zdroje.

## 🎉 Poděkování

- **Croatian Open Data Portal** - Za poskytování oficiálních dat
- **OpenStreetMap community** - Za mapová data
- **Flutter team** - Za skvělý framework
- **Všem přispěvatelům** - Za vylepšení aplikace

---

**🇭🇷 Vytvořeno s láskou pro Chorvatsko a cestování! 🌊**
