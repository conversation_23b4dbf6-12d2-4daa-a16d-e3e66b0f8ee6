import 'package:json_annotation/json_annotation.dart';

part 'parking.g.dart';

// Enums
enum ParkingType { street, garage, lot, private, residential }

enum ParkingRateType { hourly, daily, fixed, perMinute }

enum ParkingSessionStatus { active, completed, cancelled, expired, pending }

enum SharedVehicleType { bike, eBike, scooter, eScooter, car, eCar }

enum SharedVehicleStatus {
  available,
  reserved,
  inUse,
  maintenance,
  lowBattery,
  outOfService,
}

enum SharedVehicleRentalStatus { active, completed, cancelled, paused }

enum SmartReservationStatus { active, confirmed, cancelled, expired, completed }

// Models
@JsonSerializable()
class ParkingSpot {
  final String id;
  final String name;
  final String address;
  final double latitude;
  final double longitude;
  final ParkingType type;
  final int totalSpaces;
  final int availableSpaces;
  final List<ParkingRate> rates;
  final List<String> paymentMethods;
  final List<String> features;
  final String? description;
  final List<String> restrictions;
  final bool isActive;
  final DateTime lastUpdated;
  final double? maxHeight;
  final String? operatorName;
  final String? phoneNumber;

  const ParkingSpot({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.type,
    required this.totalSpaces,
    required this.availableSpaces,
    required this.rates,
    required this.paymentMethods,
    this.features = const [],
    this.description,
    this.restrictions = const [],
    this.isActive = true,
    required this.lastUpdated,
    this.maxHeight,
    this.operatorName,
    this.phoneNumber,
  });

  factory ParkingSpot.fromJson(Map<String, dynamic> json) =>
      _$ParkingSpotFromJson(json);

  Map<String, dynamic> toJson() => _$ParkingSpotToJson(this);
}

@JsonSerializable()
class ParkingRate {
  final String id;
  final String name;
  final ParkingRateType type;
  final double price;
  final String currency;
  final Duration? duration;
  final int? maxDuration;
  final List<String> validDays;
  final String? startTime;
  final String? endTime;
  final bool isActive;

  const ParkingRate({
    required this.id,
    required this.name,
    required this.type,
    required this.price,
    required this.currency,
    this.duration,
    this.maxDuration,
    this.validDays = const [],
    this.startTime,
    this.endTime,
    this.isActive = true,
  });

  factory ParkingRate.fromJson(Map<String, dynamic> json) =>
      _$ParkingRateFromJson(json);

  Map<String, dynamic> toJson() => _$ParkingRateToJson(this);
}

@JsonSerializable()
class ParkingSession {
  final String id;
  final String parkingSpotId;
  final String? userId;
  final String licensePlate;
  final DateTime startTime;
  final DateTime? endTime;
  final Duration? plannedDuration;
  final ParkingSessionStatus status;
  final double? totalCost;
  final String? currency;
  final String? paymentId;
  final List<ParkingExtension> extensions;
  final String? qrCode;

  const ParkingSession({
    required this.id,
    required this.parkingSpotId,
    this.userId,
    required this.licensePlate,
    required this.startTime,
    this.endTime,
    this.plannedDuration,
    required this.status,
    this.totalCost,
    this.currency,
    this.paymentId,
    this.extensions = const [],
    this.qrCode,
  });

  factory ParkingSession.fromJson(Map<String, dynamic> json) =>
      _$ParkingSessionFromJson(json);

  Map<String, dynamic> toJson() => _$ParkingSessionToJson(this);
}

@JsonSerializable()
class ParkingExtension {
  final String id;
  final Duration additionalTime;
  final double cost;
  final String currency;
  final DateTime timestamp;
  final String? paymentId;

  const ParkingExtension({
    required this.id,
    required this.additionalTime,
    required this.cost,
    required this.currency,
    required this.timestamp,
    this.paymentId,
  });

  factory ParkingExtension.fromJson(Map<String, dynamic> json) =>
      _$ParkingExtensionFromJson(json);

  Map<String, dynamic> toJson() => _$ParkingExtensionToJson(this);
}

@JsonSerializable()
class SharedVehicle {
  final String id;
  final SharedVehicleType type;
  final String brand;
  final String model;
  final double latitude;
  final double longitude;
  final int batteryLevel;
  final SharedVehicleStatus status;
  final double pricePerMinute;
  final double unlockFee;
  final String currency;
  final String? operatorName;
  final String? operatorLogo;
  final List<String> features;
  final DateTime lastUpdated;

  const SharedVehicle({
    required this.id,
    required this.type,
    required this.brand,
    required this.model,
    required this.latitude,
    required this.longitude,
    required this.batteryLevel,
    required this.status,
    required this.pricePerMinute,
    required this.unlockFee,
    required this.currency,
    this.operatorName,
    this.operatorLogo,
    this.features = const [],
    required this.lastUpdated,
  });

  factory SharedVehicle.fromJson(Map<String, dynamic> json) =>
      _$SharedVehicleFromJson(json);

  Map<String, dynamic> toJson() => _$SharedVehicleToJson(this);

  // Additional getters
  bool get isAvailable => status == SharedVehicleStatus.available;

  String get batteryStatusText {
    if (batteryLevel >= 80) return 'Vysoká';
    if (batteryLevel >= 50) return 'Střední';
    if (batteryLevel >= 20) return 'Nízká';
    return 'Kritická';
  }
}

@JsonSerializable()
class SharedVehicleRental {
  final String id;
  final String vehicleId;
  final String? userId;
  final DateTime startTime;
  final DateTime? endTime;
  final double startLatitude;
  final double startLongitude;
  final double? endLatitude;
  final double? endLongitude;
  final double? distance;
  final double? totalCost;
  final String? currency;
  final SharedVehicleRentalStatus status;
  final String? paymentId;

  const SharedVehicleRental({
    required this.id,
    required this.vehicleId,
    this.userId,
    required this.startTime,
    this.endTime,
    required this.startLatitude,
    required this.startLongitude,
    this.endLatitude,
    this.endLongitude,
    this.distance,
    this.totalCost,
    this.currency,
    required this.status,
    this.paymentId,
  });

  factory SharedVehicleRental.fromJson(Map<String, dynamic> json) =>
      _$SharedVehicleRentalFromJson(json);

  Map<String, dynamic> toJson() => _$SharedVehicleRentalToJson(this);

  // Additional getters
  Duration get duration {
    if (endTime != null) {
      return endTime!.difference(startTime);
    }
    return DateTime.now().difference(startTime);
  }
}

@JsonSerializable()
class ParkingPrediction {
  final String spotId;
  final DateTime targetTime;
  final double availabilityProbability;
  final int predictedAvailableSpaces;
  final double confidenceLevel;
  final List<PredictionFactor> factors;
  final DateTime generatedAt;

  const ParkingPrediction({
    required this.spotId,
    required this.targetTime,
    required this.availabilityProbability,
    required this.predictedAvailableSpaces,
    required this.confidenceLevel,
    required this.factors,
    required this.generatedAt,
  });

  factory ParkingPrediction.fromJson(Map<String, dynamic> json) =>
      _$ParkingPredictionFromJson(json);

  Map<String, dynamic> toJson() => _$ParkingPredictionToJson(this);
}

@JsonSerializable()
class PredictionFactor {
  final String name;
  final double impact;
  final String description;

  const PredictionFactor({
    required this.name,
    required this.impact,
    required this.description,
  });

  factory PredictionFactor.fromJson(Map<String, dynamic> json) =>
      _$PredictionFactorFromJson(json);

  Map<String, dynamic> toJson() => _$PredictionFactorToJson(this);
}

@JsonSerializable()
class SmartReservation {
  final String id;
  final String spotId;
  final String userId;
  final DateTime reservationTime;
  final DateTime arrivalTime;
  final Duration plannedDuration;
  final double totalCost;
  final String currency;
  final double walkingDistance;
  final Duration walkingTime;
  final SmartReservationStatus status;
  final List<String> optimizationReasons;
  final double savingsAmount;
  final DateTime expiresAt;

  const SmartReservation({
    required this.id,
    required this.spotId,
    required this.userId,
    required this.reservationTime,
    required this.arrivalTime,
    required this.plannedDuration,
    required this.totalCost,
    required this.currency,
    required this.walkingDistance,
    required this.walkingTime,
    required this.status,
    required this.optimizationReasons,
    required this.savingsAmount,
    required this.expiresAt,
  });

  factory SmartReservation.fromJson(Map<String, dynamic> json) =>
      _$SmartReservationFromJson(json);

  Map<String, dynamic> toJson() => _$SmartReservationToJson(this);
}

@JsonSerializable()
class DynamicPricing {
  final String spotId;
  final DateTime timeSlot;
  final double basePrice;
  final double currentPrice;
  final String currency;
  final double demandMultiplier;
  final List<PricingFactor> factors;
  final DateTime validUntil;

  const DynamicPricing({
    required this.spotId,
    required this.timeSlot,
    required this.basePrice,
    required this.currentPrice,
    required this.currency,
    required this.demandMultiplier,
    required this.factors,
    required this.validUntil,
  });

  factory DynamicPricing.fromJson(Map<String, dynamic> json) =>
      _$DynamicPricingFromJson(json);

  Map<String, dynamic> toJson() => _$DynamicPricingToJson(this);
}

@JsonSerializable()
class PricingFactor {
  final String name;
  final double multiplier;
  final String description;

  const PricingFactor({
    required this.name,
    required this.multiplier,
    required this.description,
  });

  factory PricingFactor.fromJson(Map<String, dynamic> json) =>
      _$PricingFactorFromJson(json);

  Map<String, dynamic> toJson() => _$PricingFactorToJson(this);
}
