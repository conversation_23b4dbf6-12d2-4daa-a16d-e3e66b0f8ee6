import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../models/offline_data.dart';
import 'offline_storage_service.dart';
import 'offline_ai_service.dart';
import 'map_service.dart';
import 'ticket_service.dart';

/// Hlavní služba pro správu offline režimu
class OfflineManagerService extends ChangeNotifier {
  static final OfflineManagerService _instance =
      OfflineManagerService._internal();
  factory OfflineManagerService() => _instance;
  OfflineManagerService._internal();

  final OfflineStorageService _storageService = OfflineStorageService();
  final OfflineAIService _aiService = OfflineAIService();
  final MapService _mapService = MapService();
  final TicketService _ticketService = TicketService();
  final Connectivity _connectivity = Connectivity();

  bool _isInitialized = false;
  bool _isOnline = true;
  bool _isDownloading = false;
  double _downloadProgress = 0.0;
  String _downloadStatus = '';
  List<OfflineDataPackage> _packages = [];
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  // Gettery
  bool get isInitialized => _isInitialized;
  bool get isOnline => _isOnline;
  bool get isDownloading => _isDownloading;
  double get downloadProgress => _downloadProgress;
  String get downloadStatus => _downloadStatus;
  List<OfflineDataPackage> get packages => _packages;

  /// Inicializuje offline manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _storageService.initialize();
      await _aiService.initialize();
      await _checkConnectivity();
      await _loadPackages();
      _setupConnectivityListener();

      _isInitialized = true;
      debugPrint('Offline manager inicializován');
    } catch (e) {
      debugPrint('Chyba při inicializaci offline manageru: $e');
      throw Exception('Nepodařilo se inicializovat offline manager');
    }
  }

  /// Zkontroluje připojení k internetu
  Future<void> _checkConnectivity() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      _isOnline = !connectivityResults.contains(ConnectivityResult.none);
      debugPrint('Stav připojení: ${_isOnline ? "online" : "offline"}');
    } catch (e) {
      debugPrint('Chyba při kontrole připojení: $e');
      _isOnline = false;
    }
  }

  /// Nastaví listener pro změny připojení
  void _setupConnectivityListener() {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen((
      List<ConnectivityResult> results,
    ) {
      final wasOnline = _isOnline;
      _isOnline = !results.contains(ConnectivityResult.none);

      if (wasOnline != _isOnline) {
        debugPrint('Změna připojení: ${_isOnline ? "online" : "offline"}');
        notifyListeners();

        if (_isOnline) {
          _syncWhenOnline();
        }
      }
    });
  }

  /// Načte dostupné balíčky
  Future<void> _loadPackages() async {
    try {
      _packages = await _storageService.getOfflinePackages();

      // Pokud nejsou žádné balíčky, vytvoří výchozí
      if (_packages.isEmpty) {
        _packages = _createDefaultPackages();
        for (final package in _packages) {
          await _storageService.saveOfflinePackage(package);
        }
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Chyba při načítání balíčků: $e');
    }
  }

  /// Vytvoří výchozí balíčky dat
  List<OfflineDataPackage> _createDefaultPackages() {
    return [
      OfflineDataPackage(
        id: 'croatia_places',
        name: 'Místa v Chorvatsku',
        description: 'Restaurace, ubytování, památky a pláže',
        type: OfflineDataType.places,
        status: OfflineDataStatus.notDownloaded,
        sizeBytes: 5 * 1024 * 1024, // 5 MB
        version: 1,
        region: 'croatia',
        metadata: {
          'places_count': 100,
          'last_update': DateTime.now().toIso8601String(),
        },
      ),

      OfflineDataPackage(
        id: 'croatia_tickets',
        name: 'Vstupenky',
        description: 'Vstupenky na památky a muzea',
        type: OfflineDataType.tickets,
        status: OfflineDataStatus.notDownloaded,
        sizeBytes: 2 * 1024 * 1024, // 2 MB
        version: 1,
        region: 'croatia',
        metadata: {
          'tickets_count': 50,
          'last_update': DateTime.now().toIso8601String(),
        },
      ),

      OfflineDataPackage(
        id: 'croatia_ai_responses',
        name: 'AI Odpovědi',
        description: 'Offline odpovědi AI asistenta',
        type: OfflineDataType.aiResponses,
        status: OfflineDataStatus.downloaded, // AI odpovědi jsou už načtené
        sizeBytes: 1 * 1024 * 1024, // 1 MB
        version: 1,
        region: 'croatia',
        downloadedAt: DateTime.now(),
        lastUpdated: DateTime.now(),
        metadata: {
          'responses_count': 20,
          'categories': ['obecné', 'doprava', 'ubytování', 'gastronomie'],
        },
      ),

      OfflineDataPackage(
        id: 'croatia_emergency',
        name: 'Nouzové služby',
        description: 'Kontakty na nouzové služby',
        type: OfflineDataType.emergency,
        status: OfflineDataStatus.notDownloaded,
        sizeBytes: 512 * 1024, // 512 KB
        version: 1,
        region: 'croatia',
        metadata: {
          'services_count': 20,
          'regions': ['zagreb', 'split', 'dubrovnik', 'rijeka'],
        },
      ),
    ];
  }

  /// Stáhne konkrétní balíček
  Future<void> downloadPackage(String packageId) async {
    final packageIndex = _packages.indexWhere((p) => p.id == packageId);
    if (packageIndex == -1) return;

    if (!_isOnline) {
      throw Exception('Pro stahování je potřeba připojení k internetu');
    }

    try {
      _isDownloading = true;
      _downloadProgress = 0.0;
      _downloadStatus = 'Připravuje se stahování...';
      notifyListeners();

      // Aktualizuje stav balíčku
      _packages[packageIndex] = _packages[packageIndex].copyWith(
        status: OfflineDataStatus.downloading,
      );
      await _storageService.saveOfflinePackage(_packages[packageIndex]);
      notifyListeners();

      // Stáhne data podle typu
      switch (_packages[packageIndex].type) {
        case OfflineDataType.places:
          await _downloadPlaces();
          break;
        case OfflineDataType.tickets:
          await _downloadTickets();
          break;
        case OfflineDataType.emergency:
          await _downloadEmergencyServices();
          break;
        default:
          break;
      }

      // Označí jako stažené
      _packages[packageIndex] = _packages[packageIndex].copyWith(
        status: OfflineDataStatus.downloaded,
        downloadedAt: DateTime.now(),
        lastUpdated: DateTime.now(),
      );
      await _storageService.saveOfflinePackage(_packages[packageIndex]);

      _downloadStatus = 'Stahování dokončeno';
      _downloadProgress = 1.0;
    } catch (e) {
      debugPrint('Chyba při stahování balíčku $packageId: $e');

      // Označí jako chybu
      _packages[packageIndex] = _packages[packageIndex].copyWith(
        status: OfflineDataStatus.error,
      );
      await _storageService.saveOfflinePackage(_packages[packageIndex]);

      _downloadStatus = 'Chyba při stahování';
      rethrow;
    } finally {
      _isDownloading = false;
      notifyListeners();
    }
  }

  /// Stáhne mapová místa
  Future<void> _downloadPlaces() async {
    _downloadStatus = 'Stahování míst...';
    _downloadProgress = 0.2;
    notifyListeners();

    // Načte místa z map service
    await _mapService.initialize();
    final places = _mapService.allPlaces;

    _downloadProgress = 0.6;
    notifyListeners();

    // Uloží offline
    await _storageService.savePlacesOffline(places);

    _downloadProgress = 0.8;
    notifyListeners();
  }

  /// Stáhne vstupenky
  Future<void> _downloadTickets() async {
    _downloadStatus = 'Stahování vstupenek...';
    _downloadProgress = 0.2;
    notifyListeners();

    // Načte vstupenky z ticket service
    await _ticketService.initialize();
    final tickets = _ticketService.tickets;

    _downloadProgress = 0.6;
    notifyListeners();

    // Uloží offline
    await _storageService.saveTicketsOffline(tickets);

    _downloadProgress = 0.8;
    notifyListeners();
  }

  /// Stáhne nouzové služby
  Future<void> _downloadEmergencyServices() async {
    _downloadStatus = 'Stahování nouzových služeb...';
    _downloadProgress = 0.5;
    notifyListeners();

    // Zde by se stáhly aktuální kontakty na nouzové služby
    // Pro demo použijeme statická data
    await Future.delayed(const Duration(seconds: 1));

    _downloadProgress = 0.8;
    notifyListeners();
  }

  /// Stáhne všechny dostupné balíčky
  Future<void> downloadAllPackages() async {
    final notDownloaded = _packages
        .where((p) => p.status == OfflineDataStatus.notDownloaded)
        .toList();

    for (int i = 0; i < notDownloaded.length; i++) {
      try {
        await downloadPackage(notDownloaded[i].id);

        // Krátká pauza mezi stahováním
        if (i < notDownloaded.length - 1) {
          await Future.delayed(const Duration(milliseconds: 500));
        }
      } catch (e) {
        debugPrint('Chyba při stahování balíčku ${notDownloaded[i].id}: $e');
        // Pokračuje s dalším balíčkem
      }
    }
  }

  /// Synchronizuje data při připojení
  Future<void> _syncWhenOnline() async {
    if (!_isOnline) return;

    try {
      debugPrint('Synchronizace offline dat...');

      // Zkontroluje aktualizace balíčků
      await _checkForUpdates();

      debugPrint('Synchronizace dokončena');
    } catch (e) {
      debugPrint('Chyba při synchronizaci: $e');
    }
  }

  /// Zkontroluje aktualizace balíčků
  Future<void> _checkForUpdates() async {
    for (final package in _packages) {
      if (package.status == OfflineDataStatus.downloaded &&
          !package.isUpToDate) {
        // Označí jako zastaralé
        final updatedPackage = package.copyWith(
          status: OfflineDataStatus.outdated,
        );

        final index = _packages.indexWhere((p) => p.id == package.id);
        if (index != -1) {
          _packages[index] = updatedPackage;
          await _storageService.saveOfflinePackage(updatedPackage);
        }
      }
    }

    notifyListeners();
  }

  /// Vymaže konkrétní balíček
  Future<void> deletePackage(String packageId) async {
    final packageIndex = _packages.indexWhere((p) => p.id == packageId);
    if (packageIndex == -1) return;

    try {
      // Vymaže data podle typu
      await _storageService.clearOfflineDataType(_packages[packageIndex].type);

      // Aktualizuje stav
      _packages[packageIndex] = _packages[packageIndex].copyWith(
        status: OfflineDataStatus.notDownloaded,
        downloadedAt: null,
      );
      await _storageService.saveOfflinePackage(_packages[packageIndex]);

      notifyListeners();
    } catch (e) {
      debugPrint('Chyba při mazání balíčku $packageId: $e');
      rethrow;
    }
  }

  /// Vymaže všechna offline data
  Future<void> clearAllOfflineData() async {
    try {
      await _storageService.clearAllOfflineData();

      // Resetuje stav všech balíčků
      for (int i = 0; i < _packages.length; i++) {
        _packages[i] = _packages[i].copyWith(
          status: OfflineDataStatus.notDownloaded,
          downloadedAt: null,
        );
        await _storageService.saveOfflinePackage(_packages[i]);
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Chyba při mazání všech offline dat: $e');
      rethrow;
    }
  }

  /// Získá statistiky offline dat
  Future<OfflineStats> getOfflineStats() async {
    final totalPackages = _packages.length;
    final downloadedPackages = _packages
        .where((p) => p.status == OfflineDataStatus.downloaded)
        .length;

    final totalSize = _packages.fold<int>(0, (sum, p) => sum + p.sizeBytes);
    final downloadedSize = _packages
        .where((p) => p.status == OfflineDataStatus.downloaded)
        .fold<int>(0, (sum, p) => sum + p.sizeBytes);

    final packagesByType = <OfflineDataType, int>{};
    for (final package in _packages) {
      packagesByType[package.type] = (packagesByType[package.type] ?? 0) + 1;
    }

    return OfflineStats(
      totalPackages: totalPackages,
      downloadedPackages: downloadedPackages,
      totalSizeBytes: totalSize,
      downloadedSizeBytes: downloadedSize,
      lastSyncAt: DateTime.now(),
      syncCount: 1,
      packagesByType: packagesByType,
    );
  }

  /// Zkontroluje, zda jsou data dostupná offline
  Future<bool> isDataAvailableOffline(OfflineDataType type) async {
    return _packages.any(
      (package) => package.type == type && package.isAvailable,
    );
  }

  /// Získá velikost offline dat na disku
  Future<int> getOfflineDataSize() async {
    return await _storageService.getOfflineDataSize();
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }
}
