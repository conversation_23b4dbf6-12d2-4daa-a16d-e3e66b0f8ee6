import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/beach.dart';
import '../services/beach_discovery_service.dart';

class BeachDiscoveryScreen extends StatefulWidget {
  const BeachDiscoveryScreen({super.key});

  @override
  State<BeachDiscoveryScreen> createState() => _BeachDiscoveryScreenState();
}

class _BeachDiscoveryScreenState extends State<BeachDiscoveryScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Beach Discovery stav
  String _selectedRegion = 'all';
  BeachType? _selectedBeachType;
  String _selectedAccessType = 'all';
  String _searchQuery = '';
  bool _isLoading = false;

  // Data
  List<Beach> _allBeaches = [];
  List<Beach> _filteredBeaches = [];
  List<WaterSport> _allWaterSports = [];

  // Statistiky
  int _totalBeaches = 0;
  int _topRated = 0;
  int _familyFriendly = 0;
  int _waterSports = 0;

  final BeachDiscoveryService _beachService = BeachDiscoveryService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _loadBeachData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadBeachData() async {
    setState(() => _isLoading = true);

    try {
      _allBeaches = await _beachService.getAllBeaches();
      _allWaterSports = await _beachService.getAllWaterSports();
      _applyFilters();
      _updateStatistics();
    } catch (e) {
      debugPrint('Chyba při načítání pláží: $e');
    }

    setState(() => _isLoading = false);
  }

  void _applyFilters() {
    _filteredBeaches = _allBeaches.where((beach) {
      // Region filter
      if (_selectedRegion != 'all' && beach.region != _selectedRegion) {
        return false;
      }

      // Beach type filter
      if (_selectedBeachType != null && beach.beachType != _selectedBeachType) {
        return false;
      }

      // Access type filter
      if (_selectedAccessType != 'all' &&
          beach.accessType != _selectedAccessType) {
        return false;
      }

      // Search query
      if (_searchQuery.isNotEmpty) {
        return beach.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            beach.description.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            beach.location.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            beach.activities.any(
              (a) => a.toLowerCase().contains(_searchQuery.toLowerCase()),
            );
      }

      return true;
    }).toList();
  }

  void _updateStatistics() {
    _totalBeaches = _allBeaches.length;
    _topRated = _allBeaches.where((b) => b.rating >= 4.5).length;
    _familyFriendly = _allBeaches.where((b) => b.isFamilyFriendly).length;
    _waterSports = _allBeaches.where((b) => b.activities.isNotEmpty).length;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Chorvatské pláže',
          style: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF006994).withValues(alpha: 0.9),
                const Color(0xFF2E8B8B).withValues(alpha: 0.8),
                const Color(0xFF4FC3F7).withValues(alpha: 0.7),
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: CustomPaint(
            painter: WatercolorBeachHeaderPainter(),
            size: Size.infinite,
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: _loadBeachData,
              icon: const Icon(Icons.refresh),
              tooltip: 'Aktualizovat pláže',
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          indicator: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Colors.white.withValues(alpha: 0.3),
          ),
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Přehled'),
            Tab(icon: Icon(Icons.beach_access), text: 'Všechny'),
            Tab(icon: Icon(Icons.star), text: 'Top hodnocené'),
            Tab(icon: Icon(Icons.family_restroom), text: 'Rodinné'),
            Tab(icon: Icon(Icons.surfing), text: 'Vodní sporty'),
            Tab(icon: Icon(Icons.map), text: 'Mapa'),
          ],
        ),
      ),
      body: Container(
        child: CustomPaint(
          painter: WatercolorBeachBackgroundPainter(),
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(),
              _buildAllBeachesTab(),
              _buildTopRatedTab(),
              _buildFamilyFriendlyTab(),
              _buildWaterSportsTab(),
              _buildMapTab(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Hlavní karta
          Container(
            child: CustomPaint(
              painter: WatercolorBeachMainCardPainter(const Color(0xFF006994)),
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Column(
                  children: [
                    Text(
                      'Objevte chorvatské pláže',
                      style: GoogleFonts.playfairDisplay(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF006994),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Najděte nejkrásnější pláže a vodní aktivity na Jadranu',
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        color: const Color(0xFF666666),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Vyhledávání
          _buildSearchSection(),

          const SizedBox(height: 20),

          // Statistiky
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            children: [
              _buildStatCard(
                'Celkem',
                '$_totalBeaches',
                'pláží',
                Icons.beach_access,
                const Color(0xFF006994),
              ),
              _buildStatCard(
                'Top hodnocené',
                '$_topRated',
                '4.5+ hvězdiček',
                Icons.star,
                const Color(0xFF4FC3F7),
              ),
              _buildStatCard(
                'Rodinné',
                '$_familyFriendly',
                'pro rodiny',
                Icons.family_restroom,
                const Color(0xFF2E8B8B),
              ),
              _buildStatCard(
                'Vodní sporty',
                '$_waterSports',
                'aktivních pláží',
                Icons.surfing,
                const Color(0xFF00BCD4),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Doporučené pláže
          _buildRecommendedSection(),

          const SizedBox(height: 20),

          // Vodní sporty přehled
          _buildWaterSportsOverview(),
        ],
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      child: CustomPaint(
        painter: WatercolorBeachSearchPainter(const Color(0xFF2E8B8B)),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Vyhledávání pláží',
                style: GoogleFonts.playfairDisplay(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2E8B8B),
                ),
              ),
              const SizedBox(height: 16),

              // Search field
              TextField(
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                    _applyFilters();
                  });
                },
                decoration: InputDecoration(
                  hintText: 'Hledat pláže, aktivity, lokace...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                  fillColor: Colors.grey[50],
                ),
              ),

              const SizedBox(height: 16),

              // Filters
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildFilterChip(
                    'Region',
                    _selectedRegion,
                    ['all', 'dalmatia', 'istria', 'kvarner', 'dubrovnik'],
                    (value) => setState(() {
                      _selectedRegion = value;
                      _applyFilters();
                    }),
                  ),
                  _buildFilterChip(
                    'Typ',
                    _selectedBeachType?.toString().split('.').last ?? 'all',
                    ['all', 'sandy', 'pebble', 'rocky', 'concrete'],
                    (value) => setState(() {
                      if (value == 'all') {
                        _selectedBeachType = null;
                      } else {
                        _selectedBeachType = BeachType.values.firstWhere(
                          (type) => type.toString().split('.').last == value,
                        );
                      }
                      _applyFilters();
                    }),
                  ),
                  _buildFilterChip(
                    'Přístup',
                    _selectedAccessType,
                    ['all', 'free', 'paid', 'hotel'],
                    (value) => setState(() {
                      _selectedAccessType = value;
                      _applyFilters();
                    }),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Container(
      child: CustomPaint(
        painter: WatercolorBeachStatCardPainter(color),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                child: CustomPaint(
                  painter: WatercolorBeachIconPainter(color),
                  child: Icon(icon, size: 32, color: color),
                ),
              ),
              const SizedBox(height: 12),
              Text(
                value,
                style: GoogleFonts.playfairDisplay(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: GoogleFonts.inter(
                  fontSize: 12,
                  color: const Color(0xFF666666),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF2C2C2C),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterChip(
    String label,
    String currentValue,
    List<String> options,
    Function(String) onChanged,
  ) {
    return Container(
      child: CustomPaint(
        painter: WatercolorBeachFilterChipPainter(const Color(0xFF006994)),
        child: PopupMenuButton<String>(
          onSelected: onChanged,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: const Color(0xFF006994).withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '$label: ${_getFilterLabel(currentValue)}',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF006994),
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  Icons.arrow_drop_down,
                  size: 16,
                  color: const Color(0xFF006994),
                ),
              ],
            ),
          ),
          itemBuilder: (context) => options
              .map(
                (option) => PopupMenuItem(
                  value: option,
                  child: Text(_getFilterLabel(option)),
                ),
              )
              .toList(),
        ),
      ),
    );
  }

  String _getFilterLabel(String value) {
    switch (value) {
      case 'all':
        return 'Vše';
      case 'dalmatia':
        return 'Dalmácie';
      case 'istria':
        return 'Istrie';
      case 'kvarner':
        return 'Kvarner';
      case 'dubrovnik':
        return 'Dubrovník';
      case 'sandy':
        return 'Písečné';
      case 'pebble':
        return 'Oblázková';
      case 'rocky':
        return 'Skalnaté';
      case 'concrete':
        return 'Betonové';
      case 'free':
        return 'Volný';
      case 'paid':
        return 'Placený';
      case 'hotel':
        return 'Hotelový';
      default:
        return value;
    }
  }

  Widget _buildRecommendedSection() {
    final recommended = _allBeaches
        .where((b) => b.rating >= 4.5)
        .take(3)
        .toList();

    return Container(
      child: CustomPaint(
        painter: WatercolorBeachRecommendedPainter(const Color(0xFF4FC3F7)),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Doporučené pláže',
                style: GoogleFonts.playfairDisplay(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF4FC3F7),
                ),
              ),
              const SizedBox(height: 16),
              ...recommended.map(
                (beach) => _buildBeachCard(beach, isCompact: true),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWaterSportsOverview() {
    return Container(
      child: CustomPaint(
        painter: WatercolorBeachWaterSportsPainter(const Color(0xFF00BCD4)),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    child: CustomPaint(
                      painter: WatercolorBeachIconPainter(
                        const Color(0xFF00BCD4),
                      ),
                      child: Icon(
                        Icons.surfing,
                        size: 32,
                        color: const Color(0xFF00BCD4),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Vodní sporty',
                          style: GoogleFonts.playfairDisplay(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF00BCD4),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${_allWaterSports.length} dostupných aktivit',
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: const Color(0xFF666666),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: const Color(0xFF00BCD4),
                    size: 16,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _allWaterSports
                    .take(4)
                    .map(
                      (sport) => Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF00BCD4).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          sport.name,
                          style: GoogleFonts.inter(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: const Color(0xFF00BCD4),
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAllBeachesTab() {
    return Column(
      children: [
        // Search bar
        Container(
          padding: const EdgeInsets.all(16),
          child: TextField(
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
                _applyFilters();
              });
            },
            decoration: InputDecoration(
              hintText: 'Hledat pláže...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
          ),
        ),

        // Results
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _filteredBeaches.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.beach_access,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Žádné pláže nenalezeny',
                        style: GoogleFonts.inter(
                          fontSize: 18,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _filteredBeaches.length,
                  itemBuilder: (context, index) {
                    return _buildBeachCard(_filteredBeaches[index]);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildTopRatedTab() {
    final topRated = _allBeaches.where((b) => b.rating >= 4.5).toList();
    topRated.sort((a, b) => b.rating.compareTo(a.rating));

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: topRated.length,
      itemBuilder: (context, index) {
        return _buildBeachCard(topRated[index], showRank: index + 1);
      },
    );
  }

  Widget _buildFamilyFriendlyTab() {
    final familyFriendly = _allBeaches
        .where((b) => b.isFamilyFriendly)
        .toList();
    familyFriendly.sort((a, b) => b.rating.compareTo(a.rating));

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: familyFriendly.length,
      itemBuilder: (context, index) {
        return _buildBeachCard(familyFriendly[index], showFamilyBadge: true);
      },
    );
  }

  Widget _buildWaterSportsTab() {
    final waterSportsBeaches = _allBeaches
        .where((b) => b.activities.isNotEmpty)
        .toList();
    waterSportsBeaches.sort(
      (a, b) => b.activities.length.compareTo(a.activities.length),
    );

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: waterSportsBeaches.length,
      itemBuilder: (context, index) {
        return _buildBeachCard(waterSportsBeaches[index], showActivities: true);
      },
    );
  }

  Widget _buildMapTab() {
    return Container(
      child: CustomPaint(
        painter: WatercolorBeachMapPainter(),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.map, size: 64, color: const Color(0xFF006994)),
              const SizedBox(height: 16),
              Text(
                'Mapa pláží',
                style: GoogleFonts.playfairDisplay(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF006994),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Připravujeme interaktivní mapu\ns watercolor designem',
                style: GoogleFonts.inter(
                  fontSize: 16,
                  color: const Color(0xFF666666),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBeachCard(
    Beach beach, {
    bool isCompact = false,
    int? showRank,
    bool showFamilyBadge = false,
    bool showActivities = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: CustomPaint(
        painter: WatercolorBeachCardPainter(
          _getBeachTypeColor(beach.beachType),
        ),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  if (showRank != null) ...[
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: const Color(0xFF006994),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          '$showRank',
                          style: GoogleFonts.inter(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                  ],

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                beach.name,
                                style: GoogleFonts.playfairDisplay(
                                  fontSize: isCompact ? 16 : 18,
                                  fontWeight: FontWeight.bold,
                                  color: const Color(0xFF2C2C2C),
                                ),
                              ),
                            ),
                            if (showFamilyBadge)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(
                                    0xFF4CAF50,
                                  ).withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'Rodinná',
                                  style: GoogleFonts.inter(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFF4CAF50),
                                  ),
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(Icons.star, size: 16, color: Colors.amber),
                            const SizedBox(width: 4),
                            Text(
                              beach.rating.toStringAsFixed(1),
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF2C2C2C),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '(${beach.reviewCount} recenzí)',
                              style: GoogleFonts.inter(
                                fontSize: 12,
                                color: const Color(0xFF666666),
                              ),
                            ),
                            const Spacer(),
                            Text(
                              _getBeachTypeLabel(beach.beachType),
                              style: GoogleFonts.inter(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: _getBeachTypeColor(beach.beachType),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  Icon(
                    _getBeachTypeIcon(beach.beachType),
                    color: _getBeachTypeColor(beach.beachType),
                    size: 24,
                  ),
                ],
              ),

              if (!isCompact) ...[
                const SizedBox(height: 12),

                // Description
                Text(
                  beach.description,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    color: const Color(0xFF666666),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 12),

                // Activities or Features
                if (showActivities && beach.activities.isNotEmpty) ...[
                  Wrap(
                    spacing: 6,
                    runSpacing: 6,
                    children: beach.activities
                        .take(4)
                        .map(
                          (activity) => Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(
                                0xFF00BCD4,
                              ).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              _getActivityLabel(activity),
                              style: GoogleFonts.inter(
                                fontSize: 12,
                                color: const Color(0xFF00BCD4),
                              ),
                            ),
                          ),
                        )
                        .toList(),
                  ),
                  const SizedBox(height: 12),
                ],

                // Features
                Row(
                  children: [
                    if (beach.hasLifeguard)
                      _buildFeatureIcon(Icons.security, 'Plavčík'),
                    if (beach.hasParking)
                      _buildFeatureIcon(Icons.local_parking, 'Parkování'),
                    if (beach.hasRestaurant)
                      _buildFeatureIcon(Icons.restaurant, 'Restaurace'),
                    if (beach.hasShower)
                      _buildFeatureIcon(Icons.shower, 'Sprcha'),
                  ],
                ),

                const SizedBox(height: 8),

                // Location
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 16,
                      color: const Color(0xFF666666),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        beach.location,
                        style: GoogleFonts.inter(
                          fontSize: 12,
                          color: const Color(0xFF666666),
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => _openBeachLocation(beach),
                      icon: const Icon(Icons.directions, size: 20),
                      color: const Color(0xFF006994),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureIcon(IconData icon, String tooltip) {
    return Padding(
      padding: const EdgeInsets.only(right: 12),
      child: Tooltip(
        message: tooltip,
        child: Icon(icon, size: 16, color: const Color(0xFF4CAF50)),
      ),
    );
  }

  Color _getBeachTypeColor(BeachType beachType) {
    switch (beachType) {
      case BeachType.sandy:
        return const Color(0xFFFFC107);
      case BeachType.pebble:
        return const Color(0xFF607D8B);
      case BeachType.rocky:
        return const Color(0xFF795548);
      case BeachType.concrete:
        return const Color(0xFF9E9E9E);
      case BeachType.mixed:
        return const Color(0xFF00BCD4);
    }
  }

  IconData _getBeachTypeIcon(BeachType beachType) {
    switch (beachType) {
      case BeachType.sandy:
        return Icons.grain;
      case BeachType.pebble:
        return Icons.circle;
      case BeachType.rocky:
        return Icons.terrain;
      case BeachType.concrete:
        return Icons.foundation;
      case BeachType.mixed:
        return Icons.waves;
    }
  }

  String _getBeachTypeLabel(BeachType beachType) {
    switch (beachType) {
      case BeachType.sandy:
        return 'Písečná';
      case BeachType.pebble:
        return 'Oblázková';
      case BeachType.rocky:
        return 'Skalnatá';
      case BeachType.concrete:
        return 'Betonová';
      case BeachType.mixed:
        return 'Smíšená';
    }
  }

  String _getActivityLabel(String activity) {
    switch (activity) {
      case 'swimming':
        return 'Plavání';
      case 'snorkeling':
        return 'Šnorchlování';
      case 'diving':
        return 'Potápění';
      case 'windsurfing':
        return 'Windsurfing';
      case 'kitesurfing':
        return 'Kitesurfing';
      case 'sailing':
        return 'Plachtění';
      case 'kayaking':
        return 'Kajak';
      case 'volleyball':
        return 'Volejbal';
      case 'fishing':
        return 'Rybaření';
      case 'picigin':
        return 'Picigin';
      default:
        return activity;
    }
  }

  void _openBeachLocation(Beach beach) {
    // Implementace otevření GPS navigace
    debugPrint('Navigace k pláži: ${beach.name}');
  }
}

// Watercolor painters pro Beach Discovery Screen
class WatercolorBeachHeaderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor vlny pro Beach header - evokuje moře
    final path1 = Path();
    path1.moveTo(0, size.height * 0.4);
    path1.quadraticBezierTo(
      size.width * 0.2,
      size.height * 0.2,
      size.width * 0.4,
      size.height * 0.5,
    );
    path1.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.8,
      size.width * 0.8,
      size.height * 0.3,
    );
    path1.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.1,
      size.width,
      size.height * 0.4,
    );
    path1.lineTo(size.width, size.height);
    path1.lineTo(0, size.height);
    path1.close();

    paint.color = const Color(0xFF006994).withValues(alpha: 0.2);
    canvas.drawPath(path1, paint);

    // Druhá vrstva - vlny
    final path2 = Path();
    path2.moveTo(0, size.height * 0.6);
    path2.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.3,
      size.width * 0.5,
      size.height * 0.7,
    );
    path2.quadraticBezierTo(
      size.width * 0.75,
      size.height * 0.9,
      size.width,
      size.height * 0.5,
    );
    path2.lineTo(size.width, size.height);
    path2.lineTo(0, size.height);
    path2.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.15);
    canvas.drawPath(path2, paint);

    // Třetí vrstva - jemné vlnky
    final path3 = Path();
    path3.moveTo(0, size.height * 0.8);
    path3.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.6,
      size.width * 0.7,
      size.height * 0.85,
    );
    path3.quadraticBezierTo(
      size.width * 0.85,
      size.height * 0.95,
      size.width,
      size.height * 0.7,
    );
    path3.lineTo(size.width, size.height);
    path3.lineTo(0, size.height);
    path3.close();

    paint.color = const Color(0xFF4FC3F7).withValues(alpha: 0.1);
    canvas.drawPath(path3, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorBeachBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Jemný watercolor efekt pro pozadí beach discovery
    final path = Path();
    path.moveTo(size.width * 0.05, size.height * 0.03);
    path.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.01,
      size.width * 0.8,
      size.height * 0.06,
    );
    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.1,
      size.width * 0.97,
      size.height * 0.97,
    );
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.99,
      size.width * 0.2,
      size.height * 0.94,
    );
    path.quadraticBezierTo(
      size.width * 0.03,
      size.height * 0.9,
      size.width * 0.05,
      size.height * 0.03,
    );
    path.close();

    paint.color = const Color(0xFF006994).withValues(alpha: 0.02);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorBeachMainCardPainter extends CustomPainter {
  final Color color;

  WatercolorBeachMainCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro hlavní beach kartu - evokuje pobřeží
    final path = Path();
    path.moveTo(size.width * 0.03, size.height * 0.08);
    path.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.01,
      size.width * 0.6,
      size.height * 0.05,
    );
    path.quadraticBezierTo(
      size.width * 0.85,
      size.height * 0.09,
      size.width * 0.97,
      size.height * 0.2,
    );
    path.quadraticBezierTo(
      size.width * 0.99,
      size.height * 0.7,
      size.width * 0.92,
      size.height * 0.92,
    );
    path.quadraticBezierTo(
      size.width * 0.75,
      size.height * 0.99,
      size.width * 0.4,
      size.height * 0.95,
    );
    path.quadraticBezierTo(
      size.width * 0.15,
      size.height * 0.91,
      size.width * 0.01,
      size.height * 0.8,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.4,
      size.width * 0.03,
      size.height * 0.08,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);

    // Druhá vrstva pro hloubku
    final path2 = Path();
    path2.moveTo(size.width * 0.1, size.height * 0.15);
    path2.quadraticBezierTo(
      size.width * 0.45,
      size.height * 0.03,
      size.width * 0.8,
      size.height * 0.12,
    );
    path2.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.25,
      size.width * 0.88,
      size.height * 0.85,
    );
    path2.quadraticBezierTo(
      size.width * 0.55,
      size.height * 0.97,
      size.width * 0.2,
      size.height * 0.88,
    );
    path2.quadraticBezierTo(
      size.width * 0.05,
      size.height * 0.75,
      size.width * 0.1,
      size.height * 0.15,
    );
    path2.close();

    paint.color = color.withValues(alpha: 0.04);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorBeachSearchPainter extends CustomPainter {
  final Color color;

  WatercolorBeachSearchPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro search sekci - evokuje vlny
    final path = Path();
    path.moveTo(size.width * 0.02, size.height * 0.12);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.02,
      size.width * 0.7,
      size.height * 0.08,
    );
    path.quadraticBezierTo(
      size.width * 0.98,
      size.height * 0.15,
      size.width * 0.96,
      size.height * 0.88,
    );
    path.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.98,
      size.width * 0.3,
      size.height * 0.92,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.85,
      size.width * 0.02,
      size.height * 0.12,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.06);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorBeachStatCardPainter extends CustomPainter {
  final Color color;

  WatercolorBeachStatCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro stat karty - evokuje kapky vody
    final path = Path();
    path.moveTo(size.width * 0.06, size.height * 0.12);
    path.quadraticBezierTo(
      size.width * 0.35,
      size.height * 0.03,
      size.width * 0.75,
      size.height * 0.09,
    );
    path.quadraticBezierTo(
      size.width * 0.94,
      size.height * 0.18,
      size.width * 0.91,
      size.height * 0.88,
    );
    path.quadraticBezierTo(
      size.width * 0.65,
      size.height * 0.97,
      size.width * 0.25,
      size.height * 0.91,
    );
    path.quadraticBezierTo(
      size.width * 0.04,
      size.height * 0.82,
      size.width * 0.06,
      size.height * 0.12,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.1);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorBeachIconPainter extends CustomPainter {
  final Color color;

  WatercolorBeachIconPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor kruh kolem beach ikony - evokuje bubliny
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2.2;

    final path = Path();
    for (int i = 0; i < 360; i += 15) {
      final angle = i * pi / 180;
      final variation = 0.7 + (sin(i * pi / 45) * 0.3);
      final x = center.dx + (radius * variation) * cos(angle);
      final y = center.dy + (radius * variation) * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    paint.color = color.withValues(alpha: 0.15);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorBeachFilterChipPainter extends CustomPainter {
  final Color color;

  WatercolorBeachFilterChipPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro filter chipy - evokuje mořské pěny
    final path = Path();
    path.moveTo(size.width * 0.08, size.height * 0.18);
    path.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.04,
      size.width * 0.8,
      size.height * 0.12,
    );
    path.quadraticBezierTo(
      size.width * 0.96,
      size.height * 0.22,
      size.width * 0.92,
      size.height * 0.82,
    );
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.96,
      size.width * 0.2,
      size.height * 0.88,
    );
    path.quadraticBezierTo(
      size.width * 0.04,
      size.height * 0.78,
      size.width * 0.08,
      size.height * 0.18,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorBeachRecommendedPainter extends CustomPainter {
  final Color color;

  WatercolorBeachRecommendedPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro recommended sekci - evokuje lagunu
    final path = Path();
    path.moveTo(size.width * 0.01, size.height * 0.1);
    path.quadraticBezierTo(
      size.width * 0.28,
      size.height * 0.02,
      size.width * 0.68,
      size.height * 0.07,
    );
    path.quadraticBezierTo(
      size.width * 0.99,
      size.height * 0.14,
      size.width * 0.97,
      size.height * 0.9,
    );
    path.quadraticBezierTo(
      size.width * 0.72,
      size.height * 0.98,
      size.width * 0.32,
      size.height * 0.93,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.86,
      size.width * 0.01,
      size.height * 0.1,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.06);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorBeachWaterSportsPainter extends CustomPainter {
  final Color color;

  WatercolorBeachWaterSportsPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro water sports sekci - evokuje vlny a spray
    final path = Path();
    path.moveTo(size.width * 0.03, size.height * 0.15);
    path.quadraticBezierTo(
      size.width * 0.35,
      size.height * 0.05,
      size.width * 0.8,
      size.height * 0.12,
    );
    path.quadraticBezierTo(
      size.width * 0.97,
      size.height * 0.2,
      size.width * 0.94,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.65,
      size.height * 0.95,
      size.width * 0.2,
      size.height * 0.88,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.8,
      size.width * 0.03,
      size.height * 0.15,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.07);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorBeachCardPainter extends CustomPainter {
  final Color color;

  WatercolorBeachCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro beach karty - evokuje pobřežní linie
    final path = Path();
    path.moveTo(size.width * 0.01, size.height * 0.16);
    path.quadraticBezierTo(
      size.width * 0.32,
      size.height * 0.04,
      size.width * 0.74,
      size.height * 0.11,
    );
    path.quadraticBezierTo(
      size.width * 0.99,
      size.height * 0.19,
      size.width * 0.97,
      size.height * 0.84,
    );
    path.quadraticBezierTo(
      size.width * 0.68,
      size.height * 0.96,
      size.width * 0.26,
      size.height * 0.89,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.81,
      size.width * 0.01,
      size.height * 0.16,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorBeachMapPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor efekt pro mapu pláží - evokuje ostrovy a pobřeží
    final path1 = Path();
    path1.moveTo(size.width * 0.08, size.height * 0.08);
    path1.quadraticBezierTo(
      size.width * 0.45,
      size.height * 0.03,
      size.width * 0.85,
      size.height * 0.12,
    );
    path1.quadraticBezierTo(
      size.width * 0.97,
      size.height * 0.25,
      size.width * 0.92,
      size.height * 0.92,
    );
    path1.quadraticBezierTo(
      size.width * 0.55,
      size.height * 0.97,
      size.width * 0.15,
      size.height * 0.88,
    );
    path1.quadraticBezierTo(
      size.width * 0.03,
      size.height * 0.75,
      size.width * 0.08,
      size.height * 0.08,
    );
    path1.close();

    paint.color = const Color(0xFF006994).withValues(alpha: 0.04);
    canvas.drawPath(path1, paint);

    // Druhá vrstva - ostrovy
    final path2 = Path();
    path2.moveTo(size.width * 0.2, size.height * 0.15);
    path2.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.1,
      size.width * 0.8,
      size.height * 0.2,
    );
    path2.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.35,
      size.width * 0.75,
      size.height * 0.85,
    );
    path2.quadraticBezierTo(
      size.width * 0.45,
      size.height * 0.9,
      size.width * 0.25,
      size.height * 0.8,
    );
    path2.quadraticBezierTo(
      size.width * 0.1,
      size.height * 0.65,
      size.width * 0.2,
      size.height * 0.15,
    );
    path2.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.03);
    canvas.drawPath(path2, paint);

    // Třetí vrstva - laguna
    final path3 = Path();
    path3.moveTo(size.width * 0.35, size.height * 0.3);
    path3.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.25,
      size.width * 0.7,
      size.height * 0.4,
    );
    path3.quadraticBezierTo(
      size.width * 0.75,
      size.height * 0.55,
      size.width * 0.6,
      size.height * 0.7,
    );
    path3.quadraticBezierTo(
      size.width * 0.45,
      size.height * 0.75,
      size.width * 0.3,
      size.height * 0.6,
    );
    path3.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.45,
      size.width * 0.35,
      size.height * 0.3,
    );
    path3.close();

    paint.color = const Color(0xFF4FC3F7).withValues(alpha: 0.02);
    canvas.drawPath(path3, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
