import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import '../models/audio_guide.dart';
import '../services/location_service.dart';

class AudioGuideService {
  static final AudioGuideService _instance = AudioGuideService._internal();
  factory AudioGuideService() => _instance;
  AudioGuideService._internal();

  final LocationService _locationService = LocationService();

  StreamController<AudioGuideEvent>? _eventController;
  StreamSubscription<Position>? _locationSubscription;

  bool _isPlaying = false;
  bool _autoPlayEnabled = true;
  double _playbackSpeed = 1.0;
  AudioGuideTrack? _currentTrack;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;

  final Map<String, AudioGuideTrack> _audioTracks = {};
  final List<AudioGuidePoint> _audioPoints = [];

  /// Stream událostí audio průvodce
  Stream<AudioGuideEvent> get eventStream {
    _eventController ??= StreamController<AudioGuideEvent>.broadcast();
    return _eventController!.stream;
  }

  /// Inicializace audio průvodce
  Future<void> initialize() async {
    await _loadAudioTracks();
    await _loadAudioPoints();

    if (_autoPlayEnabled) {
      _startLocationTracking();
    }
  }

  /// Načtení audio stop
  Future<void> _loadAudioTracks() async {
    // Simulace načtení audio stop z databáze
    _audioTracks.addAll({
      'dubrovnik_walls_intro': AudioGuideTrack(
        id: 'dubrovnik_walls_intro',
        title: 'Úvod do hradeb Dubrovníku',
        description: 'Základní informace o historii a významu hradeb',
        audioUrl: 'assets/audio/dubrovnik_walls_intro.mp3',
        duration: const Duration(minutes: 3, seconds: 45),
        language: 'cs',
        narrator: 'Petr Novák',
        placeId: 'dubrovnik_walls',
        category: AudioCategory.introduction,
        transcript: '''
Vítejte u hradeb Dubrovníku, jednoho z nejkrásnějších a nejlépe zachovaných 
fortifikačních systémů ve Středomoří. Tyto mohutné hradby, které obklopují 
staré město, byly budovány po několik století a dnes představují symbol 
Dubrovníku a celého Chorvatska.

Výstavba hradeb začala již ve 13. století, ale jejich současná podoba 
vznikala postupně až do 17. století. Celková délka hradeb dosahuje 
téměř 2 kilometrů a na některých místech jsou vysoké až 25 metrů.
''',
        keyPoints: [
          AudioKeyPoint(
            timestamp: const Duration(seconds: 30),
            title: 'Historie výstavby',
            description: '13.-17. století',
          ),
          AudioKeyPoint(
            timestamp: const Duration(minutes: 1, seconds: 15),
            title: 'Rozměry hradeb',
            description: '2 km délka, 25 m výška',
          ),
          AudioKeyPoint(
            timestamp: const Duration(minutes: 2, seconds: 30),
            title: 'UNESCO',
            description: 'Světové dědictví od 1979',
          ),
        ],
      ),

      'diocletian_palace_tour': AudioGuideTrack(
        id: 'diocletian_palace_tour',
        title: 'Prohlídka Diokleciánova paláce',
        description: 'Detailní průvodce římským palácem',
        audioUrl: 'assets/audio/diocletian_palace_tour.mp3',
        duration: const Duration(minutes: 8, seconds: 20),
        language: 'cs',
        narrator: 'Marie Svobodová',
        placeId: 'diocletian_palace',
        category: AudioCategory.detailedTour,
        transcript: '''
Nacházíte se v Diokleciánově paláci, jedné z nejlépe zachovaných 
římských staveb na světě. Tento palác byl postaven na přelomu 
3. a 4. století našeho letopočtu jako rezidence římského císaře Diokleciána.

Palác má rozlohu téměř 40 tisíc metrů čtverečních a jeho zdi jsou 
postaveny z bílého kamene z ostrova Brač. Dnes v paláci žije 
přes 3000 obyvatel a nachází se zde obchody, restaurace i muzea.
''',
        keyPoints: [
          AudioKeyPoint(
            timestamp: const Duration(minutes: 1),
            title: 'Císař Diokleciánus',
            description: 'Římský císař 284-305 n.l.',
          ),
          AudioKeyPoint(
            timestamp: const Duration(minutes: 3),
            title: 'Architektura',
            description: 'Bílý kámen z Brače',
          ),
          AudioKeyPoint(
            timestamp: const Duration(minutes: 5),
            title: 'Současnost',
            description: '3000 obyvatel',
          ),
        ],
      ),
    });
  }

  /// Načtení audio bodů pro automatické přehrávání
  Future<void> _loadAudioPoints() async {
    _audioPoints.addAll([
      AudioGuidePoint(
        id: 'dubrovnik_pile_gate',
        name: 'Pile brána',
        latitude: 42.6414,
        longitude: 18.1063,
        radius: 50,
        trackId: 'dubrovnik_walls_intro',
        autoPlay: true,
        priority: AudioPriority.high,
      ),
      AudioGuidePoint(
        id: 'diocletian_peristyle',
        name: 'Peristyl',
        latitude: 43.5081,
        longitude: 16.4402,
        radius: 30,
        trackId: 'diocletian_palace_tour',
        autoPlay: true,
        priority: AudioPriority.high,
      ),
    ]);
  }

  /// Spuštění sledování polohy pro automatické přehrávání
  void _startLocationTracking() {
    _locationSubscription = _locationService.positionStream.listen(
      (position) => _checkAudioPoints(position),
      onError: (error) {
        if (kDebugMode) {
          print('Chyba při sledování polohy pro audio: $error');
        }
      },
    );
  }

  /// Kontrola audio bodů v okolí
  void _checkAudioPoints(Position position) {
    for (final point in _audioPoints) {
      final distance = Geolocator.distanceBetween(
        position.latitude,
        position.longitude,
        point.latitude,
        point.longitude,
      );

      if (distance <= point.radius && point.autoPlay) {
        _triggerAutoPlay(point);
      }
    }
  }

  /// Automatické spuštění přehrávání
  void _triggerAutoPlay(AudioGuidePoint point) {
    if (_isPlaying && _currentTrack?.id != point.trackId) {
      // Přerušit aktuální přehrávání pro vyšší prioritu
      if (point.priority == AudioPriority.high) {
        stopPlayback();
      } else {
        return; // Nepřerušovat pro nižší prioritu
      }
    }

    final track = _audioTracks[point.trackId];
    if (track != null) {
      _eventController?.add(
        AudioGuideEvent(
          type: AudioEventType.autoPlayTriggered,
          point: point,
          track: track,
        ),
      );

      playTrack(track.id);
    }
  }

  /// Přehrání audio stopy
  Future<void> playTrack(String trackId) async {
    final track = _audioTracks[trackId];
    if (track == null) {
      throw Exception('Audio stopa nebyla nalezena: $trackId');
    }

    try {
      // Simulace přehrávání audio
      _currentTrack = track;
      _isPlaying = true;
      _currentPosition = Duration.zero;
      _totalDuration = track.duration;

      _eventController?.add(
        AudioGuideEvent(type: AudioEventType.playbackStarted, track: track),
      );

      // Simulace postupu přehrávání
      _simulatePlayback();

      if (kDebugMode) {
        print('Přehrávám: ${track.title}');
      }
    } catch (e) {
      _eventController?.add(
        AudioGuideEvent(type: AudioEventType.error, error: e.toString()),
      );
    }
  }

  /// Simulace postupu přehrávání
  void _simulatePlayback() {
    Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isPlaying || _currentTrack == null) {
        timer.cancel();
        return;
      }

      _currentPosition += Duration(seconds: (1 * _playbackSpeed).round());

      _eventController?.add(
        AudioGuideEvent(
          type: AudioEventType.positionChanged,
          position: _currentPosition,
          duration: _totalDuration,
        ),
      );

      // Kontrola klíčových bodů
      _checkKeyPoints();

      if (_currentPosition >= _totalDuration) {
        timer.cancel();
        _finishPlayback();
      }
    });
  }

  /// Kontrola klíčových bodů v audio
  void _checkKeyPoints() {
    if (_currentTrack == null) return;

    for (final keyPoint in _currentTrack!.keyPoints) {
      final diff = (_currentPosition - keyPoint.timestamp).abs();
      if (diff <= const Duration(seconds: 1)) {
        _eventController?.add(
          AudioGuideEvent(
            type: AudioEventType.keyPointReached,
            keyPoint: keyPoint,
          ),
        );
      }
    }
  }

  /// Dokončení přehrávání
  void _finishPlayback() {
    _isPlaying = false;

    _eventController?.add(
      AudioGuideEvent(
        type: AudioEventType.playbackFinished,
        track: _currentTrack,
      ),
    );

    _currentTrack = null;
    _currentPosition = Duration.zero;
  }

  /// Pozastavení přehrávání
  void pausePlayback() {
    if (_isPlaying) {
      _isPlaying = false;
      _eventController?.add(
        AudioGuideEvent(
          type: AudioEventType.playbackPaused,
          track: _currentTrack,
        ),
      );
    }
  }

  /// Obnovení přehrávání
  void resumePlayback() {
    if (!_isPlaying && _currentTrack != null) {
      _isPlaying = true;
      _eventController?.add(
        AudioGuideEvent(
          type: AudioEventType.playbackResumed,
          track: _currentTrack,
        ),
      );
      _simulatePlayback();
    }
  }

  /// Zastavení přehrávání
  void stopPlayback() {
    _isPlaying = false;
    _currentPosition = Duration.zero;

    _eventController?.add(
      AudioGuideEvent(
        type: AudioEventType.playbackStopped,
        track: _currentTrack,
      ),
    );

    _currentTrack = null;
  }

  /// Přechod na pozici
  void seekTo(Duration position) {
    if (_currentTrack != null && position <= _totalDuration) {
      _currentPosition = position;
      _eventController?.add(
        AudioGuideEvent(
          type: AudioEventType.positionChanged,
          position: _currentPosition,
          duration: _totalDuration,
        ),
      );
    }
  }

  /// Nastavení rychlosti přehrávání
  void setPlaybackSpeed(double speed) {
    _playbackSpeed = speed.clamp(0.5, 2.0);
    _eventController?.add(
      AudioGuideEvent(
        type: AudioEventType.speedChanged,
        playbackSpeed: _playbackSpeed,
      ),
    );
  }

  /// Zapnutí/vypnutí automatického přehrávání
  void setAutoPlayEnabled(bool enabled) {
    _autoPlayEnabled = enabled;

    if (enabled) {
      _startLocationTracking();
    } else {
      _locationSubscription?.cancel();
    }
  }

  /// Stažení audio stopy pro offline použití
  Future<void> downloadTrack(String trackId) async {
    final track = _audioTracks[trackId];
    if (track == null) return;

    try {
      _eventController?.add(
        AudioGuideEvent(type: AudioEventType.downloadStarted, track: track),
      );

      // Simulace stahování
      await Future.delayed(const Duration(seconds: 3));

      _eventController?.add(
        AudioGuideEvent(type: AudioEventType.downloadCompleted, track: track),
      );

      if (kDebugMode) {
        print('Staženo: ${track.title}');
      }
    } catch (e) {
      _eventController?.add(
        AudioGuideEvent(
          type: AudioEventType.downloadFailed,
          track: track,
          error: e.toString(),
        ),
      );
    }
  }

  /// Získání všech dostupných stop
  List<AudioGuideTrack> getAllTracks() {
    return _audioTracks.values.toList();
  }

  /// Získání stop pro místo
  List<AudioGuideTrack> getTracksForPlace(String placeId) {
    return _audioTracks.values
        .where((track) => track.placeId == placeId)
        .toList();
  }

  /// Vyhledání stop
  List<AudioGuideTrack> searchTracks(String query) {
    final lowerQuery = query.toLowerCase();
    return _audioTracks.values
        .where(
          (track) =>
              track.title.toLowerCase().contains(lowerQuery) ||
              track.description.toLowerCase().contains(lowerQuery),
        )
        .toList();
  }

  /// Získání aktuálního stavu
  AudioGuideState get currentState => AudioGuideState(
    isPlaying: _isPlaying,
    currentTrack: _currentTrack,
    position: _currentPosition,
    duration: _totalDuration,
    playbackSpeed: _playbackSpeed,
    autoPlayEnabled: _autoPlayEnabled,
  );

  void dispose() {
    _locationSubscription?.cancel();
    _eventController?.close();
  }
}
