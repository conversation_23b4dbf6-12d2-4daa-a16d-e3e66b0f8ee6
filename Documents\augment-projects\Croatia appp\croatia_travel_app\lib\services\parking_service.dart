import 'dart:async';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../models/smart_city_models.dart';
import '../data/local_database.dart';

class ParkingService {
  static final ParkingService _instance = ParkingService._internal();
  factory ParkingService() => _instance;
  ParkingService._internal();

  final Dio _dio = Dio();
  final LocalDatabase _localDb = LocalDatabase();

  static const String _baseUrl = 'https://api.croatia-parking.com';
  Timer? _sessionTimer;

  // ========== INICIALIZACE ==========

  /// Inicializace služby
  Future<void> initialize() async {
    debugPrint('🅿️ Inicializuji parking service...');
  }

  // ========== VYHLEDÁVÁNÍ PARKOVÁNÍ ==========

  /// Vyhledání parkovacích míst v okolí
  Future<List<ParkingSpot>> findNearbyParking({
    required double latitude,
    required double longitude,
    double radiusKm = 2.0,
    ParkingType? type,
    bool onlyAvailable = true,
  }) async {
    try {
      final response = await _dio.get(
        '$_baseUrl/parking/search',
        queryParameters: {
          'lat': latitude,
          'lng': longitude,
          'radius': radiusKm,
          if (type != null) 'type': type.name,
          'only_available': onlyAvailable,
        },
      );

      final List<dynamic> data = response.data['parking_spots'];
      return data.map((json) => ParkingSpot.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Chyba při vyhledávání parkování: $e');
      return await _getLocalParkingSpots(latitude, longitude, radiusKm);
    }
  }

  /// Alias pro kompatibilitu s novým widgetem
  Future<List<ParkingSpot>> getNearbyParkingSpots({
    required double latitude,
    required double longitude,
    double radiusKm = 2.0,
  }) async {
    return await findNearbyParking(
      latitude: latitude,
      longitude: longitude,
      radiusKm: radiusKm,
    );
  }

  /// Získání detailů parkovacího místa
  Future<ParkingSpot?> getParkingSpotDetails(String spotId) async {
    try {
      final response = await _dio.get('$_baseUrl/parking/spots/$spotId');
      return ParkingSpot.fromJson(response.data);
    } catch (e) {
      debugPrint('Chyba při načítání detailů parkování: $e');
      return null;
    }
  }

  /// Aktualizace dostupnosti parkovacích míst
  Future<void> updateParkingAvailability() async {
    try {
      final response = await _dio.get('$_baseUrl/parking/availability');
      final Map<String, dynamic> data = response.data;

      // Aktualizace lokální databáze
      for (final entry in data.entries) {
        await _localDb.updateParkingAvailability(
          entry.key,
          entry.value['available_spaces'],
        );
      }
    } catch (e) {
      debugPrint('Chyba při aktualizaci dostupnosti: $e');
    }
  }

  // ========== REZERVACE A PLATBY ==========

  /// Rezervace parkovacího místa
  Future<ParkingSession?> reserveParking({
    required String spotId,
    required String licensePlate,
    required Duration duration,
    String? userId,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/parking/reserve',
        data: {
          'spot_id': spotId,
          'license_plate': licensePlate,
          'duration_minutes': duration.inMinutes,
          if (userId != null) 'user_id': userId,
        },
      );

      final session = ParkingSession.fromJson(response.data['session']);

      // Uložení do lokální databáze
      // await _localDb.saveParkingSession(session);

      // Spuštění sledování session
      _startSessionMonitoring(session.id);

      return session;
    } catch (e) {
      debugPrint('Chyba při rezervaci parkování: $e');
      return null;
    }
  }

  /// Zahájení parkování
  Future<ParkingSession?> startParking({
    required String spotId,
    required String licensePlate,
    required Duration plannedDuration,
    String? userId,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/parking/start',
        data: {
          'spot_id': spotId,
          'license_plate': licensePlate,
          'planned_duration_minutes': plannedDuration.inMinutes,
          if (userId != null) 'user_id': userId,
        },
      );

      final session = ParkingSession.fromJson(response.data['session']);

      // Uložení do lokální databáze
      // await _localDb.saveParkingSession(session);

      // Spuštění sledování session
      _startSessionMonitoring(session.id);

      return session;
    } catch (e) {
      debugPrint('Chyba při zahájení parkování: $e');
      return null;
    }
  }

  /// Ukončení parkování
  Future<ParkingSession?> endParking(String sessionId) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/parking/sessions/$sessionId/end',
      );
      final session = ParkingSession.fromJson(response.data['session']);

      // Aktualizace v lokální databázi
      // await _localDb.updateParkingSession(session);

      // Zastavení sledování
      _stopSessionMonitoring();

      return session;
    } catch (e) {
      debugPrint('Chyba při ukončení parkování: $e');
      return null;
    }
  }

  /// Prodloužení parkování
  Future<ParkingExtension?> extendParking({
    required String sessionId,
    required Duration additionalTime,
    required String paymentMethodId,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/parking/sessions/$sessionId/extend',
        data: {
          'additional_minutes': additionalTime.inMinutes,
          'payment_method': paymentMethodId,
        },
      );

      final extension = ParkingExtension.fromJson(response.data['extension']);

      // Aktualizace session v lokální databázi
      // await _localDb.addParkingExtension(sessionId, extension);

      return extension;
    } catch (e) {
      debugPrint('Chyba při prodlužování parkování: $e');
      return null;
    }
  }

  /// Výpočet ceny parkování
  Future<double> calculateParkingCost({
    required String spotId,
    required Duration duration,
  }) async {
    try {
      final spot = await getParkingSpotDetails(spotId);
      if (spot == null) return 0.0;

      // Výpočet ceny na základě hodinové sazby
      final hours = duration.inMinutes / 60.0;
      return spot.hourlyRate * hours;
    } catch (e) {
      debugPrint('Chyba při výpočtu ceny: $e');
      return 0.0;
    }
  }

  // ========== SPRÁVA SESSION ==========

  /// Získání aktivních session uživatele
  Future<List<ParkingSession>> getActiveSessions(String userId) async {
    try {
      final response = await _dio.get(
        '$_baseUrl/parking/sessions/active/$userId',
      );
      final List<dynamic> data = response.data['sessions'];

      return data.map((json) => ParkingSession.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Chyba při načítání aktivních session: $e');
      return []; // await _localDb.getActiveParkingSessions(userId);
    }
  }

  /// Získání historie parkování
  Future<List<ParkingSession>> getParkingHistory(String userId) async {
    try {
      final response = await _dio.get(
        '$_baseUrl/parking/sessions/history/$userId',
      );
      final List<dynamic> data = response.data['sessions'];

      return data.map((json) => ParkingSession.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Chyba při načítání historie: $e');
      return []; // await _localDb.getParkingHistory(userId);
    }
  }

  // ========== SLEDOVÁNÍ SESSION ==========

  void _startSessionMonitoring(String sessionId) {
    _sessionTimer?.cancel();
    _sessionTimer = Timer.periodic(const Duration(minutes: 1), (timer) async {
      await _checkSessionStatus(sessionId);
    });
  }

  void _stopSessionMonitoring() {
    _sessionTimer?.cancel();
    _sessionTimer = null;
  }

  Future<void> _checkSessionStatus(String sessionId) async {
    try {
      final response = await _dio.get('$_baseUrl/parking/sessions/$sessionId');
      final session = ParkingSession.fromJson(response.data);

      // Kontrola, zda session brzy vyprší
      if (session.isExpiringSoon) {
        // Odeslání notifikace
        _sendExpirationNotification(session);
      }

      // Aktualizace v lokální databázi
      // await _localDb.updateParkingSession(session);
    } catch (e) {
      debugPrint('Chyba při kontrole session: $e');
    }
  }

  void _sendExpirationNotification(ParkingSession session) {
    // Implementace notifikace
    debugPrint(
      'Parkování brzy vyprší: ${session.remainingTime?.inMinutes} minut',
    );
  }

  // ========== LOKÁLNÍ DATA ==========

  Future<List<ParkingSpot>> _getLocalParkingSpots(
    double latitude,
    double longitude,
    double radiusKm,
  ) async {
    // Simulace lokálních dat pro offline režim
    return [
      ParkingSpot(
        id: 'parking_1',
        name: 'Centrum parkoviště',
        address: 'Hlavní náměstí 1',
        latitude: latitude + 0.001,
        longitude: longitude + 0.001,
        type: ParkingType.lot,
        totalSpaces: 50,
        availableSpaces: 12,
        hourlyRate: 20.0,
        dailyRate: 150.0,
        currency: 'HRK',
        paymentMethods: ['card', 'cash', 'mobile'],
        features: ParkingFeatures(
          hasEVCharging: false,
          hasDisabledAccess: true,
          hasCCTV: true,
          isCovered: false,
          has24HAccess: true,
          hasCarWash: false,
          hasToilets: true,
          maxHeight: 2.2,
        ),
        isActive: true,
        lastUpdated: DateTime.now(),
      ),
    ];
  }

  // ========== POKROČILÉ FUNKCE ==========

  /// Predikce dostupnosti parkování na základě historických dat
  Future<ParkingPrediction> predictAvailability({
    required String spotId,
    required DateTime targetTime,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/parking/predict-availability',
        data: {
          'spot_id': spotId,
          'target_time': targetTime.toIso8601String(),
          'historical_data_days': 30,
          'weather_forecast': await _getWeatherForecast(targetTime),
          'event_calendar': await _getEventCalendar(targetTime),
        },
      );

      return ParkingPrediction.fromJson(response.data);
    } catch (e) {
      debugPrint('Chyba při predikci dostupnosti: $e');
      return ParkingPrediction.fallback();
    }
  }

  /// Inteligentní rezervace s optimalizací ceny a dostupnosti
  Future<SmartReservation?> makeSmartReservation({
    required double latitude,
    required double longitude,
    required DateTime arrivalTime,
    required Duration plannedDuration,
    double maxWalkingDistance = 500.0, // metry
    double maxPrice = double.infinity,
    List<String> requiredFeatures = const [],
    String? userId,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/parking/smart-reservation',
        data: {
          'location': {'lat': latitude, 'lng': longitude},
          'arrival_time': arrivalTime.toIso8601String(),
          'planned_duration': plannedDuration.inMinutes,
          'max_walking_distance': maxWalkingDistance,
          'max_price': maxPrice,
          'required_features': requiredFeatures,
          if (userId != null) 'user_id': userId,
          'user_preferences': await _getUserParkingPreferences(userId),
        },
      );

      final reservation = SmartReservation.fromJson(
        response.data['reservation'],
      );

      // Uložení do lokální databáze
      // await _localDb.saveSmartReservation(reservation);

      return reservation;
    } catch (e) {
      debugPrint('Chyba při chytré rezervaci: $e');
      return null;
    }
  }

  /// Dynamické ceny na základě poptávky
  Future<List<DynamicPricing>> getDynamicPricing({
    required List<String> spotIds,
    required DateTime startTime,
    required DateTime endTime,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/parking/dynamic-pricing',
        data: {
          'spot_ids': spotIds,
          'start_time': startTime.toIso8601String(),
          'end_time': endTime.toIso8601String(),
        },
      );

      final List<dynamic> data = response.data['pricing'];
      return data.map((json) => DynamicPricing.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Chyba při načítání dynamických cen: $e');
      return [];
    }
  }

  /// Crowdsourcing informace o parkování
  Future<void> reportParkingStatus({
    required String spotId,
    required ParkingSpotStatus status,
    String? userId,
    String? notes,
    List<String>? photos,
  }) async {
    try {
      await _dio.post(
        '$_baseUrl/parking/report-status',
        data: {
          'spot_id': spotId,
          'status': status.name,
          'timestamp': DateTime.now().toIso8601String(),
          if (userId != null) 'user_id': userId,
          if (notes != null) 'notes': notes,
          if (photos != null) 'photos': photos,
        },
      );
    } catch (e) {
      debugPrint('Chyba při hlášení stavu parkování: $e');
    }
  }

  // ========== POMOCNÉ METODY ==========

  Future<Map<String, dynamic>> _getWeatherForecast(DateTime targetTime) async {
    // Simulace předpovědi počasí
    return {
      'temperature': 20,
      'precipitation_probability': 0.2,
      'wind_speed': 10,
    };
  }

  Future<List<Map<String, dynamic>>> _getEventCalendar(
    DateTime targetTime,
  ) async {
    // Simulace kalendáře událostí
    return [
      {
        'event_id': 'concert_1',
        'name': 'Koncert v centru',
        'start_time': targetTime.toIso8601String(),
        'expected_attendance': 5000,
      },
    ];
  }

  Future<Map<String, dynamic>> _getUserParkingPreferences(
    String? userId,
  ) async {
    if (userId == null) return {};

    // Simulace uživatelských preferencí
    return {
      'prefer_covered': true,
      'prefer_security': true,
      'max_walking_distance': 300,
      'preferred_payment_method': 'card',
    };
  }

  /// Vyčištění cache
  Future<void> clearCache() async {
    // await _localDb.clearParkingCache();
  }

  /// Dispose
  void dispose() {
    _sessionTimer?.cancel();
  }
}
