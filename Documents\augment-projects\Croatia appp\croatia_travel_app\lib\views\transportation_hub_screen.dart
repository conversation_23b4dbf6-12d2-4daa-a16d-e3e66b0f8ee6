import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/transportation_hub.dart';
import '../services/transportation_hub_service.dart';
import 'croatia_idos_screen.dart';

class TransportationHubScreen extends StatefulWidget {
  const TransportationHubScreen({super.key});

  @override
  State<TransportationHubScreen> createState() =>
      _TransportationHubScreenState();
}

class _TransportationHubScreenState extends State<TransportationHubScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Transportation Hub stav
  String _selectedRegion = 'all';
  TransportationServiceType? _selectedServiceType;
  String _searchQuery = '';
  bool _isLoading = false;

  // Data
  List<CarRental> _allCarRentals = [];
  List<ParkingLot> _allParkingLots = [];
  List<TaxiService> _allTaxiServices = [];
  List<TrafficInfo> _allTrafficInfo = [];

  // Statistiky
  int _totalCarRentals = 0;
  int _totalParkingLots = 0;
  int _totalTaxiServices = 0;
  int _activeTrafficAlerts = 0;
  int _availableParkingSpaces = 0;

  final TransportationHubService _transportationService =
      TransportationHubService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _loadTransportationData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadTransportationData() async {
    setState(() => _isLoading = true);

    try {
      _allCarRentals = await _transportationService.getAllCarRentals();
      _allParkingLots = await _transportationService.getAllParkingLots();
      _allTaxiServices = await _transportationService.getAllTaxiServices();
      _allTrafficInfo = await _transportationService.getCurrentTrafficInfo();
      _updateStatistics();
    } catch (e) {
      debugPrint('Chyba při načítání dopravních služeb: $e');
    }

    setState(() => _isLoading = false);
  }

  void _updateStatistics() {
    _totalCarRentals = _allCarRentals.length;
    _totalParkingLots = _allParkingLots.length;
    _totalTaxiServices = _allTaxiServices.length;
    _activeTrafficAlerts = _allTrafficInfo.where((t) => t.isActive).length;
    _availableParkingSpaces = _allParkingLots.fold(
      0,
      (sum, p) => sum + (p.availableSpaces ?? 0),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Dopravní hub',
          style: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF1976D2).withValues(alpha: 0.9), // Blue
                const Color(0xFF1565C0).withValues(alpha: 0.8), // Dark Blue
                const Color(0xFF0D47A1).withValues(alpha: 0.7), // Navy Blue
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: CustomPaint(
            painter: WatercolorTransportationHeaderPainter(),
            size: Size.infinite,
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: _loadTransportationData,
              icon: const Icon(Icons.refresh),
              tooltip: 'Aktualizovat dopravní služby',
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          indicator: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Colors.white.withValues(alpha: 0.3),
          ),
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Přehled'),
            Tab(icon: Icon(Icons.directions_bus), text: 'Veřejná doprava'),
            Tab(icon: Icon(Icons.directions_car), text: 'Autopůjčovny'),
            Tab(icon: Icon(Icons.local_parking), text: 'Parkování'),
            Tab(icon: Icon(Icons.local_taxi), text: 'Taxi'),
            Tab(icon: Icon(Icons.traffic), text: 'Doprava'),
          ],
        ),
      ),
      body: Container(
        child: CustomPaint(
          painter: WatercolorTransportationBackgroundPainter(),
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(),
              _buildPublicTransportTab(),
              _buildCarRentalsTab(),
              _buildParkingTab(),
              _buildTaxiTab(),
              _buildTrafficTab(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Hlavní karta
          Container(
            child: CustomPaint(
              painter: WatercolorTransportationMainCardPainter(
                const Color(0xFF1976D2),
              ),
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Column(
                  children: [
                    Text(
                      'Dopravní služby Chorvatska',
                      style: GoogleFonts.playfairDisplay(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF1976D2),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Kompletní přehled dopravních možností a služeb',
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        color: const Color(0xFF666666),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Statistiky
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            children: [
              _buildStatCard(
                'Autopůjčovny',
                '$_totalCarRentals',
                'dostupných',
                Icons.directions_car,
                const Color(0xFF1976D2),
              ),
              _buildStatCard(
                'Parkoviště',
                '$_totalParkingLots',
                'v databázi',
                Icons.local_parking,
                const Color(0xFF1565C0),
              ),
              _buildStatCard(
                'Taxi služby',
                '$_totalTaxiServices',
                'operátorů',
                Icons.local_taxi,
                const Color(0xFF0D47A1),
              ),
              _buildStatCard(
                'Volná místa',
                '$_availableParkingSpaces',
                'k parkování',
                Icons.event_available,
                const Color(0xFF42A5F5),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Veřejná doprava přehled
          _buildPublicTransportOverview(),

          const SizedBox(height: 20),

          // Dopravní informace
          _buildTrafficInfoOverview(),

          const SizedBox(height: 20),

          // Rychlé akce
          _buildQuickActionsOverview(),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Container(
      child: CustomPaint(
        painter: WatercolorTransportationStatCardPainter(color),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                child: CustomPaint(
                  painter: WatercolorTransportationIconPainter(color),
                  child: Icon(icon, size: 32, color: color),
                ),
              ),
              const SizedBox(height: 12),
              Text(
                value,
                style: GoogleFonts.playfairDisplay(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: GoogleFonts.inter(
                  fontSize: 12,
                  color: const Color(0xFF666666),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF2C2C2C),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPublicTransportOverview() {
    return Container(
      child: CustomPaint(
        painter: WatercolorTransportationPublicTransportPainter(
          const Color(0xFF1565C0),
        ),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    child: CustomPaint(
                      painter: WatercolorTransportationIconPainter(
                        const Color(0xFF1565C0),
                      ),
                      child: Icon(
                        Icons.directions_bus,
                        size: 32,
                        color: const Color(0xFF1565C0),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Veřejná doprava',
                          style: GoogleFonts.playfairDisplay(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF1565C0),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Real-time informace o spojích',
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: const Color(0xFF666666),
                          ),
                        ),
                      ],
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const CroatiaIdosScreen(),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF1565C0),
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Croatia IDOS'),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                'Autobusy, tramvaje, vlaky a trajekty s live informacemi',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: const Color(0xFF666666),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTrafficInfoOverview() {
    return Container(
      child: CustomPaint(
        painter: WatercolorTransportationTrafficInfoPainter(
          const Color(0xFF0D47A1),
        ),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    child: CustomPaint(
                      painter: WatercolorTransportationIconPainter(
                        const Color(0xFF0D47A1),
                      ),
                      child: Icon(
                        Icons.traffic,
                        size: 32,
                        color: const Color(0xFF0D47A1),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Dopravní informace',
                          style: GoogleFonts.playfairDisplay(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF0D47A1),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '$_activeTrafficAlerts aktivních upozornění',
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: const Color(0xFF666666),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: const Color(0xFF0D47A1),
                    size: 16,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                'Uzavírky, nehody a dopravní situace v reálném čase',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: const Color(0xFF666666),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActionsOverview() {
    return Container(
      child: CustomPaint(
        painter: WatercolorTransportationQuickActionsPainter(
          const Color(0xFF42A5F5),
        ),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Rychlé akce',
                style: GoogleFonts.playfairDisplay(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF42A5F5),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildQuickActionButton(
                    'Najít parkování',
                    Icons.local_parking,
                    const Color(0xFF1976D2),
                    () => _tabController.animateTo(3),
                  ),
                  _buildQuickActionButton(
                    'Zavolat taxi',
                    Icons.local_taxi,
                    const Color(0xFF1565C0),
                    () => _tabController.animateTo(4),
                  ),
                  _buildQuickActionButton(
                    'Půjčit auto',
                    Icons.directions_car,
                    const Color(0xFF0D47A1),
                    () => _tabController.animateTo(2),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return Column(
      children: [
        Container(
          child: CustomPaint(
            painter: WatercolorTransportationIconPainter(color),
            child: IconButton(
              onPressed: onPressed,
              icon: Icon(icon, size: 32, color: color),
              style: IconButton.styleFrom(
                backgroundColor: color.withValues(alpha: 0.1),
                padding: const EdgeInsets.all(16),
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: GoogleFonts.inter(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF2C2C2C),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildPublicTransportTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.directions_bus, size: 64, color: const Color(0xFF1976D2)),
          const SizedBox(height: 16),
          Text(
            'Veřejná doprava',
            style: GoogleFonts.playfairDisplay(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF1976D2),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Přejděte na dedikovanou obrazovku\npro veřejnou dopravu',
            style: GoogleFonts.inter(
              fontSize: 16,
              color: const Color(0xFF666666),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CroatiaIdosScreen(),
                ),
              );
            },
            icon: const Icon(Icons.directions_bus),
            label: const Text('Croatia IDOS'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF1976D2),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCarRentalsTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _allCarRentals.length,
      itemBuilder: (context, index) {
        return _buildCarRentalCard(_allCarRentals[index]);
      },
    );
  }

  Widget _buildParkingTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _allParkingLots.length,
      itemBuilder: (context, index) {
        return _buildParkingCard(_allParkingLots[index]);
      },
    );
  }

  Widget _buildTaxiTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _allTaxiServices.length,
      itemBuilder: (context, index) {
        return _buildTaxiCard(_allTaxiServices[index]);
      },
    );
  }

  Widget _buildTrafficTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _allTrafficInfo.length,
      itemBuilder: (context, index) {
        return _buildTrafficInfoCard(_allTrafficInfo[index]);
      },
    );
  }

  Widget _buildCarRentalCard(CarRental rental) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: CustomPaint(
        painter: WatercolorTransportationCarRentalCardPainter(
          const Color(0xFF1976D2),
        ),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          rental.name,
                          style: GoogleFonts.playfairDisplay(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF2C2C2C),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(Icons.star, size: 16, color: Colors.amber),
                            const SizedBox(width: 4),
                            Text(
                              rental.rating.toStringAsFixed(1),
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF2C2C2C),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '(${rental.reviewCount} recenzí)',
                              style: GoogleFonts.inter(
                                fontSize: 12,
                                color: const Color(0xFF666666),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.directions_car,
                    color: const Color(0xFF1976D2),
                    size: 24,
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                rental.description,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: const Color(0xFF666666),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  if (rental.hasAirportPickup)
                    _buildFeatureChip('Letiště', Icons.flight),
                  if (rental.hasDelivery)
                    _buildFeatureChip('Rozvoz', Icons.delivery_dining),
                  if (rental.hasGPS) _buildFeatureChip('GPS', Icons.gps_fixed),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.location_on,
                    size: 16,
                    color: const Color(0xFF666666),
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      rental.address,
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        color: const Color(0xFF666666),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => _contactService(rental.phone),
                    icon: const Icon(Icons.phone, size: 20),
                    color: const Color(0xFF1976D2),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildParkingCard(ParkingLot parking) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: CustomPaint(
        painter: WatercolorTransportationParkingCardPainter(
          const Color(0xFF1565C0),
        ),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          parking.name,
                          style: GoogleFonts.playfairDisplay(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF2C2C2C),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              '${parking.availableSpaces ?? 0}/${parking.totalSpaces}',
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: parking.isNearlyFull
                                    ? Colors.red
                                    : Colors.green,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'volných míst',
                              style: GoogleFonts.inter(
                                fontSize: 12,
                                color: const Color(0xFF666666),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.local_parking,
                    color: const Color(0xFF1565C0),
                    size: 24,
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF1565C0).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '${parking.hourlyRate} HRK/hod',
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF1565C0),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  if (parking.hasElectricCharging)
                    _buildFeatureChip('Nabíjení', Icons.electric_car),
                  if (parking.hasSecurity)
                    _buildFeatureChip('Ostraha', Icons.security),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.location_on,
                    size: 16,
                    color: const Color(0xFF666666),
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      parking.address,
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        color: const Color(0xFF666666),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTaxiCard(TaxiService taxi) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: CustomPaint(
        painter: WatercolorTransportationTaxiCardPainter(
          const Color(0xFF0D47A1),
        ),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          taxi.name,
                          style: GoogleFonts.playfairDisplay(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF2C2C2C),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(Icons.star, size: 16, color: Colors.amber),
                            const SizedBox(width: 4),
                            Text(
                              taxi.rating.toStringAsFixed(1),
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF2C2C2C),
                              ),
                            ),
                            const SizedBox(width: 8),
                            if (taxi.estimatedWaitTime != null)
                              Text(
                                taxi.estimatedWaitTime!,
                                style: GoogleFonts.inter(
                                  fontSize: 12,
                                  color: const Color(0xFF666666),
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.local_taxi,
                    color: const Color(0xFF0D47A1),
                    size: 24,
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  if (taxi.has24hService)
                    _buildFeatureChip('24/7', Icons.access_time),
                  if (taxi.hasAppBooking)
                    _buildFeatureChip('Aplikace', Icons.smartphone),
                  if (taxi.acceptsCreditCards)
                    _buildFeatureChip('Karty', Icons.credit_card),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.phone, size: 16, color: const Color(0xFF666666)),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      taxi.phone,
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        color: const Color(0xFF666666),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => _contactService(taxi.phone),
                    icon: const Icon(Icons.phone, size: 20),
                    color: const Color(0xFF0D47A1),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTrafficInfoCard(TrafficInfo traffic) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: CustomPaint(
        painter: WatercolorTransportationTrafficCardPainter(
          _getTrafficSeverityColor(traffic.severity),
        ),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      traffic.title,
                      style: GoogleFonts.playfairDisplay(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF2C2C2C),
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getTrafficSeverityColor(
                        traffic.severity,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _getTrafficSeverityLabel(traffic.severity),
                      style: GoogleFonts.inter(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: _getTrafficSeverityColor(traffic.severity),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                traffic.description,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: const Color(0xFF666666),
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.location_on,
                    size: 16,
                    color: const Color(0xFF666666),
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      traffic.location,
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        color: const Color(0xFF666666),
                      ),
                    ),
                  ),
                  Text(
                    _formatTrafficTime(traffic.startTime),
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      color: const Color(0xFF666666),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureChip(String label, IconData icon) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: const Color(0xFF4CAF50)),
          const SizedBox(width: 4),
          Text(
            label,
            style: GoogleFonts.inter(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF4CAF50),
            ),
          ),
        ],
      ),
    );
  }

  Color _getTrafficSeverityColor(TrafficSeverity severity) {
    switch (severity) {
      case TrafficSeverity.low:
        return const Color(0xFF4CAF50);
      case TrafficSeverity.medium:
        return const Color(0xFFFF9800);
      case TrafficSeverity.high:
        return const Color(0xFFFF5722);
      case TrafficSeverity.critical:
        return const Color(0xFFD32F2F);
    }
  }

  String _getTrafficSeverityLabel(TrafficSeverity severity) {
    switch (severity) {
      case TrafficSeverity.low:
        return 'Nízká';
      case TrafficSeverity.medium:
        return 'Střední';
      case TrafficSeverity.high:
        return 'Vysoká';
      case TrafficSeverity.critical:
        return 'Kritická';
    }
  }

  String _formatTrafficTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 60) {
      return 'před ${difference.inMinutes} min';
    } else if (difference.inHours < 24) {
      return 'před ${difference.inHours} hod';
    } else {
      return '${time.day}.${time.month}.';
    }
  }

  void _contactService(String phone) {
    debugPrint('Kontakt na službu: $phone');
    // Zde by byla implementace volání
  }
}

// Watercolor painters pro Transportation Hub Screen
class WatercolorTransportationHeaderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor efekt pro Transportation header - evokuje cesty a mosty
    final path1 = Path();
    path1.moveTo(0, size.height * 0.2);
    path1.quadraticBezierTo(
      size.width * 0.1,
      size.height * 0.05,
      size.width * 0.25,
      size.height * 0.3,
    );
    path1.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.55,
      size.width * 0.6,
      size.height * 0.1,
    );
    path1.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.02,
      size.width,
      size.height * 0.25,
    );
    path1.lineTo(size.width, size.height);
    path1.lineTo(0, size.height);
    path1.close();

    paint.color = const Color(0xFF1976D2).withValues(alpha: 0.2);
    canvas.drawPath(path1, paint);

    // Druhá vrstva - silnice
    final path2 = Path();
    path2.moveTo(0, size.height * 0.4);
    path2.quadraticBezierTo(
      size.width * 0.15,
      size.height * 0.2,
      size.width * 0.35,
      size.height * 0.5,
    );
    path2.quadraticBezierTo(
      size.width * 0.55,
      size.height * 0.75,
      size.width,
      size.height * 0.35,
    );
    path2.lineTo(size.width, size.height);
    path2.lineTo(0, size.height);
    path2.close();

    paint.color = const Color(0xFF1565C0).withValues(alpha: 0.15);
    canvas.drawPath(path2, paint);

    // Třetí vrstva - dopravní prostředky
    final path3 = Path();
    path3.moveTo(0, size.height * 0.65);
    path3.quadraticBezierTo(
      size.width * 0.2,
      size.height * 0.45,
      size.width * 0.5,
      size.height * 0.7,
    );
    path3.quadraticBezierTo(
      size.width * 0.75,
      size.height * 0.8,
      size.width,
      size.height * 0.55,
    );
    path3.lineTo(size.width, size.height);
    path3.lineTo(0, size.height);
    path3.close();

    paint.color = const Color(0xFF0D47A1).withValues(alpha: 0.1);
    canvas.drawPath(path3, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorTransportationBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Jemný watercolor efekt pro pozadí transportation hub
    final path = Path();
    path.moveTo(size.width * 0.02, size.height * 0.01);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.005,
      size.width * 0.7,
      size.height * 0.03,
    );
    path.quadraticBezierTo(
      size.width * 0.98,
      size.height * 0.06,
      size.width * 0.99,
      size.height * 0.99,
    );
    path.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.995,
      size.width * 0.3,
      size.height * 0.97,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.94,
      size.width * 0.02,
      size.height * 0.01,
    );
    path.close();

    paint.color = const Color(0xFF1976D2).withValues(alpha: 0.02);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorTransportationMainCardPainter extends CustomPainter {
  final Color color;

  WatercolorTransportationMainCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro hlavní transportation kartu - evokuje dopravní mapy
    final path = Path();
    path.moveTo(size.width * 0.005, size.height * 0.04);
    path.quadraticBezierTo(
      size.width * 0.18,
      size.height * 0.005,
      size.width * 0.45,
      size.height * 0.025,
    );
    path.quadraticBezierTo(
      size.width * 0.75,
      size.height * 0.045,
      size.width * 0.995,
      size.height * 0.12,
    );
    path.quadraticBezierTo(
      size.width * 0.998,
      size.height * 0.55,
      size.width * 0.96,
      size.height * 0.96,
    );
    path.quadraticBezierTo(
      size.width * 0.82,
      size.height * 0.995,
      size.width * 0.55,
      size.height * 0.975,
    );
    path.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.955,
      size.width * 0.002,
      size.height * 0.88,
    );
    path.quadraticBezierTo(
      size.width * 0.002,
      size.height * 0.45,
      size.width * 0.005,
      size.height * 0.04,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);

    // Druhá vrstva pro hloubku
    final path2 = Path();
    path2.moveTo(size.width * 0.06, size.height * 0.09);
    path2.quadraticBezierTo(
      size.width * 0.32,
      size.height * 0.01,
      size.width * 0.68,
      size.height * 0.07,
    );
    path2.quadraticBezierTo(
      size.width * 0.94,
      size.height * 0.15,
      size.width * 0.91,
      size.height * 0.91,
    );
    path2.quadraticBezierTo(
      size.width * 0.68,
      size.height * 0.99,
      size.width * 0.32,
      size.height * 0.93,
    );
    path2.quadraticBezierTo(
      size.width * 0.06,
      size.height * 0.85,
      size.width * 0.06,
      size.height * 0.09,
    );
    path2.close();

    paint.color = color.withValues(alpha: 0.04);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorTransportationStatCardPainter extends CustomPainter {
  final Color color;

  WatercolorTransportationStatCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro stat karty - evokuje dopravní značky
    final path = Path();
    path.moveTo(size.width * 0.03, size.height * 0.07);
    path.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.01,
      size.width * 0.65,
      size.height * 0.05,
    );
    path.quadraticBezierTo(
      size.width * 0.97,
      size.height * 0.1,
      size.width * 0.94,
      size.height * 0.93,
    );
    path.quadraticBezierTo(
      size.width * 0.75,
      size.height * 0.99,
      size.width * 0.35,
      size.height * 0.95,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.9,
      size.width * 0.03,
      size.height * 0.07,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.1);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorTransportationIconPainter extends CustomPainter {
  final Color color;

  WatercolorTransportationIconPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor kruh kolem transportation ikony - evokuje dopravní kruháče
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2.5;

    final path = Path();
    for (int i = 0; i < 360; i += 8) {
      final angle = i * pi / 180;
      final variation = 0.85 + (sin(i * pi / 20) * 0.15);
      final x = center.dx + (radius * variation) * cos(angle);
      final y = center.dy + (radius * variation) * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    paint.color = color.withValues(alpha: 0.15);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorTransportationPublicTransportPainter extends CustomPainter {
  final Color color;

  WatercolorTransportationPublicTransportPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro public transport sekci - evokuje autobusové zastávky
    final path = Path();
    path.moveTo(size.width * 0.005, size.height * 0.05);
    path.quadraticBezierTo(
      size.width * 0.2,
      size.height * 0.005,
      size.width * 0.5,
      size.height * 0.025,
    );
    path.quadraticBezierTo(
      size.width * 0.995,
      size.height * 0.07,
      size.width * 0.98,
      size.height * 0.95,
    );
    path.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.995,
      size.width * 0.5,
      size.height * 0.975,
    );
    path.quadraticBezierTo(
      size.width * 0.005,
      size.height * 0.93,
      size.width * 0.005,
      size.height * 0.05,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.06);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorTransportationTrafficInfoPainter extends CustomPainter {
  final Color color;

  WatercolorTransportationTrafficInfoPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro traffic info sekci - evokuje dopravní informace
    final path = Path();
    path.moveTo(size.width * 0.01, size.height * 0.08);
    path.quadraticBezierTo(
      size.width * 0.24,
      size.height * 0.01,
      size.width * 0.58,
      size.height * 0.05,
    );
    path.quadraticBezierTo(
      size.width * 0.99,
      size.height * 0.11,
      size.width * 0.97,
      size.height * 0.92,
    );
    path.quadraticBezierTo(
      size.width * 0.76,
      size.height * 0.99,
      size.width * 0.42,
      size.height * 0.95,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.89,
      size.width * 0.01,
      size.height * 0.08,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.07);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorTransportationQuickActionsPainter extends CustomPainter {
  final Color color;

  WatercolorTransportationQuickActionsPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro quick actions sekci - evokuje rychlé akce
    final path = Path();
    path.moveTo(size.width * 0.01, size.height * 0.06);
    path.quadraticBezierTo(
      size.width * 0.22,
      size.height * 0.005,
      size.width * 0.55,
      size.height * 0.03,
    );
    path.quadraticBezierTo(
      size.width * 0.99,
      size.height * 0.08,
      size.width * 0.96,
      size.height * 0.94,
    );
    path.quadraticBezierTo(
      size.width * 0.78,
      size.height * 0.995,
      size.width * 0.45,
      size.height * 0.97,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.92,
      size.width * 0.01,
      size.height * 0.06,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.05);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorTransportationCarRentalCardPainter extends CustomPainter {
  final Color color;

  WatercolorTransportationCarRentalCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro car rental karty - evokuje autopůjčovny
    final path = Path();
    path.moveTo(size.width * 0.005, size.height * 0.1);
    path.quadraticBezierTo(
      size.width * 0.23,
      size.height * 0.02,
      size.width * 0.6,
      size.height * 0.06,
    );
    path.quadraticBezierTo(
      size.width * 0.995,
      size.height * 0.12,
      size.width * 0.98,
      size.height * 0.9,
    );
    path.quadraticBezierTo(
      size.width * 0.77,
      size.height * 0.98,
      size.width * 0.4,
      size.height * 0.94,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.88,
      size.width * 0.005,
      size.height * 0.1,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorTransportationParkingCardPainter extends CustomPainter {
  final Color color;

  WatercolorTransportationParkingCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro parking karty - evokuje parkoviště
    final path = Path();
    path.moveTo(size.width * 0.005, size.height * 0.09);
    path.quadraticBezierTo(
      size.width * 0.21,
      size.height * 0.015,
      size.width * 0.55,
      size.height * 0.055,
    );
    path.quadraticBezierTo(
      size.width * 0.995,
      size.height * 0.11,
      size.width * 0.98,
      size.height * 0.91,
    );
    path.quadraticBezierTo(
      size.width * 0.79,
      size.height * 0.985,
      size.width * 0.45,
      size.height * 0.945,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.89,
      size.width * 0.005,
      size.height * 0.09,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorTransportationTaxiCardPainter extends CustomPainter {
  final Color color;

  WatercolorTransportationTaxiCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro taxi karty - evokuje taxi služby
    final path = Path();
    path.moveTo(size.width * 0.005, size.height * 0.08);
    path.quadraticBezierTo(
      size.width * 0.19,
      size.height * 0.01,
      size.width * 0.5,
      size.height * 0.045,
    );
    path.quadraticBezierTo(
      size.width * 0.995,
      size.height * 0.095,
      size.width * 0.98,
      size.height * 0.92,
    );
    path.quadraticBezierTo(
      size.width * 0.81,
      size.height * 0.99,
      size.width * 0.5,
      size.height * 0.955,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.905,
      size.width * 0.005,
      size.height * 0.08,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorTransportationTrafficCardPainter extends CustomPainter {
  final Color color;

  WatercolorTransportationTrafficCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro traffic karty - evokuje dopravní informace
    final path = Path();
    path.moveTo(size.width * 0.005, size.height * 0.07);
    path.quadraticBezierTo(
      size.width * 0.17,
      size.height * 0.005,
      size.width * 0.45,
      size.height * 0.035,
    );
    path.quadraticBezierTo(
      size.width * 0.995,
      size.height * 0.085,
      size.width * 0.98,
      size.height * 0.93,
    );
    path.quadraticBezierTo(
      size.width * 0.83,
      size.height * 0.995,
      size.width * 0.55,
      size.height * 0.965,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.915,
      size.width * 0.005,
      size.height * 0.07,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
