import 'package:json_annotation/json_annotation.dart';
import 'place.dart';

part 'collaborative_trip.g.dart';

@JsonSerializable()
class CollaborativeTrip {
  final String id;
  final String name;
  final String description;
  final DateTime startDate;
  final DateTime endDate;
  final String creatorId;
  final List<String> participants;
  final List<String> invitedUsers;
  final List<PlaceSuggestion> placeSuggestions;
  final List<String> tags;
  final TripStatus status;
  final TripVisibility visibility;
  final DateTime createdAt;
  final DateTime updatedAt;

  CollaborativeTrip({
    required this.id,
    required this.name,
    required this.description,
    required this.startDate,
    required this.endDate,
    required this.creatorId,
    required this.participants,
    this.invitedUsers = const [],
    this.placeSuggestions = const [],
    this.tags = const [],
    this.status = TripStatus.planning,
    this.visibility = TripVisibility.private,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CollaborativeTrip.fromJson(Map<String, dynamic> json) => 
      _$CollaborativeTripFromJson(json);
  Map<String, dynamic> toJson() => _$CollaborativeTripToJson(this);

  CollaborativeTrip copyWith({
    String? id,
    String? name,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
    String? creatorId,
    List<String>? participants,
    List<String>? invitedUsers,
    List<PlaceSuggestion>? placeSuggestions,
    List<String>? tags,
    TripStatus? status,
    TripVisibility? visibility,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CollaborativeTrip(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      creatorId: creatorId ?? this.creatorId,
      participants: participants ?? this.participants,
      invitedUsers: invitedUsers ?? this.invitedUsers,
      placeSuggestions: placeSuggestions ?? this.placeSuggestions,
      tags: tags ?? this.tags,
      status: status ?? this.status,
      visibility: visibility ?? this.visibility,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Duration get duration => endDate.difference(startDate);
  
  int get totalParticipants => participants.length;
  
  int get pendingInvitations => invitedUsers.length;
  
  int get approvedSuggestions => placeSuggestions
      .where((s) => s.status == SuggestionStatus.approved)
      .length;
  
  int get pendingSuggestions => placeSuggestions
      .where((s) => s.status == SuggestionStatus.pending)
      .length;

  bool isParticipant(String userId) => participants.contains(userId);
  
  bool isCreator(String userId) => creatorId == userId;
  
  bool isInvited(String userId) => invitedUsers.contains(userId);
}

@JsonSerializable()
class PlaceSuggestion {
  final String id;
  final Place place;
  final String suggestedBy;
  final DateTime suggestedAt;
  final List<String> votes;
  final List<String> downvotes;
  final List<SuggestionComment> comments;
  final SuggestionStatus status;
  final String? reason;

  PlaceSuggestion({
    required this.id,
    required this.place,
    required this.suggestedBy,
    required this.suggestedAt,
    this.votes = const [],
    this.downvotes = const [],
    this.comments = const [],
    this.status = SuggestionStatus.pending,
    this.reason,
  });

  factory PlaceSuggestion.fromJson(Map<String, dynamic> json) => 
      _$PlaceSuggestionFromJson(json);
  Map<String, dynamic> toJson() => _$PlaceSuggestionToJson(this);

  PlaceSuggestion copyWith({
    String? id,
    Place? place,
    String? suggestedBy,
    DateTime? suggestedAt,
    List<String>? votes,
    List<String>? downvotes,
    List<SuggestionComment>? comments,
    SuggestionStatus? status,
    String? reason,
  }) {
    return PlaceSuggestion(
      id: id ?? this.id,
      place: place ?? this.place,
      suggestedBy: suggestedBy ?? this.suggestedBy,
      suggestedAt: suggestedAt ?? this.suggestedAt,
      votes: votes ?? this.votes,
      downvotes: downvotes ?? this.downvotes,
      comments: comments ?? this.comments,
      status: status ?? this.status,
      reason: reason ?? this.reason,
    );
  }

  int get totalVotes => votes.length;
  int get totalDownvotes => downvotes.length;
  int get score => totalVotes - totalDownvotes;
  bool hasUserVoted(String userId) => votes.contains(userId) || downvotes.contains(userId);
  bool hasUserUpvoted(String userId) => votes.contains(userId);
  bool hasUserDownvoted(String userId) => downvotes.contains(userId);
}

@JsonSerializable()
class SuggestionComment {
  final String id;
  final String userId;
  final String comment;
  final DateTime createdAt;

  SuggestionComment({
    required this.id,
    required this.userId,
    required this.comment,
    required this.createdAt,
  });

  factory SuggestionComment.fromJson(Map<String, dynamic> json) => 
      _$SuggestionCommentFromJson(json);
  Map<String, dynamic> toJson() => _$SuggestionCommentToJson(this);
}

@JsonSerializable()
class TripInvitation {
  final String id;
  final String tripId;
  final String tripName;
  final String invitedBy;
  final DateTime invitedAt;
  final InvitationStatus status;
  final DateTime? respondedAt;

  TripInvitation({
    required this.id,
    required this.tripId,
    required this.tripName,
    required this.invitedBy,
    required this.invitedAt,
    this.status = InvitationStatus.pending,
    this.respondedAt,
  });

  factory TripInvitation.fromJson(Map<String, dynamic> json) => 
      _$TripInvitationFromJson(json);
  Map<String, dynamic> toJson() => _$TripInvitationToJson(this);

  TripInvitation copyWith({
    String? id,
    String? tripId,
    String? tripName,
    String? invitedBy,
    DateTime? invitedAt,
    InvitationStatus? status,
    DateTime? respondedAt,
  }) {
    return TripInvitation(
      id: id ?? this.id,
      tripId: tripId ?? this.tripId,
      tripName: tripName ?? this.tripName,
      invitedBy: invitedBy ?? this.invitedBy,
      invitedAt: invitedAt ?? this.invitedAt,
      status: status ?? this.status,
      respondedAt: respondedAt ?? this.respondedAt,
    );
  }

  bool get isPending => status == InvitationStatus.pending;
  bool get isAccepted => status == InvitationStatus.accepted;
  bool get isDeclined => status == InvitationStatus.declined;
}

enum TripStatus {
  @JsonValue('planning')
  planning,
  @JsonValue('active')
  active,
  @JsonValue('completed')
  completed,
  @JsonValue('cancelled')
  cancelled,
}

enum TripVisibility {
  @JsonValue('private')
  private,
  @JsonValue('friends')
  friends,
  @JsonValue('public')
  public,
}

enum SuggestionStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('approved')
  approved,
  @JsonValue('rejected')
  rejected,
}

enum InvitationStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('accepted')
  accepted,
  @JsonValue('declined')
  declined,
  @JsonValue('expired')
  expired,
}

extension TripStatusExtension on TripStatus {
  String get displayName {
    switch (this) {
      case TripStatus.planning:
        return 'Plánování';
      case TripStatus.active:
        return 'Aktivní';
      case TripStatus.completed:
        return 'Dokončeno';
      case TripStatus.cancelled:
        return 'Zrušeno';
    }
  }

  String get icon {
    switch (this) {
      case TripStatus.planning:
        return '📋';
      case TripStatus.active:
        return '🚀';
      case TripStatus.completed:
        return '✅';
      case TripStatus.cancelled:
        return '❌';
    }
  }
}

extension TripVisibilityExtension on TripVisibility {
  String get displayName {
    switch (this) {
      case TripVisibility.private:
        return 'Soukromé';
      case TripVisibility.friends:
        return 'Přátelé';
      case TripVisibility.public:
        return 'Veřejné';
    }
  }

  String get icon {
    switch (this) {
      case TripVisibility.private:
        return '🔒';
      case TripVisibility.friends:
        return '👥';
      case TripVisibility.public:
        return '🌍';
    }
  }
}

extension SuggestionStatusExtension on SuggestionStatus {
  String get displayName {
    switch (this) {
      case SuggestionStatus.pending:
        return 'Čeká na schválení';
      case SuggestionStatus.approved:
        return 'Schváleno';
      case SuggestionStatus.rejected:
        return 'Zamítnuto';
    }
  }

  String get icon {
    switch (this) {
      case SuggestionStatus.pending:
        return '⏳';
      case SuggestionStatus.approved:
        return '✅';
      case SuggestionStatus.rejected:
        return '❌';
    }
  }
}

extension InvitationStatusExtension on InvitationStatus {
  String get displayName {
    switch (this) {
      case InvitationStatus.pending:
        return 'Čeká na odpověď';
      case InvitationStatus.accepted:
        return 'Přijato';
      case InvitationStatus.declined:
        return 'Odmítnuto';
      case InvitationStatus.expired:
        return 'Vypršelo';
    }
  }

  String get icon {
    switch (this) {
      case InvitationStatus.pending:
        return '⏳';
      case InvitationStatus.accepted:
        return '✅';
      case InvitationStatus.declined:
        return '❌';
      case InvitationStatus.expired:
        return '⏰';
    }
  }
}
