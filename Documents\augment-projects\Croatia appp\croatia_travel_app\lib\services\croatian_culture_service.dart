import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class CroatianCultureService {
  static final CroatianCultureService _instance =
      CroatianCultureService._internal();
  factory CroatianCultureService() => _instance;
  CroatianCultureService._internal();

  StreamController<CulturalEvent>? _eventController;

  /// Stream kulturních událostí
  Stream<CulturalEvent> get eventStream {
    _eventController ??= StreamController<CulturalEvent>.broadcast();
    return _eventController!.stream;
  }

  /// Získání tradičních receptů podle regionu
  Future<List<CroatianRecipe>> getTraditionalRecipes({
    String? region,
    RecipeCategory? category,
    DifficultyLevel? difficulty,
  }) async {
    final recipes = _getAllRecipes();

    return recipes.where((recipe) {
      if (region != null && recipe.region != region) return false;
      if (category != null && recipe.category != category) return false;
      if (difficulty != null && recipe.difficulty != difficulty) return false;
      return true;
    }).toList();
  }

  /// Získání historického kontextu pro lokaci
  Future<HistoricalContext?> getHistoricalContext({
    required double latitude,
    required double longitude,
    required String placeName,
  }) async {
    // Simulace získání historických dat
    await Future.delayed(const Duration(milliseconds: 500));

    final historicalSites = _getHistoricalSites();

    // Najít nejbližší historické místo
    for (final site in historicalSites) {
      final distance = _calculateDistance(
        latitude,
        longitude,
        site.latitude,
        site.longitude,
      );

      if (distance < 5.0) {
        // Do 5 km
        return HistoricalContext(
          site: site,
          distance: distance,
          relevantPeriods: site.periods,
          keyEvents: site.keyEvents,
          culturalSignificance: site.culturalSignificance,
          recommendedReading: site.recommendedReading,
        );
      }
    }

    return null;
  }

  /// Získání chorvatských frází pro cestování
  Future<List<CroatianPhrase>> getTravelPhrases({
    PhraseCategory? category,
    DifficultyLevel? level,
  }) async {
    final phrases = _getAllPhrases();

    return phrases.where((phrase) {
      if (category != null && phrase.category != category) return false;
      if (level != null && phrase.difficulty != level) return false;
      return true;
    }).toList();
  }

  /// Získání kulturních tipů pro aktuální lokaci
  Future<List<CulturalTip>> getCulturalTips({
    required String region,
    required DateTime date,
  }) async {
    final tips = <CulturalTip>[];

    // Sezónní tipy
    final seasonalTips = _getSeasonalTips(region, date);
    tips.addAll(seasonalTips);

    // Regionální tipy
    final regionalTips = _getRegionalTips(region);
    tips.addAll(regionalTips);

    // Aktuální festivaly a události
    final currentEvents = await _getCurrentEvents(region, date);
    tips.addAll(
      currentEvents.map(
        (event) => CulturalTip(
          title: 'Aktuální událost: ${event.title}',
          description: event.description,
          type: CulturalTipType.event,
          importance: TipImportance.high,
          region: region,
        ),
      ),
    );

    return tips;
  }

  /// Získání tradičních řemesel podle regionu
  Future<List<TraditionalCraft>> getTraditionalCrafts(String region) async {
    final crafts = _getAllCrafts();
    return crafts.where((craft) => craft.regions.contains(region)).toList();
  }

  /// Získání chorvatských legend a pověstí
  Future<List<CroatianLegend>> getLocalLegends({
    required double latitude,
    required double longitude,
    double radiusKm = 10.0,
  }) async {
    final legends = _getAllLegends();

    return legends.where((legend) {
      if (legend.latitude == null || legend.longitude == null) return false;

      final distance = _calculateDistance(
        latitude,
        longitude,
        legend.latitude!,
        legend.longitude!,
      );

      return distance <= radiusKm;
    }).toList();
  }

  /// Získání doporučení pro místní zážitky
  Future<List<LocalExperience>> getLocalExperiences({
    required String region,
    required DateTime date,
    ExperienceType? type,
  }) async {
    final experiences = _getAllExperiences();

    return experiences.where((exp) {
      if (exp.region != region) return false;
      if (type != null && exp.type != type) return false;
      if (!exp.isAvailableOn(date)) return false;
      return true;
    }).toList();
  }

  /// Učení chorvatštiny - denní lekce
  Future<CroatianLesson> getDailyLesson() async {
    final prefs = await SharedPreferences.getInstance();
    final lastLessonDate = prefs.getString('last_lesson_date');
    final today = DateTime.now().toIso8601String().split('T')[0];

    if (lastLessonDate != today) {
      await prefs.setString('last_lesson_date', today);

      final lessons = _getAllLessons();
      final lessonIndex = DateTime.now().day % lessons.length;

      _eventController?.add(
        CulturalEvent(
          type: CulturalEventType.newLesson,
          title: 'Nová denní lekce chorvatštiny',
          description: 'Naučte se něco nového!',
        ),
      );

      return lessons[lessonIndex];
    }

    // Vrátit poslední lekci
    final lessons = _getAllLessons();
    final lessonIndex = (DateTime.now().day - 1) % lessons.length;
    return lessons[lessonIndex];
  }

  /// Získání všech receptů
  List<CroatianRecipe> _getAllRecipes() {
    return [
      CroatianRecipe(
        id: '1',
        name: 'Peka',
        nameEn: 'Peka (Traditional Roast)',
        description: 'Tradiční chorvatské pečené maso s brambory pod pokličkou',
        region: 'dalmatia',
        category: RecipeCategory.mainCourse,
        difficulty: DifficultyLevel.medium,
        prepTime: const Duration(hours: 3),
        ingredients: [
          'Jehněčí nebo vepřové maso - 2 kg',
          'Brambory - 1 kg',
          'Cibule - 2 ks',
          'Rozmarýn, tymián',
          'Olivový olej',
          'Sůl, pepř',
        ],
        instructions: [
          'Maso nakrájejte na kusy a osolte',
          'Brambory oloupejte a nakrájejte',
          'Vše vložte do pekáče s olivovým olejem',
          'Přikryjte pokličkou a pečte 3 hodiny',
        ],
        culturalNote:
            'Peka je tradiční způsob vaření v Dalmácii, kde se jídlo peče pod železnou pokličkou zasypané žhavými uhlíky.',
        tips: [
          'Nejlepší je použít jehněčí maso',
          'Pokličku nezvedejte během pečení',
          'Podávejte s domácím chlebem',
        ],
      ),
      CroatianRecipe(
        id: '2',
        name: 'Strukli',
        nameEn: 'Strukli (Cottage Cheese Pastry)',
        description: 'Tradiční chorvatské těstoviny s tvarohem ze Zagorje',
        region: 'zagorje',
        category: RecipeCategory.dessert,
        difficulty: DifficultyLevel.hard,
        prepTime: const Duration(hours: 2),
        ingredients: [
          'Hladká mouka - 300g',
          'Vejce - 2 ks',
          'Tvaroh - 500g',
          'Smetana - 200ml',
          'Máslo - 100g',
          'Sůl, cukr',
        ],
        instructions: [
          'Připravte těsto z mouky, vajec a soli',
          'Vyválejte velmi tenké těsto',
          'Připravte náplň z tvarohu a smetany',
          'Zabalte a vařte v osolené vodě',
        ],
        culturalNote:
            'Strukli jsou chráněné UNESCO jako nehmotné kulturní dědictví Chorvatska.',
        tips: [
          'Těsto musí být velmi tenké',
          'Nerozbalujte příliš rychle',
          'Můžete podávat sladké i slané',
        ],
      ),
      CroatianRecipe(
        id: '3',
        name: 'Crni rižot',
        nameEn: 'Black Risotto',
        description:
            'Černé rizoto s chobotnicí, specialita dalmatského pobřeží',
        region: 'dalmatia',
        category: RecipeCategory.mainCourse,
        difficulty: DifficultyLevel.medium,
        prepTime: const Duration(minutes: 45),
        ingredients: [
          'Arborio rýže - 300g',
          'Chobotnice - 1 kg',
          'Cibule - 1 ks',
          'Česnek - 3 stroužky',
          'Bílé víno - 200ml',
          'Rybí vývar - 1l',
          'Olivový olej',
        ],
        instructions: [
          'Chobotnici uvařte a nakrájejte',
          'Osmažte cibuli a česnek',
          'Přidejte rýži a opražte',
          'Postupně přidávejte vývar a míchejte',
          'Na konci přidejte chobotnici a její inkoust',
        ],
        culturalNote:
            'Černé rizoto je symbol dalmatské kuchyně, černá barva pochází z inkoustu chobotnice.',
        tips: [
          'Používejte čerstvou chobotnici',
          'Rizoto musí být krémové',
          'Podávejte ihned po přípravě',
        ],
      ),
    ];
  }

  /// Získání historických míst
  List<HistoricalSite> _getHistoricalSites() {
    return [
      HistoricalSite(
        id: '1',
        name: 'Diokleciánův palác',
        nameEn: 'Diocletian\'s Palace',
        latitude: 43.5081,
        longitude: 16.4402,
        periods: [HistoricalPeriod.roman, HistoricalPeriod.medieval],
        keyEvents: [
          'Postaveno kolem roku 305 n.l. císařem Diokleciánem',
          'Sloužilo jako císařská rezidence',
          'Později se stalo centrem města Split',
        ],
        culturalSignificance:
            'Jeden z nejlépe zachovaných římských paláců na světě, UNESCO památka',
        recommendedReading: [
          'Diokleciánův palác - Jerko Marasović',
          'Split - město v paláci - Ivo Babić',
        ],
      ),
      HistoricalSite(
        id: '2',
        name: 'Dubrovník - Staré město',
        nameEn: 'Dubrovnik Old Town',
        latitude: 42.6407,
        longitude: 18.1077,
        periods: [HistoricalPeriod.medieval, HistoricalPeriod.renaissance],
        keyEvents: [
          'Založeno v 7. století',
          'Dubrovnická republika (1358-1808)',
          'Významné obchodní centrum Středomoří',
        ],
        culturalSignificance:
            'Perla Jaderského moře, UNESCO památka, symbol chorvatské nezávislosti',
        recommendedReading: [
          'Dubrovnická republika - Bariša Krekić',
          'Historie Dubrovníku - Robin Harris',
        ],
      ),
    ];
  }

  /// Získání všech frází
  List<CroatianPhrase> _getAllPhrases() {
    return [
      CroatianPhrase(
        id: '1',
        croatian: 'Dobar dan',
        english: 'Good day',
        czech: 'Dobrý den',
        pronunciation: 'DO-bar dan',
        category: PhraseCategory.greetings,
        difficulty: DifficultyLevel.easy,
        usage: 'Formální pozdrav používaný během dne',
        culturalNote:
            'Nejčastější pozdrav v Chorvatsku, používá se od rána do večera',
      ),
      CroatianPhrase(
        id: '2',
        croatian: 'Hvala lijepo',
        english: 'Thank you very much',
        czech: 'Děkuji pěkně',
        pronunciation: 'HVA-la li-YE-po',
        category: PhraseCategory.courtesy,
        difficulty: DifficultyLevel.easy,
        usage: 'Zdvořilé poděkování',
        culturalNote: 'Chorvaté oceňují zdvořilost, vždy se vyplatí poděkovat',
      ),
      CroatianPhrase(
        id: '3',
        croatian: 'Gdje je najbliža plaža?',
        english: 'Where is the nearest beach?',
        czech: 'Kde je nejbližší pláž?',
        pronunciation: 'GDJE ye NAY-bli-zha PLA-zha',
        category: PhraseCategory.directions,
        difficulty: DifficultyLevel.medium,
        usage: 'Dotaz na směr k pláži',
        culturalNote: 'Chorvatsko má přes 1000 ostrovů a nádherné pláže',
      ),
    ];
  }

  /// Získání všech lekcí
  List<CroatianLesson> _getAllLessons() {
    return [
      CroatianLesson(
        id: '1',
        title: 'Základní pozdravy',
        titleEn: 'Basic Greetings',
        level: DifficultyLevel.easy,
        phrases: [
          'Dobar dan - Dobrý den',
          'Dobro jutro - Dobré ráno',
          'Dobra večer - Dobrý večer',
          'Laku noć - Dobrou noc',
        ],
        grammar: 'Chorvatské pozdravy se mění podle denní doby',
        culturalTip: 'Chorvaté se rádi zdraví, i s neznámými lidmi',
        exercises: ['Jak pozdravíte ráno?', 'Jak se rozloučíte večer?'],
      ),
      CroatianLesson(
        id: '2',
        title: 'V restauraci',
        titleEn: 'At the Restaurant',
        level: DifficultyLevel.medium,
        phrases: [
          'Mogu li dobiti jelovnik? - Mohu dostat jídelní lístek?',
          'Što preporučujete? - Co doporučujete?',
          'Račun, molim - Účet, prosím',
        ],
        grammar: 'Používání zdvořilostních frází s "molim" (prosím)',
        culturalTip: 'V Chorvatsku se spropitné obvykle zaokrouhluje nahoru',
        exercises: ['Jak si objednáte jídlo?', 'Jak požádáte o účet?'],
      ),
    ];
  }

  /// Výpočet vzdálenosti mezi dvěma body
  double _calculateDistance(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    // Zjednodušený výpočet vzdálenosti (Haversine formula by byla přesnější)
    final deltaLat = (lat2 - lat1).abs();
    final deltaLon = (lon2 - lon1).abs();
    return (deltaLat + deltaLon) * 111; // Přibližně km
  }

  /// Získání sezónních tipů
  List<CulturalTip> _getSeasonalTips(String region, DateTime date) {
    final month = date.month;
    final tips = <CulturalTip>[];

    if (month >= 6 && month <= 8) {
      // Léto
      tips.add(
        CulturalTip(
          title: 'Letní festivaly',
          description: 'Léto je čas mnoha kulturních festivalů v Chorvatsku',
          type: CulturalTipType.event,
          importance: TipImportance.high,
          region: region,
        ),
      );
    }

    if (month >= 9 && month <= 11) {
      // Podzim
      tips.add(
        CulturalTip(
          title: 'Vinobraní',
          description:
              'Podzim je ideální čas pro návštěvu vinařství a degustace',
          type: CulturalTipType.food,
          importance: TipImportance.medium,
          region: region,
        ),
      );
    }

    return tips;
  }

  /// Získání regionálních tipů
  List<CulturalTip> _getRegionalTips(String region) {
    switch (region) {
      case 'dalmatia':
        return [
          CulturalTip(
            title: 'Dalmatská kuchyně',
            description: 'Vyzkoušejte čerstvé ryby a mořské plody',
            type: CulturalTipType.food,
            importance: TipImportance.high,
            region: region,
          ),
        ];
      case 'istria':
        return [
          CulturalTip(
            title: 'Istrské lanýže',
            description: 'Istrie je známá svými lanýži a kvalitními víny',
            type: CulturalTipType.food,
            importance: TipImportance.high,
            region: region,
          ),
        ];
      default:
        return [];
    }
  }

  /// Získání aktuálních událostí
  Future<List<CulturalEvent>> _getCurrentEvents(
    String region,
    DateTime date,
  ) async {
    // Simulace získání aktuálních událostí
    return [
      CulturalEvent(
        type: CulturalEventType.festival,
        title: 'Dubrovník Summer Festival',
        description: 'Prestižní kulturní festival',
      ),
    ];
  }

  /// Získání všech řemesel
  List<TraditionalCraft> _getAllCrafts() {
    return [
      TraditionalCraft(
        id: '1',
        name: 'Paška čipka',
        nameEn: 'Pag Lace',
        description: 'Tradiční krajka z ostrova Pag',
        regions: ['dalmatia'],
        difficulty: DifficultyLevel.hard,
        materials: ['Bavlněná nit', 'Jehla'],
        history: 'Tradice sahá do 15. století',
        whereToLearn: ['Pag - Muzeum krajky'],
        culturalSignificance: 'UNESCO nehmotné kulturní dědictví',
      ),
    ];
  }

  /// Získání všech legend
  List<CroatianLegend> _getAllLegends() {
    return [
      CroatianLegend(
        id: '1',
        title: 'Legenda o Zlatém rohu',
        titleEn: 'Legend of the Golden Horn',
        region: 'dalmatia',
        latitude: 43.2571,
        longitude: 16.6378,
        story: 'Příběh o tom, jak vznikla nejkrásnější pláž na Braču...',
        moralLesson: 'Příroda je nejkrásnější, když ji necháme být',
        relatedPlaces: ['Zlatný roh', 'Bol', 'Brač'],
      ),
    ];
  }

  /// Získání všech zážitků
  List<LocalExperience> _getAllExperiences() {
    return [
      LocalExperience(
        id: '1',
        name: 'Degustace vín v Istrii',
        nameEn: 'Wine Tasting in Istria',
        description: 'Návštěva tradičního vinařství s degustací',
        region: 'istria',
        type: ExperienceType.culinary,
        duration: const Duration(hours: 3),
        price: 45.0,
        currency: 'EUR',
        availableMonths: [4, 5, 6, 7, 8, 9, 10],
        culturalValue: 'Poznání istrské vinařské tradice',
        whatToExpected: [
          'Prohlídka vinařství',
          'Degustace 5 vín',
          'Místní sýry a olivy',
          'Příběh o vinařské tradici',
        ],
      ),
    ];
  }

  void dispose() {
    _eventController?.close();
  }
}

// Modely pro chorvatskou kulturu
class CroatianRecipe {
  final String id;
  final String name;
  final String nameEn;
  final String description;
  final String region;
  final RecipeCategory category;
  final DifficultyLevel difficulty;
  final Duration prepTime;
  final List<String> ingredients;
  final List<String> instructions;
  final String culturalNote;
  final List<String> tips;

  CroatianRecipe({
    required this.id,
    required this.name,
    required this.nameEn,
    required this.description,
    required this.region,
    required this.category,
    required this.difficulty,
    required this.prepTime,
    required this.ingredients,
    required this.instructions,
    required this.culturalNote,
    required this.tips,
  });
}

class HistoricalSite {
  final String id;
  final String name;
  final String nameEn;
  final double latitude;
  final double longitude;
  final List<HistoricalPeriod> periods;
  final List<String> keyEvents;
  final String culturalSignificance;
  final List<String> recommendedReading;

  HistoricalSite({
    required this.id,
    required this.name,
    required this.nameEn,
    required this.latitude,
    required this.longitude,
    required this.periods,
    required this.keyEvents,
    required this.culturalSignificance,
    required this.recommendedReading,
  });
}

class HistoricalContext {
  final HistoricalSite site;
  final double distance;
  final List<HistoricalPeriod> relevantPeriods;
  final List<String> keyEvents;
  final String culturalSignificance;
  final List<String> recommendedReading;

  HistoricalContext({
    required this.site,
    required this.distance,
    required this.relevantPeriods,
    required this.keyEvents,
    required this.culturalSignificance,
    required this.recommendedReading,
  });
}

class CroatianPhrase {
  final String id;
  final String croatian;
  final String english;
  final String czech;
  final String pronunciation;
  final PhraseCategory category;
  final DifficultyLevel difficulty;
  final String usage;
  final String culturalNote;

  CroatianPhrase({
    required this.id,
    required this.croatian,
    required this.english,
    required this.czech,
    required this.pronunciation,
    required this.category,
    required this.difficulty,
    required this.usage,
    required this.culturalNote,
  });
}

class CroatianLesson {
  final String id;
  final String title;
  final String titleEn;
  final DifficultyLevel level;
  final List<String> phrases;
  final String grammar;
  final String culturalTip;
  final List<String> exercises;

  CroatianLesson({
    required this.id,
    required this.title,
    required this.titleEn,
    required this.level,
    required this.phrases,
    required this.grammar,
    required this.culturalTip,
    required this.exercises,
  });
}

class CulturalTip {
  final String title;
  final String description;
  final CulturalTipType type;
  final TipImportance importance;
  final String region;

  CulturalTip({
    required this.title,
    required this.description,
    required this.type,
    required this.importance,
    required this.region,
  });
}

class TraditionalCraft {
  final String id;
  final String name;
  final String nameEn;
  final String description;
  final List<String> regions;
  final DifficultyLevel difficulty;
  final List<String> materials;
  final String history;
  final List<String> whereToLearn;
  final String culturalSignificance;

  TraditionalCraft({
    required this.id,
    required this.name,
    required this.nameEn,
    required this.description,
    required this.regions,
    required this.difficulty,
    required this.materials,
    required this.history,
    required this.whereToLearn,
    required this.culturalSignificance,
  });
}

class CroatianLegend {
  final String id;
  final String title;
  final String titleEn;
  final String region;
  final double? latitude;
  final double? longitude;
  final String story;
  final String moralLesson;
  final List<String> relatedPlaces;

  CroatianLegend({
    required this.id,
    required this.title,
    required this.titleEn,
    required this.region,
    this.latitude,
    this.longitude,
    required this.story,
    required this.moralLesson,
    required this.relatedPlaces,
  });
}

class LocalExperience {
  final String id;
  final String name;
  final String nameEn;
  final String description;
  final String region;
  final ExperienceType type;
  final Duration duration;
  final double price;
  final String currency;
  final List<int> availableMonths;
  final String culturalValue;
  final List<String> whatToExpected;

  LocalExperience({
    required this.id,
    required this.name,
    required this.nameEn,
    required this.description,
    required this.region,
    required this.type,
    required this.duration,
    required this.price,
    required this.currency,
    required this.availableMonths,
    required this.culturalValue,
    required this.whatToExpected,
  });

  bool isAvailableOn(DateTime date) {
    return availableMonths.contains(date.month);
  }
}

class CulturalEvent {
  final CulturalEventType type;
  final String title;
  final String description;

  CulturalEvent({
    required this.type,
    required this.title,
    required this.description,
  });
}

// Enums
enum RecipeCategory { appetizer, mainCourse, dessert, drink }

enum DifficultyLevel { easy, medium, hard }

enum HistoricalPeriod { prehistoric, roman, medieval, renaissance, modern }

enum PhraseCategory {
  greetings,
  courtesy,
  directions,
  food,
  emergency,
  shopping,
}

enum CulturalTipType { food, event, tradition, etiquette, history }

enum TipImportance { low, medium, high }

enum CulturalEventType { festival, newLesson, culturalTip, historicalFact }

enum ExperienceType { culinary, cultural, adventure, educational, artisan }
