import 'package:flutter/foundation.dart';
import '../models/thematic_package.dart';
import '../data/local_database.dart';

class ThematicPackagesService {
  static final ThematicPackagesService _instance =
      ThematicPackagesService._internal();
  factory ThematicPackagesService() => _instance;
  ThematicPackagesService._internal();

  final LocalDatabase _localDb = LocalDatabase();

  final Map<String, ThematicPackage> _packages = {};
  final Map<String, List<String>> _userProgress = {};

  /// Inicializace tematických balíčků
  Future<void> initialize() async {
    await _loadPredefinedPackages();
    await _loadUserProgress();
  }

  /// Načtení předdefinovaných balíčků
  Future<void> _loadPredefinedPackages() async {
    _packages.addAll({
      'gastronomic': ThematicPackage(
        id: 'gastronomic',
        name: 'Gastronomick<PERSON> balí<PERSON>',
        description: 'Objevte autentické chutě Chorvatska',
        icon: '🍽️',
        category: PackageCategory.gastronomy,
        difficulty: PackageDifficulty.easy,
        estimatedDuration: const Duration(days: 7),
        places: [
          'dubrovnik_old_town',
          'split_diocletian_palace',
          'rovinj_old_town',
          'zagreb_upper_town',
          'korcula_old_town',
        ],
        cuisineItems: [
          'peka',
          'cevapi',
          'strukli',
          'black_risotto',
          'pag_cheese',
          'istrian_truffle',
          'dalmatian_prosciutto',
        ],
        events: [
          'truffle_festival_istria',
          'wine_festival_korcula',
          'olive_oil_festival_brac',
        ],
        activities: [
          PackageActivity(
            id: 'cooking_class',
            name: 'Kurz vaření',
            description: 'Naučte se připravit tradiční chorvatské pokrmy',
            type: ActivityType.experience,
            duration: const Duration(hours: 3),
            location: 'Split',
            price: 150.0,
            difficulty: ActivityDifficulty.beginner,
          ),
          PackageActivity(
            id: 'wine_tasting',
            name: 'Degustace vín',
            description: 'Ochutnejte nejlepší chorvatská vína',
            type: ActivityType.tasting,
            duration: const Duration(hours: 2),
            location: 'Korčula',
            price: 80.0,
            difficulty: ActivityDifficulty.beginner,
          ),
          PackageActivity(
            id: 'market_tour',
            name: 'Prohlídka trhů',
            description: 'Objevte místní ingredience na tradičních trzích',
            type: ActivityType.tour,
            duration: const Duration(hours: 2),
            location: 'Zagreb',
            price: 40.0,
            difficulty: ActivityDifficulty.easy,
          ),
        ],
        rewards: [
          PackageReward(
            id: 'gastronomy_expert',
            name: 'Gastronomický expert',
            description: 'Dokončili jste gastronomický balíček',
            icon: '👨‍🍳',
            type: RewardType.badge,
          ),
          PackageReward(
            id: 'recipe_book',
            name: 'Kniha receptů',
            description: 'Digitální kniha chorvatských receptů',
            icon: '📖',
            type: RewardType.content,
          ),
        ],
        requirements: [
          PackageRequirement(
            type: RequirementType.visitPlaces,
            target: 3,
            description: 'Navštivte alespoň 3 gastronomická místa',
          ),
          PackageRequirement(
            type: RequirementType.tryCuisine,
            target: 5,
            description: 'Ochutnejte 5 tradičních pokrmů',
          ),
          PackageRequirement(
            type: RequirementType.attendEvent,
            target: 1,
            description: 'Zúčastněte se gastronomické události',
          ),
        ],
        tips: [
          'Nejlepší čas pro gastronomické zážitky je od května do října',
          'Rezervujte si místa v restauracích předem',
          'Zkuste místní vína - každý region má své speciality',
          'Navštivte farmářské trhy pro autentické ingredience',
        ],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      'historical': ThematicPackage(
        id: 'historical',
        name: 'Historický balíček',
        description: 'Prozkoumejte bohatou historii Chorvatska',
        icon: '🏛️',
        category: PackageCategory.history,
        difficulty: PackageDifficulty.medium,
        estimatedDuration: const Duration(days: 10),
        places: [
          'dubrovnik_walls',
          'diocletian_palace',
          'pula_arena',
          'euphrasian_basilica',
          'trogir_historic_center',
          'sibenik_cathedral',
        ],
        cuisineItems: [],
        events: [
          'dubrovnik_summer_festival',
          'medieval_festival_trogir',
          'roman_festival_pula',
        ],
        activities: [
          PackageActivity(
            id: 'guided_tour_dubrovnik',
            name: 'Průvodcovská prohlídka Dubrovníku',
            description: 'Profesionální průvodce vás provede historií města',
            type: ActivityType.tour,
            duration: const Duration(hours: 3),
            location: 'Dubrovník',
            price: 120.0,
            difficulty: ActivityDifficulty.easy,
          ),
          PackageActivity(
            id: 'archaeological_workshop',
            name: 'Archeologický workshop',
            description: 'Praktické zkušenosti s archeologickými nálezy',
            type: ActivityType.workshop,
            duration: const Duration(hours: 4),
            location: 'Pula',
            price: 200.0,
            difficulty: ActivityDifficulty.intermediate,
          ),
        ],
        rewards: [
          PackageReward(
            id: 'history_scholar',
            name: 'Historický badatel',
            description: 'Dokončili jste historický balíček',
            icon: '🎓',
            type: RewardType.badge,
          ),
          PackageReward(
            id: 'historical_map',
            name: 'Historická mapa',
            description: 'Interaktivní mapa historických míst',
            icon: '🗺️',
            type: RewardType.content,
          ),
        ],
        requirements: [
          PackageRequirement(
            type: RequirementType.visitPlaces,
            target: 4,
            description: 'Navštivte alespoň 4 historická místa',
          ),
          PackageRequirement(
            type: RequirementType.completeQuiz,
            target: 3,
            description: 'Dokončete 3 historické kvízy',
          ),
        ],
        tips: [
          'Stáhněte si audio průvodce pro lepší zážitek',
          'Navštivte muzea pro hlubší pochopení historie',
          'Nejlepší fotky pořídíte v ranních hodinách',
        ],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      'beach': ThematicPackage(
        id: 'beach',
        name: 'Plážový balíček',
        description: 'Užijte si nejkrásnější pláže Chorvatska',
        icon: '🏖️',
        category: PackageCategory.nature,
        difficulty: PackageDifficulty.easy,
        estimatedDuration: const Duration(days: 14),
        places: [
          'zlatni_rat_beach',
          'sakarun_beach',
          'stiniva_beach',
          'rajska_plaza_beach',
          'punta_rata_beach',
        ],
        cuisineItems: ['grilled_fish', 'seafood_risotto', 'octopus_salad'],
        events: ['beach_volleyball_tournament', 'summer_music_festival'],
        activities: [
          PackageActivity(
            id: 'snorkeling',
            name: 'Šnorchlování',
            description: 'Objevte podvodní svět Jaderského moře',
            type: ActivityType.sport,
            duration: const Duration(hours: 2),
            location: 'Vis',
            price: 60.0,
            difficulty: ActivityDifficulty.beginner,
          ),
          PackageActivity(
            id: 'kayaking',
            name: 'Kajaking',
            description: 'Prozkoumejte pobřeží z vody',
            type: ActivityType.sport,
            duration: const Duration(hours: 4),
            location: 'Hvar',
            price: 100.0,
            difficulty: ActivityDifficulty.intermediate,
          ),
          PackageActivity(
            id: 'sunset_cruise',
            name: 'Plavba za západem slunce',
            description: 'Romantická plavba s výhledem na západ slunce',
            type: ActivityType.cruise,
            duration: const Duration(hours: 3),
            location: 'Dubrovník',
            price: 150.0,
            difficulty: ActivityDifficulty.easy,
          ),
        ],
        rewards: [
          PackageReward(
            id: 'beach_explorer',
            name: 'Průzkumník pláží',
            description: 'Navštívili jste nejkrásnější pláže Chorvatska',
            icon: '🏄‍♂️',
            type: RewardType.badge,
          ),
        ],
        requirements: [
          PackageRequirement(
            type: RequirementType.visitPlaces,
            target: 3,
            description: 'Navštivte alespoň 3 pláže',
          ),
          PackageRequirement(
            type: RequirementType.completeActivity,
            target: 2,
            description: 'Dokončete 2 vodní aktivity',
          ),
        ],
        tips: [
          'Nejlepší čas pro pláže je od června do září',
          'Vezměte si opalovací krém s vysokým faktorem',
          'Některé pláže jsou dostupné pouze lodí',
          'Respektujte místní přírodu a neznečišťujte',
        ],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    });
  }

  /// Načtení pokroku uživatele
  Future<void> _loadUserProgress() async {
    // Simulace načtení z databáze
    _userProgress.addAll({
      'gastronomic': ['dubrovnik_old_town', 'peka'],
      'historical': ['dubrovnik_walls'],
      'beach': [],
    });
  }

  /// Získání všech balíčků
  List<ThematicPackage> getAllPackages() {
    return _packages.values.toList();
  }

  /// Získání balíčku podle ID
  ThematicPackage? getPackage(String packageId) {
    return _packages[packageId];
  }

  /// Získání balíčků podle kategorie
  List<ThematicPackage> getPackagesByCategory(PackageCategory category) {
    return _packages.values
        .where((package) => package.category == category)
        .toList();
  }

  /// Získání doporučených balíčků pro uživatele
  List<ThematicPackage> getRecommendedPackages({
    List<String>? visitedPlaces,
    List<String>? preferences,
    PackageDifficulty? maxDifficulty,
  }) {
    var packages = _packages.values.toList();

    // Filtrování podle obtížnosti
    if (maxDifficulty != null) {
      packages = packages
          .where((p) => p.difficulty.index <= maxDifficulty.index)
          .toList();
    }

    // Seřazení podle relevance
    packages.sort((a, b) {
      int scoreA = _calculateRelevanceScore(a, visitedPlaces, preferences);
      int scoreB = _calculateRelevanceScore(b, visitedPlaces, preferences);
      return scoreB.compareTo(scoreA);
    });

    return packages;
  }

  /// Výpočet skóre relevance balíčku
  int _calculateRelevanceScore(
    ThematicPackage package,
    List<String>? visitedPlaces,
    List<String>? preferences,
  ) {
    int score = 0;

    // Bonus za navštívená místa v balíčku
    if (visitedPlaces != null) {
      final commonPlaces = package.places
          .where((place) => visitedPlaces.contains(place))
          .length;
      score += commonPlaces * 10;
    }

    // Bonus za preference
    if (preferences != null) {
      if (preferences.contains(package.category.name)) {
        score += 50;
      }
    }

    // Malus za vysokou obtížnost
    score -= package.difficulty.index * 5;

    return score;
  }

  /// Spuštění balíčku
  Future<void> startPackage(String packageId) async {
    final package = _packages[packageId];
    if (package == null) {
      throw Exception('Balíček nebyl nalezen: $packageId');
    }

    // Inicializace pokroku
    _userProgress[packageId] = [];

    // Uložení do databáze
    await _localDb.savePackageProgress({
      'packageId': packageId,
      'progress': [],
    });

    if (kDebugMode) {
      print('Spuštěn balíček: ${package.name}');
    }
  }

  /// Označení položky jako dokončené
  Future<void> markItemCompleted(String packageId, String itemId) async {
    final progress = _userProgress[packageId] ?? [];

    if (!progress.contains(itemId)) {
      progress.add(itemId);
      _userProgress[packageId] = progress;

      // Uložení do databáze
      await _localDb.savePackageProgress({
        'packageId': packageId,
        'progress': progress,
      });

      // Kontrola dokončení balíčku
      await _checkPackageCompletion(packageId);
    }
  }

  /// Kontrola dokončení balíčku
  Future<void> _checkPackageCompletion(String packageId) async {
    final package = _packages[packageId];
    final progress = _userProgress[packageId] ?? [];

    if (package == null) return;

    bool isCompleted = true;

    for (final requirement in package.requirements) {
      if (!_isRequirementMet(requirement, progress, package)) {
        isCompleted = false;
        break;
      }
    }

    if (isCompleted) {
      await _completePackage(packageId);
    }
  }

  /// Kontrola splnění požadavku
  bool _isRequirementMet(
    PackageRequirement requirement,
    List<String> progress,
    ThematicPackage package,
  ) {
    switch (requirement.type) {
      case RequirementType.visitPlaces:
        final visitedPlaces = progress
            .where((item) => package.places.contains(item))
            .length;
        return visitedPlaces >= requirement.target;

      case RequirementType.tryCuisine:
        final triedCuisine = progress
            .where((item) => package.cuisineItems.contains(item))
            .length;
        return triedCuisine >= requirement.target;

      case RequirementType.attendEvent:
        final attendedEvents = progress
            .where((item) => package.events.contains(item))
            .length;
        return attendedEvents >= requirement.target;

      case RequirementType.completeActivity:
        final completedActivities = progress
            .where(
              (item) =>
                  package.activities.any((activity) => activity.id == item),
            )
            .length;
        return completedActivities >= requirement.target;

      case RequirementType.completeQuiz:
        // Implementace kvízů
        return true; // Simulace

      case RequirementType.spendTime:
        // Implementace času stráveného
        return true; // Simulace

      case RequirementType.takePhotos:
        // Implementace pořízených fotek
        return true; // Simulace
    }
  }

  /// Dokončení balíčku
  Future<void> _completePackage(String packageId) async {
    final package = _packages[packageId];
    if (package == null) return;

    // Udělení odměn
    for (final reward in package.rewards) {
      await _grantReward(packageId, reward);
    }

    if (kDebugMode) {
      print('Balíček dokončen: ${package.name}');
    }
  }

  /// Udělení odměny
  Future<void> _grantReward(String packageId, PackageReward reward) async {
    // Uložení odměny do databáze
    await _localDb.saveReward({
      'packageId': packageId,
      'reward': reward.toJson(),
    });

    if (kDebugMode) {
      print('Udělena odměna: ${reward.name}');
    }
  }

  /// Získání pokroku balíčku
  PackageProgress getPackageProgress(String packageId) {
    final package = _packages[packageId];
    final progress = _userProgress[packageId] ?? [];

    if (package == null) {
      return PackageProgress(
        packageId: packageId,
        completedItems: [],
        totalItems: 0,
        completionPercentage: 0.0,
        isStarted: false,
        isCompleted: false,
        completedRequirements: [],
      );
    }

    final totalItems =
        package.places.length +
        package.cuisineItems.length +
        package.events.length +
        package.activities.length;

    final completionPercentage = totalItems > 0
        ? (progress.length / totalItems * 100).clamp(0.0, 100.0)
        : 0.0;

    final completedRequirements = package.requirements
        .where((req) => _isRequirementMet(req, progress, package))
        .toList();

    return PackageProgress(
      packageId: packageId,
      completedItems: progress,
      totalItems: totalItems,
      completionPercentage: completionPercentage,
      isStarted: progress.isNotEmpty,
      isCompleted: completedRequirements.length == package.requirements.length,
      completedRequirements: completedRequirements,
    );
  }

  /// Vyhledání balíčků
  List<ThematicPackage> searchPackages(String query) {
    final lowerQuery = query.toLowerCase();
    return _packages.values
        .where(
          (package) =>
              package.name.toLowerCase().contains(lowerQuery) ||
              package.description.toLowerCase().contains(lowerQuery),
        )
        .toList();
  }

  /// Vytvoření vlastního balíčku
  Future<ThematicPackage> createCustomPackage({
    required String name,
    required String description,
    required List<String> places,
    List<String>? cuisineItems,
    List<String>? events,
    List<PackageActivity>? activities,
  }) async {
    final packageId = DateTime.now().millisecondsSinceEpoch.toString();

    final package = ThematicPackage(
      id: packageId,
      name: name,
      description: description,
      icon: '📦',
      category: PackageCategory.custom,
      difficulty: PackageDifficulty.medium,
      estimatedDuration: Duration(days: places.length),
      places: places,
      cuisineItems: cuisineItems ?? [],
      events: events ?? [],
      activities: activities ?? [],
      rewards: [
        PackageReward(
          id: '${packageId}_completion',
          name: 'Vlastní balíček',
          description: 'Dokončili jste vlastní balíček',
          icon: '🎯',
          type: RewardType.badge,
        ),
      ],
      requirements: [
        PackageRequirement(
          type: RequirementType.visitPlaces,
          target: (places.length * 0.8).ceil(),
          description: 'Navštivte většinu míst v balíčku',
        ),
      ],
      tips: [],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    _packages[packageId] = package;
    await _localDb.saveCustomPackage({
      'id': package.id,
      'name': package.name,
      'description': package.description,
    });

    return package;
  }
}
