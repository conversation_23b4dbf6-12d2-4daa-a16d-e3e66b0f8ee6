// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'parking.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ParkingSpot _$ParkingSpotFromJson(Map<String, dynamic> json) => ParkingSpot(
      id: json['id'] as String,
      name: json['name'] as String,
      address: json['address'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      type: $enumDecode(_$ParkingTypeEnumMap, json['type']),
      totalSpaces: (json['totalSpaces'] as num).toInt(),
      availableSpaces: (json['availableSpaces'] as num).toInt(),
      rates: (json['rates'] as List<dynamic>)
          .map((e) => ParkingRate.fromJson(e as Map<String, dynamic>))
          .toList(),
      paymentMethods: (json['paymentMethods'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      features: (json['features'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      description: json['description'] as String?,
      restrictions: (json['restrictions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      isActive: json['isActive'] as bool? ?? true,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      maxHeight: (json['maxHeight'] as num?)?.toDouble(),
      operatorName: json['operatorName'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
    );

Map<String, dynamic> _$ParkingSpotToJson(ParkingSpot instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'address': instance.address,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'type': _$ParkingTypeEnumMap[instance.type]!,
      'totalSpaces': instance.totalSpaces,
      'availableSpaces': instance.availableSpaces,
      'rates': instance.rates,
      'paymentMethods': instance.paymentMethods,
      'features': instance.features,
      'description': instance.description,
      'restrictions': instance.restrictions,
      'isActive': instance.isActive,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'maxHeight': instance.maxHeight,
      'operatorName': instance.operatorName,
      'phoneNumber': instance.phoneNumber,
    };

const _$ParkingTypeEnumMap = {
  ParkingType.street: 'street',
  ParkingType.garage: 'garage',
  ParkingType.lot: 'lot',
  ParkingType.private: 'private',
  ParkingType.residential: 'residential',
};

ParkingRate _$ParkingRateFromJson(Map<String, dynamic> json) => ParkingRate(
      id: json['id'] as String,
      name: json['name'] as String,
      type: $enumDecode(_$ParkingRateTypeEnumMap, json['type']),
      price: (json['price'] as num).toDouble(),
      currency: json['currency'] as String,
      duration: json['duration'] == null
          ? null
          : Duration(microseconds: (json['duration'] as num).toInt()),
      maxDuration: (json['maxDuration'] as num?)?.toInt(),
      validDays: (json['validDays'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      startTime: json['startTime'] as String?,
      endTime: json['endTime'] as String?,
      isActive: json['isActive'] as bool? ?? true,
    );

Map<String, dynamic> _$ParkingRateToJson(ParkingRate instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': _$ParkingRateTypeEnumMap[instance.type]!,
      'price': instance.price,
      'currency': instance.currency,
      'duration': instance.duration?.inMicroseconds,
      'maxDuration': instance.maxDuration,
      'validDays': instance.validDays,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'isActive': instance.isActive,
    };

const _$ParkingRateTypeEnumMap = {
  ParkingRateType.hourly: 'hourly',
  ParkingRateType.daily: 'daily',
  ParkingRateType.fixed: 'fixed',
  ParkingRateType.perMinute: 'perMinute',
};

ParkingSession _$ParkingSessionFromJson(Map<String, dynamic> json) =>
    ParkingSession(
      id: json['id'] as String,
      parkingSpotId: json['parkingSpotId'] as String,
      userId: json['userId'] as String?,
      licensePlate: json['licensePlate'] as String,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: json['endTime'] == null
          ? null
          : DateTime.parse(json['endTime'] as String),
      plannedDuration: json['plannedDuration'] == null
          ? null
          : Duration(microseconds: (json['plannedDuration'] as num).toInt()),
      status: $enumDecode(_$ParkingSessionStatusEnumMap, json['status']),
      totalCost: (json['totalCost'] as num?)?.toDouble(),
      currency: json['currency'] as String?,
      paymentId: json['paymentId'] as String?,
      extensions: (json['extensions'] as List<dynamic>?)
              ?.map((e) => ParkingExtension.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      qrCode: json['qrCode'] as String?,
    );

Map<String, dynamic> _$ParkingSessionToJson(ParkingSession instance) =>
    <String, dynamic>{
      'id': instance.id,
      'parkingSpotId': instance.parkingSpotId,
      'userId': instance.userId,
      'licensePlate': instance.licensePlate,
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime?.toIso8601String(),
      'plannedDuration': instance.plannedDuration?.inMicroseconds,
      'status': _$ParkingSessionStatusEnumMap[instance.status]!,
      'totalCost': instance.totalCost,
      'currency': instance.currency,
      'paymentId': instance.paymentId,
      'extensions': instance.extensions,
      'qrCode': instance.qrCode,
    };

const _$ParkingSessionStatusEnumMap = {
  ParkingSessionStatus.active: 'active',
  ParkingSessionStatus.completed: 'completed',
  ParkingSessionStatus.cancelled: 'cancelled',
  ParkingSessionStatus.expired: 'expired',
  ParkingSessionStatus.pending: 'pending',
};

ParkingExtension _$ParkingExtensionFromJson(Map<String, dynamic> json) =>
    ParkingExtension(
      id: json['id'] as String,
      additionalTime:
          Duration(microseconds: (json['additionalTime'] as num).toInt()),
      cost: (json['cost'] as num).toDouble(),
      currency: json['currency'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      paymentId: json['paymentId'] as String?,
    );

Map<String, dynamic> _$ParkingExtensionToJson(ParkingExtension instance) =>
    <String, dynamic>{
      'id': instance.id,
      'additionalTime': instance.additionalTime.inMicroseconds,
      'cost': instance.cost,
      'currency': instance.currency,
      'timestamp': instance.timestamp.toIso8601String(),
      'paymentId': instance.paymentId,
    };

SharedVehicle _$SharedVehicleFromJson(Map<String, dynamic> json) =>
    SharedVehicle(
      id: json['id'] as String,
      type: $enumDecode(_$SharedVehicleTypeEnumMap, json['type']),
      brand: json['brand'] as String,
      model: json['model'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      batteryLevel: (json['batteryLevel'] as num).toInt(),
      status: $enumDecode(_$SharedVehicleStatusEnumMap, json['status']),
      pricePerMinute: (json['pricePerMinute'] as num).toDouble(),
      unlockFee: (json['unlockFee'] as num).toDouble(),
      currency: json['currency'] as String,
      operatorName: json['operatorName'] as String?,
      operatorLogo: json['operatorLogo'] as String?,
      features: (json['features'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$SharedVehicleToJson(SharedVehicle instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$SharedVehicleTypeEnumMap[instance.type]!,
      'brand': instance.brand,
      'model': instance.model,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'batteryLevel': instance.batteryLevel,
      'status': _$SharedVehicleStatusEnumMap[instance.status]!,
      'pricePerMinute': instance.pricePerMinute,
      'unlockFee': instance.unlockFee,
      'currency': instance.currency,
      'operatorName': instance.operatorName,
      'operatorLogo': instance.operatorLogo,
      'features': instance.features,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };

const _$SharedVehicleTypeEnumMap = {
  SharedVehicleType.bike: 'bike',
  SharedVehicleType.eBike: 'eBike',
  SharedVehicleType.scooter: 'scooter',
  SharedVehicleType.eScooter: 'eScooter',
  SharedVehicleType.car: 'car',
  SharedVehicleType.eCar: 'eCar',
};

const _$SharedVehicleStatusEnumMap = {
  SharedVehicleStatus.available: 'available',
  SharedVehicleStatus.reserved: 'reserved',
  SharedVehicleStatus.inUse: 'inUse',
  SharedVehicleStatus.maintenance: 'maintenance',
  SharedVehicleStatus.lowBattery: 'lowBattery',
  SharedVehicleStatus.outOfService: 'outOfService',
};

SharedVehicleRental _$SharedVehicleRentalFromJson(Map<String, dynamic> json) =>
    SharedVehicleRental(
      id: json['id'] as String,
      vehicleId: json['vehicleId'] as String,
      userId: json['userId'] as String?,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: json['endTime'] == null
          ? null
          : DateTime.parse(json['endTime'] as String),
      startLatitude: (json['startLatitude'] as num).toDouble(),
      startLongitude: (json['startLongitude'] as num).toDouble(),
      endLatitude: (json['endLatitude'] as num?)?.toDouble(),
      endLongitude: (json['endLongitude'] as num?)?.toDouble(),
      distance: (json['distance'] as num?)?.toDouble(),
      totalCost: (json['totalCost'] as num?)?.toDouble(),
      currency: json['currency'] as String?,
      status: $enumDecode(_$SharedVehicleRentalStatusEnumMap, json['status']),
      paymentId: json['paymentId'] as String?,
    );

Map<String, dynamic> _$SharedVehicleRentalToJson(
        SharedVehicleRental instance) =>
    <String, dynamic>{
      'id': instance.id,
      'vehicleId': instance.vehicleId,
      'userId': instance.userId,
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime?.toIso8601String(),
      'startLatitude': instance.startLatitude,
      'startLongitude': instance.startLongitude,
      'endLatitude': instance.endLatitude,
      'endLongitude': instance.endLongitude,
      'distance': instance.distance,
      'totalCost': instance.totalCost,
      'currency': instance.currency,
      'status': _$SharedVehicleRentalStatusEnumMap[instance.status]!,
      'paymentId': instance.paymentId,
    };

const _$SharedVehicleRentalStatusEnumMap = {
  SharedVehicleRentalStatus.active: 'active',
  SharedVehicleRentalStatus.completed: 'completed',
  SharedVehicleRentalStatus.cancelled: 'cancelled',
  SharedVehicleRentalStatus.paused: 'paused',
};

ParkingPrediction _$ParkingPredictionFromJson(Map<String, dynamic> json) =>
    ParkingPrediction(
      spotId: json['spotId'] as String,
      targetTime: DateTime.parse(json['targetTime'] as String),
      availabilityProbability:
          (json['availabilityProbability'] as num).toDouble(),
      predictedAvailableSpaces:
          (json['predictedAvailableSpaces'] as num).toInt(),
      confidenceLevel: (json['confidenceLevel'] as num).toDouble(),
      factors: (json['factors'] as List<dynamic>)
          .map((e) => PredictionFactor.fromJson(e as Map<String, dynamic>))
          .toList(),
      generatedAt: DateTime.parse(json['generatedAt'] as String),
    );

Map<String, dynamic> _$ParkingPredictionToJson(ParkingPrediction instance) =>
    <String, dynamic>{
      'spotId': instance.spotId,
      'targetTime': instance.targetTime.toIso8601String(),
      'availabilityProbability': instance.availabilityProbability,
      'predictedAvailableSpaces': instance.predictedAvailableSpaces,
      'confidenceLevel': instance.confidenceLevel,
      'factors': instance.factors,
      'generatedAt': instance.generatedAt.toIso8601String(),
    };

PredictionFactor _$PredictionFactorFromJson(Map<String, dynamic> json) =>
    PredictionFactor(
      name: json['name'] as String,
      impact: (json['impact'] as num).toDouble(),
      description: json['description'] as String,
    );

Map<String, dynamic> _$PredictionFactorToJson(PredictionFactor instance) =>
    <String, dynamic>{
      'name': instance.name,
      'impact': instance.impact,
      'description': instance.description,
    };

SmartReservation _$SmartReservationFromJson(Map<String, dynamic> json) =>
    SmartReservation(
      id: json['id'] as String,
      spotId: json['spotId'] as String,
      userId: json['userId'] as String,
      reservationTime: DateTime.parse(json['reservationTime'] as String),
      arrivalTime: DateTime.parse(json['arrivalTime'] as String),
      plannedDuration:
          Duration(microseconds: (json['plannedDuration'] as num).toInt()),
      totalCost: (json['totalCost'] as num).toDouble(),
      currency: json['currency'] as String,
      walkingDistance: (json['walkingDistance'] as num).toDouble(),
      walkingTime: Duration(microseconds: (json['walkingTime'] as num).toInt()),
      status: $enumDecode(_$SmartReservationStatusEnumMap, json['status']),
      optimizationReasons: (json['optimizationReasons'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      savingsAmount: (json['savingsAmount'] as num).toDouble(),
      expiresAt: DateTime.parse(json['expiresAt'] as String),
    );

Map<String, dynamic> _$SmartReservationToJson(SmartReservation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'spotId': instance.spotId,
      'userId': instance.userId,
      'reservationTime': instance.reservationTime.toIso8601String(),
      'arrivalTime': instance.arrivalTime.toIso8601String(),
      'plannedDuration': instance.plannedDuration.inMicroseconds,
      'totalCost': instance.totalCost,
      'currency': instance.currency,
      'walkingDistance': instance.walkingDistance,
      'walkingTime': instance.walkingTime.inMicroseconds,
      'status': _$SmartReservationStatusEnumMap[instance.status]!,
      'optimizationReasons': instance.optimizationReasons,
      'savingsAmount': instance.savingsAmount,
      'expiresAt': instance.expiresAt.toIso8601String(),
    };

const _$SmartReservationStatusEnumMap = {
  SmartReservationStatus.active: 'active',
  SmartReservationStatus.confirmed: 'confirmed',
  SmartReservationStatus.cancelled: 'cancelled',
  SmartReservationStatus.expired: 'expired',
  SmartReservationStatus.completed: 'completed',
};

DynamicPricing _$DynamicPricingFromJson(Map<String, dynamic> json) =>
    DynamicPricing(
      spotId: json['spotId'] as String,
      timeSlot: DateTime.parse(json['timeSlot'] as String),
      basePrice: (json['basePrice'] as num).toDouble(),
      currentPrice: (json['currentPrice'] as num).toDouble(),
      currency: json['currency'] as String,
      demandMultiplier: (json['demandMultiplier'] as num).toDouble(),
      factors: (json['factors'] as List<dynamic>)
          .map((e) => PricingFactor.fromJson(e as Map<String, dynamic>))
          .toList(),
      validUntil: DateTime.parse(json['validUntil'] as String),
    );

Map<String, dynamic> _$DynamicPricingToJson(DynamicPricing instance) =>
    <String, dynamic>{
      'spotId': instance.spotId,
      'timeSlot': instance.timeSlot.toIso8601String(),
      'basePrice': instance.basePrice,
      'currentPrice': instance.currentPrice,
      'currency': instance.currency,
      'demandMultiplier': instance.demandMultiplier,
      'factors': instance.factors,
      'validUntil': instance.validUntil.toIso8601String(),
    };

PricingFactor _$PricingFactorFromJson(Map<String, dynamic> json) =>
    PricingFactor(
      name: json['name'] as String,
      multiplier: (json['multiplier'] as num).toDouble(),
      description: json['description'] as String,
    );

Map<String, dynamic> _$PricingFactorToJson(PricingFactor instance) =>
    <String, dynamic>{
      'name': instance.name,
      'multiplier': instance.multiplier,
      'description': instance.description,
    };
