import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/b2b_sales.dart';

/// 💼 B2B SALES SERVICE - B2B prodejní program pro immediate revenue
class B2BSalesService {
  static final B2BSalesService _instance = B2BSalesService._internal();
  factory B2BSalesService() => _instance;
  B2BSalesService._internal();

  bool _isInitialized = false;
  final List<B2BLead> _leads = [];
  final List<B2BProposal> _proposals = [];
  final List<B2BContract> _contracts = [];
  final List<B2BClient> _clients = [];
  final Map<String, SalesMetrics> _salesMetrics = {};
  final StreamController<SalesEvent> _eventController = StreamController.broadcast();

  /// Stream prodejních událostí
  Stream<SalesEvent> get salesEvents => _eventController.stream;

  /// Inicializace služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('💼 Inicializuji B2B Sales Service...');
      
      await _loadSalesData();
      await _generateMockLeads();
      await _initializeSalesTargets();
      
      _isInitialized = true;
      debugPrint('✅ B2B Sales Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci B2B Sales: $e');
      await _generateMockLeads();
      _isInitialized = true;
    }
  }

  /// Vytvoření nového leadu
  Future<B2BLead> createLead({
    required String companyName,
    required String contactPerson,
    required String email,
    required String phone,
    required B2BSegment segment,
    required LeadSource source,
    String? notes,
    Map<String, dynamic>? customData,
  }) async {
    try {
      final lead = B2BLead(
        id: 'lead_${DateTime.now().millisecondsSinceEpoch}',
        companyName: companyName,
        contactPerson: contactPerson,
        email: email,
        phone: phone,
        segment: segment,
        source: source,
        status: LeadStatus.new_,
        score: _calculateLeadScore(segment, source),
        createdAt: DateTime.now(),
        lastContactAt: null,
        notes: notes ?? '',
        customData: customData ?? {},
      );

      _leads.add(lead);
      await _saveLeads();

      _eventController.add(SalesEvent(
        type: SalesEventType.leadCreated,
        leadId: lead.id,
        timestamp: DateTime.now(),
        data: {'segment': segment.name, 'source': source.name},
      ));

      debugPrint('🎯 Nový lead vytvořen: ${lead.companyName}');
      return lead;
    } catch (e) {
      debugPrint('❌ Chyba při vytváření leadu: $e');
      rethrow;
    }
  }

  /// Kvalifikace leadu
  Future<bool> qualifyLead({
    required String leadId,
    required int budget,
    required int employeeCount,
    required bool hasDecisionMaker,
    required UrgencyLevel urgency,
    String? qualificationNotes,
  }) async {
    try {
      final leadIndex = _leads.indexWhere((l) => l.id == leadId);
      if (leadIndex == -1) return false;

      final lead = _leads[leadIndex];
      final qualification = LeadQualification(
        budget: budget,
        employeeCount: employeeCount,
        hasDecisionMaker: hasDecisionMaker,
        urgency: urgency,
        qualifiedAt: DateTime.now(),
        qualifiedBy: 'current_user',
        notes: qualificationNotes ?? '',
      );

      final isQualified = _isLeadQualified(qualification);
      final newStatus = isQualified ? LeadStatus.qualified : LeadStatus.disqualified;

      _leads[leadIndex] = lead.copyWith(
        status: newStatus,
        qualification: qualification,
        score: _recalculateLeadScore(lead, qualification),
        lastContactAt: DateTime.now(),
      );

      await _saveLeads();

      _eventController.add(SalesEvent(
        type: isQualified ? SalesEventType.leadQualified : SalesEventType.leadDisqualified,
        leadId: leadId,
        timestamp: DateTime.now(),
        data: {'budget': budget, 'urgency': urgency.name},
      ));

      debugPrint('✅ Lead ${isQualified ? "kvalifikován" : "diskvalifikován"}: ${lead.companyName}');
      return isQualified;
    } catch (e) {
      debugPrint('❌ Chyba při kvalifikaci leadu: $e');
      return false;
    }
  }

  /// Vytvoření návrhu/proposal
  Future<B2BProposal> createProposal({
    required String leadId,
    required B2BPackage package,
    required double customPrice,
    required int contractLength,
    List<String>? customFeatures,
    String? notes,
  }) async {
    try {
      final lead = _leads.firstWhere((l) => l.id == leadId);
      
      final proposal = B2BProposal(
        id: 'proposal_${DateTime.now().millisecondsSinceEpoch}',
        leadId: leadId,
        companyName: lead.companyName,
        package: package,
        customPrice: customPrice,
        originalPrice: package.monthlyPrice,
        discount: _calculateDiscount(package.monthlyPrice, customPrice),
        contractLength: contractLength,
        customFeatures: customFeatures ?? [],
        status: ProposalStatus.draft,
        createdAt: DateTime.now(),
        validUntil: DateTime.now().add(const Duration(days: 30)),
        notes: notes ?? '',
      );

      _proposals.add(proposal);
      await _saveProposals();

      // Aktualizace leadu
      final leadIndex = _leads.indexWhere((l) => l.id == leadId);
      _leads[leadIndex] = lead.copyWith(
        status: LeadStatus.proposal,
        lastContactAt: DateTime.now(),
      );
      await _saveLeads();

      _eventController.add(SalesEvent(
        type: SalesEventType.proposalCreated,
        leadId: leadId,
        proposalId: proposal.id,
        timestamp: DateTime.now(),
        data: {'package': package.name, 'price': customPrice},
      ));

      debugPrint('📋 Návrh vytvořen pro: ${lead.companyName}');
      return proposal;
    } catch (e) {
      debugPrint('❌ Chyba při vytváření návrhu: $e');
      rethrow;
    }
  }

  /// Odeslání návrhu klientovi
  Future<bool> sendProposal({
    required String proposalId,
    String? emailTemplate,
    List<String>? attachments,
  }) async {
    try {
      final proposalIndex = _proposals.indexWhere((p) => p.id == proposalId);
      if (proposalIndex == -1) return false;

      final proposal = _proposals[proposalIndex];
      
      // Simulace odeslání emailu
      await _sendProposalEmail(proposal, emailTemplate, attachments);

      _proposals[proposalIndex] = proposal.copyWith(
        status: ProposalStatus.sent,
        sentAt: DateTime.now(),
      );
      await _saveProposals();

      _eventController.add(SalesEvent(
        type: SalesEventType.proposalSent,
        proposalId: proposalId,
        timestamp: DateTime.now(),
        data: {'method': 'email'},
      ));

      debugPrint('📧 Návrh odeslán: ${proposal.companyName}');
      return true;
    } catch (e) {
      debugPrint('❌ Chyba při odesílání návrhu: $e');
      return false;
    }
  }

  /// Uzavření obchodu
  Future<B2BContract?> closeDeal({
    required String proposalId,
    required DateTime startDate,
    required PaymentTerms paymentTerms,
    String? contractNotes,
    List<String>? additionalTerms,
  }) async {
    try {
      final proposal = _proposals.firstWhere((p) => p.id == proposalId);
      final lead = _leads.firstWhere((l) => l.id == proposal.leadId);

      final contract = B2BContract(
        id: 'contract_${DateTime.now().millisecondsSinceEpoch}',
        proposalId: proposalId,
        leadId: proposal.leadId,
        companyName: proposal.companyName,
        package: proposal.package,
        monthlyPrice: proposal.customPrice,
        contractLength: proposal.contractLength,
        totalValue: proposal.customPrice * proposal.contractLength,
        startDate: startDate,
        endDate: startDate.add(Duration(days: proposal.contractLength * 30)),
        paymentTerms: paymentTerms,
        status: ContractStatus.active,
        signedAt: DateTime.now(),
        notes: contractNotes ?? '',
        additionalTerms: additionalTerms ?? [],
      );

      _contracts.add(contract);
      await _saveContracts();

      // Vytvoření B2B klienta
      final client = B2BClient(
        id: 'client_${DateTime.now().millisecondsSinceEpoch}',
        companyName: proposal.companyName,
        contactPerson: lead.contactPerson,
        email: lead.email,
        phone: lead.phone,
        segment: lead.segment,
        package: proposal.package,
        monthlyRevenue: proposal.customPrice,
        contractId: contract.id,
        onboardedAt: DateTime.now(),
        isActive: true,
        healthScore: 100,
      );

      _clients.add(client);
      await _saveClients();

      // Aktualizace statusů
      final proposalIndex = _proposals.indexWhere((p) => p.id == proposalId);
      _proposals[proposalIndex] = proposal.copyWith(status: ProposalStatus.accepted);

      final leadIndex = _leads.indexWhere((l) => l.id == proposal.leadId);
      _leads[leadIndex] = lead.copyWith(status: LeadStatus.closed);

      await _saveProposals();
      await _saveLeads();

      _eventController.add(SalesEvent(
        type: SalesEventType.dealClosed,
        leadId: proposal.leadId,
        proposalId: proposalId,
        contractId: contract.id,
        timestamp: DateTime.now(),
        data: {'value': contract.totalValue, 'package': proposal.package.name},
      ));

      debugPrint('🎉 Obchod uzavřen: ${proposal.companyName} - ${contract.totalValue}€');
      return contract;
    } catch (e) {
      debugPrint('❌ Chyba při uzavírání obchodu: $e');
      return null;
    }
  }

  /// Získání sales dashboardu
  Future<SalesDashboard> getSalesDashboard({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    final now = DateTime.now();
    final from = fromDate ?? DateTime(now.year, now.month, 1);
    final to = toDate ?? now;

    // Filtrování dat podle období
    final periodLeads = _leads.where((l) => 
      l.createdAt.isAfter(from.subtract(const Duration(days: 1))) &&
      l.createdAt.isBefore(to.add(const Duration(days: 1)))
    ).toList();

    final periodContracts = _contracts.where((c) => 
      c.signedAt.isAfter(from.subtract(const Duration(days: 1))) &&
      c.signedAt.isBefore(to.add(const Duration(days: 1)))
    ).toList();

    // Výpočet metrik
    final totalRevenue = periodContracts.fold<double>(0, (sum, c) => sum + c.totalValue);
    final monthlyRecurringRevenue = _clients
        .where((c) => c.isActive)
        .fold<double>(0, (sum, c) => sum + c.monthlyRevenue);

    final conversionRate = periodLeads.isNotEmpty 
        ? periodContracts.length / periodLeads.length
        : 0.0;

    final averageDealSize = periodContracts.isNotEmpty
        ? totalRevenue / periodContracts.length
        : 0.0;

    final salesCycle = _calculateAverageSalesCycle(periodContracts);

    return SalesDashboard(
      periodStart: from,
      periodEnd: to,
      totalLeads: periodLeads.length,
      qualifiedLeads: periodLeads.where((l) => l.status == LeadStatus.qualified).length,
      proposalsSent: _proposals.where((p) => p.status == ProposalStatus.sent).length,
      dealsClosed: periodContracts.length,
      totalRevenue: totalRevenue,
      monthlyRecurringRevenue: monthlyRecurringRevenue,
      conversionRate: conversionRate,
      averageDealSize: averageDealSize,
      averageSalesCycle: salesCycle,
      pipelineValue: _calculatePipelineValue(),
      leadsBySource: _getLeadsBySource(periodLeads),
      revenueBySegment: _getRevenueBySegment(periodContracts),
      salesForecast: _generateSalesForecast(),
    );
  }

  /// Generování sales reportu
  Future<SalesReport> generateSalesReport({
    required ReportPeriod period,
    required List<SalesMetricType> metrics,
  }) async {
    final now = DateTime.now();
    DateTime fromDate;
    DateTime toDate = now;

    switch (period) {
      case ReportPeriod.thisMonth:
        fromDate = DateTime(now.year, now.month, 1);
        break;
      case ReportPeriod.lastMonth:
        fromDate = DateTime(now.year, now.month - 1, 1);
        toDate = DateTime(now.year, now.month, 1).subtract(const Duration(days: 1));
        break;
      case ReportPeriod.thisQuarter:
        final quarterStart = ((now.month - 1) ~/ 3) * 3 + 1;
        fromDate = DateTime(now.year, quarterStart, 1);
        break;
      case ReportPeriod.thisYear:
        fromDate = DateTime(now.year, 1, 1);
        break;
    }

    final dashboard = await getSalesDashboard(fromDate: fromDate, toDate: toDate);
    
    return SalesReport(
      id: 'report_${DateTime.now().millisecondsSinceEpoch}',
      period: period,
      fromDate: fromDate,
      toDate: toDate,
      metrics: metrics,
      dashboard: dashboard,
      insights: _generateSalesInsights(dashboard),
      recommendations: _generateSalesRecommendations(dashboard),
      generatedAt: DateTime.now(),
    );
  }

  /// Automatické lead scoring
  int _calculateLeadScore(B2BSegment segment, LeadSource source) {
    int score = 50; // Base score

    // Segment scoring
    switch (segment) {
      case B2BSegment.tourismBoard:
        score += 30;
        break;
      case B2BSegment.travelAgency:
        score += 25;
        break;
      case B2BSegment.hotel:
        score += 20;
        break;
      case B2BSegment.restaurant:
        score += 15;
        break;
      case B2BSegment.eventOrganizer:
        score += 20;
        break;
      case B2BSegment.enterprise:
        score += 25;
        break;
    }

    // Source scoring
    switch (source) {
      case LeadSource.referral:
        score += 20;
        break;
      case LeadSource.inbound:
        score += 15;
        break;
      case LeadSource.coldOutreach:
        score += 5;
        break;
      case LeadSource.event:
        score += 10;
        break;
      case LeadSource.partnership:
        score += 25;
        break;
      case LeadSource.website:
        score += 10;
        break;
    }

    return score.clamp(0, 100);
  }

  int _recalculateLeadScore(B2BLead lead, LeadQualification qualification) {
    int score = lead.score;

    // Budget scoring
    if (qualification.budget >= 10000) score += 20;
    else if (qualification.budget >= 5000) score += 15;
    else if (qualification.budget >= 2000) score += 10;
    else if (qualification.budget >= 1000) score += 5;

    // Company size scoring
    if (qualification.employeeCount >= 500) score += 15;
    else if (qualification.employeeCount >= 100) score += 10;
    else if (qualification.employeeCount >= 50) score += 5;

    // Decision maker
    if (qualification.hasDecisionMaker) score += 15;

    // Urgency
    switch (qualification.urgency) {
      case UrgencyLevel.high:
        score += 20;
        break;
      case UrgencyLevel.medium:
        score += 10;
        break;
      case UrgencyLevel.low:
        score += 0;
        break;
    }

    return score.clamp(0, 100);
  }

  bool _isLeadQualified(LeadQualification qualification) {
    return qualification.budget >= 1000 &&
           qualification.employeeCount >= 10 &&
           qualification.hasDecisionMaker;
  }

  double _calculateDiscount(double originalPrice, double customPrice) {
    return ((originalPrice - customPrice) / originalPrice * 100).clamp(0, 100);
  }

  Duration _calculateAverageSalesCycle(List<B2BContract> contracts) {
    if (contracts.isEmpty) return Duration.zero;

    final cycles = contracts.map((contract) {
      final lead = _leads.firstWhere((l) => l.id == contract.leadId);
      return contract.signedAt.difference(lead.createdAt);
    }).toList();

    final totalDays = cycles.fold<int>(0, (sum, cycle) => sum + cycle.inDays);
    return Duration(days: (totalDays / cycles.length).round());
  }

  double _calculatePipelineValue() {
    return _proposals
        .where((p) => p.status == ProposalStatus.sent)
        .fold<double>(0, (sum, p) => sum + (p.customPrice * p.contractLength));
  }

  Map<String, int> _getLeadsBySource(List<B2BLead> leads) {
    final bySource = <String, int>{};
    for (final lead in leads) {
      bySource[lead.source.displayName] = (bySource[lead.source.displayName] ?? 0) + 1;
    }
    return bySource;
  }

  Map<String, double> _getRevenueBySegment(List<B2BContract> contracts) {
    final bySegment = <String, double>{};
    for (final contract in contracts) {
      final lead = _leads.firstWhere((l) => l.id == contract.leadId);
      bySegment[lead.segment.displayName] = 
          (bySegment[lead.segment.displayName] ?? 0) + contract.totalValue;
    }
    return bySegment;
  }

  List<SalesForecast> _generateSalesForecast() {
    final forecasts = <SalesForecast>[];
    final now = DateTime.now();
    
    for (int month = 1; month <= 6; month++) {
      final forecastDate = DateTime(now.year, now.month + month, 1);
      final baseRevenue = 15000.0; // Base monthly target
      final growth = 1.15; // 15% monthly growth
      final projectedRevenue = baseRevenue * pow(growth, month);
      
      forecasts.add(SalesForecast(
        month: forecastDate,
        projectedRevenue: projectedRevenue,
        confidence: 0.85 - (month * 0.05), // Decreasing confidence
        factors: ['Seasonal trends', 'Pipeline health', 'Market conditions'],
      ));
    }
    
    return forecasts;
  }

  List<String> _generateSalesInsights(SalesDashboard dashboard) {
    final insights = <String>[];
    
    if (dashboard.conversionRate > 0.2) {
      insights.add('Výborná konverzní míra ${(dashboard.conversionRate * 100).toStringAsFixed(1)}%');
    } else if (dashboard.conversionRate < 0.1) {
      insights.add('Nízká konverzní míra - potřeba zlepšit kvalifikaci leadů');
    }
    
    if (dashboard.averageDealSize > 5000) {
      insights.add('Vysoká průměrná hodnota obchodu - zaměřte se na enterprise klienty');
    }
    
    if (dashboard.averageSalesCycle.inDays > 60) {
      insights.add('Dlouhý sales cycle - optimalizujte proces');
    }
    
    return insights;
  }

  List<String> _generateSalesRecommendations(SalesDashboard dashboard) {
    final recommendations = <String>[];
    
    if (dashboard.qualifiedLeads < dashboard.totalLeads * 0.3) {
      recommendations.add('Zlepšete lead qualification process');
    }
    
    if (dashboard.pipelineValue < dashboard.monthlyRecurringRevenue * 3) {
      recommendations.add('Zvyšte aktivitu v generování leadů');
    }
    
    recommendations.addAll([
      'Implementujte automated follow-up sekvence',
      'Vytvořte case studies pro každý segment',
      'Optimalizujte pricing strategy na základě dat',
    ]);
    
    return recommendations;
  }

  /// Simulace odeslání emailu
  Future<void> _sendProposalEmail(
    B2BProposal proposal,
    String? template,
    List<String>? attachments,
  ) async {
    // Simulace email delivery
    await Future.delayed(const Duration(seconds: 1));
    debugPrint('📧 Email odeslán: ${proposal.companyName}');
  }

  /// Načítání a ukládání dat
  Future<void> _loadSalesData() async {
    // Načtení z SharedPreferences
  }

  Future<void> _saveLeads() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'b2b_leads',
        jsonEncode(_leads.map((l) => l.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání leadů: $e');
    }
  }

  Future<void> _saveProposals() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'b2b_proposals',
        jsonEncode(_proposals.map((p) => p.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání návrhů: $e');
    }
  }

  Future<void> _saveContracts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'b2b_contracts',
        jsonEncode(_contracts.map((c) => c.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání smluv: $e');
    }
  }

  Future<void> _saveClients() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'b2b_clients',
        jsonEncode(_clients.map((c) => c.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání klientů: $e');
    }
  }

  Future<void> _generateMockLeads() async {
    // Generování mock leadů pro demo
    final mockLeads = [
      await createLead(
        companyName: 'Croatian Tourism Board',
        contactPerson: 'Ana Marić',
        email: '<EMAIL>',
        phone: '+385 1 234 5678',
        segment: B2BSegment.tourismBoard,
        source: LeadSource.inbound,
        notes: 'Zájem o enterprise řešení pro promoci Chorvatska',
      ),
      await createLead(
        companyName: 'Adriatic Travel Agency',
        contactPerson: 'Marko Novak',
        email: '<EMAIL>',
        phone: '+385 21 345 678',
        segment: B2BSegment.travelAgency,
        source: LeadSource.referral,
        notes: 'Doporučení od stávajícího klienta',
      ),
    ];
  }

  Future<void> _initializeSalesTargets() async {
    // Inicializace sales cílů
  }

  @override
  void dispose() {
    _eventController.close();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  List<B2BLead> get leads => List.unmodifiable(_leads);
  List<B2BProposal> get proposals => List.unmodifiable(_proposals);
  List<B2BContract> get contracts => List.unmodifiable(_contracts);
  List<B2BClient> get clients => List.unmodifiable(_clients);
}
