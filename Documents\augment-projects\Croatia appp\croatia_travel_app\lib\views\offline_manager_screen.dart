import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../models/offline_data.dart';
import '../services/offline_manager_service.dart';

/// Obrazovka pro správu offline dat
class OfflineManagerScreen extends StatefulWidget {
  const OfflineManagerScreen({super.key});

  @override
  State<OfflineManagerScreen> createState() => _OfflineManagerScreenState();
}

class _OfflineManagerScreenState extends State<OfflineManagerScreen> {
  final OfflineManagerService _offlineManager = OfflineManagerService();
  bool _isLoading = true;
  OfflineStats? _stats;

  @override
  void initState() {
    super.initState();
    _initializeOfflineManager();
  }

  Future<void> _initializeOfflineManager() async {
    try {
      await _offlineManager.initialize();
      _stats = await _offlineManager.getOfflineStats();
    } catch (e) {
      debugPrint('Chyba při inicializaci offline manageru: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: SafeArea(
        child: Column(
          children: [
            _buildAppBar(),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _buildContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back_ios, color: Color(0xFF2C3E50)),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Offline režim',
                  style: GoogleFonts.playfairDisplay(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF2C3E50),
                  ),
                ),
                Text(
                  'Správa offline dat',
                  style: GoogleFonts.sourceSerif4(
                    fontSize: 16,
                    color: const Color(0xFF7F8C8D),
                  ),
                ),
              ],
            ),
          ),
          _buildConnectionStatus(),
        ],
      ),
    );
  }

  Widget _buildConnectionStatus() {
    return ListenableBuilder(
      listenable: _offlineManager,
      builder: (context, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: _offlineManager.isOnline
                ? const Color(0xFF27AE60).withValues(alpha: 0.1)
                : const Color(0xFFE74C3C).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: _offlineManager.isOnline
                  ? const Color(0xFF27AE60)
                  : const Color(0xFFE74C3C),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _offlineManager.isOnline ? Icons.wifi : Icons.wifi_off,
                size: 16,
                color: _offlineManager.isOnline
                    ? const Color(0xFF27AE60)
                    : const Color(0xFFE74C3C),
              ),
              const SizedBox(width: 6),
              Text(
                _offlineManager.isOnline ? 'Online' : 'Offline',
                style: GoogleFonts.sourceSerif4(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: _offlineManager.isOnline
                      ? const Color(0xFF27AE60)
                      : const Color(0xFFE74C3C),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatsCard(),
          const SizedBox(height: 24),
          _buildDownloadProgress(),
          const SizedBox(height: 24),
          _buildPackagesList(),
          const SizedBox(height: 24),
          _buildQuickActions(),
        ],
      ),
    );
  }

  Widget _buildStatsCard() {
    if (_stats == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Přehled offline dat',
            style: GoogleFonts.playfairDisplay(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Stažené balíčky',
                  '${_stats!.downloadedPackages}/${_stats!.totalPackages}',
                  Icons.download_done,
                  const Color(0xFF27AE60),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatItem(
                  'Velikost dat',
                  _stats!.downloadedSizeDescription,
                  Icons.storage,
                  const Color(0xFF3498DB),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          LinearProgressIndicator(
            value: _stats!.downloadProgress,
            backgroundColor: const Color(0xFFECF0F1),
            valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFF27AE60)),
          ),
          const SizedBox(height: 8),
          Text(
            '${(_stats!.downloadProgress * 100).toInt()}% dat staženo',
            style: GoogleFonts.sourceSerif4(
              fontSize: 14,
              color: const Color(0xFF7F8C8D),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 20, color: color),
            const SizedBox(width: 8),
            Text(
              label,
              style: GoogleFonts.sourceSerif4(
                fontSize: 14,
                color: const Color(0xFF7F8C8D),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.sourceSerif4(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2C3E50),
          ),
        ),
      ],
    );
  }

  Widget _buildDownloadProgress() {
    return ListenableBuilder(
      listenable: _offlineManager,
      builder: (context, child) {
        if (!_offlineManager.isDownloading) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: const Color(0xFF3498DB).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: const Color(0xFF3498DB).withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Stahování...',
                    style: GoogleFonts.playfairDisplay(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF2C3E50),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              LinearProgressIndicator(
                value: _offlineManager.downloadProgress,
                backgroundColor: const Color(0xFFECF0F1),
                valueColor: const AlwaysStoppedAnimation<Color>(
                  Color(0xFF3498DB),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _offlineManager.downloadStatus,
                style: GoogleFonts.sourceSerif4(
                  fontSize: 14,
                  color: const Color(0xFF7F8C8D),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPackagesList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Dostupné balíčky',
          style: GoogleFonts.playfairDisplay(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 16),
        ListenableBuilder(
          listenable: _offlineManager,
          builder: (context, child) {
            return Column(
              children: _offlineManager.packages
                  .map((package) => _buildPackageCard(package))
                  .toList(),
            );
          },
        ),
      ],
    );
  }

  Widget _buildPackageCard(OfflineDataPackage package) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              _buildPackageIcon(package.type),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      package.name,
                      style: GoogleFonts.sourceSerif4(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF2C3E50),
                      ),
                    ),
                    Text(
                      package.description,
                      style: GoogleFonts.sourceSerif4(
                        fontSize: 14,
                        color: const Color(0xFF7F8C8D),
                      ),
                    ),
                  ],
                ),
              ),
              _buildPackageStatus(package),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Text(
                package.sizeDescription,
                style: GoogleFonts.sourceSerif4(
                  fontSize: 12,
                  color: const Color(0xFF7F8C8D),
                ),
              ),
              const Spacer(),
              _buildPackageActions(package),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPackageIcon(OfflineDataType type) {
    IconData icon;
    Color color;

    switch (type) {
      case OfflineDataType.places:
        icon = Icons.place;
        color = const Color(0xFF3498DB);
        break;
      case OfflineDataType.tickets:
        icon = Icons.confirmation_number;
        color = const Color(0xFF4CAF50);
        break;
      case OfflineDataType.aiResponses:
        icon = Icons.smart_toy;
        color = const Color(0xFF9C27B0);
        break;
      case OfflineDataType.emergency:
        icon = Icons.emergency;
        color = const Color(0xFFE74C3C);
        break;
      default:
        icon = Icons.folder;
        color = const Color(0xFF95A5A6);
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(icon, color: color, size: 24),
    );
  }

  Widget _buildPackageStatus(OfflineDataPackage package) {
    Color color;
    IconData icon;

    switch (package.status) {
      case OfflineDataStatus.downloaded:
        color = const Color(0xFF27AE60);
        icon = Icons.check_circle;
        break;
      case OfflineDataStatus.downloading:
        color = const Color(0xFF3498DB);
        icon = Icons.download;
        break;
      case OfflineDataStatus.outdated:
        color = const Color(0xFFF39C12);
        icon = Icons.update;
        break;
      case OfflineDataStatus.error:
        color = const Color(0xFFE74C3C);
        icon = Icons.error;
        break;
      default:
        color = const Color(0xFF95A5A6);
        icon = Icons.cloud_download;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            package.statusDescription,
            style: GoogleFonts.sourceSerif4(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPackageActions(OfflineDataPackage package) {
    switch (package.status) {
      case OfflineDataStatus.notDownloaded:
      case OfflineDataStatus.outdated:
      case OfflineDataStatus.error:
        return ElevatedButton.icon(
          onPressed: _offlineManager.isOnline && !_offlineManager.isDownloading
              ? () => _downloadPackage(package.id)
              : null,
          icon: const Icon(Icons.download, size: 16),
          label: const Text('Stáhnout'),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF3498DB),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            textStyle: GoogleFonts.sourceSerif4(fontSize: 12),
          ),
        );
      case OfflineDataStatus.downloaded:
        return ElevatedButton.icon(
          onPressed: () => _deletePackage(package.id),
          icon: const Icon(Icons.delete, size: 16),
          label: const Text('Smazat'),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFE74C3C),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            textStyle: GoogleFonts.sourceSerif4(fontSize: 12),
          ),
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Rychlé akce',
          style: GoogleFonts.playfairDisplay(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed:
                    _offlineManager.isOnline && !_offlineManager.isDownloading
                    ? _downloadAllPackages
                    : null,
                icon: const Icon(Icons.download_for_offline),
                label: const Text('Stáhnout vše'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF27AE60),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  textStyle: GoogleFonts.sourceSerif4(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _clearAllData,
                icon: const Icon(Icons.clear_all),
                label: const Text('Vymazat vše'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFE74C3C),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  textStyle: GoogleFonts.sourceSerif4(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Future<void> _downloadPackage(String packageId) async {
    try {
      await _offlineManager.downloadPackage(packageId);
      _stats = await _offlineManager.getOfflineStats();
      setState(() {});

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Balíček byl úspěšně stažen'),
            backgroundColor: Color(0xFF27AE60),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Chyba při stahování: $e'),
            backgroundColor: const Color(0xFFE74C3C),
          ),
        );
      }
    }
  }

  Future<void> _downloadAllPackages() async {
    try {
      await _offlineManager.downloadAllPackages();
      _stats = await _offlineManager.getOfflineStats();
      setState(() {});

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Všechny balíčky byly staženy'),
            backgroundColor: Color(0xFF27AE60),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Chyba při stahování: $e'),
            backgroundColor: const Color(0xFFE74C3C),
          ),
        );
      }
    }
  }

  Future<void> _deletePackage(String packageId) async {
    try {
      await _offlineManager.deletePackage(packageId);
      _stats = await _offlineManager.getOfflineStats();
      setState(() {});

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Balíček byl smazán'),
            backgroundColor: Color(0xFF3498DB),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Chyba při mazání: $e'),
            backgroundColor: const Color(0xFFE74C3C),
          ),
        );
      }
    }
  }

  Future<void> _clearAllData() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Vymazat všechna data?'),
        content: const Text(
          'Tato akce vymaže všechna offline data. Budete je muset stáhnout znovu.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Zrušit'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Vymazat'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _offlineManager.clearAllOfflineData();
        _stats = await _offlineManager.getOfflineStats();
        setState(() {});

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Všechna offline data byla vymazána'),
              backgroundColor: Color(0xFF3498DB),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Chyba při mazání: $e'),
              backgroundColor: const Color(0xFFE74C3C),
            ),
          );
        }
      }
    }
  }
}
