import 'dart:math';
import 'package:json_annotation/json_annotation.dart';

part 'accommodation.g.dart';

@JsonSerializable()
class Accommodation {
  final String id;
  final String name;
  final String description;
  final String address;
  final double latitude;
  final double longitude;
  final String region;
  final AccommodationType accommodationType;
  final String priceRange;
  final double rating;
  final int reviewCount;
  final List<String> amenities;
  final String phone;
  final String? website;
  final String? email;
  final bool hasWifi;
  final bool hasParking;
  final bool hasPool;
  final bool hasRestaurant;
  final bool hasAirConditioning;
  final bool hasSeaView;
  final bool hasPetPolicy;
  final bool hasAccessibility;
  final int? roomCount;
  final String? checkInTime;
  final String? checkOutTime;
  final List<String> photos;
  final bool isActive;
  final DateTime lastUpdated;

  Accommodation({
    required this.id,
    required this.name,
    required this.description,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.region,
    required this.accommodationType,
    required this.priceRange,
    required this.rating,
    required this.reviewCount,
    required this.amenities,
    required this.phone,
    this.website,
    this.email,
    required this.hasWifi,
    required this.hasParking,
    required this.hasPool,
    required this.hasRestaurant,
    required this.hasAirConditioning,
    required this.hasSeaView,
    required this.hasPetPolicy,
    required this.hasAccessibility,
    this.roomCount,
    this.checkInTime,
    this.checkOutTime,
    required this.photos,
    required this.isActive,
    required this.lastUpdated,
  });

  /// Má ubytování vysoké hodnocení (4.5+)
  bool get isTopRated => rating >= 4.5;

  /// Je vhodné pro rodiny
  bool get isFamilyFriendly =>
      hasPool && hasWifi && roomCount != null && roomCount! >= 2;

  /// Má luxusní vybavení
  bool get isLuxury =>
      hasPool && hasRestaurant && hasSeaView && priceRange == 'luxury';

  /// Je blízko moře (má výhled na moře)
  bool get isNearSea => hasSeaView;

  /// Má kompletní vybavení
  bool get isFullyEquipped =>
      hasWifi && hasParking && hasAirConditioning && hasRestaurant;

  /// Vzdálenost od zadané pozice (v km)
  double distanceFrom(double lat, double lng) {
    // Haversine formula pro výpočet vzdálenosti
    const double earthRadius = 6371; // km

    final double dLat = _toRadians(latitude - lat);
    final double dLng = _toRadians(longitude - lng);

    final double a =
        sin(dLat / 2) * sin(dLat / 2) +
        cos(_toRadians(lat)) *
            cos(_toRadians(latitude)) *
            sin(dLng / 2) *
            sin(dLng / 2);

    final double c = 2 * asin(sqrt(a));

    return earthRadius * c;
  }

  double _toRadians(double degrees) {
    return degrees * (pi / 180);
  }

  factory Accommodation.fromJson(Map<String, dynamic> json) =>
      _$AccommodationFromJson(json);
  Map<String, dynamic> toJson() => _$AccommodationToJson(this);
}

/// Typy ubytování
enum AccommodationType {
  hotel, // Hotel
  apartment, // Apartmán
  villa, // Vila
  guesthouse, // Penzion
  hostel, // Hostel
  camping, // Kemp
  resort, // Resort
  boutique, // Boutique hotel
}

/// Regiony Chorvatska pro ubytování
enum AccommodationRegion {
  istria, // Istrie
  kvarner, // Kvarner
  dalmatia, // Dalmácie
  dubrovnik, // Dubrovník
  zagreb, // Zagreb
  slavonia, // Slavonie
}

/// Cenové kategorie
enum AccommodationPriceRange {
  budget, // € (do 300 HRK/noc)
  mid, // €€ (300-600 HRK/noc)
  upscale, // €€€ (600-1200 HRK/noc)
  luxury, // €€€€ (1200+ HRK/noc)
}

/// Vybavení ubytování
enum AccommodationAmenity {
  wifi, // WiFi
  parking, // Parkování
  pool, // Bazén
  restaurant, // Restaurace
  airConditioning, // Klimatizace
  seaView, // Výhled na moře
  balcony, // Balkon
  kitchen, // Kuchyňka
  laundry, // Prádelna
  gym, // Fitness
  spa, // Spa
  petFriendly, // Pet friendly
  accessibility, // Bezbariérový
  breakfast, // Snídaně
  roomService, // Room service
  concierge, // Concierge
  businessCenter, // Business centrum
  conference, // Konferenční sál
}

/// Hodnocení ubytování od uživatele
@JsonSerializable()
class AccommodationReview {
  final String id;
  final String accommodationId;
  final String userId;
  final String userName;
  final double rating;
  final String comment;
  final List<String> photos;
  final DateTime stayDate;
  final int nightsStayed;
  final String roomType;
  final List<String> likedAmenities;
  final List<String> dislikedAspects;
  final bool wouldRecommend;
  final DateTime createdAt;

  AccommodationReview({
    required this.id,
    required this.accommodationId,
    required this.userId,
    required this.userName,
    required this.rating,
    required this.comment,
    required this.photos,
    required this.stayDate,
    required this.nightsStayed,
    required this.roomType,
    required this.likedAmenities,
    required this.dislikedAspects,
    required this.wouldRecommend,
    required this.createdAt,
  });

  factory AccommodationReview.fromJson(Map<String, dynamic> json) =>
      _$AccommodationReviewFromJson(json);
  Map<String, dynamic> toJson() => _$AccommodationReviewToJson(this);
}

/// Oblíbené ubytování uživatele
@JsonSerializable()
class FavoriteAccommodation {
  final String id;
  final String userId;
  final String accommodationId;
  final DateTime addedAt;
  final String? notes;
  final List<String> preferredRoomTypes;

  FavoriteAccommodation({
    required this.id,
    required this.userId,
    required this.accommodationId,
    required this.addedAt,
    this.notes,
    required this.preferredRoomTypes,
  });

  factory FavoriteAccommodation.fromJson(Map<String, dynamic> json) =>
      _$FavoriteAccommodationFromJson(json);
  Map<String, dynamic> toJson() => _$FavoriteAccommodationToJson(this);
}

/// Pobyt v ubytování
@JsonSerializable()
class AccommodationStay {
  final String id;
  final String userId;
  final String accommodationId;
  final DateTime checkInDate;
  final DateTime checkOutDate;
  final int nightsStayed;
  final String roomType;
  final double? userRating;
  final String? notes;
  final List<String> photos;
  final double? totalCost;
  final List<String> usedAmenities;

  AccommodationStay({
    required this.id,
    required this.userId,
    required this.accommodationId,
    required this.checkInDate,
    required this.checkOutDate,
    required this.nightsStayed,
    required this.roomType,
    this.userRating,
    this.notes,
    required this.photos,
    this.totalCost,
    required this.usedAmenities,
  });

  /// Délka pobytu v nocích
  int get duration => checkOutDate.difference(checkInDate).inDays;

  /// Průměrná cena za noc
  double? get averagePricePerNight =>
      totalCost != null ? totalCost! / nightsStayed : null;

  factory AccommodationStay.fromJson(Map<String, dynamic> json) =>
      _$AccommodationStayFromJson(json);
  Map<String, dynamic> toJson() => _$AccommodationStayToJson(this);
}

/// Typ pokoje
@JsonSerializable()
class RoomType {
  final String id;
  final String accommodationId;
  final String name;
  final String description;
  final int capacity;
  final int bedCount;
  final String bedType;
  final double size; // m²
  final List<String> amenities;
  final double pricePerNight;
  final String currency;
  final bool isAvailable;

  RoomType({
    required this.id,
    required this.accommodationId,
    required this.name,
    required this.description,
    required this.capacity,
    required this.bedCount,
    required this.bedType,
    required this.size,
    required this.amenities,
    required this.pricePerNight,
    required this.currency,
    required this.isAvailable,
  });

  /// Je pokoj vhodný pro rodiny
  bool get isFamilyRoom => capacity >= 4;

  /// Je pokoj luxusní
  bool get isLuxury => size >= 40 && amenities.contains('seaView');

  factory RoomType.fromJson(Map<String, dynamic> json) =>
      _$RoomTypeFromJson(json);
  Map<String, dynamic> toJson() => _$RoomTypeToJson(this);
}
