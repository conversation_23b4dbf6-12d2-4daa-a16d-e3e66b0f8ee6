import 'package:flutter_test/flutter_test.dart';
import 'package:croatia_travel_app/models/ai_assistant.dart';

void main() {
  group('AI Assistant Model Tests', () {
    test('Conversation context', () {
      final context = ConversationContext(
        currentLocation: 'Dubrovník',
        latitude: 42.6507,
        longitude: 18.0944,
        userPreferences: ['restaurace', 'památky'],
        sessionData: {},
      );

      expect(context.hasLocation, isTrue);
      expect(context.currentLocation, equals('Dubrovník'));
      expect(context.userPreferences, contains('restaurace'));
    });

    test('AI Message creation', () {
      final message = AIMessage(
        id: '123',
        conversationId: 'conv_1',
        sender: MessageSender.user,
        content: 'Test zpráva',
        type: MessageType.text,
        timestamp: DateTime.now(),
        isRead: true,
      );

      expect(message.isFromUser, isTrue);
      expect(message.isFromAI, isFalse);
      expect(message.content, equals('Test zpráva'));
    });

    test('AI Recommendation model', () {
      final recommendation = AIRecommendation(
        id: 'rec_1',
        title: 'Restaurant Dubrovnik',
        description: 'Skvělá restaurace s mořskými plody',
        type: RecommendationType.restaurant,
        confidence: 0.95,
        rating: 4.5,
        priceRange: '€€€',
        distance: 0.5,
        details: {'cuisine': 'seafood', 'location': 'Old Town'},
        createdAt: DateTime.now(),
      );

      expect(recommendation.title, equals('Restaurant Dubrovnik'));
      expect(recommendation.type, equals(RecommendationType.restaurant));
      expect(recommendation.confidence, equals(0.95));
      expect(recommendation.details['cuisine'], equals('seafood'));
    });

    test('Voice Command model', () {
      final voiceCommand = VoiceCommand(
        id: 'voice_1',
        transcript: 'Najdi mi restauraci',
        confidence: 0.9,
        language: 'cs-CZ',
        type: VoiceCommandType.search,
        parameters: {'query': 'restaurace'},
        timestamp: DateTime.now(),
      );

      expect(voiceCommand.transcript, equals('Najdi mi restauraci'));
      expect(voiceCommand.type, equals(VoiceCommandType.search));
      expect(voiceCommand.language, equals('cs-CZ'));
    });

    test('AI Capability model', () {
      final capability = AICapability(
        id: 'cap_1',
        name: 'Text Generation',
        description: 'Generování odpovědí na dotazy',
        type: CapabilityType.textGeneration,
        isEnabled: true,
        configuration: {'model': 'local', 'language': 'cs'},
      );

      expect(capability.name, equals('Text Generation'));
      expect(capability.isEnabled, isTrue);
      expect(capability.type, equals(CapabilityType.textGeneration));
    });
  });
}
