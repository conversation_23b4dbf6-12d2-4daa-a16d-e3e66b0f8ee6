import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../services/theme_service.dart';
import '../services/accessibility_service.dart';
import '../services/localization_service.dart';
import '../services/performance_service.dart';
import '../services/analytics_service.dart';

/// Pokročilá nastavení aplikace
class AdvancedSettingsScreen extends StatefulWidget {
  const AdvancedSettingsScreen({super.key});

  @override
  State<AdvancedSettingsScreen> createState() => _AdvancedSettingsScreenState();
}

class _AdvancedSettingsScreenState extends State<AdvancedSettingsScreen>
    with TickerProviderStateMixin {
  final ThemeService _themeService = ThemeService();
  final AccessibilityService _accessibilityService = AccessibilityService();
  final LocalizationService _localizationService = LocalizationService();
  final PerformanceService _performanceService = PerformanceService();
  final AnalyticsService _analyticsService = AnalyticsService();

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeServices();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeServices() async {
    await Future.wait([
      _themeService.initialize(),
      _accessibilityService.initialize(),
      _localizationService.initialize(),
      _performanceService.initialize(),
      _analyticsService.initialize(),
    ]);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: Text(
          'Pokročilá nastavení',
          style: GoogleFonts.inter(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF2C3E50),
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: Color(0xFF2C3E50)),
        bottom: TabBar(
          controller: _tabController,
          labelColor: const Color(0xFF006994),
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: const Color(0xFF006994),
          labelStyle: GoogleFonts.inter(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          tabs: const [
            Tab(text: 'Vzhled'),
            Tab(text: 'Přístupnost'),
            Tab(text: 'Jazyk'),
            Tab(text: 'Výkon'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAppearanceTab(),
          _buildAccessibilityTab(),
          _buildLanguageTab(),
          _buildPerformanceTab(),
        ],
      ),
    );
  }

  Widget _buildAppearanceTab() {
    return ListenableBuilder(
      listenable: _themeService,
      builder: (context, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionHeader('Téma aplikace', Icons.palette),
              _buildThemeModeSelector(),
              const SizedBox(height: 24),

              _buildSectionHeader('Barvy', Icons.color_lens),
              _buildAccentColorSelector(),
              const SizedBox(height: 24),

              _buildSectionHeader('Písmo', Icons.text_fields),
              _buildFontSizeSelector(),
              const SizedBox(height: 24),

              _buildSectionHeader('Animace', Icons.animation),
              _buildAnimationSettings(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAccessibilityTab() {
    return ListenableBuilder(
      listenable: _accessibilityService,
      builder: (context, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionHeader('Zobrazení', Icons.visibility),
              _buildAccessibilityToggle(
                'Vysoký kontrast',
                'Zvýší kontrast pro lepší čitelnost',
                _accessibilityService.highContrastMode,
                _accessibilityService.setHighContrastMode,
              ),
              _buildAccessibilityToggle(
                'Velký text',
                'Zvětší velikost textu v celé aplikaci',
                _accessibilityService.largeTextMode,
                _accessibilityService.setLargeTextMode,
              ),
              const SizedBox(height: 24),

              _buildSectionHeader('Pohyb', Icons.motion_photos_on),
              _buildAccessibilityToggle(
                'Redukce pohybu',
                'Zpomalí nebo vypne animace',
                _accessibilityService.reduceMotionMode,
                _accessibilityService.setReduceMotionMode,
              ),
              const SizedBox(height: 24),

              _buildSectionHeader('Zvuk a haptika', Icons.vibration),
              _buildAccessibilityToggle(
                'Haptická zpětná vazba',
                'Vibrace při interakcích',
                _accessibilityService.hapticFeedbackEnabled,
                _accessibilityService.setHapticFeedbackEnabled,
              ),
              _buildAccessibilityToggle(
                'Hlasové vedení',
                'Hlasové pokyny pro navigaci',
                _accessibilityService.voiceGuidanceEnabled,
                _accessibilityService.setVoiceGuidanceEnabled,
              ),
              const SizedBox(height: 24),

              _buildSectionHeader('Barvoslepost', Icons.remove_red_eye),
              _buildColorBlindModeSelector(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLanguageTab() {
    return ListenableBuilder(
      listenable: _localizationService,
      builder: (context, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionHeader('Jazyk aplikace', Icons.language),
              _buildLanguageSelector(),
              const SizedBox(height: 24),

              _buildSectionHeader('Regionální nastavení', Icons.public),
              _buildRegionalSettings(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPerformanceTab() {
    return ListenableBuilder(
      listenable: _performanceService,
      builder: (context, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionHeader('Monitoring výkonu', Icons.speed),
              _buildPerformanceMonitoring(),
              const SizedBox(height: 24),

              _buildSectionHeader('Statistiky', Icons.analytics),
              _buildPerformanceStats(),
              const SizedBox(height: 24),

              _buildSectionHeader('Optimalizace', Icons.tune),
              _buildOptimizationSettings(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Icon(icon, color: const Color(0xFF006994), size: 24),
          const SizedBox(width: 12),
          Text(
            title,
            style: GoogleFonts.inter(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF2C3E50),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildThemeModeSelector() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildThemeModeOption(ThemeMode.light, 'Světlý', Icons.light_mode),
            _buildThemeModeOption(ThemeMode.dark, 'Tmavý', Icons.dark_mode),
            _buildThemeModeOption(
              ThemeMode.system,
              'Systémový',
              Icons.settings_system_daydream,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeModeOption(ThemeMode mode, String title, IconData icon) {
    final isSelected = _themeService.themeMode == mode;

    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? const Color(0xFF006994) : Colors.grey[600],
      ),
      title: Text(
        title,
        style: GoogleFonts.inter(
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          color: isSelected ? const Color(0xFF006994) : const Color(0xFF2C3E50),
        ),
      ),
      trailing: isSelected
          ? const Icon(Icons.check, color: Color(0xFF006994))
          : null,
      onTap: () => _themeService.setThemeMode(mode),
    );
  }

  Widget _buildAccentColorSelector() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Hlavní barva',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF2C3E50),
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: ThemeService.croatianColors.entries.map((entry) {
                final isSelected = _themeService.accentColor == entry.value;
                return GestureDetector(
                  onTap: () => _themeService.setAccentColor(entry.value),
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: entry.value,
                      shape: BoxShape.circle,
                      border: isSelected
                          ? Border.all(color: Colors.white, width: 3)
                          : null,
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color: entry.value.withValues(alpha: 0.3),
                                blurRadius: 8,
                                spreadRadius: 2,
                              ),
                            ]
                          : [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 4,
                              ),
                            ],
                    ),
                    child: isSelected
                        ? const Icon(Icons.check, color: Colors.white, size: 24)
                        : null,
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFontSizeSelector() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Velikost písma',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF2C3E50),
                  ),
                ),
                Text(
                  _themeService.fontSizeDisplayName,
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Slider(
              value: _themeService.fontSizeScale,
              min: 0.8,
              max: 1.4,
              divisions: 6,
              activeColor: const Color(0xFF006994),
              onChanged: _themeService.setFontSizeScale,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Malé', style: GoogleFonts.inter(fontSize: 10)),
                Text('Velké', style: GoogleFonts.inter(fontSize: 10)),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimationSettings() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: SwitchListTile(
        title: Text(
          'Animace povoleny',
          style: GoogleFonts.inter(fontWeight: FontWeight.w500),
        ),
        subtitle: Text(
          'Zapne/vypne animace v aplikaci',
          style: GoogleFonts.inter(fontSize: 12, color: Colors.grey[600]),
        ),
        value: _themeService.animationsEnabled,
        onChanged: _themeService.setAnimationsEnabled,
        activeColor: const Color(0xFF006994),
      ),
    );
  }

  Widget _buildAccessibilityToggle(
    String title,
    String subtitle,
    bool value,
    Function(bool) onChanged,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      margin: const EdgeInsets.only(bottom: 12),
      child: SwitchListTile(
        title: Text(
          title,
          style: GoogleFonts.inter(fontWeight: FontWeight.w500),
        ),
        subtitle: Text(
          subtitle,
          style: GoogleFonts.inter(fontSize: 12, color: Colors.grey[600]),
        ),
        value: value,
        onChanged: onChanged,
        activeColor: const Color(0xFF006994),
      ),
    );
  }

  Widget _buildColorBlindModeSelector() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Režim barvosleposti',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF2C3E50),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _accessibilityService.getColorBlindModeDisplayName(),
              style: GoogleFonts.inter(fontSize: 12, color: Colors.grey[600]),
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<ColorBlindMode>(
              value: _accessibilityService.colorBlindMode,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              items: ColorBlindMode.values.map((mode) {
                return DropdownMenuItem(
                  value: mode,
                  child: Text(
                    _getColorBlindModeDisplayName(mode),
                    style: GoogleFonts.inter(fontSize: 14),
                  ),
                );
              }).toList(),
              onChanged: (mode) {
                if (mode != null) {
                  _accessibilityService.setColorBlindMode(mode);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  String _getColorBlindModeDisplayName(ColorBlindMode mode) {
    switch (mode) {
      case ColorBlindMode.none:
        return 'Žádný';
      case ColorBlindMode.protanopia:
        return 'Protanopie (červeno-zelená)';
      case ColorBlindMode.deuteranopia:
        return 'Deuteranopie (červeno-zelená)';
      case ColorBlindMode.tritanopia:
        return 'Tritanopie (modro-žlutá)';
    }
  }

  Widget _buildLanguageSelector() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Column(
        children: LocalizationService.supportedLocales.map((locale) {
          final isSelected = _localizationService.currentLocale == locale;
          final languageName = _localizationService.getLanguageName(
            locale.languageCode,
          );
          final flag = _localizationService.getLanguageFlag(
            locale.languageCode,
          );

          return ListTile(
            leading: Text(flag, style: const TextStyle(fontSize: 24)),
            title: Text(
              languageName,
              style: GoogleFonts.inter(
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                color: isSelected
                    ? const Color(0xFF006994)
                    : const Color(0xFF2C3E50),
              ),
            ),
            trailing: isSelected
                ? const Icon(Icons.check, color: Color(0xFF006994))
                : null,
            onTap: () => _localizationService.setLocale(locale),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildRegionalSettings() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildRegionalInfo(
              'Formát data',
              _localizationService.getDateFormat(),
            ),
            _buildRegionalInfo(
              'Formát času',
              _localizationService.getTimeFormat(),
            ),
            _buildRegionalInfo(
              'Měna',
              _localizationService.getCurrencySymbol(),
            ),
            _buildRegionalInfo(
              'Formát čísla',
              _localizationService.getNumberFormat(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRegionalInfo(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.inter(
              fontSize: 14,
              color: const Color(0xFF2C3E50),
            ),
          ),
          Text(
            value,
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceMonitoring() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: SwitchListTile(
        title: Text(
          'Monitoring výkonu',
          style: GoogleFonts.inter(fontWeight: FontWeight.w500),
        ),
        subtitle: Text(
          'Sleduje výkon aplikace pro optimalizaci',
          style: GoogleFonts.inter(fontSize: 12, color: Colors.grey[600]),
        ),
        value: _performanceService.isMonitoring,
        onChanged: (enabled) {
          if (enabled) {
            _performanceService.startMonitoring();
          } else {
            _performanceService.stopMonitoring();
          }
        },
        activeColor: const Color(0xFF006994),
      ),
    );
  }

  Widget _buildPerformanceStats() {
    final report = _performanceService.generateReport();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatRow(
              'Průměrná doba načítání',
              '${report.averageScreenLoadTime.toStringAsFixed(0)}ms',
            ),
            _buildStatRow(
              'Průměrné použití paměti',
              '${report.averageMemoryUsage.toStringAsFixed(1)}MB',
            ),
            _buildStatRow(
              'Průměrné FPS',
              '${report.averageFPS.toStringAsFixed(1)}',
            ),
            _buildStatRow('Celkem metrik', '${report.totalMetrics}'),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: GoogleFonts.inter(fontSize: 14)),
          Text(
            value,
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF006994),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptimizationSettings() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.clear_all, color: Color(0xFF006994)),
            title: Text(
              'Vymazat metriky',
              style: GoogleFonts.inter(fontWeight: FontWeight.w500),
            ),
            subtitle: Text(
              'Smaže všechny nasbírané performance metriky',
              style: GoogleFonts.inter(fontSize: 12, color: Colors.grey[600]),
            ),
            onTap: () {
              _performanceService.clearMetrics();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Performance metriky vymazány')),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.refresh, color: Color(0xFF006994)),
            title: Text(
              'Resetovat nastavení',
              style: GoogleFonts.inter(fontWeight: FontWeight.w500),
            ),
            subtitle: Text(
              'Obnoví všechna nastavení na výchozí hodnoty',
              style: GoogleFonts.inter(fontSize: 12, color: Colors.grey[600]),
            ),
            onTap: _showResetDialog,
          ),
        ],
      ),
    );
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Resetovat nastavení'),
        content: const Text(
          'Opravdu chcete obnovit všechna nastavení na výchozí hodnoty?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Zrušit'),
          ),
          TextButton(
            onPressed: () async {
              await Future.wait([
                _themeService.resetToDefaults(),
                _accessibilityService.resetToDefaults(),
                _localizationService.resetToDefault(),
              ]);
              if (mounted) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Nastavení resetována')),
                );
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Resetovat'),
          ),
        ],
      ),
    );
  }
}
