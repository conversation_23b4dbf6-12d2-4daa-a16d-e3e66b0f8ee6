class DictionaryEntry {
  final String id;
  final String croatian;
  final String czech;
  final String? pronunciation;
  final String? audioUrl;
  final DictionaryCategory category;
  final List<String> examples;
  final String? notes;
  final int difficulty;
  final bool isFavorite;
  final DateTime? lastUsed;

  const DictionaryEntry({
    required this.id,
    required this.croatian,
    required this.czech,
    this.pronunciation,
    this.audioUrl,
    required this.category,
    this.examples = const [],
    this.notes,
    this.difficulty = 1,
    this.isFavorite = false,
    this.lastUsed,
  });

  DictionaryEntry copyWith({
    String? id,
    String? croatian,
    String? czech,
    String? pronunciation,
    String? audioUrl,
    DictionaryCategory? category,
    List<String>? examples,
    String? notes,
    int? difficulty,
    bool? isFavorite,
    DateTime? lastUsed,
  }) {
    return DictionaryEntry(
      id: id ?? this.id,
      croatian: croatian ?? this.croatian,
      czech: czech ?? this.czech,
      pronunciation: pronunciation ?? this.pronunciation,
      audioUrl: audioUrl ?? this.audioUrl,
      category: category ?? this.category,
      examples: examples ?? this.examples,
      notes: notes ?? this.notes,
      difficulty: difficulty ?? this.difficulty,
      isFavorite: isFavorite ?? this.isFavorite,
      lastUsed: lastUsed ?? this.lastUsed,
    );
  }
}

class Phrase {
  final String id;
  final String croatian;
  final String czech;
  final String? pronunciation;
  final String? audioUrl;
  final PhraseCategory category;
  final String? context;
  final int difficulty;
  final bool isFavorite;

  const Phrase({
    required this.id,
    required this.croatian,
    required this.czech,
    this.pronunciation,
    this.audioUrl,
    required this.category,
    this.context,
    this.difficulty = 1,
    this.isFavorite = false,
  });

  Phrase copyWith({
    String? id,
    String? croatian,
    String? czech,
    String? pronunciation,
    String? audioUrl,
    PhraseCategory? category,
    String? context,
    int? difficulty,
    bool? isFavorite,
  }) {
    return Phrase(
      id: id ?? this.id,
      croatian: croatian ?? this.croatian,
      czech: czech ?? this.czech,
      pronunciation: pronunciation ?? this.pronunciation,
      audioUrl: audioUrl ?? this.audioUrl,
      category: category ?? this.category,
      context: context ?? this.context,
      difficulty: difficulty ?? this.difficulty,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }
}

enum DictionaryCategory {
  basic,
  greetings,
  food,
  transport,
  accommodation,
  shopping,
  emergency,
  numbers,
  time,
  weather,
  directions,
  culture,
  nature,
  activities,
}

enum PhraseCategory {
  greetings,
  politeness,
  questions,
  directions,
  restaurant,
  hotel,
  shopping,
  emergency,
  transport,
  weather,
  compliments,
  complaints,
}

extension DictionaryCategoryExtension on DictionaryCategory {
  String get displayName {
    switch (this) {
      case DictionaryCategory.basic:
        return 'Základní';
      case DictionaryCategory.greetings:
        return 'Pozdravy';
      case DictionaryCategory.food:
        return 'Jídlo';
      case DictionaryCategory.transport:
        return 'Doprava';
      case DictionaryCategory.accommodation:
        return 'Ubytování';
      case DictionaryCategory.shopping:
        return 'Nákupy';
      case DictionaryCategory.emergency:
        return 'Nouzové situace';
      case DictionaryCategory.numbers:
        return 'Čísla';
      case DictionaryCategory.time:
        return 'Čas';
      case DictionaryCategory.weather:
        return 'Počasí';
      case DictionaryCategory.directions:
        return 'Směry';
      case DictionaryCategory.culture:
        return 'Kultura';
      case DictionaryCategory.nature:
        return 'Příroda';
      case DictionaryCategory.activities:
        return 'Aktivity';
    }
  }

  String get icon {
    switch (this) {
      case DictionaryCategory.basic:
        return '📚';
      case DictionaryCategory.greetings:
        return '👋';
      case DictionaryCategory.food:
        return '🍽️';
      case DictionaryCategory.transport:
        return '🚗';
      case DictionaryCategory.accommodation:
        return '🏨';
      case DictionaryCategory.shopping:
        return '🛍️';
      case DictionaryCategory.emergency:
        return '🚨';
      case DictionaryCategory.numbers:
        return '🔢';
      case DictionaryCategory.time:
        return '⏰';
      case DictionaryCategory.weather:
        return '🌤️';
      case DictionaryCategory.directions:
        return '🧭';
      case DictionaryCategory.culture:
        return '🎭';
      case DictionaryCategory.nature:
        return '🌿';
      case DictionaryCategory.activities:
        return '🎯';
    }
  }
}

extension PhraseCategoryExtension on PhraseCategory {
  String get displayName {
    switch (this) {
      case PhraseCategory.greetings:
        return 'Pozdravy';
      case PhraseCategory.politeness:
        return 'Zdvořilost';
      case PhraseCategory.questions:
        return 'Otázky';
      case PhraseCategory.directions:
        return 'Směry';
      case PhraseCategory.restaurant:
        return 'Restaurace';
      case PhraseCategory.hotel:
        return 'Hotel';
      case PhraseCategory.shopping:
        return 'Nákupy';
      case PhraseCategory.emergency:
        return 'Nouzové situace';
      case PhraseCategory.transport:
        return 'Doprava';
      case PhraseCategory.weather:
        return 'Počasí';
      case PhraseCategory.compliments:
        return 'Komplimenty';
      case PhraseCategory.complaints:
        return 'Stížnosti';
    }
  }

  String get icon {
    switch (this) {
      case PhraseCategory.greetings:
        return '👋';
      case PhraseCategory.politeness:
        return '🙏';
      case PhraseCategory.questions:
        return '❓';
      case PhraseCategory.directions:
        return '🧭';
      case PhraseCategory.restaurant:
        return '🍽️';
      case PhraseCategory.hotel:
        return '🏨';
      case PhraseCategory.shopping:
        return '🛍️';
      case PhraseCategory.emergency:
        return '🚨';
      case PhraseCategory.transport:
        return '🚗';
      case PhraseCategory.weather:
        return '🌤️';
      case PhraseCategory.compliments:
        return '👍';
      case PhraseCategory.complaints:
        return '😤';
    }
  }
}

// Předdefinované slovníkové záznamy
class DictionaryData {
  static const List<Map<String, dynamic>> basicWords = [
    {
      'croatian': 'Dobar dan',
      'czech': 'Dobrý den',
      'pronunciation': 'DO-bar dan',
      'category': DictionaryCategory.greetings,
      'examples': ['Dobar dan, kako ste?'],
    },
    {
      'croatian': 'Hvala',
      'czech': 'Děkuji',
      'pronunciation': 'HVA-la',
      'category': DictionaryCategory.basic,
      'examples': ['Hvala vam puno!'],
    },
    {
      'croatian': 'Molim',
      'czech': 'Prosím',
      'pronunciation': 'MO-lim',
      'category': DictionaryCategory.basic,
      'examples': ['Molim vas, možete li mi pomoći?'],
    },
    // Další slova...
  ];

  static const List<Map<String, dynamic>> basicPhrases = [
    {
      'croatian': 'Gdje je...?',
      'czech': 'Kde je...?',
      'pronunciation': 'GDJE je',
      'category': PhraseCategory.directions,
      'context': 'Ptaní se na cestu',
    },
    {
      'croatian': 'Koliko to košta?',
      'czech': 'Kolik to stojí?',
      'pronunciation': 'KO-li-ko to KOSH-ta',
      'category': PhraseCategory.shopping,
      'context': 'Ptaní se na cenu',
    },
    // Další fráze...
  ];
}
