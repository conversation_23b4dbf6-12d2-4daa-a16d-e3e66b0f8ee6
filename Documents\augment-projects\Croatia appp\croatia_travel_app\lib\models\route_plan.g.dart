// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'route_plan.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RoutePlan _$RoutePlanFromJson(Map<String, dynamic> json) => RoutePlan(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      points: (json['points'] as List<dynamic>)
          .map((e) => RoutePoint.fromJson(e as Map<String, dynamic>))
          .toList(),
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      type: $enumDecode(_$RouteTypeEnumMap, json['type']),
      totalDistance: (json['totalDistance'] as num).toDouble(),
      estimatedDuration:
          Duration(microseconds: (json['estimatedDuration'] as num).toInt()),
      notes: json['notes'] as String?,
      isPublic: json['isPublic'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$RoutePlanToJson(RoutePlan instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'points': instance.points,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'type': _$RouteTypeEnumMap[instance.type]!,
      'totalDistance': instance.totalDistance,
      'estimatedDuration': instance.estimatedDuration.inMicroseconds,
      'notes': instance.notes,
      'isPublic': instance.isPublic,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$RouteTypeEnumMap = {
  RouteType.walking: 'walking',
  RouteType.cycling: 'cycling',
  RouteType.driving: 'driving',
  RouteType.publicTransport: 'publicTransport',
  RouteType.mixed: 'mixed',
};

RoutePoint _$RoutePointFromJson(Map<String, dynamic> json) => RoutePoint(
      id: json['id'] as String,
      placeId: json['placeId'] as String,
      name: json['name'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      order: (json['order'] as num).toInt(),
      arrivalTime: json['arrivalTime'] == null
          ? null
          : DateTime.parse(json['arrivalTime'] as String),
      departureTime: json['departureTime'] == null
          ? null
          : DateTime.parse(json['departureTime'] as String),
      stayDuration:
          Duration(microseconds: (json['stayDuration'] as num).toInt()),
      notes: json['notes'] as String?,
      activities: (json['activities'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      type: $enumDecode(_$RoutePointTypeEnumMap, json['type']),
      isVisited: json['isVisited'] as bool? ?? false,
    );

Map<String, dynamic> _$RoutePointToJson(RoutePoint instance) =>
    <String, dynamic>{
      'id': instance.id,
      'placeId': instance.placeId,
      'name': instance.name,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'order': instance.order,
      'arrivalTime': instance.arrivalTime?.toIso8601String(),
      'departureTime': instance.departureTime?.toIso8601String(),
      'stayDuration': instance.stayDuration.inMicroseconds,
      'notes': instance.notes,
      'activities': instance.activities,
      'type': _$RoutePointTypeEnumMap[instance.type]!,
      'isVisited': instance.isVisited,
    };

const _$RoutePointTypeEnumMap = {
  RoutePointType.attraction: 'attraction',
  RoutePointType.restaurant: 'restaurant',
  RoutePointType.accommodation: 'accommodation',
  RoutePointType.transport: 'transport',
  RoutePointType.shopping: 'shopping',
  RoutePointType.viewpoint: 'viewpoint',
  RoutePointType.beach: 'beach',
  RoutePointType.park: 'park',
  RoutePointType.museum: 'museum',
  RoutePointType.other: 'other',
};
