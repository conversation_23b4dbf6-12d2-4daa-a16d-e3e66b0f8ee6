// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'collaborative_trip.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CollaborativeTrip _$CollaborativeTripFromJson(Map<String, dynamic> json) =>
    CollaborativeTrip(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      creatorId: json['creatorId'] as String,
      participants: (json['participants'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      invitedUsers: (json['invitedUsers'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      placeSuggestions: (json['placeSuggestions'] as List<dynamic>?)
              ?.map((e) => PlaceSuggestion.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      status: $enumDecodeNullable(_$TripStatusEnumMap, json['status']) ??
          TripStatus.planning,
      visibility:
          $enumDecodeNullable(_$TripVisibilityEnumMap, json['visibility']) ??
              TripVisibility.private,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$CollaborativeTripToJson(CollaborativeTrip instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'creatorId': instance.creatorId,
      'participants': instance.participants,
      'invitedUsers': instance.invitedUsers,
      'placeSuggestions': instance.placeSuggestions,
      'tags': instance.tags,
      'status': _$TripStatusEnumMap[instance.status]!,
      'visibility': _$TripVisibilityEnumMap[instance.visibility]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$TripStatusEnumMap = {
  TripStatus.planning: 'planning',
  TripStatus.active: 'active',
  TripStatus.completed: 'completed',
  TripStatus.cancelled: 'cancelled',
};

const _$TripVisibilityEnumMap = {
  TripVisibility.private: 'private',
  TripVisibility.friends: 'friends',
  TripVisibility.public: 'public',
};

PlaceSuggestion _$PlaceSuggestionFromJson(Map<String, dynamic> json) =>
    PlaceSuggestion(
      id: json['id'] as String,
      place: Place.fromJson(json['place'] as Map<String, dynamic>),
      suggestedBy: json['suggestedBy'] as String,
      suggestedAt: DateTime.parse(json['suggestedAt'] as String),
      votes:
          (json['votes'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      downvotes: (json['downvotes'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      comments: (json['comments'] as List<dynamic>?)
              ?.map(
                  (e) => SuggestionComment.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      status: $enumDecodeNullable(_$SuggestionStatusEnumMap, json['status']) ??
          SuggestionStatus.pending,
      reason: json['reason'] as String?,
    );

Map<String, dynamic> _$PlaceSuggestionToJson(PlaceSuggestion instance) =>
    <String, dynamic>{
      'id': instance.id,
      'place': instance.place,
      'suggestedBy': instance.suggestedBy,
      'suggestedAt': instance.suggestedAt.toIso8601String(),
      'votes': instance.votes,
      'downvotes': instance.downvotes,
      'comments': instance.comments,
      'status': _$SuggestionStatusEnumMap[instance.status]!,
      'reason': instance.reason,
    };

const _$SuggestionStatusEnumMap = {
  SuggestionStatus.pending: 'pending',
  SuggestionStatus.approved: 'approved',
  SuggestionStatus.rejected: 'rejected',
};

SuggestionComment _$SuggestionCommentFromJson(Map<String, dynamic> json) =>
    SuggestionComment(
      id: json['id'] as String,
      userId: json['userId'] as String,
      comment: json['comment'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$SuggestionCommentToJson(SuggestionComment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'comment': instance.comment,
      'createdAt': instance.createdAt.toIso8601String(),
    };

TripInvitation _$TripInvitationFromJson(Map<String, dynamic> json) =>
    TripInvitation(
      id: json['id'] as String,
      tripId: json['tripId'] as String,
      tripName: json['tripName'] as String,
      invitedBy: json['invitedBy'] as String,
      invitedAt: DateTime.parse(json['invitedAt'] as String),
      status: $enumDecodeNullable(_$InvitationStatusEnumMap, json['status']) ??
          InvitationStatus.pending,
      respondedAt: json['respondedAt'] == null
          ? null
          : DateTime.parse(json['respondedAt'] as String),
    );

Map<String, dynamic> _$TripInvitationToJson(TripInvitation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'tripId': instance.tripId,
      'tripName': instance.tripName,
      'invitedBy': instance.invitedBy,
      'invitedAt': instance.invitedAt.toIso8601String(),
      'status': _$InvitationStatusEnumMap[instance.status]!,
      'respondedAt': instance.respondedAt?.toIso8601String(),
    };

const _$InvitationStatusEnumMap = {
  InvitationStatus.pending: 'pending',
  InvitationStatus.accepted: 'accepted',
  InvitationStatus.declined: 'declined',
  InvitationStatus.expired: 'expired',
};
