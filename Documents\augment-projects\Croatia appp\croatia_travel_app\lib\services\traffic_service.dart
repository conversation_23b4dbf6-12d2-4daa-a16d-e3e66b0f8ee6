import 'dart:async';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../models/traffic.dart';
import '../data/local_database.dart';

class TrafficService {
  static final TrafficService _instance = TrafficService._internal();
  factory TrafficService() => _instance;
  TrafficService._internal();

  final Dio _dio = Dio();
  final LocalDatabase _localDb = LocalDatabase();

  static const String _baseUrl = 'https://api.croatia-traffic.com';
  Timer? _trafficTimer;

  // ========== DOPRAVNÍ INCIDENTY ==========

  /// Získání dopravních incidentů v oblasti
  Future<List<TrafficIncident>> getTrafficIncidents({
    required double latitude,
    required double longitude,
    double radiusKm = 10.0,
    List<TrafficIncidentType>? types,
    TrafficSeverity? minSeverity,
  }) async {
    try {
      final response = await _dio.get(
        '$_baseUrl/incidents',
        queryParameters: {
          'lat': latitude,
          'lng': longitude,
          'radius': radiusKm,
          if (types != null) 'types': types.map((t) => t.name).join(','),
          if (minSeverity != null) 'min_severity': minSeverity.name,
          'status': 'active',
        },
      );

      final List<dynamic> data = response.data['incidents'];
      return data.map((json) => TrafficIncident.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Chyba při načítání dopravních incidentů: $e');
      return await _getLocalTrafficIncidents(latitude, longitude, radiusKm);
    }
  }

  /// Nahlášení dopravního incidentu
  Future<TrafficIncident?> reportTrafficIncident({
    required TrafficIncidentType type,
    required String title,
    required String description,
    required double latitude,
    required double longitude,
    TrafficSeverity severity = TrafficSeverity.medium,
    List<String>? images,
    String? reportedBy,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/incidents/report',
        data: {
          'type': type.name,
          'title': title,
          'description': description,
          'latitude': latitude,
          'longitude': longitude,
          'severity': severity.name,
          if (images != null) 'images': images,
          if (reportedBy != null) 'reported_by': reportedBy,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      final incident = TrafficIncident.fromJson(response.data['incident']);

      // Uložení do lokální databáze
      await _localDb.saveTrafficIncident({
        'id': incident.id,
        'type': incident.type.name,
        'title': incident.title,
        'description': incident.description,
        'latitude': incident.latitude,
        'longitude': incident.longitude,
        'severity': incident.severity.name,
        'status': incident.status.name,
        'reportedAt': incident.reportedAt.toIso8601String(),
        'resolvedAt': incident.resolvedAt?.toIso8601String(),
        'reportedBy': incident.reportedBy,
        'affectedRoads': incident.affectedRoads,
        'alternativeRoutes': incident.alternativeRoutes,
        'estimatedDelay': incident.estimatedDelay?.inMinutes,
        'images': incident.images,
        'upvotes': incident.upvotes,
        'downvotes': incident.downvotes,
        'isVerified': incident.isVerified,
      });

      return incident;
    } catch (e) {
      debugPrint('Chyba při hlášení incidentu: $e');
      return null;
    }
  }

  /// Hlasování o incidentu
  Future<bool> voteOnIncident(String incidentId, bool isUpvote) async {
    try {
      await _dio.post(
        '$_baseUrl/incidents/$incidentId/vote',
        data: {'vote': isUpvote ? 'up' : 'down'},
      );

      return true;
    } catch (e) {
      debugPrint('Chyba při hlasování o incidentu: $e');
      return false;
    }
  }

  // ========== DOPRAVNÍ PODMÍNKY ==========

  /// Získání aktuálních dopravních podmínek
  Future<List<TrafficCondition>> getTrafficConditions({
    required double latitude,
    required double longitude,
    double radiusKm = 20.0,
  }) async {
    try {
      final response = await _dio.get(
        '$_baseUrl/conditions',
        queryParameters: {
          'lat': latitude,
          'lng': longitude,
          'radius': radiusKm,
        },
      );

      final List<dynamic> data = response.data['conditions'];
      return data.map((json) => TrafficCondition.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Chyba při načítání dopravních podmínek: $e');
      return [];
    }
  }

  /// Získání dopravních podmínek pro konkrétní cestu
  Future<List<TrafficCondition>> getRouteTrafficConditions({
    required List<Map<String, double>> waypoints,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/conditions/route',
        data: {'waypoints': waypoints},
      );

      final List<dynamic> data = response.data['conditions'];
      return data.map((json) => TrafficCondition.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Chyba při načítání podmínek trasy: $e');
      return [];
    }
  }

  // ========== DOPRAVNÍ KAMERY ==========

  /// Získání dopravních kamer v oblasti
  Future<List<TrafficCamera>> getTrafficCameras({
    required double latitude,
    required double longitude,
    double radiusKm = 15.0,
    CameraType? type,
  }) async {
    try {
      final response = await _dio.get(
        '$_baseUrl/cameras',
        queryParameters: {
          'lat': latitude,
          'lng': longitude,
          'radius': radiusKm,
          if (type != null) 'type': type.name,
          'active': true,
        },
      );

      final List<dynamic> data = response.data['cameras'];
      return data.map((json) => TrafficCamera.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Chyba při načítání dopravních kamer: $e');
      return [];
    }
  }

  /// Aktualizace obrázku z kamery
  Future<String?> refreshCameraImage(String cameraId) async {
    try {
      final response = await _dio.post('$_baseUrl/cameras/$cameraId/refresh');
      return response.data['image_url'];
    } catch (e) {
      debugPrint('Chyba při aktualizaci kamery: $e');
      return null;
    }
  }

  // ========== UZAVÍRKY SILNIC ==========

  /// Získání uzavírek silnic
  Future<List<RoadClosure>> getRoadClosures({
    required double latitude,
    required double longitude,
    double radiusKm = 25.0,
    bool onlyActive = true,
  }) async {
    try {
      final response = await _dio.get(
        '$_baseUrl/closures',
        queryParameters: {
          'lat': latitude,
          'lng': longitude,
          'radius': radiusKm,
          'only_active': onlyActive,
        },
      );

      final List<dynamic> data = response.data['closures'];
      return data.map((json) => RoadClosure.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Chyba při načítání uzavírek: $e');
      return [];
    }
  }

  // ========== DOPRAVNÍ UPOZORNĚNÍ ==========

  /// Získání dopravních upozornění
  Future<List<TrafficAlert>> getTrafficAlerts({
    String? city,
    TrafficSeverity? minSeverity,
  }) async {
    try {
      final response = await _dio.get(
        '$_baseUrl/alerts',
        queryParameters: {
          if (city != null) 'city': city,
          if (minSeverity != null) 'min_severity': minSeverity.name,
          'active': true,
        },
      );

      final List<dynamic> data = response.data['alerts'];
      return data.map((json) => TrafficAlert.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Chyba při načítání upozornění: $e');
      return [];
    }
  }

  // ========== REAL-TIME AKTUALIZACE ==========

  /// Spuštění real-time aktualizací dopravní situace
  void startTrafficUpdates({
    required double latitude,
    required double longitude,
    required Function(List<TrafficIncident>) onIncidentsUpdate,
    required Function(List<TrafficCondition>) onConditionsUpdate,
  }) {
    _trafficTimer?.cancel();
    _trafficTimer = Timer.periodic(const Duration(minutes: 2), (timer) async {
      // Aktualizace incidentů
      final incidents = await getTrafficIncidents(
        latitude: latitude,
        longitude: longitude,
      );
      onIncidentsUpdate(incidents);

      // Aktualizace podmínek
      final conditions = await getTrafficConditions(
        latitude: latitude,
        longitude: longitude,
      );
      onConditionsUpdate(conditions);
    });
  }

  /// Zastavení real-time aktualizací
  void stopTrafficUpdates() {
    _trafficTimer?.cancel();
    _trafficTimer = null;
  }

  // ========== ANALÝZA TRASY ==========

  /// Analýza dopravní situace na trase
  Future<Map<String, dynamic>> analyzeRouteTraffic({
    required List<Map<String, double>> waypoints,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/analyze/route',
        data: {'waypoints': waypoints},
      );

      return response.data;
    } catch (e) {
      debugPrint('Chyba při analýze trasy: $e');
      return {};
    }
  }

  // ========== LOKÁLNÍ DATA ==========

  Future<List<TrafficIncident>> _getLocalTrafficIncidents(
    double latitude,
    double longitude,
    double radiusKm,
  ) async {
    // Simulace lokálních dat pro offline režim
    return [
      TrafficIncident(
        id: 'incident_1',
        type: TrafficIncidentType.congestion,
        title: 'Zácpa na hlavní silnici',
        description: 'Hustý provoz směrem do centra',
        latitude: latitude + 0.005,
        longitude: longitude + 0.005,
        severity: TrafficSeverity.medium,
        status: TrafficIncidentStatus.active,
        reportedAt: DateTime.now().subtract(const Duration(minutes: 30)),
        affectedRoads: ['A1', 'D1'],
      ),
    ];
  }

  /// Vyčištění cache
  Future<void> clearCache() async {
    await _localDb.clearTrafficCache();
  }

  // ========== POKROČILÉ FUNKCE ==========

  /// AI predikce dopravní situace
  Future<TrafficPrediction> predictTrafficConditions({
    required double latitude,
    required double longitude,
    required DateTime targetTime,
    double radiusKm = 5.0,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/traffic/predict',
        data: {
          'location': {'lat': latitude, 'lng': longitude},
          'target_time': targetTime.toIso8601String(),
          'radius_km': radiusKm,
          'historical_data_weeks': 8,
          'weather_forecast': await _getWeatherForecast(targetTime),
          'event_calendar': await _getEventCalendar(targetTime),
          'construction_schedule': await _getConstructionSchedule(targetTime),
        },
      );

      return TrafficPrediction.fromJson(response.data);
    } catch (e) {
      debugPrint('Chyba při predikci dopravy: $e');
      return TrafficPrediction.fallback();
    }
  }

  /// Inteligentní doporučení alternativních tras
  Future<List<AlternativeRoute>> getSmartAlternatives({
    required double fromLat,
    required double fromLng,
    required double toLat,
    required double toLng,
    DateTime? departureTime,
    List<String> avoidIncidentTypes = const [],
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/traffic/smart-alternatives',
        data: {
          'from': {'lat': fromLat, 'lng': fromLng},
          'to': {'lat': toLat, 'lng': toLng},
          'departure_time': (departureTime ?? DateTime.now()).toIso8601String(),
          'avoid_incident_types': avoidIncidentTypes,
          'current_traffic_data': await _getCurrentTrafficData(),
          'user_preferences': await _getUserRoutePreferences(),
        },
      );

      final List<dynamic> data = response.data['alternatives'];
      return data.map((json) => AlternativeRoute.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Chyba při načítání alternativ: $e');
      return [];
    }
  }

  /// Crowdsourcing dopravních informací s ověřením
  Future<bool> reportVerifiedIncident({
    required String title,
    required String description,
    required TrafficIncidentType type,
    required double latitude,
    required double longitude,
    required TrafficSeverity severity,
    List<String> photos = const [],
    String? userId,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/traffic/report-verified',
        data: {
          'title': title,
          'description': description,
          'type': type.name,
          'location': {'lat': latitude, 'lng': longitude},
          'severity': severity.name,
          'photos': photos,
          'timestamp': DateTime.now().toIso8601String(),
          if (userId != null) 'user_id': userId,
          'device_info': await _getDeviceInfo(),
          'location_accuracy': await _getLocationAccuracy(),
        },
      );

      final incidentId = response.data['incident_id'] as String;

      // Spuštění ověřovacího procesu
      await _startVerificationProcess(incidentId);

      return true;
    } catch (e) {
      debugPrint('Chyba při hlášení incidentu: $e');
      return false;
    }
  }

  /// Real-time sledování dopravního toku s ML analýzou
  Future<TrafficFlowAnalysis> analyzeTrafficFlow({
    required String roadId,
    required DateTime startTime,
    required DateTime endTime,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/traffic/flow-analysis',
        data: {
          'road_id': roadId,
          'start_time': startTime.toIso8601String(),
          'end_time': endTime.toIso8601String(),
          'analysis_type': 'ml_enhanced',
          'include_predictions': true,
        },
      );

      return TrafficFlowAnalysis.fromJson(response.data);
    } catch (e) {
      debugPrint('Chyba při analýze toku: $e');
      return TrafficFlowAnalysis.empty();
    }
  }

  /// Adaptivní upozornění na základě uživatelského profilu
  Future<List<PersonalizedAlert>> getPersonalizedAlerts({
    required String userId,
    required double latitude,
    required double longitude,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/traffic/personalized-alerts',
        data: {
          'user_id': userId,
          'location': {'lat': latitude, 'lng': longitude},
          'user_routes': await _getUserFrequentRoutes(userId),
          'user_schedule': await _getUserSchedule(userId),
          'alert_preferences': await _getAlertPreferences(userId),
        },
      );

      final List<dynamic> data = response.data['alerts'];
      return data.map((json) => PersonalizedAlert.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Chyba při načítání upozornění: $e');
      return [];
    }
  }

  /// Optimalizace semafórů na základě real-time dat
  Future<TrafficLightOptimization> optimizeTrafficLights({
    required String intersectionId,
    required Duration timeWindow,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/traffic/optimize-lights',
        data: {
          'intersection_id': intersectionId,
          'time_window_minutes': timeWindow.inMinutes,
          'current_traffic_volume': await _getCurrentTrafficVolume(
            intersectionId,
          ),
          'pedestrian_activity': await _getPedestrianActivity(intersectionId),
          'emergency_vehicles': await _getEmergencyVehicles(intersectionId),
        },
      );

      return TrafficLightOptimization.fromJson(response.data);
    } catch (e) {
      debugPrint('Chyba při optimalizaci semaforů: $e');
      return TrafficLightOptimization.empty();
    }
  }

  // ========== POMOCNÉ METODY ==========

  Future<Map<String, dynamic>> _getWeatherForecast(DateTime targetTime) async {
    return {
      'temperature': 22,
      'precipitation': 0.1,
      'wind_speed': 15,
      'visibility': 8,
    };
  }

  Future<List<Map<String, dynamic>>> _getEventCalendar(
    DateTime targetTime,
  ) async {
    return [
      {
        'event_id': 'football_match',
        'name': 'Fotbalový zápas',
        'start_time': targetTime.toIso8601String(),
        'expected_attendance': 30000,
        'traffic_impact': 'high',
      },
    ];
  }

  Future<List<Map<String, dynamic>>> _getConstructionSchedule(
    DateTime targetTime,
  ) async {
    return [
      {
        'project_id': 'road_repair_1',
        'location': 'Hlavní třída',
        'start_time': targetTime
            .subtract(const Duration(days: 7))
            .toIso8601String(),
        'end_time': targetTime.add(const Duration(days: 14)).toIso8601String(),
        'lane_closures': 2,
      },
    ];
  }

  Future<Map<String, dynamic>> _getCurrentTrafficData() async {
    return {
      'average_speed': 35,
      'congestion_level': 'moderate',
      'incident_count': 3,
      'weather_impact': 'low',
    };
  }

  Future<Map<String, dynamic>> _getUserRoutePreferences() async {
    return {
      'avoid_highways': false,
      'avoid_tolls': true,
      'prefer_scenic': false,
      'max_detour_minutes': 15,
    };
  }

  Future<Map<String, dynamic>> _getDeviceInfo() async {
    return {'platform': 'android', 'version': '12', 'app_version': '1.0.0'};
  }

  Future<double> _getLocationAccuracy() async => 5.0; // metry

  Future<void> _startVerificationProcess(String incidentId) async {
    // Spuštění procesu ověření incidentu
    debugPrint('Spouštím ověření incidentu: $incidentId');
  }

  Future<List<Map<String, dynamic>>> _getUserFrequentRoutes(
    String userId,
  ) async {
    return [
      {'from': 'home', 'to': 'work', 'frequency': 10, 'usual_time': '08:00'},
    ];
  }

  Future<Map<String, dynamic>> _getUserSchedule(String userId) async {
    return {
      'work_days': ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
      'work_start': '08:00',
      'work_end': '17:00',
    };
  }

  Future<Map<String, dynamic>> _getAlertPreferences(String userId) async {
    return {
      'severity_threshold': 'medium',
      'notification_types': ['incidents', 'delays', 'closures'],
      'advance_notice_minutes': 30,
    };
  }

  Future<int> _getCurrentTrafficVolume(String intersectionId) async => 150;
  Future<int> _getPedestrianActivity(String intersectionId) async => 25;
  Future<List<String>> _getEmergencyVehicles(String intersectionId) async => [];

  /// Dispose
  void dispose() {
    _trafficTimer?.cancel();
  }
}
