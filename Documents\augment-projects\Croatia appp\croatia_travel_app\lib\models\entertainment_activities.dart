import 'dart:math';

/// Kompletní model pro Entertainment & Activities
class EntertainmentActivity {
  final String id;
  final String name;
  final String description;
  final EntertainmentActivityType activityType;
  final String address;
  final double latitude;
  final double longitude;
  final String region;
  final double rating;
  final int reviewCount;
  final List<String> features;
  final String? operatingHours;
  final String? priceRange;
  final bool isActive;
  final bool isFamilyFriendly;
  final bool isIndoor;
  final bool requiresBooking;
  final AgeGroup targetAgeGroup;
  final ActivityDifficulty difficulty;
  final Duration? estimatedDuration;
  final List<String> equipment;
  final String? phone;
  final String? website;
  final String? email;
  final List<String> supportedLanguages;
  final DateTime lastUpdated;

  EntertainmentActivity({
    required this.id,
    required this.name,
    required this.description,
    required this.activityType,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.region,
    required this.rating,
    required this.reviewCount,
    required this.features,
    this.operatingHours,
    this.priceRange,
    required this.isActive,
    required this.isFamilyFriendly,
    required this.isIndoor,
    required this.requiresBooking,
    required this.targetAgeGroup,
    required this.difficulty,
    this.estimatedDuration,
    required this.equipment,
    this.phone,
    this.website,
    this.email,
    required this.supportedLanguages,
    required this.lastUpdated,
  });

  /// Má aktivita vysoké hodnocení (4.0+)
  bool get isTopRated => rating >= 4.0;

  /// Je dostupná pro rodiny s dětmi
  bool get isForFamilies => isFamilyFriendly && targetAgeGroup != AgeGroup.adults;

  /// Je vhodná pro začátečníky
  bool get isForBeginners => difficulty == ActivityDifficulty.easy || difficulty == ActivityDifficulty.beginner;

  /// Vzdálenost od zadané pozice (v km)
  double distanceFrom(double lat, double lng) {
    const double earthRadius = 6371;
    
    final double dLat = _toRadians(latitude - lat);
    final double dLng = _toRadians(longitude - lng);
    
    final double a = 
        sin(dLat / 2) * sin(dLat / 2) +
        cos(lat) * cos(latitude) * 
        sin(dLng / 2) * sin(dLng / 2);
    
    final double c = 2 * asin(sqrt(a));
    
    return earthRadius * c;
  }

  double _toRadians(double degrees) {
    return degrees * (pi / 180);
  }
}

/// Typy zábavních aktivit
enum EntertainmentActivityType {
  amusementPark,    // Zábavní park
  waterPark,        // Aquapark
  adventurePark,    // Adventure park
  zoo,              // Zoo
  aquarium,         // Akvárium
  playground,       // Dětské hřiště
  hiking,           // Pěší turistika
  cycling,          // Cyklistika
  climbing,         // Lezení
  kayaking,         // Kajak
  diving,           // Potápění
  sailing,          // Plachetnice
  nightclub,        // Noční klub
  bar,              // Bar
  liveMusic,        // Živá hudba
  theater,          // Divadlo
  cinema,           // Kino
  bowling,          // Bowling
  minigolf,         // Minigolf
  karting,          // Motokáry
  workshop,         // Workshop
  artClass,         // Umělecký kurz
  cookingClass,     // Vaření
  danceClass,       // Tanec
  fitnessClass,     // Fitness
}

/// Věková skupina
enum AgeGroup {
  children,         // Děti (0-12)
  teenagers,        // Teenageři (13-17)
  adults,           // Dospělí (18+)
  seniors,          // Senioři (65+)
  allAges,          // Všechny věky
}

/// Obtížnost aktivity
enum ActivityDifficulty {
  easy,             // Snadná
  beginner,         // Začátečník
  intermediate,     // Pokročilý
  advanced,         // Pokročilý
  expert,           // Expert
}

/// Zábavní park
class AmusementPark {
  final String id;
  final String name;
  final String description;
  final String address;
  final double latitude;
  final double longitude;
  final String region;
  final double rating;
  final int reviewCount;
  final List<Attraction> attractions;
  final String operatingHours;
  final String priceRange;
  final bool hasRestaurants;
  final bool hasParking;
  final bool hasDisabledAccess;
  final bool hasFirstAid;
  final int minAge;
  final int maxCapacity;
  final List<String> safetyFeatures;
  final String? phone;
  final String? website;
  final DateTime lastUpdated;

  AmusementPark({
    required this.id,
    required this.name,
    required this.description,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.region,
    required this.rating,
    required this.reviewCount,
    required this.attractions,
    required this.operatingHours,
    required this.priceRange,
    required this.hasRestaurants,
    required this.hasParking,
    required this.hasDisabledAccess,
    required this.hasFirstAid,
    required this.minAge,
    required this.maxCapacity,
    required this.safetyFeatures,
    this.phone,
    this.website,
    required this.lastUpdated,
  });

  /// Vzdálenost od zadané pozice (v km)
  double distanceFrom(double lat, double lng) {
    const double earthRadius = 6371;
    
    final double dLat = _toRadians(latitude - lat);
    final double dLng = _toRadians(longitude - lng);
    
    final double a = 
        sin(dLat / 2) * sin(dLat / 2) +
        cos(lat) * cos(latitude) * 
        sin(dLng / 2) * sin(dLng / 2);
    
    final double c = 2 * asin(sqrt(a));
    
    return earthRadius * c;
  }

  double _toRadians(double degrees) {
    return degrees * (pi / 180);
  }
}

/// Atrakce v zábavním parku
class Attraction {
  final String id;
  final String name;
  final String description;
  final AttractionType attractionType;
  final int minAge;
  final int? maxAge;
  final int minHeight; // v cm
  final int? maxHeight; // v cm
  final Duration duration;
  final int capacity;
  final bool isOperational;
  final List<String> safetyRequirements;
  final String? imageUrl;

  Attraction({
    required this.id,
    required this.name,
    required this.description,
    required this.attractionType,
    required this.minAge,
    this.maxAge,
    required this.minHeight,
    this.maxHeight,
    required this.duration,
    required this.capacity,
    required this.isOperational,
    required this.safetyRequirements,
    this.imageUrl,
  });
}

/// Typy atrakcí
enum AttractionType {
  rollerCoaster,    // Horská dráha
  waterSlide,       // Vodní skluzavka
  carousel,         // Kolotoč
  ferrisWheel,      // Ruské kolo
  bumperCars,       // Autíčka
  swings,           // Houpačky
  playground,       // Hřiště
  pool,             // Bazén
  arcade,           // Herní automat
  show,             // Představení
}

/// Outdoor aktivita
class OutdoorActivity {
  final String id;
  final String name;
  final String description;
  final OutdoorActivityType activityType;
  final String location;
  final double latitude;
  final double longitude;
  final String region;
  final double rating;
  final int reviewCount;
  final ActivityDifficulty difficulty;
  final Duration estimatedDuration;
  final double distance; // v km
  final int elevation; // v metrech
  final List<String> equipment;
  final List<String> safetyTips;
  final String? bestSeason;
  final String? weatherRequirements;
  final bool requiresGuide;
  final String? guideContact;
  final DateTime lastUpdated;

  OutdoorActivity({
    required this.id,
    required this.name,
    required this.description,
    required this.activityType,
    required this.location,
    required this.latitude,
    required this.longitude,
    required this.region,
    required this.rating,
    required this.reviewCount,
    required this.difficulty,
    required this.estimatedDuration,
    required this.distance,
    required this.elevation,
    required this.equipment,
    required this.safetyTips,
    this.bestSeason,
    this.weatherRequirements,
    required this.requiresGuide,
    this.guideContact,
    required this.lastUpdated,
  });

  /// Vzdálenost od zadané pozice (v km)
  double distanceFrom(double lat, double lng) {
    const double earthRadius = 6371;
    
    final double dLat = _toRadians(latitude - lat);
    final double dLng = _toRadians(longitude - lng);
    
    final double a = 
        sin(dLat / 2) * sin(dLat / 2) +
        cos(lat) * cos(latitude) * 
        sin(dLng / 2) * sin(dLng / 2);
    
    final double c = 2 * asin(sqrt(a));
    
    return earthRadius * c;
  }

  double _toRadians(double degrees) {
    return degrees * (pi / 180);
  }
}

/// Typy outdoor aktivit
enum OutdoorActivityType {
  hiking,           // Pěší turistika
  cycling,          // Cyklistika
  mountainBiking,   // Horská cyklistika
  rockClimbing,     // Skalní lezení
  kayaking,         // Kajak
  canoeing,         // Kanoistika
  sailing,          // Plachetnice
  windsurfing,      // Windsurfing
  diving,           // Potápění
  snorkeling,       // Šnorchlování
  fishing,          // Rybaření
  birdWatching,     // Pozorování ptáků
  photography,      // Fotografování
  geocaching,       // Geocaching
}

/// Noční zábava
class NightlifeVenue {
  final String id;
  final String name;
  final String description;
  final NightlifeType venueType;
  final String address;
  final double latitude;
  final double longitude;
  final String region;
  final double rating;
  final int reviewCount;
  final String operatingHours;
  final int minAge;
  final bool hasLiveMusic;
  final bool hasDanceFloor;
  final bool hasOutdoorSeating;
  final bool acceptsReservations;
  final List<String> musicGenres;
  final List<String> drinkSpecialties;
  final String? dressCode;
  final String? phone;
  final String? website;
  final DateTime lastUpdated;

  NightlifeVenue({
    required this.id,
    required this.name,
    required this.description,
    required this.venueType,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.region,
    required this.rating,
    required this.reviewCount,
    required this.operatingHours,
    required this.minAge,
    required this.hasLiveMusic,
    required this.hasDanceFloor,
    required this.hasOutdoorSeating,
    required this.acceptsReservations,
    required this.musicGenres,
    required this.drinkSpecialties,
    this.dressCode,
    this.phone,
    this.website,
    required this.lastUpdated,
  });

  /// Vzdálenost od zadané pozice (v km)
  double distanceFrom(double lat, double lng) {
    const double earthRadius = 6371;
    
    final double dLat = _toRadians(latitude - lat);
    final double dLng = _toRadians(longitude - lng);
    
    final double a = 
        sin(dLat / 2) * sin(dLat / 2) +
        cos(lat) * cos(latitude) * 
        sin(dLng / 2) * sin(dLng / 2);
    
    final double c = 2 * asin(sqrt(a));
    
    return earthRadius * c;
  }

  double _toRadians(double degrees) {
    return degrees * (pi / 180);
  }
}

/// Typy nočního života
enum NightlifeType {
  nightclub,        // Noční klub
  bar,              // Bar
  pub,              // Hospoda
  cocktailBar,      // Koktejlový bar
  wineBar,          // Vinárna
  beerGarden,       // Pivní zahrada
  liveMusic,        // Živá hudba
  jazzClub,         // Jazz klub
  karaoke,          // Karaoke
  casino,           // Kasino
}

/// Rodinná aktivita
class FamilyActivity {
  final String id;
  final String name;
  final String description;
  final FamilyActivityType activityType;
  final String address;
  final double latitude;
  final double longitude;
  final String region;
  final double rating;
  final int reviewCount;
  final AgeGroup targetAgeGroup;
  final String operatingHours;
  final String priceRange;
  final bool hasParking;
  final bool hasRestrooms;
  final bool hasDisabledAccess;
  final bool hasStrollerAccess;
  final List<String> safetyFeatures;
  final String? phone;
  final String? website;
  final DateTime lastUpdated;

  FamilyActivity({
    required this.id,
    required this.name,
    required this.description,
    required this.activityType,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.region,
    required this.rating,
    required this.reviewCount,
    required this.targetAgeGroup,
    required this.operatingHours,
    required this.priceRange,
    required this.hasParking,
    required this.hasRestrooms,
    required this.hasDisabledAccess,
    required this.hasStrollerAccess,
    required this.safetyFeatures,
    this.phone,
    this.website,
    required this.lastUpdated,
  });

  /// Vzdálenost od zadané pozice (v km)
  double distanceFrom(double lat, double lng) {
    const double earthRadius = 6371;
    
    final double dLat = _toRadians(latitude - lat);
    final double dLng = _toRadians(longitude - lng);
    
    final double a = 
        sin(dLat / 2) * sin(dLat / 2) +
        cos(lat) * cos(latitude) * 
        sin(dLng / 2) * sin(dLng / 2);
    
    final double c = 2 * asin(sqrt(a));
    
    return earthRadius * c;
  }

  double _toRadians(double degrees) {
    return degrees * (pi / 180);
  }
}

/// Typy rodinných aktivit
enum FamilyActivityType {
  playground,       // Dětské hřiště
  zoo,              // Zoo
  aquarium,         // Akvárium
  museum,           // Muzeum
  scienceCenter,    // Vědecké centrum
  park,             // Park
  beach,            // Pláž
  picnicArea,       // Piknikové místo
  miniGolf,         // Minigolf
  bowling,          // Bowling
  cinema,           // Kino
  theater,          // Divadlo
  workshop,         // Workshop
  farmVisit,        // Návštěva farmy
}
