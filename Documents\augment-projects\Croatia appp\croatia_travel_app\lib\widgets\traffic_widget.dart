import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import '../models/traffic.dart';
import '../services/traffic_service.dart';

class TrafficWidget extends StatefulWidget {
  const TrafficWidget({super.key});

  @override
  State<TrafficWidget> createState() => _TrafficWidgetState();
}

class _TrafficWidgetState extends State<TrafficWidget> {
  final TrafficService _trafficService = TrafficService();

  List<TrafficIncident> _incidents = [];
  List<TrafficCondition> _conditions = [];
  List<TrafficCamera> _cameras = [];
  List<TrafficAlert> _alerts = [];
  bool _isLoading = false;
  Position? _currentPosition;

  @override
  void initState() {
    super.initState();
    _loadCurrentLocation();
  }

  @override
  void dispose() {
    _trafficService.stopTrafficUpdates();
    _trafficService.dispose();
    super.dispose();
  }

  Future<void> _loadCurrentLocation() async {
    try {
      setState(() => _isLoading = true);

      _currentPosition = await Geolocator.getCurrentPosition();
      await _loadTrafficData();
      _startRealTimeUpdates();
    } catch (e) {
      _showError('Chyba při načítání polohy: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadTrafficData() async {
    if (_currentPosition == null) return;

    try {
      // Načtení incidentů
      final incidents = await _trafficService.getTrafficIncidents(
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
      );

      // Načtení podmínek
      final conditions = await _trafficService.getTrafficConditions(
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
      );

      // Načtení kamer
      final cameras = await _trafficService.getTrafficCameras(
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
      );

      // Načtení upozornění
      final alerts = await _trafficService.getTrafficAlerts(city: 'Zagreb');

      setState(() {
        _incidents = incidents;
        _conditions = conditions;
        _cameras = cameras;
        _alerts = alerts;
      });
    } catch (e) {
      _showError('Chyba při načítání dopravních dat: $e');
    }
  }

  void _startRealTimeUpdates() {
    if (_currentPosition == null) return;

    _trafficService.startTrafficUpdates(
      latitude: _currentPosition!.latitude,
      longitude: _currentPosition!.longitude,
      onIncidentsUpdate: (incidents) {
        if (mounted) {
          setState(() => _incidents = incidents);
        }
      },
      onConditionsUpdate: (conditions) {
        if (mounted) {
          setState(() => _conditions = conditions);
        }
      },
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _loadTrafficData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildQuickActions(),
            const SizedBox(height: 24),
            if (_alerts.isNotEmpty) ...[
              _buildTrafficAlerts(),
              const SizedBox(height: 24),
            ],
            _buildTrafficIncidents(),
            const SizedBox(height: 24),
            _buildTrafficConditions(),
            const SizedBox(height: 24),
            _buildTrafficCameras(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Rychlé akce',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _reportIncident,
                    icon: const Icon(Icons.report),
                    label: const Text('Nahlásit incident'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _checkRoute,
                    icon: const Icon(Icons.route),
                    label: const Text('Zkontrolovat trasu'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrafficAlerts() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Dopravní upozornění',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _alerts.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final alert = _alerts[index];
                return _buildAlertTile(alert);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAlertTile(TrafficAlert alert) {
    return ListTile(
      leading: Icon(
        _getAlertIcon(alert.type),
        color: _getSeverityColor(alert.severity),
      ),
      title: Text(alert.title),
      subtitle: Text(alert.message),
      trailing: Text(
        alert.typeText,
        style: Theme.of(context).textTheme.bodySmall,
      ),
    );
  }

  Widget _buildTrafficIncidents() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Dopravní incidenty',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else if (_incidents.isEmpty)
              const Text('Žádné incidenty v okolí')
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _incidents.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final incident = _incidents[index];
                  return _buildIncidentTile(incident);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildIncidentTile(TrafficIncident incident) {
    return ListTile(
      leading: Icon(
        _getIncidentIcon(incident.type),
        color: _getSeverityColor(incident.severity),
      ),
      title: Text(incident.title),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(incident.description),
          Text(
            '${incident.typeText} • ${incident.severityText}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: _getSeverityColor(incident.severity),
            ),
          ),
          if (incident.estimatedDelay != null)
            Text('Zpoždění: ${incident.estimatedDelay!.inMinutes} min'),
        ],
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: () => _voteOnIncident(incident, true),
                icon: const Icon(Icons.thumb_up, size: 16),
              ),
              Text('${incident.upvotes}'),
            ],
          ),
        ],
      ),
      onTap: () => _showIncidentDetails(incident),
    );
  }

  Widget _buildTrafficConditions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Dopravní podmínky',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (_conditions.isEmpty)
              const Text('Žádné informace o dopravních podmínkách')
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _conditions.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final condition = _conditions[index];
                  return _buildConditionTile(condition);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildConditionTile(TrafficCondition condition) {
    final flowColor = _getFlowColor(condition.flow);

    return ListTile(
      leading: Icon(Icons.add_road, color: flowColor),
      title: Text(condition.roadName),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Průměrná rychlost: ${condition.averageSpeed.toInt()} km/h'),
          Text(
            'Provoz: ${condition.flowText}',
            style: TextStyle(color: flowColor, fontWeight: FontWeight.bold),
          ),
          if (condition.delay.inMinutes > 0)
            Text('Zpoždění: ${condition.delay.inMinutes} min'),
        ],
      ),
    );
  }

  Widget _buildTrafficCameras() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Dopravní kamery',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (_cameras.isEmpty)
              const Text('Žádné kamery v okolí')
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _cameras.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final camera = _cameras[index];
                  return _buildCameraTile(camera);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCameraTile(TrafficCamera camera) {
    return ListTile(
      leading: Icon(
        Icons.videocam,
        color: camera.isActive ? Colors.green : Colors.grey,
      ),
      title: Text(camera.name),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [Text(camera.location), Text('Typ: ${camera.typeText}')],
      ),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () => _showCameraImage(camera),
    );
  }

  IconData _getIncidentIcon(TrafficIncidentType type) {
    switch (type) {
      case TrafficIncidentType.accident:
        return Icons.car_crash;
      case TrafficIncidentType.roadwork:
        return Icons.construction;
      case TrafficIncidentType.closure:
        return Icons.block;
      case TrafficIncidentType.congestion:
        return Icons.traffic;
      case TrafficIncidentType.weather:
        return Icons.cloud;
      case TrafficIncidentType.event:
        return Icons.event;
      case TrafficIncidentType.breakdown:
        return Icons.car_repair;
      case TrafficIncidentType.other:
        return Icons.warning;
    }
  }

  IconData _getAlertIcon(TrafficAlertType type) {
    switch (type) {
      case TrafficAlertType.general:
        return Icons.info;
      case TrafficAlertType.weather:
        return Icons.cloud;
      case TrafficAlertType.emergency:
        return Icons.emergency;
      case TrafficAlertType.event:
        return Icons.event;
      case TrafficAlertType.maintenance:
        return Icons.build;
    }
  }

  Color _getSeverityColor(TrafficSeverity severity) {
    switch (severity) {
      case TrafficSeverity.low:
        return Colors.green;
      case TrafficSeverity.medium:
        return Colors.orange;
      case TrafficSeverity.high:
        return Colors.red;
      case TrafficSeverity.critical:
        return Colors.purple;
    }
  }

  Color _getFlowColor(TrafficFlow flow) {
    switch (flow) {
      case TrafficFlow.free:
        return Colors.green;
      case TrafficFlow.light:
        return Colors.lightGreen;
      case TrafficFlow.moderate:
        return Colors.orange;
      case TrafficFlow.heavy:
        return Colors.red;
      case TrafficFlow.congested:
        return Colors.purple;
    }
  }

  void _reportIncident() {
    // Implementace hlášení incidentu
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Otevírám formulář pro hlášení incidentu...'),
      ),
    );
  }

  void _checkRoute() {
    // Implementace kontroly trasy
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Kontroluji dopravní situaci na trase...')),
    );
  }

  void _voteOnIncident(TrafficIncident incident, bool isUpvote) {
    // Implementace hlasování o incidentu
    _trafficService.voteOnIncident(incident.id, isUpvote);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isUpvote
              ? 'Hlasoval jste pro incident'
              : 'Hlasoval jste proti incidentu',
        ),
      ),
    );
  }

  void _showIncidentDetails(TrafficIncident incident) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(incident.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(incident.description),
            const SizedBox(height: 8),
            Text('Typ: ${incident.typeText}'),
            Text('Závažnost: ${incident.severityText}'),
            if (incident.affectedRoads.isNotEmpty)
              Text('Postižené silnice: ${incident.affectedRoads.join(', ')}'),
            if (incident.estimatedDelay != null)
              Text(
                'Odhadované zpoždění: ${incident.estimatedDelay!.inMinutes} min',
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zavřít'),
          ),
        ],
      ),
    );
  }

  void _showCameraImage(TrafficCamera camera) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(camera.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.network(
              camera.imageUrl,
              errorBuilder: (context, error, stackTrace) =>
                  const Text('Chyba při načítání obrázku'),
            ),
            const SizedBox(height: 8),
            Text('Lokace: ${camera.location}'),
            Text('Poslední aktualizace: ${camera.lastUpdated.toString()}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zavřít'),
          ),
        ],
      ),
    );
  }
}
