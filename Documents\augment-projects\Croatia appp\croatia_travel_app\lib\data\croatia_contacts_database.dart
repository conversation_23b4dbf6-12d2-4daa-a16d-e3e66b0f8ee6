import '../models/contact_info.dart';

/// 📞 KOMPLETNÍ DATABÁZE KONTAKTŮ PRO CHORVATSKO
class CroatiaContactsDatabase {
  /// 🏛️ ÚŘADY A INSTITUCE - KOMPLETNÍ DATABÁZE
  static List<ContactInfo> getGovernmentOffices() {
    return [
      // ========== ZAGREB ==========
      ContactInfo(
        id: 'zagreb_city_hall',
        name: 'Gradska uprava Zagreb',
        nameEn: 'Zagreb City Administration',
        category: ContactCategory.government,
        phone: '+385 1 6106 111',
        email: '<EMAIL>',
        website: 'https://www.zagreb.hr',
        address: 'Trg Stjepana Radića 1, 10000 Zagreb',
        openingHours: 'Pon-Pet: 7:30-15:30',
        services: [
          'Građanski dokumenti',
          'Dozvole',
          'Registracije',
          'Porezi',
          'Urbanizam',
        ],
        languages: ['Hrvatski', 'English'],
        latitude: 45.8150,
        longitude: 15.9819,
        city: 'Zagreb',
        region: 'Grad Zagreb',
      ),

      ContactInfo(
        id: 'zagreb_mup',
        name: 'MUP Zagreb - Policijska uprava',
        nameEn: 'Zagreb Police Administration',
        category: ContactCategory.government,
        phone: '+385 1 4561 111',
        emergencyPhone: '192',
        email: '<EMAIL>',
        website: 'https://www.mup.hr',
        address: 'Petrinjska 30, 10000 Zagreb',
        openingHours: 'Pon-Pet: 8:00-16:00',
        services: [
          'Osobne iskaznice',
          'Putovnice',
          'Vozačke dozvole',
          'Registracija vozila',
        ],
        languages: ['Hrvatski', 'English'],
        latitude: 45.8081,
        longitude: 15.9756,
        city: 'Zagreb',
        region: 'Grad Zagreb',
      ),

      ContactInfo(
        id: 'zagreb_tax_office',
        name: 'Porezna uprava Zagreb',
        nameEn: 'Zagreb Tax Administration',
        category: ContactCategory.government,
        phone: '+385 1 6001 111',
        email: '<EMAIL>',
        website: 'https://www.porezna-uprava.hr',
        address: 'Katančićeva 5, 10000 Zagreb',
        openingHours: 'Pon-Pet: 8:00-16:00',
        services: ['Porezi', 'PDV', 'Carinske usluge', 'Porezne prijave'],
        languages: ['Hrvatski', 'English'],
        latitude: 45.8144,
        longitude: 15.9758,
        city: 'Zagreb',
        region: 'Grad Zagreb',
      ),

      // ========== SPLIT ==========
      ContactInfo(
        id: 'split_city_hall',
        name: 'Gradska uprava Split',
        nameEn: 'Split City Administration',
        category: ContactCategory.government,
        phone: '+385 21 345 345',
        email: '<EMAIL>',
        website: 'https://www.split.hr',
        address: 'Trg Gaje Bulata 1, 21000 Split',
        openingHours: 'Pon-Pet: 7:30-15:30',
        services: [
          'Građanski dokumenti',
          'Komunalne usluge',
          'Urbanizam',
          'Turistička taksa',
        ],
        languages: ['Hrvatski', 'English', 'Italiano'],
        latitude: 43.5081,
        longitude: 16.4402,
        city: 'Split',
        region: 'Splitsko-dalmatinska',
      ),

      ContactInfo(
        id: 'split_mup',
        name: 'MUP Split - Policijska uprava',
        nameEn: 'Split Police Administration',
        category: ContactCategory.government,
        phone: '+385 21 307 111',
        emergencyPhone: '192',
        email: '<EMAIL>',
        website: 'https://www.mup.hr',
        address: 'Trg Hrvatske bratske zajednice 9, 21000 Split',
        openingHours: 'Pon-Pet: 8:00-16:00',
        services: [
          'Osobne iskaznice',
          'Putovnice',
          'Vozačke dozvole',
          'Registracija vozila',
        ],
        languages: ['Hrvatski', 'English', 'Italiano'],
        latitude: 43.5147,
        longitude: 16.4435,
        city: 'Split',
        region: 'Splitsko-dalmatinska',
      ),

      ContactInfo(
        id: 'split_tax_office',
        name: 'Porezna uprava Split',
        nameEn: 'Split Tax Administration',
        category: ContactCategory.government,
        phone: '+385 21 329 329',
        email: '<EMAIL>',
        website: 'https://www.porezna-uprava.hr',
        address: 'Domovinskog rata 2, 21000 Split',
        openingHours: 'Pon-Pet: 8:00-16:00',
        services: ['Porezi', 'PDV', 'Porezne prijave', 'Turistička taksa'],
        languages: ['Hrvatski', 'English', 'Italiano'],
        latitude: 43.5092,
        longitude: 16.4394,
        city: 'Split',
        region: 'Splitsko-dalmatinska',
      ),

      // ========== DUBROVNIK ==========
      ContactInfo(
        id: 'dubrovnik_city_hall',
        name: 'Gradska uprava Dubrovnik',
        nameEn: 'Dubrovnik City Administration',
        category: ContactCategory.government,
        phone: '+385 20 323 100',
        email: '<EMAIL>',
        website: 'https://www.dubrovnik.hr',
        address: 'Ul. Grada Vukovara 1, 20000 Dubrovnik',
        openingHours: 'Pon-Pet: 7:30-15:30',
        services: [
          'Turistička taksa',
          'Dozvole za snimanje',
          'Komunalne usluge',
          'Građanski dokumenti',
        ],
        languages: ['Hrvatski', 'English', 'Italiano', 'Deutsch'],
        latitude: 42.6507,
        longitude: 18.0944,
        city: 'Dubrovnik',
        region: 'Dubrovačko-neretvanska',
      ),

      ContactInfo(
        id: 'dubrovnik_mup',
        name: 'MUP Dubrovnik - Policijska uprava',
        nameEn: 'Dubrovnik Police Administration',
        category: ContactCategory.government,
        phone: '+385 20 457 111',
        emergencyPhone: '192',
        email: '<EMAIL>',
        website: 'https://www.mup.hr',
        address: 'Ul. Nikole Tesle 1, 20000 Dubrovnik',
        openingHours: 'Pon-Pet: 8:00-16:00',
        services: [
          'Osobne iskaznice',
          'Putovnice',
          'Vozačke dozvole',
          'Registracija vozila',
        ],
        languages: ['Hrvatski', 'English', 'Italiano', 'Deutsch'],
        latitude: 42.6324,
        longitude: 18.1074,
        city: 'Dubrovnik',
        region: 'Dubrovačko-neretvanska',
      ),

      // ========== RIJEKA ==========
      ContactInfo(
        id: 'rijeka_city_hall',
        name: 'Gradska uprava Rijeka',
        nameEn: 'Rijeka City Administration',
        category: ContactCategory.government,
        phone: '+385 51 209 309',
        email: '<EMAIL>',
        website: 'https://www.rijeka.hr',
        address: 'Korzo 16, 51000 Rijeka',
        openingHours: 'Pon-Pet: 7:30-15:30',
        services: [
          'Građanski dokumenti',
          'Lučke usluge',
          'Komunalije',
          'Urbanizam',
        ],
        languages: ['Hrvatski', 'English', 'Italiano'],
        latitude: 45.3271,
        longitude: 14.4422,
        city: 'Rijeka',
        region: 'Primorsko-goranska',
      ),

      ContactInfo(
        id: 'rijeka_mup',
        name: 'MUP Rijeka - Policijska uprava',
        nameEn: 'Rijeka Police Administration',
        category: ContactCategory.government,
        phone: '+385 51 313 111',
        emergencyPhone: '192',
        email: '<EMAIL>',
        website: 'https://www.mup.hr',
        address: 'Ul. Viktora Cara Emina 5, 51000 Rijeka',
        openingHours: 'Pon-Pet: 8:00-16:00',
        services: [
          'Osobne iskaznice',
          'Putovnice',
          'Vozačke dozvole',
          'Registracija vozila',
        ],
        languages: ['Hrvatski', 'English', 'Italiano'],
        latitude: 45.3371,
        longitude: 14.4058,
        city: 'Rijeka',
        region: 'Primorsko-goranska',
      ),

      // ========== ZADAR ==========
      ContactInfo(
        id: 'zadar_city_hall',
        name: 'Gradska uprava Zadar',
        nameEn: 'Zadar City Administration',
        category: ContactCategory.government,
        phone: '+385 23 208 000',
        email: '<EMAIL>',
        website: 'https://www.grad-zadar.hr',
        address: 'Narodni trg 1, 23000 Zadar',
        openingHours: 'Pon-Pet: 7:30-15:30',
        services: [
          'Građanski dokumenti',
          'Komunalne usluge',
          'Urbanizam',
          'Turistička taksa',
        ],
        languages: ['Hrvatski', 'English', 'Italiano'],
        latitude: 44.1194,
        longitude: 15.2314,
        city: 'Zadar',
        region: 'Zadarska',
      ),

      ContactInfo(
        id: 'zadar_mup',
        name: 'MUP Zadar - Policijska uprava',
        nameEn: 'Zadar Police Administration',
        category: ContactCategory.government,
        phone: '+385 23 309 111',
        emergencyPhone: '192',
        email: '<EMAIL>',
        website: 'https://www.mup.hr',
        address: 'Ul. Petra Zoranića 2, 23000 Zadar',
        openingHours: 'Pon-Pet: 8:00-16:00',
        services: [
          'Osobne iskaznice',
          'Putovnice',
          'Vozačke dozvole',
          'Registracija vozila',
        ],
        languages: ['Hrvatski', 'English', 'Italiano'],
        latitude: 44.1156,
        longitude: 15.2267,
        city: 'Zadar',
        region: 'Zadarska',
      ),

      // ========== PULA ==========
      ContactInfo(
        id: 'pula_city_hall',
        name: 'Gradska uprava Pula',
        nameEn: 'Pula City Administration',
        category: ContactCategory.government,
        phone: '+385 52 371 600',
        email: '<EMAIL>',
        website: 'https://www.pula.hr',
        address: 'Giardini 1, 52100 Pula',
        openingHours: 'Pon-Pet: 7:30-15:30',
        services: [
          'Građanski dokumenti',
          'Komunalne usluge',
          'Urbanizam',
          'Turistička taksa',
        ],
        languages: ['Hrvatski', 'English', 'Italiano'],
        latitude: 44.8683,
        longitude: 13.8481,
        city: 'Pula',
        region: 'Istarska',
      ),

      ContactInfo(
        id: 'pula_mup',
        name: 'MUP Pula - Policijska uprava',
        nameEn: 'Pula Police Administration',
        category: ContactCategory.government,
        phone: '+385 52 511 111',
        emergencyPhone: '192',
        email: '<EMAIL>',
        website: 'https://www.mup.hr',
        address: 'Ul. Mate Balote 6, 52100 Pula',
        openingHours: 'Pon-Pet: 8:00-16:00',
        services: [
          'Osobne iskaznice',
          'Putovnice',
          'Vozačke dozvole',
          'Registracija vozila',
        ],
        languages: ['Hrvatski', 'English', 'Italiano'],
        latitude: 44.8654,
        longitude: 13.8493,
        city: 'Pula',
        region: 'Istarska',
      ),

      // ========== OSIJEK ==========
      ContactInfo(
        id: 'osijek_city_hall',
        name: 'Gradska uprava Osijek',
        nameEn: 'Osijek City Administration',
        category: ContactCategory.government,
        phone: '+385 31 224 100',
        email: '<EMAIL>',
        website: 'https://www.osijek.hr',
        address: 'Trg Ante Starčevića 4, 31000 Osijek',
        openingHours: 'Pon-Pet: 7:30-15:30',
        services: [
          'Građanski dokumenti',
          'Komunalne usluge',
          'Urbanizam',
          'Porezi',
        ],
        languages: ['Hrvatski', 'English'],
        latitude: 45.5550,
        longitude: 18.6955,
        city: 'Osijek',
        region: 'Osječko-baranjska',
      ),

      ContactInfo(
        id: 'osijek_mup',
        name: 'MUP Osijek - Policijska uprava',
        nameEn: 'Osijek Police Administration',
        category: ContactCategory.government,
        phone: '+385 31 230 111',
        emergencyPhone: '192',
        email: '<EMAIL>',
        website: 'https://www.mup.hr',
        address: 'Ul. Josipa Huttlera 33, 31000 Osijek',
        openingHours: 'Pon-Pet: 8:00-16:00',
        services: [
          'Osobne iskaznice',
          'Putovnice',
          'Vozačke dozvole',
          'Registracija vozila',
        ],
        languages: ['Hrvatski', 'English'],
        latitude: 45.5547,
        longitude: 18.6918,
        city: 'Osijek',
        region: 'Osječko-baranjska',
      ),

      // ========== VARAŽDIN ==========
      ContactInfo(
        id: 'varazdin_city_hall',
        name: 'Gradska uprava Varaždin',
        nameEn: 'Varaždin City Administration',
        category: ContactCategory.government,
        phone: '+385 42 390 111',
        email: '<EMAIL>',
        website: 'https://www.varazdin.hr',
        address: 'Trg kralja Tomislava 1, 42000 Varaždin',
        openingHours: 'Pon-Pet: 7:30-15:30',
        services: [
          'Građanski dokumenti',
          'Komunalne usluge',
          'Urbanizam',
          'Porezi',
        ],
        languages: ['Hrvatski', 'English'],
        latitude: 46.3044,
        longitude: 16.3378,
        city: 'Varaždin',
        region: 'Varaždinska',
      ),

      // ========== ŠIBENIK ==========
      ContactInfo(
        id: 'sibenik_city_hall',
        name: 'Gradska uprava Šibenik',
        nameEn: 'Šibenik City Administration',
        category: ContactCategory.government,
        phone: '+385 22 214 252',
        email: '<EMAIL>',
        website: 'https://www.sibenik.hr',
        address: 'Trg Republike Hrvatske 3, 22000 Šibenik',
        openingHours: 'Pon-Pet: 7:30-15:30',
        services: [
          'Građanski dokumenti',
          'Komunalne usluge',
          'Urbanizam',
          'Turistička taksa',
        ],
        languages: ['Hrvatski', 'English', 'Italiano'],
        latitude: 43.7350,
        longitude: 15.8952,
        city: 'Šibenik',
        region: 'Šibensko-kninska',
      ),

      // ========== KARLOVAC ==========
      ContactInfo(
        id: 'karlovac_city_hall',
        name: 'Gradska uprava Karlovac',
        nameEn: 'Karlovac City Administration',
        category: ContactCategory.government,
        phone: '+385 47 615 115',
        email: '<EMAIL>',
        website: 'https://www.karlovac.hr',
        address: 'Banija 2, 47000 Karlovac',
        openingHours: 'Pon-Pet: 7:30-15:30',
        services: [
          'Građanski dokumenti',
          'Komunalne usluge',
          'Urbanizam',
          'Porezi',
        ],
        languages: ['Hrvatski', 'English'],
        latitude: 45.4870,
        longitude: 15.5378,
        city: 'Karlovac',
        region: 'Karlovačka',
      ),
    ];
  }

  /// 🏥 NEMOCNICE A ZDRAVOTNICKÁ ZAŘÍZENÍ
  static List<ContactInfo> getHealthcareProviders() {
    return [
      // ZAGREB
      ContactInfo(
        id: 'kbc_zagreb',
        name: 'KBC Zagreb',
        nameEn: 'Zagreb University Hospital Centre',
        category: ContactCategory.healthcare,
        phone: '+385 1 2388 888',
        emergencyPhone: '194',
        email: '<EMAIL>',
        website: 'https://www.kbc-zagreb.hr',
        address: 'Kišpatićeva 12, 10000 Zagreb',
        openingHours: '24/7 - Hitna služba',
        services: [
          'Hitna medicina',
          'Specijalistički pregledi',
          'Operacije',
          'Dijagnostika',
        ],
        languages: ['Hrvatski', 'English'],
        latitude: 45.8150,
        longitude: 15.9700,
        city: 'Zagreb',
        region: 'Zagreb',
        isEmergency: true,
      ),

      ContactInfo(
        id: 'dom_zdravlja_zagreb',
        name: 'Dom zdravlja Zagreb - Centar',
        nameEn: 'Zagreb Health Center',
        category: ContactCategory.healthcare,
        phone: '+385 1 4604 200',
        email: '<EMAIL>',
        website: 'https://www.dzz.hr',
        address: 'Runjaninova 4, 10000 Zagreb',
        openingHours: 'Pon-Pet: 7:00-19:00, Sub: 8:00-14:00',
        services: ['Opća medicina', 'Preventivni pregledi', 'Vakcinacija'],
        languages: ['Hrvatski', 'English'],
        latitude: 45.8144,
        longitude: 15.9758,
        city: 'Zagreb',
        region: 'Zagreb',
      ),

      // SPLIT
      ContactInfo(
        id: 'kbc_split',
        name: 'KBC Split',
        nameEn: 'Split University Hospital Centre',
        category: ContactCategory.healthcare,
        phone: '+385 21 556 111',
        emergencyPhone: '194',
        email: '<EMAIL>',
        website: 'https://www.kbsplit.hr',
        address: 'Spinčićeva 1, 21000 Split',
        openingHours: '24/7 - Hitna služba',
        services: [
          'Hitna medicina',
          'Kardiologija',
          'Neurologija',
          'Onkologija',
        ],
        languages: ['Hrvatski', 'English', 'Italiano'],
        latitude: 43.5147,
        longitude: 16.4435,
        city: 'Split',
        region: 'Dalmacija',
        isEmergency: true,
      ),

      // DUBROVNIK
      ContactInfo(
        id: 'obd_dubrovnik',
        name: 'Opća bolnica Dubrovnik',
        nameEn: 'Dubrovnik General Hospital',
        category: ContactCategory.healthcare,
        phone: '+385 20 431 777',
        emergencyPhone: '194',
        email: '<EMAIL>',
        website: 'https://www.bolnica-du.hr',
        address: 'Dr. Roka Mišetića 2, 20000 Dubrovnik',
        openingHours: '24/7 - Hitna služba',
        services: ['Hitna medicina', 'Interna medicina', 'Kirurgija'],
        languages: ['Hrvatski', 'English', 'Italiano', 'Deutsch'],
        latitude: 42.6324,
        longitude: 18.1074,
        city: 'Dubrovnik',
        region: 'Dubrovačko-neretvanska',
        isEmergency: true,
      ),

      // RIJEKA
      ContactInfo(
        id: 'kbc_rijeka',
        name: 'KBC Rijeka',
        nameEn: 'Rijeka University Hospital Centre',
        category: ContactCategory.healthcare,
        phone: '+385 51 658 111',
        emergencyPhone: '194',
        email: '<EMAIL>',
        website: 'https://www.kbc-rijeka.hr',
        address: 'Krešimirova 42, 51000 Rijeka',
        openingHours: '24/7 - Hitna služba',
        services: ['Hitna medicina', 'Transplantacije', 'Pedijatrija'],
        languages: ['Hrvatski', 'English', 'Italiano'],
        latitude: 45.3371,
        longitude: 14.4058,
        city: 'Rijeka',
        region: 'Primorsko-goranska',
        isEmergency: true,
      ),
    ];
  }

  /// 🎭 DIVADLA A KULTURNÍ INSTITUCE
  static List<ContactInfo> getCulturalInstitutions() {
    return [
      // ZAGREB
      ContactInfo(
        id: 'hnk_zagreb',
        name: 'Hrvatsko narodno kazalište Zagreb',
        nameEn: 'Croatian National Theatre Zagreb',
        category: ContactCategory.culture,
        phone: '+385 1 4888 418',
        email: '<EMAIL>',
        website: 'https://www.hnk.hr',
        address: 'Trg Republike Hrvatske 15, 10000 Zagreb',
        openingHours: 'Pon-Pet: 10:00-19:00, Sub: 10:00-14:00',
        services: ['Opera', 'Balet', 'Drama', 'Prodaja ulaznica'],
        languages: ['Hrvatski', 'English'],
        latitude: 45.8094,
        longitude: 15.9706,
        city: 'Zagreb',
        region: 'Zagreb',
      ),

      ContactInfo(
        id: 'msz',
        name: 'Muzej suvremene umjetnosti Zagreb',
        nameEn: 'Museum of Contemporary Art Zagreb',
        category: ContactCategory.culture,
        phone: '+385 1 6052 700',
        email: '<EMAIL>',
        website: 'https://www.msu.hr',
        address: 'Avenija Dubrovnik 17, 10000 Zagreb',
        openingHours: 'Uto-Pet: 11:00-19:00, Sub-Ned: 11:00-18:00',
        services: ['Izložbe', 'Edukacijski programi', 'Muzejska trgovina'],
        languages: ['Hrvatski', 'English'],
        latitude: 45.7833,
        longitude: 15.9667,
        city: 'Zagreb',
        region: 'Zagreb',
      ),

      // SPLIT
      ContactInfo(
        id: 'hnk_split',
        name: 'Hrvatsko narodno kazalište Split',
        nameEn: 'Croatian National Theatre Split',
        category: ContactCategory.culture,
        phone: '+385 21 344 999',
        email: '<EMAIL>',
        website: 'https://www.hnk-split.hr',
        address: 'Trg Gaje Bulata 1, 21000 Split',
        openingHours: 'Pon-Pet: 9:00-20:00, Sub: 9:00-14:00',
        services: ['Opera', 'Drama', 'Balet', 'Prodaja ulaznica'],
        languages: ['Hrvatski', 'English', 'Italiano'],
        latitude: 43.5081,
        longitude: 16.4402,
        city: 'Split',
        region: 'Dalmacija',
      ),

      // DUBROVNIK
      ContactInfo(
        id: 'dubrovnik_summer_festival',
        name: 'Dubrovačke ljetne igre',
        nameEn: 'Dubrovnik Summer Festival',
        category: ContactCategory.culture,
        phone: '+385 20 326 100',
        email: '<EMAIL>',
        website: 'https://www.dubrovnik-festival.hr',
        address: 'Poljana Paska Miličevića 1, 20000 Dubrovnik',
        openingHours: 'Pon-Pet: 8:00-16:00',
        services: [
          'Festival organizacija',
          'Prodaja ulaznica',
          'Kulturni programi',
        ],
        languages: ['Hrvatski', 'English', 'Italiano', 'Deutsch'],
        latitude: 42.6419,
        longitude: 18.1081,
        city: 'Dubrovnik',
        region: 'Dubrovačko-neretvanska',
      ),

      // RIJEKA
      ContactInfo(
        id: 'hnk_rijeka',
        name: 'Hrvatsko narodno kazalište Rijeka',
        nameEn: 'Croatian National Theatre Rijeka',
        category: ContactCategory.culture,
        phone: '+385 51 337 114',
        email: '<EMAIL>',
        website: 'https://www.hnk-rijeka.hr',
        address: 'Uljarska 1, 51000 Rijeka',
        openingHours: 'Pon-Pet: 10:00-19:00, Sub: 10:00-14:00',
        services: ['Opera', 'Drama', 'Balet', 'Prodaja ulaznica'],
        languages: ['Hrvatski', 'English', 'Italiano'],
        latitude: 45.3271,
        longitude: 14.4422,
        city: 'Rijeka',
        region: 'Primorsko-goranska',
      ),
    ];
  }

  /// 🚨 NOUZOVÉ SLUŽBY
  static List<ContactInfo> getEmergencyServices() {
    return [
      ContactInfo(
        id: 'police_national',
        name: 'Policija',
        nameEn: 'Police',
        category: ContactCategory.emergency,
        phone: '192',
        address: 'Celostátní',
        openingHours: '24/7',
        services: ['Nouzové situace', 'Kriminalita', 'Dopravní nehody'],
        languages: ['Hrvatski', 'English'],
        latitude: 45.1,
        longitude: 15.2,
        city: 'Celé Chorvatsko',
        region: 'Celé Chorvatsko',
        isEmergency: true,
      ),

      ContactInfo(
        id: 'fire_national',
        name: 'Vatrogasci',
        nameEn: 'Fire Department',
        category: ContactCategory.emergency,
        phone: '193',
        address: 'Celostátní',
        openingHours: '24/7',
        services: ['Požáry', 'Záchranné akce', 'Technická pomoc'],
        languages: ['Hrvatski', 'English'],
        latitude: 45.1,
        longitude: 15.2,
        city: 'Celé Chorvatsko',
        region: 'Celé Chorvatsko',
        isEmergency: true,
      ),

      ContactInfo(
        id: 'medical_national',
        name: 'Hitna medicinska pomoć',
        nameEn: 'Emergency Medical Services',
        category: ContactCategory.emergency,
        phone: '194',
        address: 'Celostátní',
        openingHours: '24/7',
        services: ['Zdravotní nouzové stavy', 'Sanitky', 'První pomoc'],
        languages: ['Hrvatski', 'English'],
        latitude: 45.1,
        longitude: 15.2,
        city: 'Celé Chorvatsko',
        region: 'Celé Chorvatsko',
        isEmergency: true,
      ),

      ContactInfo(
        id: 'sea_rescue',
        name: 'Pomorska i gorska služba spašavanja',
        nameEn: 'Sea and Mountain Rescue',
        category: ContactCategory.emergency,
        phone: '195',
        address: 'Celostátní',
        openingHours: '24/7',
        services: ['Námořní záchrana', 'Horská záchrana', 'Pátrání'],
        languages: ['Hrvatski', 'English'],
        latitude: 45.1,
        longitude: 15.2,
        city: 'Celé Chorvatsko',
        region: 'Celé Chorvatsko',
        isEmergency: true,
      ),
    ];
  }

  /// 📞 ZÍSKÁNÍ VŠECH KONTAKTŮ
  static List<ContactInfo> getAllContacts() {
    return [
      ...getGovernmentOffices(),
      ...getHealthcareProviders(),
      ...getCulturalInstitutions(),
      ...getEmergencyServices(),
    ];
  }

  /// 🔍 VYHLEDÁNÍ KONTAKTŮ PODLE MĚSTA
  static List<ContactInfo> getContactsByCity(String city) {
    return getAllContacts()
        .where(
          (contact) => contact.city.toLowerCase().contains(city.toLowerCase()),
        )
        .toList();
  }

  /// 🏷️ VYHLEDÁNÍ KONTAKTŮ PODLE KATEGORIE
  static List<ContactInfo> getContactsByCategory(ContactCategory category) {
    return getAllContacts()
        .where((contact) => contact.category == category)
        .toList();
  }
}
