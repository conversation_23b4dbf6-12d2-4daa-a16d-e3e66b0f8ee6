import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/accommodation.dart';
import '../services/accommodation_discovery_service.dart';

class AccommodationDiscoveryScreen extends StatefulWidget {
  const AccommodationDiscoveryScreen({super.key});

  @override
  State<AccommodationDiscoveryScreen> createState() =>
      _AccommodationDiscoveryScreenState();
}

class _AccommodationDiscoveryScreenState
    extends State<AccommodationDiscoveryScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Accommodation Discovery stav
  String _selectedRegion = 'all';
  AccommodationType? _selectedAccommodationType;
  String _selectedPriceRange = 'all';
  String _searchQuery = '';
  bool _isLoading = false;

  // Data
  List<Accommodation> _allAccommodations = [];
  List<Accommodation> _filteredAccommodations = [];

  // Statistiky
  int _totalAccommodations = 0;
  int _topRated = 0;
  int _familyFriendly = 0;
  int _luxury = 0;
  int _seaView = 0;

  final AccommodationDiscoveryService _accommodationService =
      AccommodationDiscoveryService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _loadAccommodationData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadAccommodationData() async {
    setState(() => _isLoading = true);

    try {
      _allAccommodations = await _accommodationService.getAllAccommodations();
      _applyFilters();
      _updateStatistics();
    } catch (e) {
      debugPrint('Chyba při načítání ubytování: $e');
    }

    setState(() => _isLoading = false);
  }

  void _applyFilters() {
    _filteredAccommodations = _allAccommodations.where((accommodation) {
      // Region filter
      if (_selectedRegion != 'all' && accommodation.region != _selectedRegion) {
        return false;
      }

      // Accommodation type filter
      if (_selectedAccommodationType != null &&
          accommodation.accommodationType != _selectedAccommodationType) {
        return false;
      }

      // Price range filter
      if (_selectedPriceRange != 'all' &&
          accommodation.priceRange != _selectedPriceRange) {
        return false;
      }

      // Search query
      if (_searchQuery.isNotEmpty) {
        return accommodation.name.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            accommodation.description.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            accommodation.address.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            );
      }

      return true;
    }).toList();
  }

  void _updateStatistics() {
    _totalAccommodations = _allAccommodations.length;
    _topRated = _allAccommodations.where((a) => a.rating >= 4.5).length;
    _familyFriendly = _allAccommodations
        .where((a) => a.isFamilyFriendly)
        .length;
    _luxury = _allAccommodations.where((a) => a.isLuxury).length;
    _seaView = _allAccommodations.where((a) => a.hasSeaView).length;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Chorvatské ubytování',
          style: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFFFF6B35).withValues(alpha: 0.9),
                const Color(0xFFD84315).withValues(alpha: 0.8),
                const Color(0xFF8D6E63).withValues(alpha: 0.7),
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: CustomPaint(
            painter: WatercolorAccommodationHeaderPainter(),
            size: Size.infinite,
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: _loadAccommodationData,
              icon: const Icon(Icons.refresh),
              tooltip: 'Aktualizovat ubytování',
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          indicator: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Colors.white.withValues(alpha: 0.3),
          ),
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Přehled'),
            Tab(icon: Icon(Icons.hotel), text: 'Všechny'),
            Tab(icon: Icon(Icons.star), text: 'Top hodnocené'),
            Tab(icon: Icon(Icons.family_restroom), text: 'Rodinné'),
            Tab(icon: Icon(Icons.diamond), text: 'Luxusní'),
            Tab(icon: Icon(Icons.map), text: 'Mapa'),
          ],
        ),
      ),
      body: Container(
        child: CustomPaint(
          painter: WatercolorAccommodationBackgroundPainter(),
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(),
              _buildAllAccommodationsTab(),
              _buildTopRatedTab(),
              _buildFamilyFriendlyTab(),
              _buildLuxuryTab(),
              _buildMapTab(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Hlavní karta
          Container(
            child: CustomPaint(
              painter: WatercolorAccommodationMainCardPainter(
                const Color(0xFFFF6B35),
              ),
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Column(
                  children: [
                    Text(
                      'Objevte chorvatské ubytování',
                      style: GoogleFonts.playfairDisplay(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFFFF6B35),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Najděte perfektní místo pro váš pobyt v Chorvatsku',
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        color: const Color(0xFF666666),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Vyhledávání
          _buildSearchSection(),

          const SizedBox(height: 20),

          // Statistiky
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            children: [
              _buildStatCard(
                'Celkem',
                '$_totalAccommodations',
                'ubytování',
                Icons.hotel,
                const Color(0xFFFF6B35),
              ),
              _buildStatCard(
                'Top hodnocené',
                '$_topRated',
                '4.5+ hvězdiček',
                Icons.star,
                const Color(0xFFD84315),
              ),
              _buildStatCard(
                'Rodinné',
                '$_familyFriendly',
                'pro rodiny',
                Icons.family_restroom,
                const Color(0xFF8D6E63),
              ),
              _buildStatCard(
                'Výhled na moře',
                '$_seaView',
                's výhledem',
                Icons.waves,
                const Color(0xFF006994),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Doporučená ubytování
          _buildRecommendedSection(),

          const SizedBox(height: 20),

          // Luxusní ubytování přehled
          _buildLuxuryOverview(),
        ],
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      child: CustomPaint(
        painter: WatercolorAccommodationSearchPainter(const Color(0xFFD84315)),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Vyhledávání ubytování',
                style: GoogleFonts.playfairDisplay(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFFD84315),
                ),
              ),
              const SizedBox(height: 16),

              // Search field
              TextField(
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                    _applyFilters();
                  });
                },
                decoration: InputDecoration(
                  hintText: 'Hledat hotely, apartmány, vily...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                  fillColor: Colors.grey[50],
                ),
              ),

              const SizedBox(height: 16),

              // Filters
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildFilterChip(
                    'Region',
                    _selectedRegion,
                    [
                      'all',
                      'dalmatia',
                      'istria',
                      'kvarner',
                      'dubrovnik',
                      'zagreb',
                    ],
                    (value) => setState(() {
                      _selectedRegion = value;
                      _applyFilters();
                    }),
                  ),
                  _buildFilterChip(
                    'Typ',
                    _selectedAccommodationType?.toString().split('.').last ??
                        'all',
                    [
                      'all',
                      'hotel',
                      'apartment',
                      'villa',
                      'guesthouse',
                      'resort',
                      'hostel',
                    ],
                    (value) => setState(() {
                      if (value == 'all') {
                        _selectedAccommodationType = null;
                      } else {
                        _selectedAccommodationType = AccommodationType.values
                            .firstWhere(
                              (type) =>
                                  type.toString().split('.').last == value,
                            );
                      }
                      _applyFilters();
                    }),
                  ),
                  _buildFilterChip(
                    'Cena',
                    _selectedPriceRange,
                    ['all', 'budget', 'mid', 'upscale', 'luxury'],
                    (value) => setState(() {
                      _selectedPriceRange = value;
                      _applyFilters();
                    }),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Container(
      child: CustomPaint(
        painter: WatercolorAccommodationStatCardPainter(color),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                child: CustomPaint(
                  painter: WatercolorAccommodationIconPainter(color),
                  child: Icon(icon, size: 32, color: color),
                ),
              ),
              const SizedBox(height: 12),
              Text(
                value,
                style: GoogleFonts.playfairDisplay(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: GoogleFonts.inter(
                  fontSize: 12,
                  color: const Color(0xFF666666),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF2C2C2C),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterChip(
    String label,
    String currentValue,
    List<String> options,
    Function(String) onChanged,
  ) {
    return Container(
      child: CustomPaint(
        painter: WatercolorAccommodationFilterChipPainter(
          const Color(0xFFFF6B35),
        ),
        child: PopupMenuButton<String>(
          onSelected: onChanged,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: const Color(0xFFFF6B35).withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '$label: ${_getFilterLabel(currentValue)}',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFFFF6B35),
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  Icons.arrow_drop_down,
                  size: 16,
                  color: const Color(0xFFFF6B35),
                ),
              ],
            ),
          ),
          itemBuilder: (context) => options
              .map(
                (option) => PopupMenuItem(
                  value: option,
                  child: Text(_getFilterLabel(option)),
                ),
              )
              .toList(),
        ),
      ),
    );
  }

  String _getFilterLabel(String value) {
    switch (value) {
      case 'all':
        return 'Vše';
      case 'dalmatia':
        return 'Dalmácie';
      case 'istria':
        return 'Istrie';
      case 'kvarner':
        return 'Kvarner';
      case 'dubrovnik':
        return 'Dubrovník';
      case 'zagreb':
        return 'Zagreb';
      case 'hotel':
        return 'Hotel';
      case 'apartment':
        return 'Apartmán';
      case 'villa':
        return 'Vila';
      case 'guesthouse':
        return 'Penzion';
      case 'resort':
        return 'Resort';
      case 'hostel':
        return 'Hostel';
      case 'budget':
        return 'Levné (€)';
      case 'mid':
        return 'Střední (€€)';
      case 'upscale':
        return 'Dražší (€€€)';
      case 'luxury':
        return 'Luxusní (€€€€)';
      default:
        return value;
    }
  }

  Widget _buildRecommendedSection() {
    final recommended = _allAccommodations
        .where((a) => a.rating >= 4.5)
        .take(3)
        .toList();

    return Container(
      child: CustomPaint(
        painter: WatercolorAccommodationRecommendedPainter(
          const Color(0xFF8D6E63),
        ),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Doporučená ubytování',
                style: GoogleFonts.playfairDisplay(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF8D6E63),
                ),
              ),
              const SizedBox(height: 16),
              ...recommended.map(
                (accommodation) =>
                    _buildAccommodationCard(accommodation, isCompact: true),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLuxuryOverview() {
    final luxury = _allAccommodations
        .where((a) => a.priceRange == 'luxury')
        .length;

    return Container(
      child: CustomPaint(
        painter: WatercolorAccommodationLuxuryPainter(const Color(0xFFD84315)),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    child: CustomPaint(
                      painter: WatercolorAccommodationIconPainter(
                        const Color(0xFFD84315),
                      ),
                      child: Icon(
                        Icons.diamond,
                        size: 32,
                        color: const Color(0xFFD84315),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Luxusní ubytování',
                          style: GoogleFonts.playfairDisplay(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFFD84315),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '$luxury prémiových možností',
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: const Color(0xFF666666),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: const Color(0xFFD84315),
                    size: 16,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                'Objevte nejlepší hotely a resorty s výjimečným servisem',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: const Color(0xFF666666),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAllAccommodationsTab() {
    return Column(
      children: [
        // Search bar
        Container(
          padding: const EdgeInsets.all(16),
          child: TextField(
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
                _applyFilters();
              });
            },
            decoration: InputDecoration(
              hintText: 'Hledat ubytování...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
          ),
        ),

        // Results
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _filteredAccommodations.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.hotel, size: 64, color: Colors.grey[400]),
                      const SizedBox(height: 16),
                      Text(
                        'Žádné ubytování nenalezeno',
                        style: GoogleFonts.inter(
                          fontSize: 18,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _filteredAccommodations.length,
                  itemBuilder: (context, index) {
                    return _buildAccommodationCard(
                      _filteredAccommodations[index],
                    );
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildTopRatedTab() {
    final topRated = _allAccommodations.where((a) => a.rating >= 4.5).toList();
    topRated.sort((a, b) => b.rating.compareTo(a.rating));

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: topRated.length,
      itemBuilder: (context, index) {
        return _buildAccommodationCard(topRated[index], showRank: index + 1);
      },
    );
  }

  Widget _buildFamilyFriendlyTab() {
    final familyFriendly = _allAccommodations
        .where((a) => a.isFamilyFriendly)
        .toList();
    familyFriendly.sort((a, b) => b.rating.compareTo(a.rating));

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: familyFriendly.length,
      itemBuilder: (context, index) {
        return _buildAccommodationCard(
          familyFriendly[index],
          showFamilyBadge: true,
        );
      },
    );
  }

  Widget _buildLuxuryTab() {
    final luxury = _allAccommodations
        .where((a) => a.priceRange == 'luxury' || a.isLuxury)
        .toList();
    luxury.sort((a, b) => b.rating.compareTo(a.rating));

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: luxury.length,
      itemBuilder: (context, index) {
        return _buildAccommodationCard(luxury[index], showLuxuryBadge: true);
      },
    );
  }

  Widget _buildMapTab() {
    return Container(
      child: CustomPaint(
        painter: WatercolorAccommodationMapPainter(),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.map, size: 64, color: const Color(0xFFFF6B35)),
              const SizedBox(height: 16),
              Text(
                'Mapa ubytování',
                style: GoogleFonts.playfairDisplay(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFFFF6B35),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Připravujeme interaktivní mapu\ns watercolor designem',
                style: GoogleFonts.inter(
                  fontSize: 16,
                  color: const Color(0xFF666666),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAccommodationCard(
    Accommodation accommodation, {
    bool isCompact = false,
    int? showRank,
    bool showFamilyBadge = false,
    bool showLuxuryBadge = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: CustomPaint(
        painter: WatercolorAccommodationCardPainter(
          _getAccommodationTypeColor(accommodation.accommodationType),
        ),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  if (showRank != null) ...[
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: const Color(0xFFFF6B35),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          '$showRank',
                          style: GoogleFonts.inter(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                  ],

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                accommodation.name,
                                style: GoogleFonts.playfairDisplay(
                                  fontSize: isCompact ? 16 : 18,
                                  fontWeight: FontWeight.bold,
                                  color: const Color(0xFF2C2C2C),
                                ),
                              ),
                            ),
                            if (showFamilyBadge)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(
                                    0xFF4CAF50,
                                  ).withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'Rodinné',
                                  style: GoogleFonts.inter(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFF4CAF50),
                                  ),
                                ),
                              ),
                            if (showLuxuryBadge)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(
                                    0xFFD84315,
                                  ).withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'Luxusní',
                                  style: GoogleFonts.inter(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFFD84315),
                                  ),
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(Icons.star, size: 16, color: Colors.amber),
                            const SizedBox(width: 4),
                            Text(
                              accommodation.rating.toStringAsFixed(1),
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF2C2C2C),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '(${accommodation.reviewCount} recenzí)',
                              style: GoogleFonts.inter(
                                fontSize: 12,
                                color: const Color(0xFF666666),
                              ),
                            ),
                            const Spacer(),
                            Text(
                              _getPriceRangeLabel(accommodation.priceRange),
                              style: GoogleFonts.inter(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: _getPriceRangeColor(
                                  accommodation.priceRange,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  Icon(
                    _getAccommodationTypeIcon(accommodation.accommodationType),
                    color: _getAccommodationTypeColor(
                      accommodation.accommodationType,
                    ),
                    size: 24,
                  ),
                ],
              ),

              if (!isCompact) ...[
                const SizedBox(height: 12),

                // Description
                Text(
                  accommodation.description,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    color: const Color(0xFF666666),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 12),

                // Amenities
                Row(
                  children: [
                    if (accommodation.hasWifi)
                      _buildAmenityIcon(Icons.wifi, 'WiFi'),
                    if (accommodation.hasParking)
                      _buildAmenityIcon(Icons.local_parking, 'Parkování'),
                    if (accommodation.hasPool)
                      _buildAmenityIcon(Icons.pool, 'Bazén'),
                    if (accommodation.hasRestaurant)
                      _buildAmenityIcon(Icons.restaurant, 'Restaurace'),
                    if (accommodation.hasSeaView)
                      _buildAmenityIcon(Icons.waves, 'Výhled na moře'),
                  ],
                ),

                const SizedBox(height: 8),

                // Address and contact
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 16,
                      color: const Color(0xFF666666),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        accommodation.address,
                        style: GoogleFonts.inter(
                          fontSize: 12,
                          color: const Color(0xFF666666),
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => _contactAccommodation(accommodation),
                      icon: const Icon(Icons.phone, size: 20),
                      color: const Color(0xFFFF6B35),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAmenityIcon(IconData icon, String tooltip) {
    return Padding(
      padding: const EdgeInsets.only(right: 12),
      child: Tooltip(
        message: tooltip,
        child: Icon(icon, size: 16, color: const Color(0xFF4CAF50)),
      ),
    );
  }

  Color _getAccommodationTypeColor(AccommodationType accommodationType) {
    switch (accommodationType) {
      case AccommodationType.hotel:
        return const Color(0xFFFF6B35);
      case AccommodationType.apartment:
        return const Color(0xFF2196F3);
      case AccommodationType.villa:
        return const Color(0xFF4CAF50);
      case AccommodationType.guesthouse:
        return const Color(0xFF9C27B0);
      case AccommodationType.hostel:
        return const Color(0xFFFF9800);
      case AccommodationType.camping:
        return const Color(0xFF795548);
      case AccommodationType.resort:
        return const Color(0xFFE91E63);
      case AccommodationType.boutique:
        return const Color(0xFF607D8B);
    }
  }

  IconData _getAccommodationTypeIcon(AccommodationType accommodationType) {
    switch (accommodationType) {
      case AccommodationType.hotel:
        return Icons.hotel;
      case AccommodationType.apartment:
        return Icons.apartment;
      case AccommodationType.villa:
        return Icons.villa;
      case AccommodationType.guesthouse:
        return Icons.house;
      case AccommodationType.hostel:
        return Icons.bed;
      case AccommodationType.camping:
        return Icons.nature;
      case AccommodationType.resort:
        return Icons.beach_access;
      case AccommodationType.boutique:
        return Icons.diamond;
    }
  }

  String _getPriceRangeLabel(String priceRange) {
    switch (priceRange) {
      case 'budget':
        return '€';
      case 'mid':
        return '€€';
      case 'upscale':
        return '€€€';
      case 'luxury':
        return '€€€€';
      default:
        return priceRange;
    }
  }

  Color _getPriceRangeColor(String priceRange) {
    switch (priceRange) {
      case 'budget':
        return const Color(0xFF4CAF50);
      case 'mid':
        return const Color(0xFF2196F3);
      case 'upscale':
        return const Color(0xFFFF9800);
      case 'luxury':
        return const Color(0xFFE91E63);
      default:
        return const Color(0xFF666666);
    }
  }

  void _contactAccommodation(Accommodation accommodation) {
    // Implementace kontaktování ubytování
    debugPrint(
      'Kontakt na ubytování: ${accommodation.name} - ${accommodation.phone}',
    );
  }
}

// Watercolor painters pro Accommodation Discovery Screen
class WatercolorAccommodationHeaderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor efekt pro Accommodation header - evokuje budovy a architekturu
    final path1 = Path();
    path1.moveTo(0, size.height * 0.3);
    path1.quadraticBezierTo(
      size.width * 0.15,
      size.height * 0.1,
      size.width * 0.35,
      size.height * 0.4,
    );
    path1.quadraticBezierTo(
      size.width * 0.55,
      size.height * 0.7,
      size.width * 0.75,
      size.height * 0.2,
    );
    path1.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.05,
      size.width,
      size.height * 0.35,
    );
    path1.lineTo(size.width, size.height);
    path1.lineTo(0, size.height);
    path1.close();

    paint.color = const Color(0xFFFF6B35).withValues(alpha: 0.2);
    canvas.drawPath(path1, paint);

    // Druhá vrstva - budovy
    final path2 = Path();
    path2.moveTo(0, size.height * 0.5);
    path2.quadraticBezierTo(
      size.width * 0.2,
      size.height * 0.25,
      size.width * 0.45,
      size.height * 0.6,
    );
    path2.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.85,
      size.width,
      size.height * 0.45,
    );
    path2.lineTo(size.width, size.height);
    path2.lineTo(0, size.height);
    path2.close();

    paint.color = const Color(0xFFD84315).withValues(alpha: 0.15);
    canvas.drawPath(path2, paint);

    // Třetí vrstva - střechy
    final path3 = Path();
    path3.moveTo(0, size.height * 0.75);
    path3.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.55,
      size.width * 0.6,
      size.height * 0.8,
    );
    path3.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.9,
      size.width,
      size.height * 0.65,
    );
    path3.lineTo(size.width, size.height);
    path3.lineTo(0, size.height);
    path3.close();

    paint.color = const Color(0xFF8D6E63).withValues(alpha: 0.1);
    canvas.drawPath(path3, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorAccommodationBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Jemný watercolor efekt pro pozadí accommodation discovery
    final path = Path();
    path.moveTo(size.width * 0.04, size.height * 0.02);
    path.quadraticBezierTo(
      size.width * 0.35,
      size.height * 0.01,
      size.width * 0.75,
      size.height * 0.05,
    );
    path.quadraticBezierTo(
      size.width * 0.96,
      size.height * 0.08,
      size.width * 0.98,
      size.height * 0.98,
    );
    path.quadraticBezierTo(
      size.width * 0.65,
      size.height * 0.99,
      size.width * 0.25,
      size.height * 0.95,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.92,
      size.width * 0.04,
      size.height * 0.02,
    );
    path.close();

    paint.color = const Color(0xFFFF6B35).withValues(alpha: 0.02);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorAccommodationMainCardPainter extends CustomPainter {
  final Color color;

  WatercolorAccommodationMainCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro hlavní accommodation kartu - evokuje útulný domov
    final path = Path();
    path.moveTo(size.width * 0.02, size.height * 0.06);
    path.quadraticBezierTo(
      size.width * 0.22,
      size.height * 0.01,
      size.width * 0.55,
      size.height * 0.04,
    );
    path.quadraticBezierTo(
      size.width * 0.82,
      size.height * 0.07,
      size.width * 0.98,
      size.height * 0.18,
    );
    path.quadraticBezierTo(
      size.width * 0.99,
      size.height * 0.65,
      size.width * 0.94,
      size.height * 0.94,
    );
    path.quadraticBezierTo(
      size.width * 0.78,
      size.height * 0.99,
      size.width * 0.45,
      size.height * 0.96,
    );
    path.quadraticBezierTo(
      size.width * 0.18,
      size.height * 0.93,
      size.width * 0.01,
      size.height * 0.82,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.45,
      size.width * 0.02,
      size.height * 0.06,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);

    // Druhá vrstva pro hloubku
    final path2 = Path();
    path2.moveTo(size.width * 0.08, size.height * 0.12);
    path2.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.02,
      size.width * 0.75,
      size.height * 0.1,
    );
    path2.quadraticBezierTo(
      size.width * 0.92,
      size.height * 0.22,
      size.width * 0.9,
      size.height * 0.88,
    );
    path2.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.98,
      size.width * 0.25,
      size.height * 0.9,
    );
    path2.quadraticBezierTo(
      size.width * 0.06,
      size.height * 0.78,
      size.width * 0.08,
      size.height * 0.12,
    );
    path2.close();

    paint.color = color.withValues(alpha: 0.04);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorAccommodationSearchPainter extends CustomPainter {
  final Color color;

  WatercolorAccommodationSearchPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro search sekci - evokuje hledání domova
    final path = Path();
    path.moveTo(size.width * 0.01, size.height * 0.1);
    path.quadraticBezierTo(
      size.width * 0.28,
      size.height * 0.01,
      size.width * 0.65,
      size.height * 0.06,
    );
    path.quadraticBezierTo(
      size.width * 0.99,
      size.height * 0.12,
      size.width * 0.97,
      size.height * 0.9,
    );
    path.quadraticBezierTo(
      size.width * 0.72,
      size.height * 0.99,
      size.width * 0.35,
      size.height * 0.94,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.88,
      size.width * 0.01,
      size.height * 0.1,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.06);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorAccommodationStatCardPainter extends CustomPainter {
  final Color color;

  WatercolorAccommodationStatCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro stat karty - evokuje domečky
    final path = Path();
    path.moveTo(size.width * 0.05, size.height * 0.1);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.02,
      size.width * 0.7,
      size.height * 0.08,
    );
    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.15,
      size.width * 0.92,
      size.height * 0.9,
    );
    path.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.98,
      size.width * 0.3,
      size.height * 0.92,
    );
    path.quadraticBezierTo(
      size.width * 0.03,
      size.height * 0.85,
      size.width * 0.05,
      size.height * 0.1,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.1);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorAccommodationIconPainter extends CustomPainter {
  final Color color;

  WatercolorAccommodationIconPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor kruh kolem accommodation ikony - evokuje útulnost
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2.3;

    final path = Path();
    for (int i = 0; i < 360; i += 12) {
      final angle = i * pi / 180;
      final variation = 0.75 + (sin(i * pi / 30) * 0.25);
      final x = center.dx + (radius * variation) * cos(angle);
      final y = center.dy + (radius * variation) * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    paint.color = color.withValues(alpha: 0.15);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorAccommodationFilterChipPainter extends CustomPainter {
  final Color color;

  WatercolorAccommodationFilterChipPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro filter chipy - evokuje štítky
    final path = Path();
    path.moveTo(size.width * 0.06, size.height * 0.15);
    path.quadraticBezierTo(
      size.width * 0.35,
      size.height * 0.03,
      size.width * 0.75,
      size.height * 0.1,
    );
    path.quadraticBezierTo(
      size.width * 0.97,
      size.height * 0.2,
      size.width * 0.94,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.65,
      size.height * 0.97,
      size.width * 0.25,
      size.height * 0.9,
    );
    path.quadraticBezierTo(
      size.width * 0.03,
      size.height * 0.8,
      size.width * 0.06,
      size.height * 0.15,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorAccommodationRecommendedPainter extends CustomPainter {
  final Color color;

  WatercolorAccommodationRecommendedPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro recommended sekci - evokuje doporučené domy
    final path = Path();
    path.moveTo(size.width * 0.02, size.height * 0.08);
    path.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.01,
      size.width * 0.6,
      size.height * 0.05,
    );
    path.quadraticBezierTo(
      size.width * 0.98,
      size.height * 0.11,
      size.width * 0.96,
      size.height * 0.92,
    );
    path.quadraticBezierTo(
      size.width * 0.75,
      size.height * 0.99,
      size.width * 0.4,
      size.height * 0.95,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.89,
      size.width * 0.02,
      size.height * 0.08,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.06);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorAccommodationLuxuryPainter extends CustomPainter {
  final Color color;

  WatercolorAccommodationLuxuryPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro luxury sekci - evokuje luxusní hotely
    final path = Path();
    path.moveTo(size.width * 0.02, size.height * 0.12);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.03,
      size.width * 0.7,
      size.height * 0.09,
    );
    path.quadraticBezierTo(
      size.width * 0.98,
      size.height * 0.17,
      size.width * 0.95,
      size.height * 0.88,
    );
    path.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.97,
      size.width * 0.3,
      size.height * 0.91,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.83,
      size.width * 0.02,
      size.height * 0.12,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.07);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorAccommodationCardPainter extends CustomPainter {
  final Color color;

  WatercolorAccommodationCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro accommodation karty - evokuje budovy
    final path = Path();
    path.moveTo(size.width * 0.01, size.height * 0.14);
    path.quadraticBezierTo(
      size.width * 0.28,
      size.height * 0.03,
      size.width * 0.68,
      size.height * 0.09,
    );
    path.quadraticBezierTo(
      size.width * 0.99,
      size.height * 0.16,
      size.width * 0.97,
      size.height * 0.86,
    );
    path.quadraticBezierTo(
      size.width * 0.72,
      size.height * 0.97,
      size.width * 0.32,
      size.height * 0.91,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.84,
      size.width * 0.01,
      size.height * 0.14,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorAccommodationMapPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor efekt pro mapu ubytování - evokuje města a budovy
    final path1 = Path();
    path1.moveTo(size.width * 0.06, size.height * 0.06);
    path1.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.02,
      size.width * 0.8,
      size.height * 0.1,
    );
    path1.quadraticBezierTo(
      size.width * 0.94,
      size.height * 0.2,
      size.width * 0.9,
      size.height * 0.94,
    );
    path1.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.98,
      size.width * 0.2,
      size.height * 0.9,
    );
    path1.quadraticBezierTo(
      size.width * 0.04,
      size.height * 0.8,
      size.width * 0.06,
      size.height * 0.06,
    );
    path1.close();

    paint.color = const Color(0xFFFF6B35).withValues(alpha: 0.04);
    canvas.drawPath(path1, paint);

    // Druhá vrstva - čtvrti
    final path2 = Path();
    path2.moveTo(size.width * 0.15, size.height * 0.12);
    path2.quadraticBezierTo(
      size.width * 0.45,
      size.height * 0.08,
      size.width * 0.75,
      size.height * 0.15,
    );
    path2.quadraticBezierTo(
      size.width * 0.88,
      size.height * 0.3,
      size.width * 0.8,
      size.height * 0.88,
    );
    path2.quadraticBezierTo(
      size.width * 0.55,
      size.height * 0.92,
      size.width * 0.25,
      size.height * 0.85,
    );
    path2.quadraticBezierTo(
      size.width * 0.12,
      size.height * 0.7,
      size.width * 0.15,
      size.height * 0.12,
    );
    path2.close();

    paint.color = const Color(0xFFD84315).withValues(alpha: 0.03);
    canvas.drawPath(path2, paint);

    // Třetí vrstva - centrum
    final path3 = Path();
    path3.moveTo(size.width * 0.3, size.height * 0.25);
    path3.quadraticBezierTo(
      size.width * 0.55,
      size.height * 0.2,
      size.width * 0.7,
      size.height * 0.3,
    );
    path3.quadraticBezierTo(
      size.width * 0.75,
      size.height * 0.45,
      size.width * 0.65,
      size.height * 0.75,
    );
    path3.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.8,
      size.width * 0.35,
      size.height * 0.7,
    );
    path3.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.55,
      size.width * 0.3,
      size.height * 0.25,
    );
    path3.close();

    paint.color = const Color(0xFF8D6E63).withValues(alpha: 0.02);
    canvas.drawPath(path3, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
