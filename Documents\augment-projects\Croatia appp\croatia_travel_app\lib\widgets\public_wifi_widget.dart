import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import '../models/city_services.dart';
import '../services/city_services_service.dart';

class PublicWifiWidget extends StatefulWidget {
  const PublicWifiWidget({super.key});

  @override
  State<PublicWifiWidget> createState() => _PublicWifiWidgetState();
}

class _PublicWifiWidgetState extends State<PublicWifiWidget> {
  final CityServicesService _cityService = CityServicesService();

  List<PublicWifi> _nearbyWifi = [];
  bool _isLoading = false;
  Position? _currentPosition;
  WifiType? _selectedType;

  @override
  void initState() {
    super.initState();
    _loadNearbyWifi();
  }

  Future<void> _loadNearbyWifi() async {
    try {
      setState(() => _isLoading = true);

      _currentPosition = await Geolocator.getCurrentPosition();

      final wifiSpots = await _cityService.getNearbyWifi(
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
        radius: 1000,
      );

      setState(() => _nearbyWifi = wifiSpots);
    } catch (e) {
      _showError('Chyba při načítání WiFi sítí: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _loadNearbyWifi,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildQuickActions(),
            const SizedBox(height: 24),
            _buildWifiFilter(),
            const SizedBox(height: 16),
            _buildWifiStats(),
            const SizedBox(height: 24),
            _buildNearbyWifi(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Rychlé akce',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _findNearestWifi,
                    icon: const Icon(Icons.wifi_find),
                    label: const Text('Nejbližší WiFi'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _showWifiMap,
                    icon: const Icon(Icons.map),
                    label: const Text('Mapa WiFi'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWifiFilter() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Filtr',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<WifiType?>(
              value: _selectedType,
              decoration: const InputDecoration(
                labelText: 'Typ WiFi',
                border: OutlineInputBorder(),
              ),
              items: [
                const DropdownMenuItem(
                  value: null,
                  child: Text('Všechny typy'),
                ),
                ...WifiType.values.map(
                  (type) => DropdownMenuItem(
                    value: type,
                    child: Text(_getWifiTypeName(type)),
                  ),
                ),
              ],
              onChanged: (value) {
                setState(() => _selectedType = value);
                _loadNearbyWifi();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWifiStats() {
    final totalWifi = _nearbyWifi.length;
    final freeWifi = _nearbyWifi
        .where((w) => !w.requiresPassword && !w.requiresRegistration)
        .length;
    final fastWifi = _nearbyWifi
        .where((w) => w.speedMbps != null && w.speedMbps! >= 10)
        .length;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Statistiky WiFi',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Celkem',
                    '$totalWifi',
                    Colors.blue,
                    Icons.wifi,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Zdarma',
                    '$freeWifi',
                    Colors.green,
                    Icons.wifi_protected_setup,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Rychlé',
                    '$fastWifi',
                    Colors.orange,
                    Icons.speed,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNearbyWifi() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'WiFi v okolí',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else if (_nearbyWifi.isEmpty)
              const Text('Žádné WiFi sítě v okolí')
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _nearbyWifi.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final wifi = _nearbyWifi[index];
                  return _buildWifiTile(wifi);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildWifiTile(PublicWifi wifi) {
    return ListTile(
      leading: Icon(
        _getWifiTypeIcon(wifi.type),
        color: wifi.isActive ? Colors.green : Colors.grey,
      ),
      title: Text(wifi.name),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(wifi.address),
          Row(
            children: [
              if (wifi.speedMbps != null) ...[
                Icon(
                  Icons.speed,
                  size: 16,
                  color: _getSpeedColor(wifi.speedMbps!),
                ),
                Text(' ${wifi.speedMbps} Mbps'),
                const SizedBox(width: 8),
              ],
              if (!wifi.requiresPassword && !wifi.requiresRegistration)
                const Icon(Icons.lock_open, size: 16, color: Colors.green)
              else if (wifi.requiresPassword)
                const Icon(Icons.lock, size: 16, color: Colors.orange)
              else if (wifi.requiresRegistration)
                const Icon(Icons.person_add, size: 16, color: Colors.blue),
            ],
          ),
          Text(_getWifiTypeName(wifi.type)),
          if (wifi.timeLimit != null)
            Text('Časový limit: ${wifi.timeLimit!.inHours}h'),
        ],
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: wifi.isActive ? Colors.green : Colors.grey,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              wifi.isActive ? 'Aktivní' : 'Neaktivní',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      onTap: wifi.isActive ? () => _showWifiDetails(wifi) : null,
    );
  }

  String _getWifiTypeName(WifiType type) {
    switch (type) {
      case WifiType.municipal:
        return 'Městské';
      case WifiType.library:
        return 'Knihovna';
      case WifiType.park:
        return 'Park';
      case WifiType.transport:
        return 'Doprava';
      case WifiType.tourist:
        return 'Turistické';
      case WifiType.emergency:
        return 'Nouzové';
    }
  }

  IconData _getWifiTypeIcon(WifiType type) {
    switch (type) {
      case WifiType.municipal:
        return Icons.location_city;
      case WifiType.library:
        return Icons.library_books;
      case WifiType.park:
        return Icons.park;
      case WifiType.transport:
        return Icons.directions_bus;
      case WifiType.tourist:
        return Icons.camera_alt;
      case WifiType.emergency:
        return Icons.emergency;
    }
  }

  Color _getSpeedColor(double speed) {
    if (speed >= 50) return Colors.green;
    if (speed >= 20) return Colors.orange;
    if (speed >= 10) return Colors.yellow;
    return Colors.red;
  }

  void _findNearestWifi() {
    if (_nearbyWifi.isNotEmpty) {
      _showWifiDetails(_nearbyWifi.first);
    } else {
      _showError('Žádné WiFi sítě v okolí');
    }
  }

  void _showWifiMap() {
    // Implementace mapy WiFi
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Otevírám mapu WiFi sítí...')));
  }

  void _showWifiDetails(PublicWifi wifi) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(wifi.name),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (wifi.description != null) Text(wifi.description!),
              const SizedBox(height: 8),
              Text('Adresa: ${wifi.address}'),
              Text('Typ: ${_getWifiTypeName(wifi.type)}'),
              if (wifi.speedMbps != null)
                Text('Rychlost: ${wifi.speedMbps} Mbps'),
              if (wifi.userLimit != null)
                Text('Limit uživatelů: ${wifi.userLimit}'),
              if (wifi.timeLimit != null)
                Text('Časový limit: ${wifi.timeLimit!.inHours} hodin'),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    wifi.requiresPassword ? Icons.lock : Icons.lock_open,
                    color: wifi.requiresPassword ? Colors.orange : Colors.green,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(wifi.requiresPassword ? 'Vyžaduje heslo' : 'Bez hesla'),
                ],
              ),
              if (wifi.requiresRegistration) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(Icons.person_add, color: Colors.blue, size: 16),
                    const SizedBox(width: 4),
                    Text('Vyžaduje registraci'),
                  ],
                ),
              ],
              if (wifi.password != null && !wifi.requiresRegistration) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    children: [
                      const Text('Heslo: '),
                      Expanded(
                        child: Text(
                          wifi.password!,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                      IconButton(
                        onPressed: () => _copyPassword(wifi.password!),
                        icon: const Icon(Icons.copy, size: 16),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zavřít'),
          ),
          if (wifi.isActive)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _connectToWifi(wifi);
              },
              child: const Text('Připojit'),
            ),
        ],
      ),
    );
  }

  void _copyPassword(String password) {
    // Implementace kopírování hesla do schránky
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Heslo zkopírováno do schránky')),
    );
  }

  Future<void> _connectToWifi(PublicWifi wifi) async {
    try {
      final result = await _cityService.connectToWifi(wifi.id);

      if (mounted && result) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Připojeno k ${wifi.name}')));

        if (wifi.requiresRegistration) {
          _showRegistrationDialog(wifi, {});
        }
      } else if (mounted) {
        _showError('Chyba při připojování k WiFi');
      }
    } catch (e) {
      if (mounted) {
        _showError('Chyba při připojování: $e');
      }
    }
  }

  void _showRegistrationDialog(
    PublicWifi wifi,
    Map<String, dynamic> connectionData,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Registrace - ${wifi.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Pro připojení k této síti je nutná registrace.'),
            const SizedBox(height: 16),
            TextFormField(
              decoration: const InputDecoration(labelText: 'E-mail'),
            ),
            const SizedBox(height: 8),
            TextFormField(
              decoration: const InputDecoration(labelText: 'Telefon'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zrušit'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Registrace dokončena, připojuji...'),
                  ),
                );
              }
            },
            child: const Text('Registrovat'),
          ),
        ],
      ),
    );
  }
}
