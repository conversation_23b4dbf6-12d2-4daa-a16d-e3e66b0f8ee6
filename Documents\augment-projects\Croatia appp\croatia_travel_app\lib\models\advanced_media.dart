/// 🎬 ADVANCED MEDIA MODELS - Modely pro pokročilé media funkce
library;

/// Time-lapse video
class TimeLapseVideo {
  final String id;
  final String title;
  final String videoPath;
  final String thumbnailPath;
  final int frameCount;
  final double frameRate;
  final Duration duration;
  final TimeLapseStyle style;
  final String? backgroundMusic;
  final DateTime createdAt;

  const TimeLapseVideo({
    required this.id,
    required this.title,
    required this.videoPath,
    required this.thumbnailPath,
    required this.frameCount,
    required this.frameRate,
    required this.duration,
    required this.style,
    this.backgroundMusic,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'videoPath': videoPath,
      'thumbnailPath': thumbnailPath,
      'frameCount': frameCount,
      'frameRate': frameRate,
      'duration': duration.inMilliseconds,
      'style': style.name,
      'backgroundMusic': backgroundMusic,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

/// Styl time-lapse
enum TimeLapseStyle {
  smooth, // Plynulý
  dramatic, // Dramatický
  vintage, // Retro
  cinematic, // Filmový
  hyperlapse, // Hyperlapse
}

extension TimeLapseStyleExtension on TimeLapseStyle {
  String get displayName {
    switch (this) {
      case TimeLapseStyle.smooth:
        return 'Plynulý';
      case TimeLapseStyle.dramatic:
        return 'Dramatický';
      case TimeLapseStyle.vintage:
        return 'Retro';
      case TimeLapseStyle.cinematic:
        return 'Filmový';
      case TimeLapseStyle.hyperlapse:
        return 'Hyperlapse';
    }
  }
}

/// Přechod v time-lapse
class TimeLapseTransition {
  final TransitionType type;
  final Duration duration;
  final int startFrame;
  final int endFrame;

  const TimeLapseTransition({
    required this.type,
    required this.duration,
    required this.startFrame,
    required this.endFrame,
  });
}

/// Typ přechodu
enum TransitionType {
  fade, // Prolínání
  slide, // Posun
  zoom, // Přiblížení
  rotate, // Rotace
  dissolve, // Rozpuštění
}

/// Media filtr
class MediaFilter {
  final String id;
  final String name;
  final String description;
  final FilterType type;
  final String thumbnailPath;
  final Map<String, FilterParameter> parameters;

  const MediaFilter({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.thumbnailPath,
    this.parameters = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'thumbnailPath': thumbnailPath,
      'parameters': parameters.map((k, v) => MapEntry(k, v.toJson())),
    };
  }
}

/// Typ filtru
enum FilterType {
  vintage, // Vintage
  dramatic, // Dramatický
  cinematic, // Filmový
  artistic, // Umělecký
  nature, // Přírodní
  portrait, // Portrétní
  blackWhite, // Černobílý
  custom, // Vlastní
}

/// Parametr filtru
class FilterParameter {
  final String name;
  final double defaultValue;
  final double minValue;
  final double maxValue;

  const FilterParameter(
    this.name,
    this.defaultValue,
    this.minValue,
    this.maxValue,
  );

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'defaultValue': defaultValue,
      'minValue': minValue,
      'maxValue': maxValue,
    };
  }
}

/// Layout kolláže
enum CollageLayout {
  grid2x2, // Mřížka 2x2
  grid3x3, // Mřížka 3x3
  horizontal, // Horizontální
  vertical, // Vertikální
  mosaic, // Mozaika
  magazine, // Časopisový
  polaroid, // Polaroid
  custom, // Vlastní
}

extension CollageLayoutExtension on CollageLayout {
  String get displayName {
    switch (this) {
      case CollageLayout.grid2x2:
        return 'Mřížka 2×2';
      case CollageLayout.grid3x3:
        return 'Mřížka 3×3';
      case CollageLayout.horizontal:
        return 'Horizontální';
      case CollageLayout.vertical:
        return 'Vertikální';
      case CollageLayout.mosaic:
        return 'Mozaika';
      case CollageLayout.magazine:
        return 'Časopisový';
      case CollageLayout.polaroid:
        return 'Polaroid';
      case CollageLayout.custom:
        return 'Vlastní';
    }
  }

  int get maxPhotos {
    switch (this) {
      case CollageLayout.grid2x2:
        return 4;
      case CollageLayout.grid3x3:
        return 9;
      case CollageLayout.horizontal:
        return 3;
      case CollageLayout.vertical:
        return 3;
      case CollageLayout.mosaic:
        return 6;
      case CollageLayout.magazine:
        return 5;
      case CollageLayout.polaroid:
        return 4;
      case CollageLayout.custom:
        return 10;
    }
  }
}

/// Ambient zvuk
class AmbientSound {
  final String id;
  final String title;
  final String audioPath;
  final Duration duration;
  final AmbientSoundType type;
  final DateTime recordedAt;
  final List<double> waveformData;

  const AmbientSound({
    required this.id,
    required this.title,
    required this.audioPath,
    required this.duration,
    required this.type,
    required this.recordedAt,
    this.waveformData = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'audioPath': audioPath,
      'duration': duration.inMilliseconds,
      'type': type.name,
      'recordedAt': recordedAt.toIso8601String(),
      'waveformData': waveformData,
    };
  }
}

/// Typ ambient zvuku
enum AmbientSoundType {
  nature, // Příroda
  city, // Město
  ocean, // Oceán
  rain, // Déšť
  wind, // Vítr
  birds, // Ptáci
  traffic, // Doprava
  cafe, // Kavárna
  custom, // Vlastní
}

extension AmbientSoundTypeExtension on AmbientSoundType {
  String get displayName {
    switch (this) {
      case AmbientSoundType.nature:
        return 'Příroda';
      case AmbientSoundType.city:
        return 'Město';
      case AmbientSoundType.ocean:
        return 'Oceán';
      case AmbientSoundType.rain:
        return 'Déšť';
      case AmbientSoundType.wind:
        return 'Vítr';
      case AmbientSoundType.birds:
        return 'Ptáci';
      case AmbientSoundType.traffic:
        return 'Doprava';
      case AmbientSoundType.cafe:
        return 'Kavárna';
      case AmbientSoundType.custom:
        return 'Vlastní';
    }
  }

  String get icon {
    switch (this) {
      case AmbientSoundType.nature:
        return '🌿';
      case AmbientSoundType.city:
        return '🏙️';
      case AmbientSoundType.ocean:
        return '🌊';
      case AmbientSoundType.rain:
        return '🌧️';
      case AmbientSoundType.wind:
        return '💨';
      case AmbientSoundType.birds:
        return '🐦';
      case AmbientSoundType.traffic:
        return '🚗';
      case AmbientSoundType.cafe:
        return '☕';
      case AmbientSoundType.custom:
        return '🎵';
    }
  }
}

/// Live Photo
class LivePhoto {
  final String id;
  final String title;
  final String imagePath;
  final String videoPath;
  final String thumbnailPath;
  final Duration duration;
  final DateTime createdAt;

  const LivePhoto({
    required this.id,
    required this.title,
    required this.imagePath,
    required this.videoPath,
    required this.thumbnailPath,
    required this.duration,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'imagePath': imagePath,
      'videoPath': videoPath,
      'thumbnailPath': thumbnailPath,
      'duration': duration.inMilliseconds,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

/// Úroveň stabilizace
enum StabilizationLevel {
  low, // Nízká
  medium, // Střední
  high, // Vysoká
  extreme, // Extrémní
}

extension StabilizationLevelExtension on StabilizationLevel {
  String get displayName {
    switch (this) {
      case StabilizationLevel.low:
        return 'Nízká';
      case StabilizationLevel.medium:
        return 'Střední';
      case StabilizationLevel.high:
        return 'Vysoká';
      case StabilizationLevel.extreme:
        return 'Extrémní';
    }
  }

  double get strength {
    switch (this) {
      case StabilizationLevel.low:
        return 0.3;
      case StabilizationLevel.medium:
        return 0.6;
      case StabilizationLevel.high:
        return 0.8;
      case StabilizationLevel.extreme:
        return 1.0;
    }
  }
}

/// Media projekt
class MediaProject {
  final String id;
  final String name;
  final String description;
  final MediaProjectType type;
  final List<String> assets; // Cesty k souborům
  final DateTime createdAt;
  final DateTime lastModified;
  final Map<String, dynamic> settings;

  const MediaProject({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    this.assets = const [],
    required this.createdAt,
    required this.lastModified,
    this.settings = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'assets': assets,
      'createdAt': createdAt.toIso8601String(),
      'lastModified': lastModified.toIso8601String(),
      'settings': settings,
    };
  }
}

/// Typ media projektu
enum MediaProjectType {
  timeLapse, // Time-lapse
  collage, // Kolláž
  livePhoto, // Live Photo
  slideshow, // Slideshow
  videoEdit, // Úprava videa
  audioMix, // Audio mix
}

/// Výsledek zpracování media
class MediaProcessingResult {
  final bool success;
  final String? outputPath;
  final String? errorMessage;
  final Duration processingTime;
  final Map<String, dynamic>? metadata;

  const MediaProcessingResult({
    required this.success,
    this.outputPath,
    this.errorMessage,
    required this.processingTime,
    this.metadata,
  });

  factory MediaProcessingResult.success({
    required String outputPath,
    required Duration processingTime,
    Map<String, dynamic>? metadata,
  }) {
    return MediaProcessingResult(
      success: true,
      outputPath: outputPath,
      processingTime: processingTime,
      metadata: metadata,
    );
  }

  factory MediaProcessingResult.failure({
    required String errorMessage,
    required Duration processingTime,
  }) {
    return MediaProcessingResult(
      success: false,
      errorMessage: errorMessage,
      processingTime: processingTime,
    );
  }
}
