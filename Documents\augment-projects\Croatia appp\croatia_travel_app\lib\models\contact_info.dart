import 'dart:math' as math;

/// 📞 MODEL PRO KONTAKTNÍ INFORMACE
class ContactInfo {
  final String id;
  final String name;
  final String? nameEn;
  final ContactCategory category;
  final String phone;
  final String? emergencyPhone;
  final String? email;
  final String? website;
  final String address;
  final String openingHours;
  final List<String> services;
  final List<String> languages;
  final double latitude;
  final double longitude;
  final String city;
  final String region;
  final bool isEmergency;
  final String? description;
  final Map<String, dynamic>? additionalInfo;

  ContactInfo({
    required this.id,
    required this.name,
    this.nameEn,
    required this.category,
    required this.phone,
    this.emergencyPhone,
    this.email,
    this.website,
    required this.address,
    required this.openingHours,
    required this.services,
    required this.languages,
    required this.latitude,
    required this.longitude,
    required this.city,
    required this.region,
    this.isEmergency = false,
    this.description,
    this.additionalInfo,
  });

  /// Převod na JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'nameEn': nameEn,
      'category': category.toString(),
      'phone': phone,
      'emergencyPhone': emergencyPhone,
      'email': email,
      'website': website,
      'address': address,
      'openingHours': openingHours,
      'services': services,
      'languages': languages,
      'latitude': latitude,
      'longitude': longitude,
      'city': city,
      'region': region,
      'isEmergency': isEmergency,
      'description': description,
      'additionalInfo': additionalInfo,
    };
  }

  /// Vytvoření z JSON
  factory ContactInfo.fromJson(Map<String, dynamic> json) {
    return ContactInfo(
      id: json['id'],
      name: json['name'],
      nameEn: json['nameEn'],
      category: ContactCategory.values.firstWhere(
        (e) => e.toString() == json['category'],
        orElse: () => ContactCategory.other,
      ),
      phone: json['phone'],
      emergencyPhone: json['emergencyPhone'],
      email: json['email'],
      website: json['website'],
      address: json['address'],
      openingHours: json['openingHours'],
      services: List<String>.from(json['services'] ?? []),
      languages: List<String>.from(json['languages'] ?? []),
      latitude: json['latitude']?.toDouble() ?? 0.0,
      longitude: json['longitude']?.toDouble() ?? 0.0,
      city: json['city'],
      region: json['region'],
      isEmergency: json['isEmergency'] ?? false,
      description: json['description'],
      additionalInfo: json['additionalInfo'],
    );
  }

  /// Kopírování s možností změny
  ContactInfo copyWith({
    String? id,
    String? name,
    String? nameEn,
    ContactCategory? category,
    String? phone,
    String? emergencyPhone,
    String? email,
    String? website,
    String? address,
    String? openingHours,
    List<String>? services,
    List<String>? languages,
    double? latitude,
    double? longitude,
    String? city,
    String? region,
    bool? isEmergency,
    String? description,
    Map<String, dynamic>? additionalInfo,
  }) {
    return ContactInfo(
      id: id ?? this.id,
      name: name ?? this.name,
      nameEn: nameEn ?? this.nameEn,
      category: category ?? this.category,
      phone: phone ?? this.phone,
      emergencyPhone: emergencyPhone ?? this.emergencyPhone,
      email: email ?? this.email,
      website: website ?? this.website,
      address: address ?? this.address,
      openingHours: openingHours ?? this.openingHours,
      services: services ?? this.services,
      languages: languages ?? this.languages,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      city: city ?? this.city,
      region: region ?? this.region,
      isEmergency: isEmergency ?? this.isEmergency,
      description: description ?? this.description,
      additionalInfo: additionalInfo ?? this.additionalInfo,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ContactInfo && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ContactInfo(id: $id, name: $name, category: $category, city: $city)';
  }
}

/// 🏷️ KATEGORIE KONTAKTŮ
enum ContactCategory {
  government, // Úřady a instituce
  healthcare, // Zdravotnictví
  culture, // Kultura a divadla
  emergency, // Nouzové služby
  education, // Vzdělávání
  transport, // Doprava
  tourism, // Turistické informace
  business, // Obchodní služby
  other, // Ostatní
}

/// 📱 ROZŠÍŘENÍ PRO KATEGORIE
extension ContactCategoryExtension on ContactCategory {
  /// Název kategorie v češtině
  String get displayName {
    switch (this) {
      case ContactCategory.government:
        return 'Úřady a instituce';
      case ContactCategory.healthcare:
        return 'Zdravotnictví';
      case ContactCategory.culture:
        return 'Kultura a divadla';
      case ContactCategory.emergency:
        return 'Nouzové služby';
      case ContactCategory.education:
        return 'Vzdělávání';
      case ContactCategory.transport:
        return 'Doprava';
      case ContactCategory.tourism:
        return 'Turistické informace';
      case ContactCategory.business:
        return 'Obchodní služby';
      case ContactCategory.other:
        return 'Ostatní';
    }
  }

  /// Název kategorie v chorvatštině
  String get displayNameHr {
    switch (this) {
      case ContactCategory.government:
        return 'Uredi i institucije';
      case ContactCategory.healthcare:
        return 'Zdravstvo';
      case ContactCategory.culture:
        return 'Kultura i kazališta';
      case ContactCategory.emergency:
        return 'Hitne službe';
      case ContactCategory.education:
        return 'Obrazovanje';
      case ContactCategory.transport:
        return 'Prijevoz';
      case ContactCategory.tourism:
        return 'Turističke informacije';
      case ContactCategory.business:
        return 'Poslovne usluge';
      case ContactCategory.other:
        return 'Ostalo';
    }
  }

  /// Ikona pro kategorii
  String get icon {
    switch (this) {
      case ContactCategory.government:
        return '🏛️';
      case ContactCategory.healthcare:
        return '🏥';
      case ContactCategory.culture:
        return '🎭';
      case ContactCategory.emergency:
        return '🚨';
      case ContactCategory.education:
        return '🎓';
      case ContactCategory.transport:
        return '🚌';
      case ContactCategory.tourism:
        return 'ℹ️';
      case ContactCategory.business:
        return '💼';
      case ContactCategory.other:
        return '📞';
    }
  }

  /// Barva pro kategorii
  int get colorValue {
    switch (this) {
      case ContactCategory.government:
        return 0xFF006994; // Jaderská modrá
      case ContactCategory.healthcare:
        return 0xFFFF6B35; // Korálová
      case ContactCategory.culture:
        return 0xFF9C27B0; // Fialová
      case ContactCategory.emergency:
        return 0xFFE53E3E; // Červená
      case ContactCategory.education:
        return 0xFF4CAF50; // Zelená
      case ContactCategory.transport:
        return 0xFF2E8B8B; // Středomořská tyrkysová
      case ContactCategory.tourism:
        return 0xFF00BCD4; // Cyan
      case ContactCategory.business:
        return 0xFF795548; // Hnědá
      case ContactCategory.other:
        return 0xFF607D8B; // Modrošedá
    }
  }
}

/// 🔍 POMOCNÉ FUNKCE PRO KONTAKTY
class ContactUtils {
  /// Formátování telefonního čísla
  static String formatPhoneNumber(String phone) {
    // Odstranění všech ne-číselných znaků kromě +
    String cleaned = phone.replaceAll(RegExp(r'[^\d+]'), '');

    if (cleaned.startsWith('+385')) {
      // Chorvatské číslo
      String number = cleaned.substring(4);
      if (number.length >= 8) {
        return '+385 ${number.substring(0, 2)} ${number.substring(2, 5)} ${number.substring(5)}';
      }
    }

    return phone; // Vrátit původní pokud nelze formátovat
  }

  /// Kontrola, zda je kontakt otevřený
  static bool isOpenNow(String openingHours) {
    if (openingHours.contains('24/7')) return true;
    if (openingHours.toLowerCase().contains('zavřeno')) return false;

    // Zde by byla komplexnější logika pro parsování otevírací doby
    // Pro demo vrátíme true
    return true;
  }

  /// Získání vzdálenosti od uživatele
  static double calculateDistance(
    double userLat,
    double userLng,
    double contactLat,
    double contactLng,
  ) {
    // Haversine formula pro výpočet vzdálenosti
    const double earthRadius = 6371; // km

    double dLat = _toRadians(contactLat - userLat);
    double dLng = _toRadians(contactLng - userLng);

    double a =
        math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_toRadians(userLat)) *
            math.cos(_toRadians(contactLat)) *
            math.sin(dLng / 2) *
            math.sin(dLng / 2);

    double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));

    return earthRadius * c;
  }

  static double _toRadians(double degree) {
    return degree * (math.pi / 180);
  }
}
