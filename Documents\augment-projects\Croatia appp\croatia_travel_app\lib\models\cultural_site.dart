import 'dart:math';
import 'package:json_annotation/json_annotation.dart';

part 'cultural_site.g.dart';

@JsonSerializable()
class CulturalSite {
  final String id;
  final String name;
  final String description;
  final String address;
  final double latitude;
  final double longitude;
  final String region;
  final CulturalSiteType siteType;
  final HistoricalPeriod historicalPeriod;
  final ArchitecturalStyle architecturalStyle;
  final double rating;
  final int reviewCount;
  final bool isUnescoSite;
  final String? unescoYear;
  final List<String> features;
  final String? openingHours;
  final String? ticketPrice;
  final String? phone;
  final String? website;
  final bool hasGuidedTours;
  final bool hasAudioGuide;
  final bool isAccessible;
  final bool hasParking;
  final bool hasGiftShop;
  final bool hasCafe;
  final List<String> photos;
  final List<String> virtualTours;
  final String? bestTimeToVisit;
  final int? estimatedVisitDuration; // minutes
  final List<String> nearbyAttractions;
  final DateTime lastUpdated;

  CulturalSite({
    required this.id,
    required this.name,
    required this.description,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.region,
    required this.siteType,
    required this.historicalPeriod,
    required this.architecturalStyle,
    required this.rating,
    required this.reviewCount,
    required this.isUnescoSite,
    this.unescoYear,
    required this.features,
    this.openingHours,
    this.ticketPrice,
    this.phone,
    this.website,
    required this.hasGuidedTours,
    required this.hasAudioGuide,
    required this.isAccessible,
    required this.hasParking,
    required this.hasGiftShop,
    required this.hasCafe,
    required this.photos,
    required this.virtualTours,
    this.bestTimeToVisit,
    this.estimatedVisitDuration,
    required this.nearbyAttractions,
    required this.lastUpdated,
  });

  /// Má kulturní místo vysoké hodnocení (4.5+)
  bool get isTopRated => rating >= 4.5;

  /// Je vhodné pro rodiny
  bool get isFamilyFriendly => hasAudioGuide && isAccessible && hasCafe;

  /// Má kompletní služby
  bool get hasFullServices =>
      hasGuidedTours && hasAudioGuide && hasGiftShop && hasCafe;

  /// Je historicky významné
  bool get isHistoricallySignificant =>
      isUnescoSite ||
      historicalPeriod == HistoricalPeriod.medieval ||
      historicalPeriod == HistoricalPeriod.roman;

  /// Vzdálenost od zadané pozice (v km)
  double distanceFrom(double lat, double lng) {
    // Haversine formula pro výpočet vzdálenosti
    const double earthRadius = 6371; // km

    final double dLat = _toRadians(latitude - lat);
    final double dLng = _toRadians(longitude - lng);

    final double a =
        sin(dLat / 2) * sin(dLat / 2) +
        cos(_toRadians(lat)) *
            cos(_toRadians(latitude)) *
            sin(dLng / 2) *
            sin(dLng / 2);

    final double c = 2 * asin(sqrt(a));

    return earthRadius * c;
  }

  double _toRadians(double degrees) {
    return degrees * (pi / 180);
  }

  factory CulturalSite.fromJson(Map<String, dynamic> json) =>
      _$CulturalSiteFromJson(json);
  Map<String, dynamic> toJson() => _$CulturalSiteToJson(this);
}

/// Typy kulturních míst
enum CulturalSiteType {
  castle, // Hrad
  fortress, // Pevnost
  church, // Kostel
  monastery, // Klášter
  museum, // Muzeum
  gallery, // Galerie
  palace, // Palác
  ruins, // Ruiny
  monument, // Památník
  archaeologicalSite, // Archeologické naleziště
  historicTown, // Historické město
  culturalCenter, // Kulturní centrum
}

/// Historická období
enum HistoricalPeriod {
  prehistoric, // Pravěk
  roman, // Římské období
  byzantine, // Byzantské období
  medieval, // Středověk
  renaissance, // Renesance
  baroque, // Baroko
  modern, // Moderní doba
  contemporary, // Současnost
}

/// Architektonické styly
enum ArchitecturalStyle {
  roman, // Románský
  romanesque, // Románský (starší styl)
  byzantine, // Byzantský
  gothic, // Gotický
  renaissance, // Renesanční
  baroque, // Barokní
  neoclassical, // Neoklasicistní
  modern, // Moderní
  contemporary, // Současný
  vernacular, // Lidový
  mixed, // Smíšený
}

/// Regiony Chorvatska pro kulturní místa
enum CulturalRegion {
  istria, // Istrie
  kvarner, // Kvarner
  dalmatia, // Dalmácie
  dubrovnik, // Dubrovník
  zagreb, // Zagreb
  slavonia, // Slavonie
  lika, // Lika
}

/// Kulturní událost
@JsonSerializable()
class CulturalEvent {
  final String id;
  final String name;
  final String description;
  final String location;
  final DateTime startDate;
  final DateTime endDate;
  final CulturalEventType eventType;
  final String? ticketPrice;
  final String? website;
  final String? phone;
  final List<String> photos;
  final bool isRecurring;
  final String? recurrencePattern;

  CulturalEvent({
    required this.id,
    required this.name,
    required this.description,
    required this.location,
    required this.startDate,
    required this.endDate,
    required this.eventType,
    this.ticketPrice,
    this.website,
    this.phone,
    required this.photos,
    required this.isRecurring,
    this.recurrencePattern,
  });

  /// Je událost aktuálně probíhající
  bool get isOngoing {
    final now = DateTime.now();
    return now.isAfter(startDate) && now.isBefore(endDate);
  }

  /// Je událost nadcházející
  bool get isUpcoming {
    final now = DateTime.now();
    return now.isBefore(startDate);
  }

  /// Délka události ve dnech
  int get durationInDays => endDate.difference(startDate).inDays + 1;

  factory CulturalEvent.fromJson(Map<String, dynamic> json) =>
      _$CulturalEventFromJson(json);
  Map<String, dynamic> toJson() => _$CulturalEventToJson(this);
}

/// Typy kulturních událostí
enum CulturalEventType {
  festival, // Festival
  concert, // Koncert
  exhibition, // Výstava
  theater, // Divadlo
  dance, // Tanec
  folklore, // Folklor
  gastronomy, // Gastronomie
  crafts, // Řemesla
  religious, // Náboženské
  historical, // Historické
}

/// Hodnocení kulturního místa od uživatele
@JsonSerializable()
class CulturalSiteReview {
  final String id;
  final String culturalSiteId;
  final String userId;
  final String userName;
  final double rating;
  final String comment;
  final List<String> photos;
  final DateTime visitDate;
  final List<String> likedFeatures;
  final List<String> suggestions;
  final bool wouldRecommend;
  final DateTime createdAt;

  CulturalSiteReview({
    required this.id,
    required this.culturalSiteId,
    required this.userId,
    required this.userName,
    required this.rating,
    required this.comment,
    required this.photos,
    required this.visitDate,
    required this.likedFeatures,
    required this.suggestions,
    required this.wouldRecommend,
    required this.createdAt,
  });

  factory CulturalSiteReview.fromJson(Map<String, dynamic> json) =>
      _$CulturalSiteReviewFromJson(json);
  Map<String, dynamic> toJson() => _$CulturalSiteReviewToJson(this);
}

/// Oblíbené kulturní místo uživatele
@JsonSerializable()
class FavoriteCulturalSite {
  final String id;
  final String userId;
  final String culturalSiteId;
  final DateTime addedAt;
  final String? notes;
  final List<String> interests;

  FavoriteCulturalSite({
    required this.id,
    required this.userId,
    required this.culturalSiteId,
    required this.addedAt,
    this.notes,
    required this.interests,
  });

  factory FavoriteCulturalSite.fromJson(Map<String, dynamic> json) =>
      _$FavoriteCulturalSiteFromJson(json);
  Map<String, dynamic> toJson() => _$FavoriteCulturalSiteToJson(this);
}

/// Návštěva kulturního místa
@JsonSerializable()
class CulturalSiteVisit {
  final String id;
  final String userId;
  final String culturalSiteId;
  final DateTime visitDate;
  final Duration? visitDuration;
  final double? userRating;
  final String? notes;
  final List<String> photos;
  final bool hadGuidedTour;
  final bool usedAudioGuide;
  final List<String> visitedFeatures;

  CulturalSiteVisit({
    required this.id,
    required this.userId,
    required this.culturalSiteId,
    required this.visitDate,
    this.visitDuration,
    this.userRating,
    this.notes,
    required this.photos,
    required this.hadGuidedTour,
    required this.usedAudioGuide,
    required this.visitedFeatures,
  });

  factory CulturalSiteVisit.fromJson(Map<String, dynamic> json) =>
      _$CulturalSiteVisitFromJson(json);
  Map<String, dynamic> toJson() => _$CulturalSiteVisitToJson(this);
}

/// Kulturní trasa
@JsonSerializable()
class CulturalRoute {
  final String id;
  final String name;
  final String description;
  final List<String> culturalSiteIds;
  final int estimatedDuration; // hours
  final String difficulty; // easy, moderate, challenging
  final String theme; // UNESCO, medieval, baroque, etc.
  final double rating;
  final int reviewCount;

  CulturalRoute({
    required this.id,
    required this.name,
    required this.description,
    required this.culturalSiteIds,
    required this.estimatedDuration,
    required this.difficulty,
    required this.theme,
    required this.rating,
    required this.reviewCount,
  });

  /// Počet míst na trase
  int get siteCount => culturalSiteIds.length;

  /// Je trasa vhodná pro jeden den
  bool get isOneDayRoute => estimatedDuration <= 8;

  factory CulturalRoute.fromJson(Map<String, dynamic> json) =>
      _$CulturalRouteFromJson(json);
  Map<String, dynamic> toJson() => _$CulturalRouteToJson(this);
}
