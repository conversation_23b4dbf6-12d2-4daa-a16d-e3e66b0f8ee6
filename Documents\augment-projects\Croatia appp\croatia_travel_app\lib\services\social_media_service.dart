import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import '../models/diary_entry.dart';
import '../models/place.dart';
import '../models/route_plan.dart';

class SocialMediaService {
  static final SocialMediaService _instance = SocialMediaService._internal();
  factory SocialMediaService() => _instance;
  SocialMediaService._internal();

  /// Sdílení deníkového záznamu
  Future<bool> shareDiaryEntry(
    DiaryEntry entry, {
    List<String>? imagePaths,
    SocialPlatform? platform,
  }) async {
    try {
      final content = _generateDiaryContent(entry);

      // Simulace sdílení - v reálné aplikaci by se použil share_plus package
      debugPrint('Sdílení deníkového záznamu: $content');
      if (imagePaths != null && imagePaths.isNotEmpty) {
        debugPrint('S obrázky: ${imagePaths.length}');
      }

      return true;
    } catch (e) {
      debugPrint('Chyba při sdílen<PERSON> deníkového záznamu: $e');
      return false;
    }
  }

  /// Sdílení místa
  Future<bool> sharePlace(
    Place place, {
    String? personalNote,
    List<String>? imagePaths,
  }) async {
    try {
      final content = _generatePlaceContent(place, personalNote);

      // Simulace sdílení
      debugPrint('Sdílení místa: $content');
      if (imagePaths != null && imagePaths.isNotEmpty) {
        debugPrint('S obrázky: ${imagePaths.length}');
      }

      return true;
    } catch (e) {
      debugPrint('Chyba při sdílení místa: $e');
      return false;
    }
  }

  /// Sdílení trasy
  Future<bool> shareRoute(
    RoutePlan route, {
    String? personalNote,
    Uint8List? mapScreenshot,
  }) async {
    try {
      final content = _generateRouteContent(route, personalNote);

      // Simulace sdílení
      debugPrint('Sdílení trasy: $content');
      if (mapScreenshot != null) {
        debugPrint('S mapovým snímkem');
      }

      return true;
    } catch (e) {
      debugPrint('Chyba při sdílení trasy: $e');
      return false;
    }
  }

  /// Sdílení na Instagram Stories
  Future<bool> shareToInstagramStory({
    required String imagePath,
    String? text,
    String? backgroundTopColor,
    String? backgroundBottomColor,
  }) async {
    try {
      // Implementace sdílení na Instagram Stories
      // V reálné aplikaci by se použil Instagram Basic Display API

      final content = text ?? 'Objevuji Chorvatsko! 🇭🇷 #CroatiaTravel';

      debugPrint('Sdílení na Instagram Stories: $content');
      debugPrint('Obrázek: $imagePath');

      return true;
    } catch (e) {
      debugPrint('Chyba při sdílení na Instagram: $e');
      return false;
    }
  }

  /// Sdílení na Facebook
  Future<bool> shareToFacebook({
    required String content,
    List<String>? imagePaths,
    String? link,
  }) async {
    try {
      // Implementace sdílení na Facebook
      // V reálné aplikaci by se použil Facebook SDK

      debugPrint('Sdílení na Facebook: $content');
      if (imagePaths != null && imagePaths.isNotEmpty) {
        debugPrint('S obrázky: ${imagePaths.length}');
      }

      return true;
    } catch (e) {
      debugPrint('Chyba při sdílení na Facebook: $e');
      return false;
    }
  }

  /// Vytvoření Instagram Story šablony
  Future<String> createInstagramStoryTemplate(
    DiaryEntry entry, {
    String? imagePath,
  }) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final templatePath = '${tempDir.path}/instagram_story_${entry.id}.png';

      // Simulace vytvoření šablony
      // V reálné aplikaci by se použila knihovna pro generování obrázků

      return templatePath;
    } catch (e) {
      debugPrint('Chyba při vytváření Instagram šablony: $e');
      rethrow;
    }
  }

  /// Generování obsahu pro deníkový záznam
  String _generateDiaryContent(DiaryEntry entry) {
    final buffer = StringBuffer();

    buffer.writeln('🇭🇷 ${entry.title}');
    buffer.writeln();

    if (entry.location != null) {
      buffer.writeln('📍 ${entry.location}');
    }

    if (entry.mood != null) {
      buffer.writeln('${entry.mood!.emoji} ${entry.mood!.displayName}');
    }

    if (entry.rating != null) {
      final stars = '⭐' * entry.rating!.round();
      buffer.writeln('$stars (${entry.rating}/5)');
    }

    buffer.writeln();

    // Zkrácený obsah pro sdílení
    final excerpt = entry.excerpt;
    buffer.writeln(excerpt);

    if (entry.tags.isNotEmpty) {
      buffer.writeln();
      final hashtags = entry.tags
          .map((tag) => '#${tag.replaceAll(' ', '')}')
          .join(' ');
      buffer.writeln(hashtags);
    }

    buffer.writeln();
    buffer.writeln('#CroatiaTravel #Chorvatsko #Cestování');

    return buffer.toString();
  }

  /// Generování obsahu pro místo
  String _generatePlaceContent(Place place, String? personalNote) {
    final buffer = StringBuffer();

    buffer.writeln('🏛️ ${place.name}');
    buffer.writeln();
    buffer.writeln('📍 ${place.region.toUpperCase()}');
    buffer.writeln();

    if (personalNote != null && personalNote.isNotEmpty) {
      buffer.writeln(personalNote);
      buffer.writeln();
    }

    buffer.writeln(place.description);

    if (place.rating != null && place.rating! > 0) {
      final stars = '⭐' * place.rating!.round();
      buffer.writeln();
      buffer.writeln('$stars (${place.rating}/5)');
    }

    if (place.tags.isNotEmpty) {
      buffer.writeln();
      final hashtags = place.tags
          .map((tag) => '#${tag.replaceAll(' ', '')}')
          .join(' ');
      buffer.writeln(hashtags);
    }

    buffer.writeln();
    buffer.writeln('#CroatiaTravel #${place.region} #Doporučuji');

    return buffer.toString();
  }

  /// Generování obsahu pro trasu
  String _generateRouteContent(RoutePlan route, String? personalNote) {
    final buffer = StringBuffer();

    buffer.writeln('🗺️ ${route.name}');
    buffer.writeln();

    if (personalNote != null && personalNote.isNotEmpty) {
      buffer.writeln(personalNote);
      buffer.writeln();
    }

    buffer.writeln('📅 ${route.estimatedDuration.inDays} dní');
    buffer.writeln('📍 ${route.points.length} zastávek');

    if (route.points.isNotEmpty) {
      buffer.writeln();
      buffer.writeln('Trasa:');
      for (int i = 0; i < route.points.length && i < 5; i++) {
        final point = route.points[i];
        buffer.writeln('${i + 1}. ${point.name}');
      }

      if (route.points.length > 5) {
        buffer.writeln('... a další');
      }
    }

    // Simulace tags - v reálné aplikaci by RoutePlan měl tags property
    buffer.writeln();
    buffer.writeln('#CroatiaTravel #CestovníTrasa #Itinerář');

    buffer.writeln();
    buffer.writeln('#CroatiaTravel #CestovníTrasa #Itinerář');

    return buffer.toString();
  }

  /// Získání dostupných platforem pro sdílení
  List<SocialPlatform> getAvailablePlatforms() {
    return [
      SocialPlatform.general,
      SocialPlatform.instagram,
      SocialPlatform.facebook,
      SocialPlatform.twitter,
      SocialPlatform.whatsapp,
      SocialPlatform.email,
    ];
  }

  /// Kontrola dostupnosti platformy
  Future<bool> isPlatformAvailable(SocialPlatform platform) async {
    // V reálné aplikaci by se kontrolovala dostupnost konkrétních aplikací
    return true;
  }
}

enum SocialPlatform { general, instagram, facebook, twitter, whatsapp, email }

extension SocialPlatformExtension on SocialPlatform {
  String get displayName {
    switch (this) {
      case SocialPlatform.general:
        return 'Obecné sdílení';
      case SocialPlatform.instagram:
        return 'Instagram';
      case SocialPlatform.facebook:
        return 'Facebook';
      case SocialPlatform.twitter:
        return 'Twitter';
      case SocialPlatform.whatsapp:
        return 'WhatsApp';
      case SocialPlatform.email:
        return 'Email';
    }
  }

  String get icon {
    switch (this) {
      case SocialPlatform.general:
        return '📱';
      case SocialPlatform.instagram:
        return '📷';
      case SocialPlatform.facebook:
        return '👥';
      case SocialPlatform.twitter:
        return '🐦';
      case SocialPlatform.whatsapp:
        return '💬';
      case SocialPlatform.email:
        return '📧';
    }
  }

  Color get color {
    switch (this) {
      case SocialPlatform.general:
        return const Color(0xFF6B7280);
      case SocialPlatform.instagram:
        return const Color(0xFFE4405F);
      case SocialPlatform.facebook:
        return const Color(0xFF1877F2);
      case SocialPlatform.twitter:
        return const Color(0xFF1DA1F2);
      case SocialPlatform.whatsapp:
        return const Color(0xFF25D366);
      case SocialPlatform.email:
        return const Color(0xFF34495E);
    }
  }
}
