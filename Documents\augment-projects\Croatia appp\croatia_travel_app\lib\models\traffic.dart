import 'package:json_annotation/json_annotation.dart';

part 'traffic.g.dart';

// ========== DOPRAVNÍ SITUACE ==========

@JsonSerializable()
class TrafficIncident {
  final String id;
  final TrafficIncidentType type;
  final String title;
  final String description;
  final double latitude;
  final double longitude;
  final TrafficSeverity severity;
  final TrafficIncidentStatus status;
  final DateTime reportedAt;
  final DateTime? resolvedAt;
  final String? reportedBy;
  final List<String> affectedRoads;
  final List<String> alternativeRoutes;
  final Duration? estimatedDelay;
  final List<String> images;
  final int upvotes;
  final int downvotes;
  final bool isVerified;

  TrafficIncident({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.latitude,
    required this.longitude,
    required this.severity,
    required this.status,
    required this.reportedAt,
    this.resolvedAt,
    this.reportedBy,
    this.affectedRoads = const [],
    this.alternativeRoutes = const [],
    this.estimatedDelay,
    this.images = const [],
    this.upvotes = 0,
    this.downvotes = 0,
    this.isVerified = false,
  });

  factory TrafficIncident.fromJson(Map<String, dynamic> json) =>
      _$TrafficIncidentFromJson(json);
  Map<String, dynamic> toJson() => _$TrafficIncidentToJson(this);

  Duration get duration {
    final end = resolvedAt ?? DateTime.now();
    return end.difference(reportedAt);
  }

  bool get isActive => status == TrafficIncidentStatus.active;
  bool get isResolved => status == TrafficIncidentStatus.resolved;

  String get severityText {
    switch (severity) {
      case TrafficSeverity.low:
        return 'Nízká';
      case TrafficSeverity.medium:
        return 'Střední';
      case TrafficSeverity.high:
        return 'Vysoká';
      case TrafficSeverity.critical:
        return 'Kritická';
    }
  }

  String get typeText {
    switch (type) {
      case TrafficIncidentType.accident:
        return 'Nehoda';
      case TrafficIncidentType.roadwork:
        return 'Stavební práce';
      case TrafficIncidentType.closure:
        return 'Uzavírka';
      case TrafficIncidentType.congestion:
        return 'Zácpa';
      case TrafficIncidentType.weather:
        return 'Počasí';
      case TrafficIncidentType.event:
        return 'Událost';
      case TrafficIncidentType.breakdown:
        return 'Porucha vozidla';
      case TrafficIncidentType.other:
        return 'Ostatní';
    }
  }
}

@JsonSerializable()
class TrafficCondition {
  final String roadId;
  final String roadName;
  final TrafficFlow flow;
  final double averageSpeed; // km/h
  final double freeFlowSpeed; // km/h
  final int travelTime; // v sekundách
  final int freeFlowTravelTime; // v sekundách
  final DateTime lastUpdated;
  final List<TrafficSegment> segments;

  TrafficCondition({
    required this.roadId,
    required this.roadName,
    required this.flow,
    required this.averageSpeed,
    required this.freeFlowSpeed,
    required this.travelTime,
    required this.freeFlowTravelTime,
    required this.lastUpdated,
    this.segments = const [],
  });

  factory TrafficCondition.fromJson(Map<String, dynamic> json) =>
      _$TrafficConditionFromJson(json);
  Map<String, dynamic> toJson() => _$TrafficConditionToJson(this);

  double get speedRatio => averageSpeed / freeFlowSpeed;

  Duration get delay {
    final delaySeconds = travelTime - freeFlowTravelTime;
    return Duration(seconds: delaySeconds);
  }

  String get flowText {
    switch (flow) {
      case TrafficFlow.free:
        return 'Volný';
      case TrafficFlow.light:
        return 'Lehký';
      case TrafficFlow.moderate:
        return 'Střední';
      case TrafficFlow.heavy:
        return 'Hustý';
      case TrafficFlow.congested:
        return 'Zácpa';
    }
  }
}

@JsonSerializable()
class TrafficSegment {
  final String id;
  final double startLatitude;
  final double startLongitude;
  final double endLatitude;
  final double endLongitude;
  final TrafficFlow flow;
  final double speed;
  final int length; // v metrech

  TrafficSegment({
    required this.id,
    required this.startLatitude,
    required this.startLongitude,
    required this.endLatitude,
    required this.endLongitude,
    required this.flow,
    required this.speed,
    required this.length,
  });

  factory TrafficSegment.fromJson(Map<String, dynamic> json) =>
      _$TrafficSegmentFromJson(json);
  Map<String, dynamic> toJson() => _$TrafficSegmentToJson(this);
}

@JsonSerializable()
class TrafficCamera {
  final String id;
  final String name;
  final String location;
  final double latitude;
  final double longitude;
  final String imageUrl;
  final DateTime lastUpdated;
  final bool isActive;
  final String? description;
  final CameraType type;

  TrafficCamera({
    required this.id,
    required this.name,
    required this.location,
    required this.latitude,
    required this.longitude,
    required this.imageUrl,
    required this.lastUpdated,
    this.isActive = true,
    this.description,
    required this.type,
  });

  factory TrafficCamera.fromJson(Map<String, dynamic> json) =>
      _$TrafficCameraFromJson(json);
  Map<String, dynamic> toJson() => _$TrafficCameraToJson(this);

  String get typeText {
    switch (type) {
      case CameraType.traffic:
        return 'Dopravní';
      case CameraType.weather:
        return 'Počasí';
      case CameraType.security:
        return 'Bezpečnostní';
      case CameraType.toll:
        return 'Mýtná';
    }
  }
}

@JsonSerializable()
class RoadClosure {
  final String id;
  final String roadName;
  final String reason;
  final DateTime startTime;
  final DateTime? endTime;
  final bool isPlanned;
  final List<String> affectedDirections;
  final List<String> alternativeRoutes;
  final double latitude;
  final double longitude;
  final ClosureType type;
  final String? contactInfo;

  RoadClosure({
    required this.id,
    required this.roadName,
    required this.reason,
    required this.startTime,
    this.endTime,
    this.isPlanned = false,
    this.affectedDirections = const [],
    this.alternativeRoutes = const [],
    required this.latitude,
    required this.longitude,
    required this.type,
    this.contactInfo,
  });

  factory RoadClosure.fromJson(Map<String, dynamic> json) =>
      _$RoadClosureFromJson(json);
  Map<String, dynamic> toJson() => _$RoadClosureToJson(this);

  bool get isActive {
    final now = DateTime.now();
    return now.isAfter(startTime) &&
        (endTime == null || now.isBefore(endTime!));
  }

  Duration? get duration {
    if (endTime == null) return null;
    return endTime!.difference(startTime);
  }

  String get typeText {
    switch (type) {
      case ClosureType.full:
        return 'Úplná uzavírka';
      case ClosureType.partial:
        return 'Částečná uzavírka';
      case ClosureType.lane:
        return 'Uzavírka pruhu';
      case ClosureType.temporary:
        return 'Dočasná uzavírka';
    }
  }
}

@JsonSerializable()
class TrafficAlert {
  final String id;
  final String title;
  final String message;
  final TrafficAlertType type;
  final TrafficSeverity severity;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final List<String> affectedAreas;
  final bool isActive;

  TrafficAlert({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.severity,
    required this.createdAt,
    this.expiresAt,
    this.affectedAreas = const [],
    this.isActive = true,
  });

  factory TrafficAlert.fromJson(Map<String, dynamic> json) =>
      _$TrafficAlertFromJson(json);
  Map<String, dynamic> toJson() => _$TrafficAlertToJson(this);

  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  String get typeText {
    switch (type) {
      case TrafficAlertType.general:
        return 'Obecné';
      case TrafficAlertType.weather:
        return 'Počasí';
      case TrafficAlertType.emergency:
        return 'Nouzová situace';
      case TrafficAlertType.event:
        return 'Událost';
      case TrafficAlertType.maintenance:
        return 'Údržba';
    }
  }
}

// ========== ENUMS ==========

enum TrafficIncidentType {
  accident,
  roadwork,
  closure,
  congestion,
  weather,
  event,
  breakdown,
  other,
}

enum TrafficSeverity { low, medium, high, critical }

enum TrafficIncidentStatus { active, resolved, investigating, clearing }

enum TrafficFlow { free, light, moderate, heavy, congested }

enum CameraType { traffic, weather, security, toll }

enum ClosureType { full, partial, lane, temporary }

enum TrafficAlertType { general, weather, emergency, event, maintenance }

// ========== POKROČILÉ MODELY DOPRAVY ==========

@JsonSerializable()
class TrafficPrediction {
  final String id;
  final double latitude;
  final double longitude;
  final DateTime targetTime;
  final double radiusKm;
  final TrafficFlow predictedFlow;
  final double averageSpeed;
  final double confidenceLevel; // 0-1
  final List<PredictionFactor> factors;
  final Duration estimatedDelay;
  final List<String> affectedRoads;
  final DateTime generatedAt;

  TrafficPrediction({
    required this.id,
    required this.latitude,
    required this.longitude,
    required this.targetTime,
    required this.radiusKm,
    required this.predictedFlow,
    required this.averageSpeed,
    required this.confidenceLevel,
    required this.factors,
    required this.estimatedDelay,
    required this.affectedRoads,
    required this.generatedAt,
  });

  factory TrafficPrediction.fromJson(Map<String, dynamic> json) =>
      _$TrafficPredictionFromJson(json);
  Map<String, dynamic> toJson() => _$TrafficPredictionToJson(this);

  factory TrafficPrediction.fallback() => TrafficPrediction(
    id: 'fallback',
    latitude: 0,
    longitude: 0,
    targetTime: DateTime.now(),
    radiusKm: 5,
    predictedFlow: TrafficFlow.moderate,
    averageSpeed: 30,
    confidenceLevel: 0.1,
    factors: [],
    estimatedDelay: Duration.zero,
    affectedRoads: [],
    generatedAt: DateTime.now(),
  );
}

@JsonSerializable()
class AlternativeRoute {
  final String id;
  final String name;
  final List<LatLng> path;
  final Duration estimatedTime;
  final double distance;
  final TrafficFlow expectedFlow;
  final List<String> avoidedIncidents;
  final double fuelSavings;
  final double timeSavings;
  final RouteQuality quality;

  AlternativeRoute({
    required this.id,
    required this.name,
    required this.path,
    required this.estimatedTime,
    required this.distance,
    required this.expectedFlow,
    required this.avoidedIncidents,
    required this.fuelSavings,
    required this.timeSavings,
    required this.quality,
  });

  factory AlternativeRoute.fromJson(Map<String, dynamic> json) =>
      _$AlternativeRouteFromJson(json);
  Map<String, dynamic> toJson() => _$AlternativeRouteToJson(this);
}

@JsonSerializable()
class TrafficFlowAnalysis {
  final String roadId;
  final DateTime startTime;
  final DateTime endTime;
  final double averageSpeed;
  final double peakSpeed;
  final double minSpeed;
  final List<FlowDataPoint> dataPoints;
  final List<TrafficPattern> patterns;
  final TrafficPrediction nextHourPrediction;

  TrafficFlowAnalysis({
    required this.roadId,
    required this.startTime,
    required this.endTime,
    required this.averageSpeed,
    required this.peakSpeed,
    required this.minSpeed,
    required this.dataPoints,
    required this.patterns,
    required this.nextHourPrediction,
  });

  factory TrafficFlowAnalysis.fromJson(Map<String, dynamic> json) =>
      _$TrafficFlowAnalysisFromJson(json);
  Map<String, dynamic> toJson() => _$TrafficFlowAnalysisToJson(this);

  factory TrafficFlowAnalysis.empty() => TrafficFlowAnalysis(
    roadId: '',
    startTime: DateTime.now(),
    endTime: DateTime.now(),
    averageSpeed: 0,
    peakSpeed: 0,
    minSpeed: 0,
    dataPoints: [],
    patterns: [],
    nextHourPrediction: TrafficPrediction.fallback(),
  );
}

@JsonSerializable()
class PersonalizedAlert {
  final String id;
  final String title;
  final String message;
  final TrafficAlertType type;
  final TrafficSeverity severity;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final List<String> affectedRoutes;
  final double relevanceScore; // 0-1
  final Map<String, dynamic> actionData;

  PersonalizedAlert({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.severity,
    required this.createdAt,
    this.expiresAt,
    required this.affectedRoutes,
    required this.relevanceScore,
    required this.actionData,
  });

  factory PersonalizedAlert.fromJson(Map<String, dynamic> json) =>
      _$PersonalizedAlertFromJson(json);
  Map<String, dynamic> toJson() => _$PersonalizedAlertToJson(this);
}

@JsonSerializable()
class TrafficLightOptimization {
  final String intersectionId;
  final DateTime optimizedAt;
  final Duration timeWindow;
  final List<LightPhase> currentPhases;
  final List<LightPhase> optimizedPhases;
  final double efficiencyGain; // procenta
  final double waitTimeReduction; // sekundy
  final Map<String, dynamic> metrics;

  TrafficLightOptimization({
    required this.intersectionId,
    required this.optimizedAt,
    required this.timeWindow,
    required this.currentPhases,
    required this.optimizedPhases,
    required this.efficiencyGain,
    required this.waitTimeReduction,
    required this.metrics,
  });

  factory TrafficLightOptimization.fromJson(Map<String, dynamic> json) =>
      _$TrafficLightOptimizationFromJson(json);
  Map<String, dynamic> toJson() => _$TrafficLightOptimizationToJson(this);

  factory TrafficLightOptimization.empty() => TrafficLightOptimization(
    intersectionId: '',
    optimizedAt: DateTime.now(),
    timeWindow: Duration.zero,
    currentPhases: [],
    optimizedPhases: [],
    efficiencyGain: 0,
    waitTimeReduction: 0,
    metrics: {},
  );
}

// ========== POMOCNÉ MODELY ==========

@JsonSerializable()
class LatLng {
  final double latitude;
  final double longitude;

  LatLng({required this.latitude, required this.longitude});

  factory LatLng.fromJson(Map<String, dynamic> json) => _$LatLngFromJson(json);
  Map<String, dynamic> toJson() => _$LatLngToJson(this);
}

@JsonSerializable()
class PredictionFactor {
  final String name;
  final double impact; // -1 to 1
  final String description;

  PredictionFactor({
    required this.name,
    required this.impact,
    required this.description,
  });

  factory PredictionFactor.fromJson(Map<String, dynamic> json) =>
      _$PredictionFactorFromJson(json);
  Map<String, dynamic> toJson() => _$PredictionFactorToJson(this);
}

enum RouteQuality { excellent, good, fair, poor }

@JsonSerializable()
class FlowDataPoint {
  final DateTime timestamp;
  final double speed;
  final int vehicleCount;
  final TrafficFlow flow;

  FlowDataPoint({
    required this.timestamp,
    required this.speed,
    required this.vehicleCount,
    required this.flow,
  });

  factory FlowDataPoint.fromJson(Map<String, dynamic> json) =>
      _$FlowDataPointFromJson(json);
  Map<String, dynamic> toJson() => _$FlowDataPointToJson(this);
}

@JsonSerializable()
class TrafficPattern {
  final String name;
  final String description;
  final double confidence; // 0-1
  final Duration duration;
  final List<String> triggers;

  TrafficPattern({
    required this.name,
    required this.description,
    required this.confidence,
    required this.duration,
    required this.triggers,
  });

  factory TrafficPattern.fromJson(Map<String, dynamic> json) =>
      _$TrafficPatternFromJson(json);
  Map<String, dynamic> toJson() => _$TrafficPatternToJson(this);
}

@JsonSerializable()
class LightPhase {
  final String direction;
  final Duration greenTime;
  final Duration redTime;
  final Duration yellowTime;
  final int priority;

  LightPhase({
    required this.direction,
    required this.greenTime,
    required this.redTime,
    required this.yellowTime,
    required this.priority,
  });

  factory LightPhase.fromJson(Map<String, dynamic> json) =>
      _$LightPhaseFromJson(json);
  Map<String, dynamic> toJson() => _$LightPhaseToJson(this);
}
