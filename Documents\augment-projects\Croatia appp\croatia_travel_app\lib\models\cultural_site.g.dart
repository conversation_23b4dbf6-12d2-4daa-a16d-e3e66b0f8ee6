// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cultural_site.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CulturalSite _$CulturalSiteFromJson(Map<String, dynamic> json) => CulturalSite(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      address: json['address'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      region: json['region'] as String,
      siteType: $enumDecode(_$CulturalSiteTypeEnumMap, json['siteType']),
      historicalPeriod:
          $enumDecode(_$HistoricalPeriodEnumMap, json['historicalPeriod']),
      architecturalStyle:
          $enumDecode(_$ArchitecturalStyleEnumMap, json['architecturalStyle']),
      rating: (json['rating'] as num).toDouble(),
      reviewCount: (json['reviewCount'] as num).toInt(),
      isUnescoSite: json['isUnescoSite'] as bool,
      unescoYear: json['unescoYear'] as String?,
      features:
          (json['features'] as List<dynamic>).map((e) => e as String).toList(),
      openingHours: json['openingHours'] as String?,
      ticketPrice: json['ticketPrice'] as String?,
      phone: json['phone'] as String?,
      website: json['website'] as String?,
      hasGuidedTours: json['hasGuidedTours'] as bool,
      hasAudioGuide: json['hasAudioGuide'] as bool,
      isAccessible: json['isAccessible'] as bool,
      hasParking: json['hasParking'] as bool,
      hasGiftShop: json['hasGiftShop'] as bool,
      hasCafe: json['hasCafe'] as bool,
      photos:
          (json['photos'] as List<dynamic>).map((e) => e as String).toList(),
      virtualTours: (json['virtualTours'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      bestTimeToVisit: json['bestTimeToVisit'] as String?,
      estimatedVisitDuration: (json['estimatedVisitDuration'] as num?)?.toInt(),
      nearbyAttractions: (json['nearbyAttractions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$CulturalSiteToJson(CulturalSite instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'address': instance.address,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'region': instance.region,
      'siteType': _$CulturalSiteTypeEnumMap[instance.siteType]!,
      'historicalPeriod': _$HistoricalPeriodEnumMap[instance.historicalPeriod]!,
      'architecturalStyle':
          _$ArchitecturalStyleEnumMap[instance.architecturalStyle]!,
      'rating': instance.rating,
      'reviewCount': instance.reviewCount,
      'isUnescoSite': instance.isUnescoSite,
      'unescoYear': instance.unescoYear,
      'features': instance.features,
      'openingHours': instance.openingHours,
      'ticketPrice': instance.ticketPrice,
      'phone': instance.phone,
      'website': instance.website,
      'hasGuidedTours': instance.hasGuidedTours,
      'hasAudioGuide': instance.hasAudioGuide,
      'isAccessible': instance.isAccessible,
      'hasParking': instance.hasParking,
      'hasGiftShop': instance.hasGiftShop,
      'hasCafe': instance.hasCafe,
      'photos': instance.photos,
      'virtualTours': instance.virtualTours,
      'bestTimeToVisit': instance.bestTimeToVisit,
      'estimatedVisitDuration': instance.estimatedVisitDuration,
      'nearbyAttractions': instance.nearbyAttractions,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };

const _$CulturalSiteTypeEnumMap = {
  CulturalSiteType.castle: 'castle',
  CulturalSiteType.fortress: 'fortress',
  CulturalSiteType.church: 'church',
  CulturalSiteType.monastery: 'monastery',
  CulturalSiteType.museum: 'museum',
  CulturalSiteType.gallery: 'gallery',
  CulturalSiteType.palace: 'palace',
  CulturalSiteType.ruins: 'ruins',
  CulturalSiteType.monument: 'monument',
  CulturalSiteType.archaeologicalSite: 'archaeologicalSite',
  CulturalSiteType.historicTown: 'historicTown',
  CulturalSiteType.culturalCenter: 'culturalCenter',
};

const _$HistoricalPeriodEnumMap = {
  HistoricalPeriod.prehistoric: 'prehistoric',
  HistoricalPeriod.roman: 'roman',
  HistoricalPeriod.byzantine: 'byzantine',
  HistoricalPeriod.medieval: 'medieval',
  HistoricalPeriod.renaissance: 'renaissance',
  HistoricalPeriod.baroque: 'baroque',
  HistoricalPeriod.modern: 'modern',
  HistoricalPeriod.contemporary: 'contemporary',
};

const _$ArchitecturalStyleEnumMap = {
  ArchitecturalStyle.roman: 'roman',
  ArchitecturalStyle.romanesque: 'romanesque',
  ArchitecturalStyle.byzantine: 'byzantine',
  ArchitecturalStyle.gothic: 'gothic',
  ArchitecturalStyle.renaissance: 'renaissance',
  ArchitecturalStyle.baroque: 'baroque',
  ArchitecturalStyle.neoclassical: 'neoclassical',
  ArchitecturalStyle.modern: 'modern',
  ArchitecturalStyle.contemporary: 'contemporary',
  ArchitecturalStyle.vernacular: 'vernacular',
  ArchitecturalStyle.mixed: 'mixed',
};

CulturalEvent _$CulturalEventFromJson(Map<String, dynamic> json) =>
    CulturalEvent(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      location: json['location'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      eventType: $enumDecode(_$CulturalEventTypeEnumMap, json['eventType']),
      ticketPrice: json['ticketPrice'] as String?,
      website: json['website'] as String?,
      phone: json['phone'] as String?,
      photos:
          (json['photos'] as List<dynamic>).map((e) => e as String).toList(),
      isRecurring: json['isRecurring'] as bool,
      recurrencePattern: json['recurrencePattern'] as String?,
    );

Map<String, dynamic> _$CulturalEventToJson(CulturalEvent instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'location': instance.location,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'eventType': _$CulturalEventTypeEnumMap[instance.eventType]!,
      'ticketPrice': instance.ticketPrice,
      'website': instance.website,
      'phone': instance.phone,
      'photos': instance.photos,
      'isRecurring': instance.isRecurring,
      'recurrencePattern': instance.recurrencePattern,
    };

const _$CulturalEventTypeEnumMap = {
  CulturalEventType.festival: 'festival',
  CulturalEventType.concert: 'concert',
  CulturalEventType.exhibition: 'exhibition',
  CulturalEventType.theater: 'theater',
  CulturalEventType.dance: 'dance',
  CulturalEventType.folklore: 'folklore',
  CulturalEventType.gastronomy: 'gastronomy',
  CulturalEventType.crafts: 'crafts',
  CulturalEventType.religious: 'religious',
  CulturalEventType.historical: 'historical',
};

CulturalSiteReview _$CulturalSiteReviewFromJson(Map<String, dynamic> json) =>
    CulturalSiteReview(
      id: json['id'] as String,
      culturalSiteId: json['culturalSiteId'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      rating: (json['rating'] as num).toDouble(),
      comment: json['comment'] as String,
      photos:
          (json['photos'] as List<dynamic>).map((e) => e as String).toList(),
      visitDate: DateTime.parse(json['visitDate'] as String),
      likedFeatures: (json['likedFeatures'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      suggestions: (json['suggestions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      wouldRecommend: json['wouldRecommend'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$CulturalSiteReviewToJson(CulturalSiteReview instance) =>
    <String, dynamic>{
      'id': instance.id,
      'culturalSiteId': instance.culturalSiteId,
      'userId': instance.userId,
      'userName': instance.userName,
      'rating': instance.rating,
      'comment': instance.comment,
      'photos': instance.photos,
      'visitDate': instance.visitDate.toIso8601String(),
      'likedFeatures': instance.likedFeatures,
      'suggestions': instance.suggestions,
      'wouldRecommend': instance.wouldRecommend,
      'createdAt': instance.createdAt.toIso8601String(),
    };

FavoriteCulturalSite _$FavoriteCulturalSiteFromJson(
        Map<String, dynamic> json) =>
    FavoriteCulturalSite(
      id: json['id'] as String,
      userId: json['userId'] as String,
      culturalSiteId: json['culturalSiteId'] as String,
      addedAt: DateTime.parse(json['addedAt'] as String),
      notes: json['notes'] as String?,
      interests:
          (json['interests'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$FavoriteCulturalSiteToJson(
        FavoriteCulturalSite instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'culturalSiteId': instance.culturalSiteId,
      'addedAt': instance.addedAt.toIso8601String(),
      'notes': instance.notes,
      'interests': instance.interests,
    };

CulturalSiteVisit _$CulturalSiteVisitFromJson(Map<String, dynamic> json) =>
    CulturalSiteVisit(
      id: json['id'] as String,
      userId: json['userId'] as String,
      culturalSiteId: json['culturalSiteId'] as String,
      visitDate: DateTime.parse(json['visitDate'] as String),
      visitDuration: json['visitDuration'] == null
          ? null
          : Duration(microseconds: (json['visitDuration'] as num).toInt()),
      userRating: (json['userRating'] as num?)?.toDouble(),
      notes: json['notes'] as String?,
      photos:
          (json['photos'] as List<dynamic>).map((e) => e as String).toList(),
      hadGuidedTour: json['hadGuidedTour'] as bool,
      usedAudioGuide: json['usedAudioGuide'] as bool,
      visitedFeatures: (json['visitedFeatures'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$CulturalSiteVisitToJson(CulturalSiteVisit instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'culturalSiteId': instance.culturalSiteId,
      'visitDate': instance.visitDate.toIso8601String(),
      'visitDuration': instance.visitDuration?.inMicroseconds,
      'userRating': instance.userRating,
      'notes': instance.notes,
      'photos': instance.photos,
      'hadGuidedTour': instance.hadGuidedTour,
      'usedAudioGuide': instance.usedAudioGuide,
      'visitedFeatures': instance.visitedFeatures,
    };

CulturalRoute _$CulturalRouteFromJson(Map<String, dynamic> json) =>
    CulturalRoute(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      culturalSiteIds: (json['culturalSiteIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      estimatedDuration: (json['estimatedDuration'] as num).toInt(),
      difficulty: json['difficulty'] as String,
      theme: json['theme'] as String,
      rating: (json['rating'] as num).toDouble(),
      reviewCount: (json['reviewCount'] as num).toInt(),
    );

Map<String, dynamic> _$CulturalRouteToJson(CulturalRoute instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'culturalSiteIds': instance.culturalSiteIds,
      'estimatedDuration': instance.estimatedDuration,
      'difficulty': instance.difficulty,
      'theme': instance.theme,
      'rating': instance.rating,
      'reviewCount': instance.reviewCount,
    };
