import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/transport_connection.dart';
import '../services/croatia_transport_service.dart';

/// 🚌 CROATIA IDOS - Vyhledávání spojení po celém Chorvatsku
class CroatiaIdosScreen extends StatefulWidget {
  const CroatiaIdosScreen({super.key});

  @override
  State<CroatiaIdosScreen> createState() => _CroatiaIdosScreenState();
}

class _CroatiaIdosScreenState extends State<CroatiaIdosScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Controllers
  final TextEditingController _fromController = TextEditingController();
  final TextEditingController _toController = TextEditingController();

  // State
  bool _isSearching = false;
  List<TransportConnection> _searchResults = [];
  DateTime _selectedDate = DateTime.now();
  List<TransportType> _selectedTypes = [];
  String? _errorMessage;

  // Service
  final CroatiaTransportService _transportService = CroatiaTransportService();

  // Chorvatská města
  final List<String> _croatianCities = [
    'Zagreb',
    'Split',
    'Rijeka',
    'Osijek',
    'Zadar',
    'Pula',
    'Slavonski Brod',
    'Karlovac',
    'Varaždin',
    'Šibenik',
    'Sisak',
    'Vinkovci',
    'Vukovar',
    'Dubrovnik',
    'Bjelovar',
    'Koprivnica',
    'Požega',
    'Čakovec',
    'Virovitica',
    'Gospić',
    'Knin',
    'Imotski',
    'Makarska',
    'Trogir',
    'Kaštela',
    'Solin',
    'Sinj',
    'Omiš',
    'Hvar',
    'Korčula',
    'Vis',
    'Brač',
    'Krk',
    'Cres',
    'Lošinj',
    'Pag',
    'Rab',
    'Rovinj',
    'Poreč',
    'Umag',
    'Novigrad',
    'Motovun',
    'Pazin',
    'Labin',
    'Opatija',
    'Crikvenica',
    'Novi Vinodolski',
    'Senj',
    'Novalja',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _selectedTypes = [
      TransportType.bus,
      TransportType.ferry,
      TransportType.train,
    ];
  }

  @override
  void dispose() {
    _tabController.dispose();
    _fromController.dispose();
    _toController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          '🚌 Croatia IDOS',
          style: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF006994),
                const Color(0xFF2E8B8B),
                const Color(0xFF4CAF50),
              ],
            ),
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.search), text: 'Vyhledat'),
            Tab(icon: Icon(Icons.list), text: 'Výsledky'),
            Tab(icon: Icon(Icons.info), text: 'Dopravci'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [_buildSearchTab(), _buildResultsTab(), _buildOperatorsTab()],
      ),
    );
  }

  Widget _buildSearchTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF006994).withValues(alpha: 0.1),
                  const Color(0xFF2E8B8B).withValues(alpha: 0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFF006994).withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              children: [
                Text(
                  '🇭🇷 Vyhledávání spojení po Chorvatsku',
                  style: GoogleFonts.playfairDisplay(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF006994),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  'Najděte spojení autobusy, vlaky a trajekty mezi všemi chorvatskými městy',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    color: const Color(0xFF666666),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Vyhledávací formulář
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Odkud
                Text(
                  'Odkud',
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF2C2C2C),
                  ),
                ),
                const SizedBox(height: 8),
                _buildCityAutocomplete(
                  _fromController,
                  'Vyberte výchozí město',
                ),

                const SizedBox(height: 20),

                // Kam
                Text(
                  'Kam',
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF2C2C2C),
                  ),
                ),
                const SizedBox(height: 8),
                _buildCityAutocomplete(_toController, 'Vyberte cílové město'),

                const SizedBox(height: 20),

                // Datum
                Text(
                  'Datum cesty',
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF2C2C2C),
                  ),
                ),
                const SizedBox(height: 8),
                InkWell(
                  onTap: _selectDate,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          color: const Color(0xFF006994),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          '${_selectedDate.day}.${_selectedDate.month}.${_selectedDate.year}',
                          style: GoogleFonts.inter(fontSize: 16),
                        ),
                        const Spacer(),
                        Icon(
                          Icons.arrow_drop_down,
                          color: Colors.grey.shade600,
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 20),

                // Typy dopravy
                Text(
                  'Typy dopravy',
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF2C2C2C),
                  ),
                ),
                const SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: TransportType.values.map((type) {
                    final isSelected = _selectedTypes.contains(type);
                    return FilterChip(
                      label: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(type.icon),
                          const SizedBox(width: 4),
                          Text(type.displayNameHr),
                        ],
                      ),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          if (selected) {
                            _selectedTypes.add(type);
                          } else {
                            _selectedTypes.remove(type);
                          }
                        });
                      },
                      backgroundColor: Colors.grey.shade100,
                      selectedColor: Color(
                        type.colorValue,
                      ).withValues(alpha: 0.2),
                      checkmarkColor: Color(type.colorValue),
                    );
                  }).toList(),
                ),

                const SizedBox(height: 24),

                // Vyhledat tlačítko
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isSearching ? null : _searchConnections,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF006994),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: _isSearching
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'Vyhledávám...',
                                style: GoogleFonts.inter(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          )
                        : Text(
                            '🔍 Vyhledat spojení',
                            style: GoogleFonts.inter(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Rychlé spojení
          _buildQuickConnections(),
        ],
      ),
    );
  }

  Widget _buildCityAutocomplete(TextEditingController controller, String hint) {
    return Autocomplete<String>(
      optionsBuilder: (TextEditingValue textEditingValue) {
        if (textEditingValue.text.isEmpty) {
          return const Iterable<String>.empty();
        }
        return _croatianCities.where(
          (city) =>
              city.toLowerCase().contains(textEditingValue.text.toLowerCase()),
        );
      },
      onSelected: (String selection) {
        controller.text = selection;
      },
      fieldViewBuilder: (context, controller, focusNode, onEditingComplete) {
        return TextField(
          controller: controller,
          focusNode: focusNode,
          onEditingComplete: onEditingComplete,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(Icons.location_on, color: const Color(0xFF006994)),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: const Color(0xFF006994)),
            ),
          ),
        );
      },
      optionsViewBuilder: (context, onSelected, options) {
        return Align(
          alignment: Alignment.topLeft,
          child: Material(
            elevation: 4,
            borderRadius: BorderRadius.circular(12),
            child: Container(
              constraints: const BoxConstraints(maxHeight: 200),
              width: MediaQuery.of(context).size.width - 32,
              child: ListView.builder(
                padding: EdgeInsets.zero,
                itemCount: options.length,
                itemBuilder: (context, index) {
                  final option = options.elementAt(index);
                  return ListTile(
                    leading: Text('🏙️'),
                    title: Text(option),
                    onTap: () => onSelected(option),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuickConnections() {
    final quickRoutes = [
      {'from': 'Zagreb', 'to': 'Split', 'popular': true},
      {'from': 'Split', 'to': 'Dubrovnik', 'popular': true},
      {'from': 'Zagreb', 'to': 'Rijeka', 'popular': false},
      {'from': 'Rijeka', 'to': 'Pag', 'popular': false},
      {'from': 'Zagreb', 'to': 'Pula', 'popular': false},
      {'from': 'Split', 'to': 'Hvar', 'popular': true},
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '⚡ Rychlé spojení',
            style: GoogleFonts.playfairDisplay(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF006994),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Nejpopulárnější trasy v Chorvatsku',
            style: GoogleFonts.inter(
              fontSize: 14,
              color: const Color(0xFF666666),
            ),
          ),
          const SizedBox(height: 16),
          ...quickRoutes.map(
            (route) => Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: InkWell(
                onTap: () {
                  _fromController.text = route['from'] as String;
                  _toController.text = route['to'] as String;
                },
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: (route['popular'] as bool)
                          ? const Color(0xFF006994).withValues(alpha: 0.3)
                          : Colors.grey.shade300,
                    ),
                    borderRadius: BorderRadius.circular(8),
                    color: (route['popular'] as bool)
                        ? const Color(0xFF006994).withValues(alpha: 0.05)
                        : null,
                  ),
                  child: Row(
                    children: [
                      Text(
                        (route['popular'] as bool) ? '🔥' : '🚌',
                        style: const TextStyle(fontSize: 16),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        '${route['from']} → ${route['to']}',
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF2C2C2C),
                        ),
                      ),
                      const Spacer(),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 14,
                        color: Colors.grey.shade600,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultsTab() {
    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (_searchResults.isEmpty) {
      return _buildEmptyResultsState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        return _buildConnectionCard(_searchResults[index]);
      },
    );
  }

  Widget _buildEmptyResultsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('🔍', style: const TextStyle(fontSize: 64)),
          const SizedBox(height: 16),
          Text(
            'Vyhledejte spojení',
            style: GoogleFonts.playfairDisplay(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF666666),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Zadejte výchozí a cílové město pro vyhledání spojení',
            style: GoogleFonts.inter(
              fontSize: 14,
              color: const Color(0xFF666666),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('❌', style: const TextStyle(fontSize: 64)),
          const SizedBox(height: 16),
          Text(
            'Chyba při vyhledávání',
            style: GoogleFonts.playfairDisplay(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage!,
            style: GoogleFonts.inter(
              fontSize: 14,
              color: const Color(0xFF666666),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _errorMessage = null;
              });
            },
            child: Text('Zkusit znovu'),
          ),
        ],
      ),
    );
  }

  Widget _buildOperatorsTab() {
    final operators = [
      {
        'name': 'Arriva Croatia',
        'website': 'https://arriva.com.hr',
        'phone': '+385 72 660 660',
        'types': ['🚌 Autobusy'],
        'regions': ['Zagreb', 'Varaždin', 'Koprivnica'],
        'logo': '🚌',
      },
      {
        'name': 'Autotrans',
        'website': 'https://www.autotrans.hr',
        'phone': '+385 21 338 347',
        'types': ['🚌 Autobusy'],
        'regions': ['Split', 'Makarska', 'Dubrovnik'],
        'logo': '🚌',
      },
      {
        'name': 'Jadrolinija',
        'website': 'https://www.jadrolinija.hr',
        'phone': '+385 51 666 111',
        'types': ['⛴️ Trajekty', '🚤 Katamarany'],
        'regions': ['Všechny ostrovy', 'Jadranské pobřeží'],
        'logo': '⛴️',
      },
      {
        'name': 'Hrvatske željeznice (HŽ)',
        'website': 'https://www.hzpp.hr',
        'phone': '+385 1 3782 583',
        'types': ['🚂 Vlaky'],
        'regions': ['Celé Chorvatsko'],
        'logo': '🚂',
      },
      {
        'name': 'Croatia Airlines',
        'website': 'https://www.croatiaairlines.com',
        'phone': '+385 1 6676 555',
        'types': ['✈️ Letadla'],
        'regions': ['Domácí lety'],
        'logo': '✈️',
      },
    ];

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: operators.length,
      itemBuilder: (context, index) {
        final operator = operators[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: const Color(0xFF006994).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Center(
                        child: Text(
                          operator['logo'] as String,
                          style: const TextStyle(fontSize: 24),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            operator['name'] as String,
                            style: GoogleFonts.playfairDisplay(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF2C2C2C),
                            ),
                          ),
                          Text(
                            (operator['types'] as List<String>).join(', '),
                            style: GoogleFonts.inter(
                              fontSize: 12,
                              color: const Color(0xFF006994),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Kontaktní informace
                _buildOperatorInfo(Icons.phone, operator['phone'] as String),
                _buildOperatorInfo(Icons.web, operator['website'] as String),
                _buildOperatorInfo(
                  Icons.location_on,
                  'Regiony: ${(operator['regions'] as List<String>).join(', ')}',
                ),

                const SizedBox(height: 16),

                // Akční tlačítka
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () =>
                            _callOperator(operator['phone'] as String),
                        icon: Icon(Icons.phone),
                        label: Text('Zavolat'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: const Color(0xFF006994),
                          side: BorderSide(color: const Color(0xFF006994)),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () =>
                            _openWebsite(operator['website'] as String),
                        icon: Icon(Icons.web),
                        label: Text('Web'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF006994),
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildConnectionCard(TransportConnection connection) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header s typem dopravy
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Color(connection.type.colorValue).withValues(alpha: 0.1),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Color(connection.type.colorValue),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Text(
                      connection.type.icon,
                      style: const TextStyle(fontSize: 20),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        connection.operatorName,
                        style: GoogleFonts.inter(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF2C2C2C),
                        ),
                      ),
                      Text(
                        connection.type.displayNameHr,
                        style: GoogleFonts.inter(
                          fontSize: 12,
                          color: Color(connection.type.colorValue),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                if (connection.price != null)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Color(connection.type.colorValue),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${connection.price!.toStringAsFixed(0)} ${connection.currency}',
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // Detaily spojení
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Trasa
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            connection.fromCity,
                            style: GoogleFonts.playfairDisplay(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF2C2C2C),
                            ),
                          ),
                          Text(
                            connection.fromStation,
                            style: GoogleFonts.inter(
                              fontSize: 12,
                              color: const Color(0xFF666666),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Column(
                        children: [
                          Icon(
                            Icons.arrow_forward,
                            color: Color(connection.type.colorValue),
                            size: 24,
                          ),
                          Text(
                            _formatDuration(connection.duration),
                            style: GoogleFonts.inter(
                              fontSize: 12,
                              color: const Color(0xFF666666),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            connection.toCity,
                            style: GoogleFonts.playfairDisplay(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF2C2C2C),
                            ),
                          ),
                          Text(
                            connection.toStation,
                            style: GoogleFonts.inter(
                              fontSize: 12,
                              color: const Color(0xFF666666),
                            ),
                            textAlign: TextAlign.end,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Akční tlačítka
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _showConnectionDetails(connection),
                        icon: Icon(Icons.info_outline),
                        label: Text('Detaily'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Color(connection.type.colorValue),
                          side: BorderSide(
                            color: Color(connection.type.colorValue),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _buyTicket(connection),
                        icon: Icon(Icons.shopping_cart),
                        label: Text('Koupit jízdenku'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Color(connection.type.colorValue),
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOperatorInfo(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 16, color: const Color(0xFF666666)),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: GoogleFonts.inter(
                fontSize: 14,
                color: const Color(0xFF666666),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Akční metody
  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _searchConnections() async {
    if (_fromController.text.isEmpty || _toController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Vyplňte výchozí a cílové město')),
      );
      return;
    }

    setState(() {
      _isSearching = true;
      _errorMessage = null;
    });

    try {
      final results = await _transportService.searchConnections(
        from: _fromController.text,
        to: _toController.text,
        date: _selectedDate,
        transportTypes: _selectedTypes,
      );

      setState(() {
        _searchResults = results;
        _isSearching = false;
      });

      // Přepnout na tab s výsledky
      _tabController.animateTo(1);
    } catch (e) {
      setState(() {
        _errorMessage = 'Chyba při vyhledávání spojení: $e';
        _isSearching = false;
      });
    }
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    if (hours > 0) {
      return '${hours}h ${minutes}min';
    } else {
      return '${minutes}min';
    }
  }

  void _showConnectionDetails(TransportConnection connection) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Color(connection.type.colorValue).withValues(alpha: 0.1),
                borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
              ),
              child: Column(
                children: [
                  Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Detaily spojení',
                    style: GoogleFonts.playfairDisplay(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF2C2C2C),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${connection.fromCity} → ${connection.toCity}',
                      style: GoogleFonts.playfairDisplay(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Dopravce: ${connection.operatorName}',
                      style: GoogleFonts.inter(fontSize: 14),
                    ),
                    Text(
                      'Typ: ${connection.type.displayNameHr}',
                      style: GoogleFonts.inter(fontSize: 14),
                    ),
                    if (connection.distance != null)
                      Text(
                        'Vzdálenost: ${connection.distance!.toStringAsFixed(0)} km',
                        style: GoogleFonts.inter(fontSize: 14),
                      ),
                    const SizedBox(height: 16),
                    if (connection.notes != null) ...[
                      Text(
                        'Poznámky:',
                        style: GoogleFonts.inter(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        connection.notes!,
                        style: GoogleFonts.inter(fontSize: 14),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _buyTicket(TransportConnection connection) async {
    final url = Uri.parse(connection.operatorWebsite);
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Nelze otevřít web dopravce: ${connection.operatorWebsite}',
          ),
        ),
      );
    }
  }

  void _callOperator(String phone) async {
    final url = Uri(scheme: 'tel', path: phone);
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    }
  }

  void _openWebsite(String website) async {
    final url = Uri.parse(website);
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }
}
