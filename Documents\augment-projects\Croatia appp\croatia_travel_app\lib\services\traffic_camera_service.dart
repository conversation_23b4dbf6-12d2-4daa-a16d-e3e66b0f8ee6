import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import '../models/traffic_camera.dart';

/// 📹 TRAFFIC CAMERA SERVICE - Dopravní kamery a monitoring
class TrafficCameraService {
  static final TrafficCameraService _instance =
      TrafficCameraService._internal();
  factory TrafficCameraService() => _instance;
  TrafficCameraService._internal();

  bool _isInitialized = false;
  final List<TrafficCamera> _cameras = [];
  final List<ParkingCamera> _parkingCameras = [];
  final Map<String, TrafficStatus> _trafficStatusCache = {};
  DateTime? _lastCacheUpdate;

  // Konfigurace
  static const Duration _cacheValidDuration = Duration(minutes: 5);

  /// Inicializace služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('📹 Inicializuji Traffic Camera Service...');

      await _loadTrafficCameras();
      await _loadParkingCameras();

      _isInitialized = true;
      debugPrint('✅ Traffic Camera Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Traffic Camera: $e');
      await _loadMockData();
      _isInitialized = true;
    }
  }

  /// Získání dopravních kamer v okolí
  Future<List<TrafficCamera>> getNearbyTrafficCameras({
    required double latitude,
    required double longitude,
    double radiusKm = 10.0,
    CameraType? type,
  }) async {
    await _ensureInitialized();

    var cameras = _cameras.where((camera) {
      // Filtrování podle typu
      if (type != null && camera.type != type) return false;

      // Filtrování podle vzdálenosti
      final distance =
          Geolocator.distanceBetween(
            latitude,
            longitude,
            camera.latitude,
            camera.longitude,
          ) /
          1000; // převod na km

      return distance <= radiusKm;
    }).toList();

    // Seřazení podle vzdálenosti
    cameras.sort((a, b) {
      final distanceA = Geolocator.distanceBetween(
        latitude,
        longitude,
        a.latitude,
        a.longitude,
      );
      final distanceB = Geolocator.distanceBetween(
        latitude,
        longitude,
        b.latitude,
        b.longitude,
      );
      return distanceA.compareTo(distanceB);
    });

    return cameras;
  }

  /// Získání parkovacích kamer
  Future<List<ParkingCamera>> getParkingCameras({
    required double latitude,
    required double longitude,
    double radiusKm = 5.0,
  }) async {
    await _ensureInitialized();

    var cameras = _parkingCameras.where((camera) {
      final distance =
          Geolocator.distanceBetween(
            latitude,
            longitude,
            camera.latitude,
            camera.longitude,
          ) /
          1000;

      return distance <= radiusKm;
    }).toList();

    // Seřazení podle vzdálenosti
    cameras.sort((a, b) {
      final distanceA = Geolocator.distanceBetween(
        latitude,
        longitude,
        a.latitude,
        a.longitude,
      );
      final distanceB = Geolocator.distanceBetween(
        latitude,
        longitude,
        b.latitude,
        b.longitude,
      );
      return distanceA.compareTo(distanceB);
    });

    return cameras;
  }

  /// LEGAL COMPLIANCE: Odstraněno - porušení soukromí a copyright
  /// Místo přímého přístupu k kamerám používáme agregované dopravní informace
  @Deprecated('Removed for legal compliance - use getTrafficInfo instead')
  Future<String?> getCameraStreamUrl(String cameraId) async {
    debugPrint(
      '⚖️ LEGAL NOTICE: Camera streaming removed for privacy compliance',
    );
    debugPrint('📊 Use getTrafficInfo() for aggregated traffic data instead');
    return null;
  }

  /// Získání aktuálního dopravního stavu
  Future<TrafficStatus> getCurrentTrafficStatus({
    required double latitude,
    required double longitude,
    double radiusKm = 20.0,
  }) async {
    final cacheKey = '${latitude}_${longitude}_$radiusKm';

    // Kontrola cache
    if (_isTrafficCacheValid(cacheKey)) {
      return _trafficStatusCache[cacheKey]!;
    }

    try {
      // Získání kamer v okolí
      final cameras = await getNearbyTrafficCameras(
        latitude: latitude,
        longitude: longitude,
        radiusKm: radiusKm,
      );

      // Analýza dopravní situace na základě kamer
      final status = await _analyzeTrafficFromCameras(cameras);

      // Uložení do cache
      _trafficStatusCache[cacheKey] = status;
      _lastCacheUpdate = DateTime.now();

      return status;
    } catch (e) {
      debugPrint('❌ Chyba při získávání dopravního stavu: $e');
      return _createFallbackTrafficStatus();
    }
  }

  /// LEGAL COMPLIANCE: Odstraněno - porušení soukromí a copyright
  @Deprecated('Removed for legal compliance - use getTrafficInfo instead')
  Future<String?> getCameraSnapshot(String cameraId) async {
    debugPrint(
      '⚖️ LEGAL NOTICE: Camera snapshots removed for privacy compliance',
    );
    return null;
  }

  /// LEGAL COMPLIANCE: Odstraněno - porušení soukromí a copyright
  @Deprecated('Removed for legal compliance - use getTrafficInfo instead')
  Future<List<CameraSnapshot>> getCameraHistory(
    String cameraId, {
    DateTime? from,
    DateTime? to,
    int limit = 24,
  }) async {
    debugPrint(
      '⚖️ LEGAL NOTICE: Camera history removed for privacy compliance',
    );
    return [];
  }

  /// Sledování kamery v reálném čase
  Stream<CameraUpdate> watchCamera(String cameraId) async* {
    while (true) {
      try {
        final snapshot = await getCameraSnapshot(cameraId);
        if (snapshot != null) {
          yield CameraUpdate(
            cameraId: cameraId,
            imageUrl: snapshot,
            timestamp: DateTime.now(),
            trafficDensity: _generateRandomTrafficDensity(),
          );
        }

        // Aktualizace každých 30 sekund
        await Future.delayed(const Duration(seconds: 30));
      } catch (e) {
        debugPrint('❌ Chyba při sledování kamery: $e');
        await Future.delayed(const Duration(seconds: 60));
      }
    }
  }

  /// Analýza dopravy z kamer pomocí AI
  Future<TrafficStatus> _analyzeTrafficFromCameras(
    List<TrafficCamera> cameras,
  ) async {
    if (cameras.isEmpty) {
      return _createFallbackTrafficStatus();
    }

    // Simulace AI analýzy dopravní situace
    final random = Random();
    final congestionLevels = cameras.map((camera) {
      return random.nextDouble(); // 0.0 - 1.0 (žádná - vysoká zácpa)
    }).toList();

    final averageCongestion =
        congestionLevels.reduce((a, b) => a + b) / congestionLevels.length;

    TrafficLevel level;
    if (averageCongestion < 0.3) {
      level = TrafficLevel.light;
    } else if (averageCongestion < 0.7) {
      level = TrafficLevel.moderate;
    } else {
      level = TrafficLevel.heavy;
    }

    return TrafficStatus(
      level: level,
      averageSpeed: _calculateAverageSpeed(level),
      congestionPercentage: (averageCongestion * 100).round(),
      incidents: _generateRandomIncidents(),
      lastUpdated: DateTime.now(),
      affectedRoutes: _generateAffectedRoutes(level),
    );
  }

  /// Načtení dopravních kamer
  Future<void> _loadTrafficCameras() async {
    // V produkci by zde bylo volání na API
    // Pro demo načítáme mock data
    await _loadMockData();
  }

  /// Načtení parkovacích kamer
  Future<void> _loadParkingCameras() async {
    // Mock data pro parkovací kamery
    _parkingCameras.addAll([
      ParkingCamera(
        id: 'park_cam_1',
        name: 'Parking Centar - Kamera 1',
        latitude: 45.815,
        longitude: 15.982,
        parkingLotId: 'parking_centar_1',
        totalSpots: 150,
        occupiedSpots: 89,
        isActive: true,
        lastUpdated: DateTime.now(),
      ),
      ParkingCamera(
        id: 'park_cam_2',
        name: 'Parking Tkalčićeva - Kamera 1',
        latitude: 45.814,
        longitude: 15.978,
        parkingLotId: 'parking_tkalciceva',
        totalSpots: 80,
        occupiedSpots: 65,
        isActive: true,
        lastUpdated: DateTime.now(),
      ),
    ]);
  }

  /// Načtení mock dat
  Future<void> _loadMockData() async {
    _cameras.addAll([
      TrafficCamera(
        id: 'cam_001',
        name: 'Zagreb - Slavonska avenija',
        latitude: 45.815,
        longitude: 15.982,
        type: CameraType.traffic,
        direction: 'Sever-Jih',
        isActive: true,
        hasLiveStream: true,
        quality: CameraQuality.hd,
        lastUpdated: DateTime.now(),
      ),
      TrafficCamera(
        id: 'cam_002',
        name: 'Split - Domovinskog rata',
        latitude: 43.508,
        longitude: 16.440,
        type: CameraType.highway,
        direction: 'Východ-Západ',
        isActive: true,
        hasLiveStream: true,
        quality: CameraQuality.fullHd,
        lastUpdated: DateTime.now(),
      ),
      TrafficCamera(
        id: 'cam_003',
        name: 'Dubrovník - Stradun',
        latitude: 42.641,
        longitude: 18.108,
        type: CameraType.city,
        direction: 'Panorama',
        isActive: true,
        hasLiveStream: false,
        quality: CameraQuality.hd,
        lastUpdated: DateTime.now(),
      ),
    ]);
  }

  /// Pomocné metody
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  bool _isTrafficCacheValid(String cacheKey) {
    return _trafficStatusCache.containsKey(cacheKey) &&
        _lastCacheUpdate != null &&
        DateTime.now().difference(_lastCacheUpdate!) < _cacheValidDuration;
  }

  TrafficStatus _createFallbackTrafficStatus() {
    return TrafficStatus(
      level: TrafficLevel.moderate,
      averageSpeed: 45,
      congestionPercentage: 35,
      incidents: [],
      lastUpdated: DateTime.now(),
      affectedRoutes: [],
    );
  }

  int _calculateAverageSpeed(TrafficLevel level) {
    switch (level) {
      case TrafficLevel.light:
        return 60 + Random().nextInt(20); // 60-80 km/h
      case TrafficLevel.moderate:
        return 30 + Random().nextInt(30); // 30-60 km/h
      case TrafficLevel.heavy:
        return 10 + Random().nextInt(20); // 10-30 km/h
    }
  }

  List<TrafficIncident> _generateRandomIncidents() {
    final random = Random();
    final incidents = <TrafficIncident>[];

    if (random.nextBool()) {
      incidents.add(
        TrafficIncident(
          id: 'incident_${DateTime.now().millisecondsSinceEpoch}',
          type: IncidentType.values[random.nextInt(IncidentType.values.length)],
          description: 'Dopravní incident na hlavní trase',
          latitude: 45.815 + (random.nextDouble() - 0.5) * 0.1,
          longitude: 15.982 + (random.nextDouble() - 0.5) * 0.1,
          severity: IncidentSeverity
              .values[random.nextInt(IncidentSeverity.values.length)],
          reportedAt: DateTime.now().subtract(
            Duration(minutes: random.nextInt(60)),
          ),
        ),
      );
    }

    return incidents;
  }

  List<String> _generateAffectedRoutes(TrafficLevel level) {
    if (level == TrafficLevel.light) return [];

    final routes = ['A1', 'A3', 'D1', 'Slavonska avenija'];
    final affectedCount = level == TrafficLevel.heavy ? 3 : 1;

    return routes.take(affectedCount).toList();
  }

  TrafficDensity _generateRandomTrafficDensity() {
    final values = TrafficDensity.values;
    return values[Random().nextInt(values.length)];
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  List<TrafficCamera> get allCameras => List.unmodifiable(_cameras);
  List<ParkingCamera> get allParkingCameras =>
      List.unmodifiable(_parkingCameras);
}
