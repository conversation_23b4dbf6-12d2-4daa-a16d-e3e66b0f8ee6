import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

/// 💳 STRIPE PAYMENT SERVICE - Secure payment processing
class StripePaymentService {
  static final StripePaymentService _instance =
      StripePaymentService._internal();
  factory StripePaymentService() => _instance;
  StripePaymentService._internal();

  bool _isInitialized = false;
  final StreamController<PaymentEvent> _eventController =
      StreamController.broadcast();

  // Stripe configuration
  static const String _publishableKey =
      'pk_test_YOUR_PUBLISHABLE_KEY_HERE'; // REPLACE WITH YOUR KEY
  static const String _secretKey =
      'sk_test_YOUR_SECRET_KEY_HERE'; // REPLACE WITH YOUR KEY
  static const String _baseUrl = 'https://api.stripe.com/v1';

  /// Stream payment událostí
  Stream<PaymentEvent> get paymentEvents => _eventController.stream;

  /// Inicializace Stripe
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('💳 Inicializuji Stripe Payment Service...');

      // Inicializace Stripe SDK
      Stripe.publishableKey = _publishableKey;
      await Stripe.instance.applySettings();

      _isInitialized = true;
      debugPrint('✅ Stripe Payment Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Stripe: $e');
      rethrow;
    }
  }

  /// Vytvoření Payment Intent na serveru
  Future<Map<String, dynamic>> createPaymentIntent({
    required double amount,
    required String currency,
    String? customerId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/payment_intents'),
        headers: {
          'Authorization': 'Bearer $_secretKey',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'amount': (amount * 100).round().toString(), // Stripe uses cents
          'currency': currency.toLowerCase(),
          'automatic_payment_methods[enabled]': 'true',
          if (customerId != null) 'customer': customerId,
          if (metadata != null)
            ...metadata.map((k, v) => MapEntry('metadata[$k]', v.toString())),
        },
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to create payment intent: ${response.body}');
      }
    } catch (e) {
      debugPrint('❌ Chyba při vytváření Payment Intent: $e');
      rethrow;
    }
  }

  /// Zpracování platby kartou
  Future<PaymentResult> processCardPayment({
    required double amount,
    required String currency,
    required String description,
    String? customerId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      debugPrint('💳 Zpracovávám platbu kartou: $amount $currency');

      // 1. Vytvoření Payment Intent
      final paymentIntent = await createPaymentIntent(
        amount: amount,
        currency: currency,
        customerId: customerId,
        metadata: metadata,
      );

      // 2. Inicializace payment sheet
      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: paymentIntent['client_secret'],
          merchantDisplayName: 'Croatia Travel App',
          style: ThemeMode.system,
          billingDetails: const BillingDetails(name: 'Croatia Travel User'),
        ),
      );

      // 3. Zobrazení payment sheet
      await Stripe.instance.presentPaymentSheet();

      // 4. Platba úspěšná
      final result = PaymentResult(
        success: true,
        paymentIntentId: paymentIntent['id'],
        amount: amount,
        currency: currency,
        description: description,
        timestamp: DateTime.now(),
        paymentMethod: 'card',
      );

      _eventController.add(
        PaymentEvent(
          type: PaymentEventType.paymentSucceeded,
          paymentId: paymentIntent['id'],
          amount: amount,
          timestamp: DateTime.now(),
        ),
      );

      debugPrint('✅ Platba kartou úspěšná: ${paymentIntent['id']}');
      return result;
    } on StripeException catch (e) {
      debugPrint('❌ Stripe chyba: ${e.error.localizedMessage}');

      final result = PaymentResult(
        success: false,
        error: e.error.localizedMessage ?? 'Payment failed',
        timestamp: DateTime.now(),
      );

      _eventController.add(
        PaymentEvent(
          type: PaymentEventType.paymentFailed,
          error: e.error.localizedMessage,
          timestamp: DateTime.now(),
        ),
      );

      return result;
    } catch (e) {
      debugPrint('❌ Obecná chyba při platbě: $e');

      final result = PaymentResult(
        success: false,
        error: 'Payment processing failed',
        timestamp: DateTime.now(),
      );

      return result;
    }
  }

  /// Zpracování Google Pay platby
  Future<PaymentResult> processGooglePayPayment({
    required double amount,
    required String currency,
    required String description,
  }) async {
    try {
      debugPrint('📱 Zpracovávám Google Pay platbu: $amount $currency');

      // 1. Kontrola dostupnosti Google Pay
      final isGooglePaySupported = await Stripe.instance
          .isPlatformPaySupported();

      if (!isGooglePaySupported) {
        throw Exception('Google Pay není podporován na tomto zařízení');
      }

      // 2. Vytvoření Payment Intent
      final paymentIntent = await createPaymentIntent(
        amount: amount,
        currency: currency,
      );

      // 3. Zpracování Google Pay platby
      await Stripe.instance.confirmPlatformPayPaymentIntent(
        clientSecret: paymentIntent['client_secret'],
        confirmParams: PlatformPayConfirmParams.googlePay(
          googlePay: GooglePayParams(
            testEnv: true,
            merchantName: 'Croatia Travel App',
            merchantCountryCode: 'HR',
            currencyCode: currency,
          ),
        ),
      );

      final result = PaymentResult(
        success: true,
        paymentIntentId: paymentIntent['id'],
        amount: amount,
        currency: currency,
        description: description,
        timestamp: DateTime.now(),
        paymentMethod: 'google_pay',
      );

      _eventController.add(
        PaymentEvent(
          type: PaymentEventType.paymentSucceeded,
          paymentId: paymentIntent['id'],
          amount: amount,
          timestamp: DateTime.now(),
        ),
      );

      debugPrint('✅ Google Pay platba úspěšná: ${paymentIntent['id']}');
      return result;
    } catch (e) {
      debugPrint('❌ Chyba při Google Pay platbě: $e');

      final result = PaymentResult(
        success: false,
        error: e.toString(),
        timestamp: DateTime.now(),
      );

      return result;
    }
  }

  /// Zpracování Apple Pay platby
  Future<PaymentResult> processApplePayPayment({
    required double amount,
    required String currency,
    required String description,
  }) async {
    try {
      debugPrint('🍎 Zpracovávám Apple Pay platbu: $amount $currency');

      // 1. Kontrola dostupnosti Apple Pay
      final isApplePaySupported = await Stripe.instance.isApplePaySupported();

      if (!isApplePaySupported) {
        throw Exception('Apple Pay není podporován na tomto zařízení');
      }

      // 2. Vytvoření Payment Intent
      final paymentIntent = await createPaymentIntent(
        amount: amount,
        currency: currency,
      );

      // 3. Prezentace Apple Pay
      await Stripe.instance.presentApplePay(
        ApplePayPresentParams(
          cartItems: [
            ApplePayCartSummaryItem.immediate(
              label: description,
              amount: amount.toString(),
            ),
          ],
          country: 'HR',
          currency: currency,
        ),
      );

      // 4. Potvrzení platby
      await Stripe.instance.confirmApplePayPayment(
        paymentIntent['client_secret'],
      );

      final result = PaymentResult(
        success: true,
        paymentIntentId: paymentIntent['id'],
        amount: amount,
        currency: currency,
        description: description,
        timestamp: DateTime.now(),
        paymentMethod: 'apple_pay',
      );

      _eventController.add(
        PaymentEvent(
          type: PaymentEventType.paymentSucceeded,
          paymentId: paymentIntent['id'],
          amount: amount,
          timestamp: DateTime.now(),
        ),
      );

      debugPrint('✅ Apple Pay platba úspěšná: ${paymentIntent['id']}');
      return result;
    } catch (e) {
      debugPrint('❌ Chyba při Apple Pay platbě: $e');

      final result = PaymentResult(
        success: false,
        error: e.toString(),
        timestamp: DateTime.now(),
      );

      return result;
    }
  }

  /// Vytvoření zákazníka v Stripe
  Future<String?> createCustomer({
    required String email,
    String? name,
    String? phone,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/customers'),
        headers: {
          'Authorization': 'Bearer $_secretKey',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'email': email,
          if (name != null) 'name': name,
          if (phone != null) 'phone': phone,
          if (metadata != null)
            ...metadata.map((k, v) => MapEntry('metadata[$k]', v.toString())),
        },
      );

      if (response.statusCode == 200) {
        final customer = jsonDecode(response.body);
        return customer['id'];
      } else {
        throw Exception('Failed to create customer: ${response.body}');
      }
    } catch (e) {
      debugPrint('❌ Chyba při vytváření zákazníka: $e');
      return null;
    }
  }

  /// Uložení platební metody
  Future<bool> savePaymentMethod({
    required String customerId,
    required String paymentMethodId,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/payment_methods/$paymentMethodId/attach'),
        headers: {
          'Authorization': 'Bearer $_secretKey',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {'customer': customerId},
      );

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('❌ Chyba při ukládání platební metody: $e');
      return false;
    }
  }

  /// Získání uložených platebních metod
  Future<List<PaymentMethodInfo>> getSavedPaymentMethods(
    String customerId,
  ) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/payment_methods?customer=$customerId&type=card'),
        headers: {'Authorization': 'Bearer $_secretKey'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final paymentMethods = data['data'] as List;

        return paymentMethods
            .map(
              (pm) => PaymentMethodInfo(
                id: pm['id'],
                type: pm['type'],
                cardBrand: pm['card']?['brand'],
                cardLast4: pm['card']?['last4'],
                cardExpMonth: pm['card']?['exp_month'],
                cardExpYear: pm['card']?['exp_year'],
              ),
            )
            .toList();
      } else {
        throw Exception('Failed to get payment methods: ${response.body}');
      }
    } catch (e) {
      debugPrint('❌ Chyba při získávání platebních metod: $e');
      return [];
    }
  }

  /// Refund platby
  Future<bool> refundPayment({
    required String paymentIntentId,
    double? amount,
    String? reason,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/refunds'),
        headers: {
          'Authorization': 'Bearer $_secretKey',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'payment_intent': paymentIntentId,
          if (amount != null) 'amount': (amount * 100).round().toString(),
          if (reason != null) 'reason': reason,
        },
      );

      if (response.statusCode == 200) {
        _eventController.add(
          PaymentEvent(
            type: PaymentEventType.paymentRefunded,
            paymentId: paymentIntentId,
            amount: amount,
            timestamp: DateTime.now(),
          ),
        );
        return true;
      } else {
        throw Exception('Failed to refund payment: ${response.body}');
      }
    } catch (e) {
      debugPrint('❌ Chyba při refund: $e');
      return false;
    }
  }

  /// Získání detailů platby
  Future<PaymentDetails?> getPaymentDetails(String paymentIntentId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/payment_intents/$paymentIntentId'),
        headers: {'Authorization': 'Bearer $_secretKey'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return PaymentDetails.fromJson(data);
      } else {
        throw Exception('Failed to get payment details: ${response.body}');
      }
    } catch (e) {
      debugPrint('❌ Chyba při získávání detailů platby: $e');
      return null;
    }
  }

  @override
  void dispose() {
    _eventController.close();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
}

/// Payment Result Model
class PaymentResult {
  final bool success;
  final String? paymentIntentId;
  final double? amount;
  final String? currency;
  final String? description;
  final String? error;
  final DateTime timestamp;
  final String? paymentMethod;

  PaymentResult({
    required this.success,
    this.paymentIntentId,
    this.amount,
    this.currency,
    this.description,
    this.error,
    required this.timestamp,
    this.paymentMethod,
  });
}

/// Payment Event Model
class PaymentEvent {
  final PaymentEventType type;
  final String? paymentId;
  final double? amount;
  final String? error;
  final DateTime timestamp;

  PaymentEvent({
    required this.type,
    this.paymentId,
    this.amount,
    this.error,
    required this.timestamp,
  });
}

enum PaymentEventType { paymentSucceeded, paymentFailed, paymentRefunded }

/// Payment Method Info Model
class PaymentMethodInfo {
  final String id;
  final String type;
  final String? cardBrand;
  final String? cardLast4;
  final int? cardExpMonth;
  final int? cardExpYear;

  PaymentMethodInfo({
    required this.id,
    required this.type,
    this.cardBrand,
    this.cardLast4,
    this.cardExpMonth,
    this.cardExpYear,
  });
}

/// Payment Details Model
class PaymentDetails {
  final String id;
  final double amount;
  final String currency;
  final String status;
  final DateTime created;
  final String? description;

  PaymentDetails({
    required this.id,
    required this.amount,
    required this.currency,
    required this.status,
    required this.created,
    this.description,
  });

  factory PaymentDetails.fromJson(Map<String, dynamic> json) {
    return PaymentDetails(
      id: json['id'],
      amount: (json['amount'] as int) / 100.0,
      currency: json['currency'],
      status: json['status'],
      created: DateTime.fromMillisecondsSinceEpoch(json['created'] * 1000),
      description: json['description'],
    );
  }
}
