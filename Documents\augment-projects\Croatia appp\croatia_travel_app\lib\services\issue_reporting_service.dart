import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import '../models/smart_city_models.dart';

/// Služba pro hlášení problémů ve městě
class IssueReportingService {
  static final IssueReportingService _instance =
      IssueReportingService._internal();
  factory IssueReportingService() => _instance;
  IssueReportingService._internal();

  final Dio _dio = Dio();
  final Map<String, List<IssueReport>> _cache = {};
  final Map<String, DateTime> _lastUpdate = {};

  /// Inicializace služby
  Future<void> initialize() async {
    _dio.options = BaseOptions(
      connectTimeout: const Duration(seconds: 15),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'User-Agent': 'CroatiaTravel/1.0 (Issue Reporting)',
        'Accept': 'application/json',
      },
    );

    if (kDebugMode) {
      _dio.interceptors.add(
        LogInterceptor(
          requestBody: false,
          responseBody: false,
          logPrint: (obj) => debugPrint('[IssueReporting] $obj'),
        ),
      );
    }
  }

  /// Nahlášení nového problému
  Future<IssueReport?> reportIssue({
    required String title,
    required String description,
    required IssueCategory category,
    required double latitude,
    required double longitude,
    required String address,
    IssuePriority priority = IssuePriority.medium,
    List<String> photos = const [],
    String? reporterUserId,
    String? reporterName,
    String? reporterEmail,
    String? reporterPhone,
  }) async {
    try {
      debugPrint('📢 Nahlašuji problém: $title');

      final reportData = {
        'title': title,
        'description': description,
        'category': category.name,
        'priority': priority.name,
        'latitude': latitude,
        'longitude': longitude,
        'address': address,
        'photos': photos,
        'timestamp': DateTime.now().toIso8601String(),
        if (reporterUserId != null) 'reporter_user_id': reporterUserId,
        if (reporterName != null) 'reporter_name': reporterName,
        if (reporterEmail != null) 'reporter_email': reporterEmail,
        if (reporterPhone != null) 'reporter_phone': reporterPhone,
      };

      final response = await _dio.post('/issues/report', data: reportData);

      if (response.statusCode == 200 || response.statusCode == 201) {
        final issue = _parseIssueReport(response.data);

        // Invalidace cache pro oblast
        _invalidateCacheForArea(latitude, longitude);

        return issue;
      }
    } catch (e) {
      debugPrint('❌ Chyba při hlášení problému: $e');
    }

    // Mock hlášení pro demo
    return _createMockIssueReport(
      title,
      description,
      category,
      latitude,
      longitude,
      address,
      priority,
      reporterUserId,
    );
  }

  /// Získání problémů v oblasti
  Future<List<IssueReport>> getIssuesInArea({
    required double latitude,
    required double longitude,
    double radiusKm = 2.0,
    IssueCategory? category,
    IssueStatus? status,
  }) async {
    try {
      debugPrint('🗺️ Načítám problémy v oblasti $latitude, $longitude');

      // Kontrola cache
      final cacheKey =
          '${latitude.toStringAsFixed(3)}_${longitude.toStringAsFixed(3)}';
      if (_isCacheValid(cacheKey)) {
        debugPrint('✅ Používám cache pro problémy');
        return _filterIssues(_cache[cacheKey]!, category, status);
      }

      // Pokus o získání skutečných dat
      final realData = await _fetchRealIssuesData(
        latitude,
        longitude,
        radiusKm,
        category,
        status,
      );
      if (realData.isNotEmpty) {
        _cache[cacheKey] = realData;
        _lastUpdate[cacheKey] = DateTime.now();
        return _filterIssues(realData, category, status);
      }

      // Fallback na mock data
      debugPrint('⚠️ Používám mock data pro problémy');
      final mockData = _generateMockIssues(latitude, longitude);
      _cache[cacheKey] = mockData;
      _lastUpdate[cacheKey] = DateTime.now();

      return _filterIssues(mockData, category, status);
    } catch (e) {
      debugPrint('❌ Chyba při načítání problémů: $e');
      return _generateMockIssues(latitude, longitude);
    }
  }

  /// Získání detailů problému
  Future<IssueReport?> getIssueDetails(String issueId) async {
    try {
      final response = await _dio.get('/issues/$issueId');

      if (response.statusCode == 200) {
        return _parseIssueReport(response.data);
      }
    } catch (e) {
      debugPrint('Chyba při načítání detailů problému: $e');
    }

    return null;
  }

  /// Přidání komentáře k problému
  Future<bool> addComment({
    required String issueId,
    required String comment,
    required String userId,
    List<String>? photos,
  }) async {
    try {
      await _dio.post(
        '/issues/$issueId/comments',
        data: {
          'comment': comment,
          'user_id': userId,
          'timestamp': DateTime.now().toIso8601String(),
          if (photos != null) 'photos': photos,
        },
      );

      return true;
    } catch (e) {
      debugPrint('Chyba při přidávání komentáře: $e');
      return false;
    }
  }

  /// Hlasování o problému (upvote/downvote)
  Future<bool> voteOnIssue({
    required String issueId,
    required String userId,
    required bool isUpvote,
  }) async {
    try {
      await _dio.post(
        '/issues/$issueId/vote',
        data: {
          'user_id': userId,
          'vote_type': isUpvote ? 'upvote' : 'downvote',
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      return true;
    } catch (e) {
      debugPrint('Chyba při hlasování: $e');
      return false;
    }
  }

  /// Sledování problému
  Future<bool> followIssue(String issueId, String userId) async {
    try {
      await _dio.post('/issues/$issueId/follow', data: {'user_id': userId});

      return true;
    } catch (e) {
      debugPrint('Chyba při sledování problému: $e');
      return false;
    }
  }

  /// Získání problémů uživatele
  Future<List<IssueReport>> getUserIssues(String userId) async {
    try {
      final response = await _dio.get('/issues/user/$userId');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['issues'] ?? [];
        return data.map((json) => _parseIssueReport(json)).toList();
      }
    } catch (e) {
      debugPrint('Chyba při načítání uživatelských problémů: $e');
    }

    return [];
  }

  /// Získání statistik hlášení
  Future<IssueStatistics?> getIssueStatistics(String cityId) async {
    try {
      final response = await _dio.get('/issues/statistics/$cityId');

      if (response.statusCode == 200) {
        return IssueStatistics.fromJson(response.data);
      }
    } catch (e) {
      debugPrint('Chyba při načítání statistik: $e');
    }

    return _generateMockStatistics(cityId);
  }

  /// Pokus o získání skutečných dat
  Future<List<IssueReport>> _fetchRealIssuesData(
    double latitude,
    double longitude,
    double radiusKm,
    IssueCategory? category,
    IssueStatus? status,
  ) async {
    // Zde by byla integrace se skutečnými API:
    // - Zagreb: https://www.zagreb.hr/api/issues
    // - Split: https://www.split.hr/api/issues
    // - Rijeka: https://www.rijeka.hr/api/issues

    // Pro demo vracíme prázdný seznam
    return [];
  }

  /// Generování mock dat
  List<IssueReport> _generateMockIssues(double lat, double lng) {
    final issues = <IssueReport>[];
    final random = DateTime.now().millisecondsSinceEpoch;

    final categories = IssueCategory.values;
    final priorities = IssuePriority.values;
    final statuses = IssueStatus.values;

    for (int i = 0; i < 8; i++) {
      final offsetLat = (random % 2000 - 1000) / 100000.0;
      final offsetLng = (random % 2000 - 1000) / 100000.0;

      issues.add(
        IssueReport(
          id: 'issue_${lat.toStringAsFixed(3)}_$i',
          title: _getIssueTitle(i),
          description: _getIssueDescription(i),
          category: categories[i % categories.length],
          priority: priorities[i % priorities.length],
          latitude: lat + offsetLat,
          longitude: lng + offsetLng,
          address: _getIssueAddress(i),
          photos: i % 3 == 0 ? ['photo_$i.jpg'] : [],
          reporterUserId: 'user_${100 + i}',
          status: statuses[i % statuses.length],
          createdAt: DateTime.now().subtract(Duration(days: i, hours: i * 2)),
          resolvedAt: i % 4 == 0
              ? DateTime.now().subtract(Duration(days: i - 1))
              : null,
          assignedTo: i % 3 == 0 ? 'admin_${i % 3}' : null,
          updates: [],
        ),
      );
    }

    return issues;
  }

  String _getIssueTitle(int index) {
    final titles = [
      'Rozbitá lampa na ulici',
      'Díra v silnici',
      'Přeplněný kontejner na odpad',
      'Nefunkční semafor',
      'Poškozený chodník',
      'Hlučná stavba v noci',
      'Nelegální parkování',
      'Poškozená zastávka MHD',
    ];
    return titles[index % titles.length];
  }

  String _getIssueDescription(int index) {
    final descriptions = [
      'Pouliční lampa nefunguje už několik dní, oblast je špatně osvětlená.',
      'Velká díra v silnici způsobuje problémy řidičům.',
      'Kontejner na odpad je přeplněný a odpad se rozpadá kolem.',
      'Semafor na křižovatce nefunguje, vytváří se dopravní zácpy.',
      'Chodník je poškozený a nebezpečný pro chodce.',
      'Stavební práce probíhají i v nočních hodinách.',
      'Vozidla parkují na místech pro invalidy bez oprávnění.',
      'Zastávka MHD má rozbitá skla a je poškozená.',
    ];
    return descriptions[index % descriptions.length];
  }

  String _getIssueAddress(int index) {
    final addresses = [
      'Ilica 15',
      'Trg bana Jelačića 5',
      'Maksimirska 128',
      'Savska cesta 41',
      'Vlaška 79',
      'Gundulićeva 24',
      'Martićeva 29',
      'Heinzelova 62',
    ];
    return addresses[index % addresses.length];
  }

  IssueReport _createMockIssueReport(
    String title,
    String description,
    IssueCategory category,
    double latitude,
    double longitude,
    String address,
    IssuePriority priority,
    String? reporterUserId,
  ) {
    return IssueReport(
      id: 'issue_${DateTime.now().millisecondsSinceEpoch}',
      title: title,
      description: description,
      category: category,
      priority: priority,
      latitude: latitude,
      longitude: longitude,
      address: address,
      photos: [],
      reporterUserId: reporterUserId ?? 'anonymous',
      status: IssueStatus.reported,
      createdAt: DateTime.now(),
      updates: [],
    );
  }

  IssueStatistics _generateMockStatistics(String cityId) {
    return IssueStatistics(
      cityId: cityId,
      totalReports: 1250,
      resolvedReports: 980,
      pendingReports: 270,
      averageResolutionTime: const Duration(days: 5),
      mostCommonCategory: IssueCategory.roads,
      lastUpdated: DateTime.now(),
    );
  }

  List<IssueReport> _filterIssues(
    List<IssueReport> issues,
    IssueCategory? category,
    IssueStatus? status,
  ) {
    var filtered = issues;

    if (category != null) {
      filtered = filtered.where((issue) => issue.category == category).toList();
    }

    if (status != null) {
      filtered = filtered.where((issue) => issue.status == status).toList();
    }

    return filtered;
  }

  void _invalidateCacheForArea(double latitude, double longitude) {
    final cacheKey =
        '${latitude.toStringAsFixed(3)}_${longitude.toStringAsFixed(3)}';
    _cache.remove(cacheKey);
    _lastUpdate.remove(cacheKey);
  }

  bool _isCacheValid(String key) {
    final lastUpdate = _lastUpdate[key];
    if (lastUpdate == null) return false;

    return DateTime.now().difference(lastUpdate) < const Duration(minutes: 10);
  }

  IssueReport _parseIssueReport(Map<String, dynamic> json) {
    return IssueReport(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      category: IssueCategory.values.firstWhere(
        (c) => c.name == json['category'],
        orElse: () => IssueCategory.other,
      ),
      priority: IssuePriority.values.firstWhere(
        (p) => p.name == json['priority'],
        orElse: () => IssuePriority.medium,
      ),
      latitude: json['latitude']?.toDouble() ?? 0.0,
      longitude: json['longitude']?.toDouble() ?? 0.0,
      address: json['address'] ?? '',
      photos: List<String>.from(json['photos'] ?? []),
      reporterUserId: json['reporter_user_id'] ?? '',
      status: IssueStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => IssueStatus.reported,
      ),
      createdAt: DateTime.parse(json['created_at']),
      resolvedAt: json['resolved_at'] != null
          ? DateTime.parse(json['resolved_at'])
          : null,
      assignedTo: json['assigned_to'],
      updates:
          (json['updates'] as List?)
              ?.map(
                (u) => IssueUpdate(
                  id: u['id'] ?? '',
                  issueId: u['issue_id'] ?? '',
                  message: u['message'] ?? '',
                  authorId: u['author_id'] ?? '',
                  timestamp: DateTime.parse(u['timestamp']),
                  photos: List<String>.from(u['photos'] ?? []),
                ),
              )
              .toList() ??
          [],
    );
  }

  void clearCache() {
    _cache.clear();
    _lastUpdate.clear();
  }

  void dispose() {
    clearCache();
  }
}

/// Statistiky hlášení problémů
class IssueStatistics {
  final String cityId;
  final int totalReports;
  final int resolvedReports;
  final int pendingReports;
  final Duration averageResolutionTime;
  final IssueCategory mostCommonCategory;
  final DateTime lastUpdated;

  IssueStatistics({
    required this.cityId,
    required this.totalReports,
    required this.resolvedReports,
    required this.pendingReports,
    required this.averageResolutionTime,
    required this.mostCommonCategory,
    required this.lastUpdated,
  });

  double get resolutionRate =>
      totalReports > 0 ? (resolvedReports / totalReports) * 100 : 0;

  factory IssueStatistics.fromJson(Map<String, dynamic> json) {
    return IssueStatistics(
      cityId: json['city_id'] ?? '',
      totalReports: json['total_reports'] ?? 0,
      resolvedReports: json['resolved_reports'] ?? 0,
      pendingReports: json['pending_reports'] ?? 0,
      averageResolutionTime: Duration(
        days: json['average_resolution_days'] ?? 0,
      ),
      mostCommonCategory: IssueCategory.values.firstWhere(
        (c) => c.name == json['most_common_category'],
        orElse: () => IssueCategory.other,
      ),
      lastUpdated: DateTime.parse(json['last_updated']),
    );
  }
}
