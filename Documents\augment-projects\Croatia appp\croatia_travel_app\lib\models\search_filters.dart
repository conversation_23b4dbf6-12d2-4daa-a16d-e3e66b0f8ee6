import '../models/diary_entry.dart';

/// 🔍 SEARCH MODELS - Modely pro pokročilé vyhledávání

/// Výsledky vyhledávání
class SearchResults {
  final List<DiaryEntry> entries;
  final int totalCount;
  final int searchTime; // v mi<PERSON>ekundách
  final List<String> suggestions;
  final Map<String, dynamic> filters;
  final SearchStats? stats;

  const SearchResults({
    required this.entries,
    required this.totalCount,
    required this.searchTime,
    this.suggestions = const [],
    this.filters = const {},
    this.stats,
  });

  bool get isEmpty => entries.isEmpty;
  bool get isNotEmpty => entries.isNotEmpty;
  bool get hasMoreResults => totalCount > entries.length;

  factory SearchResults.empty() {
    return const SearchResults(
      entries: [],
      totalCount: 0,
      searchTime: 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'entries': entries.map((e) => e.to<PERSON><PERSON>()).toList(),
      'totalCount': totalCount,
      'searchTime': searchTime,
      'suggestions': suggestions,
      'filters': filters,
      'stats': stats?.toJson(),
    };
  }
}

/// Statistiky vyhledávání
class SearchStats {
  final Map<DiaryMood, int> moodDistribution;
  final Map<String, int> locationDistribution;
  final Map<String, int> weatherDistribution;
  final Map<int, int> yearDistribution;
  final Map<int, int> monthDistribution;
  final double averageRating;
  final int entriesWithPhotos;
  final int entriesWithVideos;

  const SearchStats({
    required this.moodDistribution,
    required this.locationDistribution,
    required this.weatherDistribution,
    required this.yearDistribution,
    required this.monthDistribution,
    required this.averageRating,
    required this.entriesWithPhotos,
    required this.entriesWithVideos,
  });

  Map<String, dynamic> toJson() {
    return {
      'moodDistribution': moodDistribution.map((k, v) => MapEntry(k.name, v)),
      'locationDistribution': locationDistribution,
      'weatherDistribution': weatherDistribution,
      'yearDistribution': yearDistribution,
      'monthDistribution': monthDistribution,
      'averageRating': averageRating,
      'entriesWithPhotos': entriesWithPhotos,
      'entriesWithVideos': entriesWithVideos,
    };
  }
}

/// Filtry pro vyhledávání
class SearchFilters {
  final String? query;
  final List<DiaryMood> moods;
  final DateTime? fromDate;
  final DateTime? toDate;
  final List<String> weatherConditions;
  final List<String> locations;
  final List<String> companions;
  final int? minRating;
  final int? maxRating;
  final bool? hasPhotos;
  final bool? hasVideos;
  final bool? hasVoiceNotes;
  final String? season;
  final List<String> tags;
  final SortBy sortBy;
  final SortOrder sortOrder;

  const SearchFilters({
    this.query,
    this.moods = const [],
    this.fromDate,
    this.toDate,
    this.weatherConditions = const [],
    this.locations = const [],
    this.companions = const [],
    this.minRating,
    this.maxRating,
    this.hasPhotos,
    this.hasVideos,
    this.hasVoiceNotes,
    this.season,
    this.tags = const [],
    this.sortBy = SortBy.date,
    this.sortOrder = SortOrder.descending,
  });

  SearchFilters copyWith({
    String? query,
    List<DiaryMood>? moods,
    DateTime? fromDate,
    DateTime? toDate,
    List<String>? weatherConditions,
    List<String>? locations,
    List<String>? companions,
    int? minRating,
    int? maxRating,
    bool? hasPhotos,
    bool? hasVideos,
    bool? hasVoiceNotes,
    String? season,
    List<String>? tags,
    SortBy? sortBy,
    SortOrder? sortOrder,
  }) {
    return SearchFilters(
      query: query ?? this.query,
      moods: moods ?? this.moods,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      weatherConditions: weatherConditions ?? this.weatherConditions,
      locations: locations ?? this.locations,
      companions: companions ?? this.companions,
      minRating: minRating ?? this.minRating,
      maxRating: maxRating ?? this.maxRating,
      hasPhotos: hasPhotos ?? this.hasPhotos,
      hasVideos: hasVideos ?? this.hasVideos,
      hasVoiceNotes: hasVoiceNotes ?? this.hasVoiceNotes,
      season: season ?? this.season,
      tags: tags ?? this.tags,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }

  bool get isEmpty {
    return query == null &&
           moods.isEmpty &&
           fromDate == null &&
           toDate == null &&
           weatherConditions.isEmpty &&
           locations.isEmpty &&
           companions.isEmpty &&
           minRating == null &&
           maxRating == null &&
           hasPhotos == null &&
           hasVideos == null &&
           hasVoiceNotes == null &&
           season == null &&
           tags.isEmpty;
  }

  int get activeFiltersCount {
    int count = 0;
    if (query != null && query!.isNotEmpty) count++;
    if (moods.isNotEmpty) count++;
    if (fromDate != null || toDate != null) count++;
    if (weatherConditions.isNotEmpty) count++;
    if (locations.isNotEmpty) count++;
    if (companions.isNotEmpty) count++;
    if (minRating != null || maxRating != null) count++;
    if (hasPhotos != null) count++;
    if (hasVideos != null) count++;
    if (hasVoiceNotes != null) count++;
    if (season != null) count++;
    if (tags.isNotEmpty) count++;
    return count;
  }

  Map<String, dynamic> toJson() {
    return {
      'query': query,
      'moods': moods.map((m) => m.name).toList(),
      'fromDate': fromDate?.toIso8601String(),
      'toDate': toDate?.toIso8601String(),
      'weatherConditions': weatherConditions,
      'locations': locations,
      'companions': companions,
      'minRating': minRating,
      'maxRating': maxRating,
      'hasPhotos': hasPhotos,
      'hasVideos': hasVideos,
      'hasVoiceNotes': hasVoiceNotes,
      'season': season,
      'tags': tags,
      'sortBy': sortBy.name,
      'sortOrder': sortOrder.name,
    };
  }

  factory SearchFilters.fromJson(Map<String, dynamic> json) {
    return SearchFilters(
      query: json['query'] as String?,
      moods: (json['moods'] as List<dynamic>?)
          ?.map((m) => DiaryMood.values.firstWhere((mood) => mood.name == m))
          .toList() ?? [],
      fromDate: json['fromDate'] != null 
          ? DateTime.parse(json['fromDate'] as String)
          : null,
      toDate: json['toDate'] != null 
          ? DateTime.parse(json['toDate'] as String)
          : null,
      weatherConditions: (json['weatherConditions'] as List<dynamic>?)?.cast<String>() ?? [],
      locations: (json['locations'] as List<dynamic>?)?.cast<String>() ?? [],
      companions: (json['companions'] as List<dynamic>?)?.cast<String>() ?? [],
      minRating: json['minRating'] as int?,
      maxRating: json['maxRating'] as int?,
      hasPhotos: json['hasPhotos'] as bool?,
      hasVideos: json['hasVideos'] as bool?,
      hasVoiceNotes: json['hasVoiceNotes'] as bool?,
      season: json['season'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.cast<String>() ?? [],
      sortBy: SortBy.values.firstWhere(
        (s) => s.name == json['sortBy'],
        orElse: () => SortBy.date,
      ),
      sortOrder: SortOrder.values.firstWhere(
        (s) => s.name == json['sortOrder'],
        orElse: () => SortOrder.descending,
      ),
    );
  }
}

/// Způsoby řazení
enum SortBy {
  date,
  relevance,
  rating,
  title,
  location,
  mood,
  wordCount,
}

extension SortByExtension on SortBy {
  String get displayName {
    switch (this) {
      case SortBy.date:
        return 'Datum';
      case SortBy.relevance:
        return 'Relevance';
      case SortBy.rating:
        return 'Hodnocení';
      case SortBy.title:
        return 'Název';
      case SortBy.location:
        return 'Lokace';
      case SortBy.mood:
        return 'Nálada';
      case SortBy.wordCount:
        return 'Délka';
    }
  }
}

/// Směr řazení
enum SortOrder {
  ascending,
  descending,
}

extension SortOrderExtension on SortOrder {
  String get displayName {
    switch (this) {
      case SortOrder.ascending:
        return 'Vzestupně';
      case SortOrder.descending:
        return 'Sestupně';
    }
  }
}

/// Uložené vyhledávání
class SavedSearch {
  final String id;
  final String name;
  final SearchFilters filters;
  final DateTime createdAt;
  final DateTime lastUsed;
  final int useCount;
  final bool isNotificationEnabled;

  const SavedSearch({
    required this.id,
    required this.name,
    required this.filters,
    required this.createdAt,
    required this.lastUsed,
    this.useCount = 0,
    this.isNotificationEnabled = false,
  });

  SavedSearch copyWith({
    String? id,
    String? name,
    SearchFilters? filters,
    DateTime? createdAt,
    DateTime? lastUsed,
    int? useCount,
    bool? isNotificationEnabled,
  }) {
    return SavedSearch(
      id: id ?? this.id,
      name: name ?? this.name,
      filters: filters ?? this.filters,
      createdAt: createdAt ?? this.createdAt,
      lastUsed: lastUsed ?? this.lastUsed,
      useCount: useCount ?? this.useCount,
      isNotificationEnabled: isNotificationEnabled ?? this.isNotificationEnabled,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'filters': filters.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'lastUsed': lastUsed.toIso8601String(),
      'useCount': useCount,
      'isNotificationEnabled': isNotificationEnabled,
    };
  }

  factory SavedSearch.fromJson(Map<String, dynamic> json) {
    return SavedSearch(
      id: json['id'] as String,
      name: json['name'] as String,
      filters: SearchFilters.fromJson(json['filters'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastUsed: DateTime.parse(json['lastUsed'] as String),
      useCount: json['useCount'] as int? ?? 0,
      isNotificationEnabled: json['isNotificationEnabled'] as bool? ?? false,
    );
  }
}

/// Rychlé vyhledávání
class QuickSearch {
  final String title;
  final String description;
  final SearchFilters filters;
  final String icon;
  final String color;

  const QuickSearch({
    required this.title,
    required this.description,
    required this.filters,
    required this.icon,
    required this.color,
  });

  static List<QuickSearch> getDefaults() {
    return [
      QuickSearch(
        title: 'Šťastné vzpomínky',
        description: 'Záznamy s pozitivní náladou',
        filters: const SearchFilters(
          moods: [DiaryMood.veryHappy, DiaryMood.happy, DiaryMood.excited],
        ),
        icon: '😊',
        color: '#4CAF50',
      ),
      QuickSearch(
        title: 'Letní dobrodružství',
        description: 'Záznamy z letních měsíců',
        filters: const SearchFilters(
          season: 'léto',
          weatherConditions: ['slunečno', 'jasno'],
        ),
        icon: '☀️',
        color: '#FF9800',
      ),
      QuickSearch(
        title: 'Pláže a moře',
        description: 'Všechny záznamy z pláží',
        filters: const SearchFilters(
          query: 'pláž moře koupání',
        ),
        icon: '🏖️',
        color: '#2196F3',
      ),
      QuickSearch(
        title: 'S fotkami',
        description: 'Záznamy obsahující fotografie',
        filters: const SearchFilters(
          hasPhotos: true,
        ),
        icon: '📸',
        color: '#9C27B0',
      ),
      QuickSearch(
        title: 'Loni dnes',
        description: 'Co jsem dělal před rokem',
        filters: SearchFilters(
          fromDate: DateTime.now().subtract(const Duration(days: 370)),
          toDate: DateTime.now().subtract(const Duration(days: 360)),
        ),
        icon: '📅',
        color: '#607D8B',
      ),
    ];
  }
}
