import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _notificationsEnabled = true;
  bool _offlineMode = false;
  bool _darkMode = false;
  String _language = 'cs';
  String _currency = 'EUR';
  bool _locationServices = true;
  bool _autoSync = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Nastavení',
          style: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF006994).withValues(alpha: 0.9),
                const Color(0xFF2E8B8B).withValues(alpha: 0.8),
                const Color(0xFF006994).withValues(alpha: 0.9),
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: CustomPaint(
            painter: WatercolorSettingsHeaderPainter(),
            size: Size.infinite,
          ),
        ),
      ),
      body: ListView(
        children: [
          // Profil sekce
          _buildSection('Profil', [
            ListTile(
              leading: const CircleAvatar(child: Icon(Icons.person)),
              title: const Text('Cestovatel'),
              subtitle: const Text('<EMAIL>'),
              trailing: const Icon(Icons.edit),
              onTap: _editProfile,
            ),
          ]),

          // Obecné nastavení
          _buildSection('Obecné', [
            ListTile(
              leading: const Icon(Icons.language),
              title: const Text('Jazyk'),
              subtitle: Text(_getLanguageName(_language)),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _showLanguageDialog,
            ),
            ListTile(
              leading: const Icon(Icons.attach_money),
              title: const Text('Měna'),
              subtitle: Text(_currency),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _showCurrencyDialog,
            ),
            SwitchListTile(
              secondary: const Icon(Icons.dark_mode),
              title: const Text('Tmavý režim'),
              subtitle: const Text('Použít tmavé téma'),
              value: _darkMode,
              onChanged: (value) {
                setState(() {
                  _darkMode = value;
                });
                _showFeatureComingSoon('Tmavý režim');
              },
            ),
          ]),

          // Notifikace
          _buildSection('Notifikace', [
            SwitchListTile(
              secondary: const Icon(Icons.notifications),
              title: const Text('Povolit notifikace'),
              subtitle: const Text('Dostávat upozornění o událostech'),
              value: _notificationsEnabled,
              onChanged: (value) {
                setState(() {
                  _notificationsEnabled = value;
                });
              },
            ),
          ]),

          // Synchronizace a data
          _buildSection('Data a synchronizace', [
            SwitchListTile(
              secondary: const Icon(Icons.sync),
              title: const Text('Automatická synchronizace'),
              subtitle: const Text('Synchronizovat data při připojení'),
              value: _autoSync,
              onChanged: (value) {
                setState(() {
                  _autoSync = value;
                });
              },
            ),
            SwitchListTile(
              secondary: const Icon(Icons.offline_bolt),
              title: const Text('Offline režim'),
              subtitle: const Text('Používat aplikaci bez internetu'),
              value: _offlineMode,
              onChanged: (value) {
                setState(() {
                  _offlineMode = value;
                });
                _showFeatureComingSoon('Offline režim');
              },
            ),
            ListTile(
              leading: const Icon(Icons.backup),
              title: const Text('Zálohování'),
              subtitle: const Text('Zálohovat data do cloudu'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showFeatureComingSoon('Zálohování'),
            ),
          ]),

          // Soukromí a bezpečnost
          _buildSection('Soukromí a bezpečnost', [
            SwitchListTile(
              secondary: const Icon(Icons.location_on),
              title: const Text('Služby polohy'),
              subtitle: const Text('Povolit přístup k poloze'),
              value: _locationServices,
              onChanged: (value) {
                setState(() {
                  _locationServices = value;
                });
              },
            ),
            ListTile(
              leading: const Icon(Icons.privacy_tip),
              title: const Text('Zásady ochrany osobních údajů'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _showPrivacyPolicy,
            ),
            ListTile(
              leading: const Icon(Icons.security),
              title: const Text('Podmínky použití'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _showTermsOfService,
            ),
          ]),

          // Úložiště
          _buildSection('Úložiště', [
            ListTile(
              leading: const Icon(Icons.storage),
              title: const Text('Správa úložiště'),
              subtitle: const Text('Použito: 245 MB'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _showStorageManagement,
            ),
            ListTile(
              leading: const Icon(Icons.clear_all),
              title: const Text('Vymazat cache'),
              subtitle: const Text(
                'Uvolnit místo odstraněním dočasných souborů',
              ),
              onTap: _clearCache,
            ),
          ]),

          // Podpora
          _buildSection('Podpora', [
            ListTile(
              leading: const Icon(Icons.help),
              title: const Text('Nápověda'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _showHelp,
            ),
            ListTile(
              leading: const Icon(Icons.feedback),
              title: const Text('Zpětná vazba'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _sendFeedback,
            ),
            ListTile(
              leading: const Icon(Icons.star_rate),
              title: const Text('Ohodnotit aplikaci'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _rateApp,
            ),
          ]),

          // O aplikaci
          _buildSection('O aplikaci', [
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('Verze aplikace'),
              subtitle: const Text('1.0.0 (Beta)'),
            ),
            ListTile(
              leading: const Icon(Icons.update),
              title: const Text('Zkontrolovat aktualizace'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _checkForUpdates,
            ),
          ]),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: CustomPaint(
        painter: WatercolorSectionPainter(),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 20, 20, 12),
                child: Text(
                  title,
                  style: GoogleFonts.playfairDisplay(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF006994),
                  ),
                ),
              ),
              ...children.map((child) => _wrapWithWatercolor(child)),
              const SizedBox(height: 8),
            ],
          ),
        ),
      ),
    );
  }

  Widget _wrapWithWatercolor(Widget child) {
    if (child is SwitchListTile) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
        child: CustomPaint(
          painter: WatercolorListTilePainter(const Color(0xFF2E8B8B)),
          child: child,
        ),
      );
    } else if (child is ListTile) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
        child: CustomPaint(
          painter: WatercolorListTilePainter(const Color(0xFF006994)),
          child: child,
        ),
      );
    }
    return child;
  }

  String _getLanguageName(String code) {
    switch (code) {
      case 'cs':
        return 'Čeština';
      case 'en':
        return 'English';
      case 'hr':
        return 'Hrvatski';
      default:
        return 'Čeština';
    }
  }

  void _editProfile() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Upravit profil'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: InputDecoration(
                labelText: 'Jméno',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 16),
            TextField(
              decoration: InputDecoration(
                labelText: 'Email',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zrušit'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showFeatureComingSoon('Úprava profilu');
            },
            child: const Text('Uložit'),
          ),
        ],
      ),
    );
  }

  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Vyberte jazyk'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('Čeština'),
              value: 'cs',
              groupValue: _language,
              onChanged: (value) {
                setState(() {
                  _language = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<String>(
              title: const Text('English'),
              value: 'en',
              groupValue: _language,
              onChanged: (value) {
                setState(() {
                  _language = value!;
                });
                Navigator.of(context).pop();
                _showFeatureComingSoon('Změna jazyka');
              },
            ),
            RadioListTile<String>(
              title: const Text('Hrvatski'),
              value: 'hr',
              groupValue: _language,
              onChanged: (value) {
                setState(() {
                  _language = value!;
                });
                Navigator.of(context).pop();
                _showFeatureComingSoon('Změna jazyka');
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showCurrencyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Vyberte měnu'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('EUR (€)'),
              value: 'EUR',
              groupValue: _currency,
              onChanged: (value) {
                setState(() {
                  _currency = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<String>(
              title: const Text('CZK (Kč)'),
              value: 'CZK',
              groupValue: _currency,
              onChanged: (value) {
                setState(() {
                  _currency = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<String>(
              title: const Text('HRK (kn)'),
              value: 'HRK',
              groupValue: _currency,
              onChanged: (value) {
                setState(() {
                  _currency = value!;
                });
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showPrivacyPolicy() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Zásady ochrany osobních údajů'),
        content: const SingleChildScrollView(
          child: Text(
            'Vaše soukromí je pro nás důležité. Tato aplikace shromažďuje pouze nezbytné údaje pro poskytování služeb...\n\n'
            'Podrobné zásady ochrany osobních údajů najdete na našich webových stránkách.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zavřít'),
          ),
        ],
      ),
    );
  }

  void _showTermsOfService() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Podmínky použití'),
        content: const SingleChildScrollView(
          child: Text(
            'Používáním této aplikace souhlasíte s našimi podmínkami použití...\n\n'
            'Úplné podmínky najdete na našich webových stránkách.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zavřít'),
          ),
        ],
      ),
    );
  }

  void _showStorageManagement() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Správa úložiště'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Použité úložiště:'),
            SizedBox(height: 8),
            Text('• Fotografie: 180 MB'),
            Text('• Hlasové poznámky: 45 MB'),
            Text('• Offline mapy: 15 MB'),
            Text('• Cache: 5 MB'),
            SizedBox(height: 16),
            Text('Celkem: 245 MB'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zavřít'),
          ),
        ],
      ),
    );
  }

  void _clearCache() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Vymazat cache'),
        content: const Text('Opravdu chcete vymazat všechny dočasné soubory?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zrušit'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Cache byla vymazána'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Vymazat'),
          ),
        ],
      ),
    );
  }

  void _showHelp() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(title: const Text('Nápověda')),
          body: const Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Často kladené otázky',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 16),
                Text('Q: Jak přidat nové místo do deníku?'),
                Text('A: Klikněte na + v sekci Deník a vyplňte informace.'),
                SizedBox(height: 12),
                Text('Q: Jak funguje offline režim?'),
                Text('A: Offline režim bude dostupný v příští verzi.'),
                SizedBox(height: 12),
                Text('Q: Jak exportovat data?'),
                Text('A: V sekci Deník použijte tlačítko Export.'),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _sendFeedback() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Zpětná vazba'),
        content: const TextField(
          maxLines: 4,
          decoration: InputDecoration(
            hintText: 'Napište nám vaše připomínky...',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zrušit'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Děkujeme za zpětnou vazbu!'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Odeslat'),
          ),
        ],
      ),
    );
  }

  void _rateApp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Ohodnotit aplikaci'),
        content: const Text(
          'Líbí se vám naše aplikace? Ohodnoťte nás v obchodě s aplikacemi!',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Později'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showFeatureComingSoon('Hodnocení aplikace');
            },
            child: const Text('Ohodnotit'),
          ),
        ],
      ),
    );
  }

  void _checkForUpdates() {
    showDialog(
      context: context,
      builder: (context) => const AlertDialog(
        title: Text('Kontrola aktualizací'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Kontrolujeme dostupné aktualizace...'),
          ],
        ),
      ),
    );

    Future.delayed(const Duration(seconds: 2), () {
      Navigator.of(context).pop();
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Aktualizace'),
          content: const Text('Používáte nejnovější verzi aplikace.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    });
  }

  void _showFeatureComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature bude dostupný v příští verzi'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}

// Watercolor painters pro Settings Screen
class WatercolorSettingsHeaderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor vlny pro Settings header
    final path1 = Path();
    path1.moveTo(0, size.height * 0.3);
    path1.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.1,
      size.width * 0.5,
      size.height * 0.4,
    );
    path1.quadraticBezierTo(
      size.width * 0.75,
      size.height * 0.7,
      size.width,
      size.height * 0.2,
    );
    path1.lineTo(size.width, size.height);
    path1.lineTo(0, size.height);
    path1.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.2);
    canvas.drawPath(path1, paint);

    // Druhá vrstva
    final path2 = Path();
    path2.moveTo(0, size.height * 0.5);
    path2.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.2,
      size.width * 0.7,
      size.height * 0.6,
    );
    path2.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.8,
      size.width,
      size.height * 0.4,
    );
    path2.lineTo(size.width, size.height);
    path2.lineTo(0, size.height);
    path2.close();

    paint.color = const Color(0xFFFF6B35).withValues(alpha: 0.1);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorSectionPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro sekce
    final path = Path();
    path.moveTo(size.width * 0.05, size.height * 0.1);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.02,
      size.width * 0.7,
      size.height * 0.08,
    );
    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.15,
      size.width * 0.98,
      size.height * 0.9,
    );
    path.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.98,
      size.width * 0.3,
      size.height * 0.92,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.85,
      size.width * 0.05,
      size.height * 0.1,
    );
    path.close();

    paint.color = const Color(0xFF006994).withValues(alpha: 0.08);
    canvas.drawPath(path, paint);

    // Druhá vrstva pro hloubku
    final path2 = Path();
    path2.moveTo(size.width * 0.1, size.height * 0.2);
    path2.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.05,
      size.width * 0.9,
      size.height * 0.15,
    );
    path2.lineTo(size.width * 0.85, size.height * 0.8);
    path2.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.95,
      size.width * 0.15,
      size.height * 0.85,
    );
    path2.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.05);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorListTilePainter extends CustomPainter {
  final Color color;

  WatercolorListTilePainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Jemný watercolor efekt pro list tiles
    final path = Path();
    path.moveTo(size.width * 0.02, size.height * 0.2);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.05,
      size.width * 0.7,
      size.height * 0.15,
    );
    path.quadraticBezierTo(
      size.width * 0.98,
      size.height * 0.25,
      size.width * 0.95,
      size.height * 0.8,
    );
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.95,
      size.width * 0.2,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.5,
      size.width * 0.02,
      size.height * 0.2,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.06);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
