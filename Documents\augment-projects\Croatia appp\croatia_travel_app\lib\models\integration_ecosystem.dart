/// 🔗 INTEGRATION ECOSYSTEM MODELS - Modely pro ekosystém integrací
library;

/// Poskytovatel integrace
class IntegrationProvider {
  final String id;
  final String name;
  final String description;
  final IntegrationCategory category;
  final String icon;
  final bool isAvailable;
  final List<String> requiredScopes;
  final List<String> features;
  final String? documentationUrl;

  const IntegrationProvider({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.icon,
    required this.isAvailable,
    this.requiredScopes = const [],
    this.features = const [],
    this.documentationUrl,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category.name,
      'icon': icon,
      'isAvailable': isAvailable,
      'requiredScopes': requiredScopes,
      'features': features,
      'documentationUrl': documentationUrl,
    };
  }
}

/// Kategorie integrace
enum IntegrationCategory {
  storage, // Úložiště
  social, // Sociální sítě
  productivity, // Produktivita
  location, // Lokace
  media, // Media
  analytics, // Analytika
  communication, // Komunikace
  finance, // Finance
}

extension IntegrationCategoryExtension on IntegrationCategory {
  String get displayName {
    switch (this) {
      case IntegrationCategory.storage:
        return 'Úložiště';
      case IntegrationCategory.social:
        return 'Sociální sítě';
      case IntegrationCategory.productivity:
        return 'Produktivita';
      case IntegrationCategory.location:
        return 'Lokace';
      case IntegrationCategory.media:
        return 'Media';
      case IntegrationCategory.analytics:
        return 'Analytika';
      case IntegrationCategory.communication:
        return 'Komunikace';
      case IntegrationCategory.finance:
        return 'Finance';
    }
  }

  String get icon {
    switch (this) {
      case IntegrationCategory.storage:
        return '☁️';
      case IntegrationCategory.social:
        return '📱';
      case IntegrationCategory.productivity:
        return '⚡';
      case IntegrationCategory.location:
        return '📍';
      case IntegrationCategory.media:
        return '🎬';
      case IntegrationCategory.analytics:
        return '📊';
      case IntegrationCategory.communication:
        return '💬';
      case IntegrationCategory.finance:
        return '💰';
    }
  }
}

/// Aktivní integrace
class ActiveIntegration {
  final String id;
  final String providerId;
  final String userId;
  final String accessToken;
  final String? refreshToken;
  final List<String> scopes;
  final bool isActive;
  final DateTime connectedAt;
  final DateTime? lastSyncAt;
  final Map<String, dynamic> settings;

  const ActiveIntegration({
    required this.id,
    required this.providerId,
    required this.userId,
    required this.accessToken,
    this.refreshToken,
    this.scopes = const [],
    required this.isActive,
    required this.connectedAt,
    this.lastSyncAt,
    this.settings = const {},
  });

  ActiveIntegration copyWith({
    String? id,
    String? providerId,
    String? userId,
    String? accessToken,
    String? refreshToken,
    List<String>? scopes,
    bool? isActive,
    DateTime? connectedAt,
    DateTime? lastSyncAt,
    Map<String, dynamic>? settings,
  }) {
    return ActiveIntegration(
      id: id ?? this.id,
      providerId: providerId ?? this.providerId,
      userId: userId ?? this.userId,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      scopes: scopes ?? this.scopes,
      isActive: isActive ?? this.isActive,
      connectedAt: connectedAt ?? this.connectedAt,
      lastSyncAt: lastSyncAt ?? this.lastSyncAt,
      settings: settings ?? this.settings,
    );
  }

  Duration get connectedDuration => DateTime.now().difference(connectedAt);
  bool get needsSync =>
      lastSyncAt == null || DateTime.now().difference(lastSyncAt!).inHours > 24;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'providerId': providerId,
      'userId': userId,
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'scopes': scopes,
      'isActive': isActive,
      'connectedAt': connectedAt.toIso8601String(),
      'lastSyncAt': lastSyncAt?.toIso8601String(),
      'settings': settings,
    };
  }

  factory ActiveIntegration.fromJson(Map<String, dynamic> json) {
    return ActiveIntegration(
      id: json['id'] as String,
      providerId: json['providerId'] as String,
      userId: json['userId'] as String,
      accessToken: json['accessToken'] as String,
      refreshToken: json['refreshToken'] as String?,
      scopes: (json['scopes'] as List<dynamic>).cast<String>(),
      isActive: json['isActive'] as bool,
      connectedAt: DateTime.parse(json['connectedAt'] as String),
      lastSyncAt: json['lastSyncAt'] != null
          ? DateTime.parse(json['lastSyncAt'] as String)
          : null,
      settings: Map<String, dynamic>.from(json['settings'] ?? {}),
    );
  }
}

/// Výsledek integrace
class IntegrationResult {
  final bool success;
  final String? message;
  final String? error;
  final Map<String, dynamic>? data;

  const IntegrationResult({
    required this.success,
    this.message,
    this.error,
    this.data,
  });

  factory IntegrationResult.success({
    String? message,
    Map<String, dynamic>? data,
  }) {
    return IntegrationResult(success: true, message: message, data: data);
  }

  factory IntegrationResult.failure({
    required String error,
    Map<String, dynamic>? data,
  }) {
    return IntegrationResult(success: false, error: error, data: data);
  }
}

/// Sociální platforma
enum SocialPlatform { facebook, instagram, twitter, linkedin, tiktok }

extension SocialPlatformExtension on SocialPlatform {
  String get displayName {
    switch (this) {
      case SocialPlatform.facebook:
        return 'Facebook';
      case SocialPlatform.instagram:
        return 'Instagram';
      case SocialPlatform.twitter:
        return 'Twitter';
      case SocialPlatform.linkedin:
        return 'LinkedIn';
      case SocialPlatform.tiktok:
        return 'TikTok';
    }
  }

  List<String> get requiredScopes {
    switch (this) {
      case SocialPlatform.facebook:
        return ['public_profile', 'email', 'pages_manage_posts'];
      case SocialPlatform.instagram:
        return ['user_profile', 'user_media'];
      case SocialPlatform.twitter:
        return ['tweet.read', 'tweet.write', 'users.read'];
      case SocialPlatform.linkedin:
        return ['r_liteprofile', 'w_member_social'];
      case SocialPlatform.tiktok:
        return ['user.info.basic', 'video.publish'];
    }
  }

  String get icon {
    switch (this) {
      case SocialPlatform.facebook:
        return '📘';
      case SocialPlatform.instagram:
        return '📷';
      case SocialPlatform.twitter:
        return '🐦';
      case SocialPlatform.linkedin:
        return '💼';
      case SocialPlatform.tiktok:
        return '🎵';
    }
  }
}

/// Cloud provider
enum CloudProvider { dropbox, onedrive, icloud, googleDrive }

extension CloudProviderExtension on CloudProvider {
  String get displayName {
    switch (this) {
      case CloudProvider.dropbox:
        return 'Dropbox';
      case CloudProvider.onedrive:
        return 'OneDrive';
      case CloudProvider.icloud:
        return 'iCloud';
      case CloudProvider.googleDrive:
        return 'Google Drive';
    }
  }

  List<String> get requiredScopes {
    switch (this) {
      case CloudProvider.dropbox:
        return ['files.content.write', 'files.content.read'];
      case CloudProvider.onedrive:
        return ['Files.ReadWrite', 'Files.ReadWrite.All'];
      case CloudProvider.icloud:
        return ['CloudKit'];
      case CloudProvider.googleDrive:
        return ['https://www.googleapis.com/auth/drive.file'];
    }
  }

  String get icon {
    switch (this) {
      case CloudProvider.dropbox:
        return '📦';
      case CloudProvider.onedrive:
        return '☁️';
      case CloudProvider.icloud:
        return '☁️';
      case CloudProvider.googleDrive:
        return '💾';
    }
  }
}

/// Webhook endpoint
class WebhookEndpoint {
  final String id;
  final String url;
  final List<WebhookEvent> events;
  final String? secret;
  final Map<String, String> headers;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? lastTriggered;
  final int successCount;
  final int failureCount;

  const WebhookEndpoint({
    required this.id,
    required this.url,
    required this.events,
    this.secret,
    this.headers = const {},
    required this.isActive,
    required this.createdAt,
    this.lastTriggered,
    this.successCount = 0,
    this.failureCount = 0,
  });

  double get successRate => (successCount + failureCount) > 0
      ? successCount / (successCount + failureCount)
      : 0.0;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'url': url,
      'events': events.map((e) => e.name).toList(),
      'secret': secret,
      'headers': headers,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'lastTriggered': lastTriggered?.toIso8601String(),
      'successCount': successCount,
      'failureCount': failureCount,
    };
  }
}

/// Webhook událost
enum WebhookEvent {
  entryCreated,
  entryUpdated,
  entryDeleted,
  photoUploaded,
  achievementUnlocked,
  userRegistered,
  subscriptionChanged,
}

/// OAuth provider
enum OAuthProvider { google, apple, facebook, microsoft }

extension OAuthProviderExtension on OAuthProvider {
  String get displayName {
    switch (this) {
      case OAuthProvider.google:
        return 'Google';
      case OAuthProvider.apple:
        return 'Apple';
      case OAuthProvider.facebook:
        return 'Facebook';
      case OAuthProvider.microsoft:
        return 'Microsoft';
    }
  }

  List<String> get defaultScopes {
    switch (this) {
      case OAuthProvider.google:
        return ['openid', 'email', 'profile'];
      case OAuthProvider.apple:
        return ['name', 'email'];
      case OAuthProvider.facebook:
        return ['public_profile', 'email'];
      case OAuthProvider.microsoft:
        return ['openid', 'profile', 'email'];
    }
  }

  String get icon {
    switch (this) {
      case OAuthProvider.google:
        return '🔍';
      case OAuthProvider.apple:
        return '🍎';
      case OAuthProvider.facebook:
        return '📘';
      case OAuthProvider.microsoft:
        return '🪟';
    }
  }
}

/// OAuth výsledek
class OAuthResult {
  final OAuthProvider provider;
  final String accessToken;
  final String? refreshToken;
  final DateTime expiresAt;
  final List<String> scopes;
  final Map<String, dynamic> userInfo;

  const OAuthResult({
    required this.provider,
    required this.accessToken,
    this.refreshToken,
    required this.expiresAt,
    this.scopes = const [],
    this.userInfo = const {},
  });

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  Duration get timeUntilExpiry => expiresAt.difference(DateTime.now());

  Map<String, dynamic> toJson() {
    return {
      'provider': provider.name,
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'expiresAt': expiresAt.toIso8601String(),
      'scopes': scopes,
      'userInfo': userInfo,
    };
  }
}

/// Stav synchronizace
class SyncStatus {
  final bool isSuccess;
  final DateTime timestamp;
  final String? errorMessage;
  final Map<String, dynamic>? metadata;

  const SyncStatus({
    required this.isSuccess,
    required this.timestamp,
    this.errorMessage,
    this.metadata,
  });

  factory SyncStatus.success({Map<String, dynamic>? metadata}) {
    return SyncStatus(
      isSuccess: true,
      timestamp: DateTime.now(),
      metadata: metadata,
    );
  }

  factory SyncStatus.failure(String error, {Map<String, dynamic>? metadata}) {
    return SyncStatus(
      isSuccess: false,
      timestamp: DateTime.now(),
      errorMessage: error,
      metadata: metadata,
    );
  }
}

/// Událost integrace
class IntegrationEvent {
  final IntegrationEventType type;
  final String providerId;
  final DateTime timestamp;
  final Map<String, dynamic>? data;

  const IntegrationEvent({
    required this.type,
    required this.providerId,
    required this.timestamp,
    this.data,
  });
}

/// Typ události integrace
enum IntegrationEventType {
  connected,
  disconnected,
  authenticated,
  dataImported,
  backupCompleted,
  syncStarted,
  syncCompleted,
  syncFailed,
  tokenRefreshed,
  webhookTriggered,
}

/// Stav integračního ekosystému
class IntegrationStatus {
  final int connectedIntegrations;
  final int totalAvailableProviders;
  final double connectionRate;
  final Map<String, SyncStatus> syncStatuses;
  final DateTime? lastSyncAt;
  final double ecosystemHealth;

  const IntegrationStatus({
    required this.connectedIntegrations,
    required this.totalAvailableProviders,
    required this.connectionRate,
    this.syncStatuses = const {},
    this.lastSyncAt,
    required this.ecosystemHealth,
  });

  String get healthLevel {
    if (ecosystemHealth >= 0.9) return 'Výborné';
    if (ecosystemHealth >= 0.7) return 'Dobré';
    if (ecosystemHealth >= 0.5) return 'Průměrné';
    return 'Potřebuje pozornost';
  }

  String get connectionLevel {
    if (connectionRate >= 0.8) return 'Plně propojené';
    if (connectionRate >= 0.5) return 'Dobře propojené';
    if (connectionRate >= 0.3) return 'Částečně propojené';
    return 'Minimálně propojené';
  }

  Map<String, dynamic> toJson() {
    return {
      'connectedIntegrations': connectedIntegrations,
      'totalAvailableProviders': totalAvailableProviders,
      'connectionRate': connectionRate,
      'syncStatuses': syncStatuses.map(
        (k, v) => MapEntry(k, {
          'isSuccess': v.isSuccess,
          'timestamp': v.timestamp.toIso8601String(),
          'errorMessage': v.errorMessage,
        }),
      ),
      'lastSyncAt': lastSyncAt?.toIso8601String(),
      'ecosystemHealth': ecosystemHealth,
    };
  }
}
