import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import '../models/emergency_services.dart';

/// 🚨 LEGÁLNÍ SLUŽBA PRO NOUZOVÉ KONTAKTY
///
/// Používá pouze oficiální a veřejně dostupné zdroje:
/// - Oficiální vládní databáze (data.gov.hr)
/// - Ministerstvo zdravotnictví (miz.hr)
/// - Chorvatský červený kříž
/// - Oficiální turistické informace
/// - Veřejné registry zdravotnických zařízení
class EmergencyServicesService {
  static final EmergencyServicesService _instance =
      EmergencyServicesService._internal();
  factory EmergencyServicesService() => _instance;
  EmergencyServicesService._internal();

  bool _isInitialized = false;

  // Cache pro offline použití
  List<EmergencyContact> _emergencyContacts = [];
  List<HealthcareProvider> _healthcareProviders = [];
  List<TouristAssistance> _touristAssistance = [];

  /// Inicializace služby s legálními daty
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🚨 Inicializuji nouzové služby...');

      // Načtení oficiálních dat
      await Future.wait([
        _loadOfficialEmergencyContacts(),
        _loadOfficialHealthcareProviders(),
        _loadOfficialTouristAssistance(),
      ]);

      _isInitialized = true;
      debugPrint('✅ Nouzové služby inicializovány');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci nouzových služeb: $e');
      // Načteme fallback data
      await _loadFallbackData();
      _isInitialized = true;
    }
  }

  /// Získání nouzových kontaktů podle lokace
  Future<List<EmergencyContact>> getNearbyEmergencyContacts({
    required double latitude,
    required double longitude,
    double radiusKm = 50.0,
    EmergencyType? type,
  }) async {
    await _ensureInitialized();

    var contacts = _emergencyContacts.where((contact) {
      // Filtrování podle typu
      if (type != null && contact.type != type) return false;

      // Filtrování podle vzdálenosti
      final distance =
          Geolocator.distanceBetween(
            latitude,
            longitude,
            contact.latitude,
            contact.longitude,
          ) /
          1000; // převod na km

      return distance <= radiusKm;
    }).toList();

    // Seřazení podle vzdálenosti
    contacts.sort((a, b) {
      final distanceA = Geolocator.distanceBetween(
        latitude,
        longitude,
        a.latitude,
        a.longitude,
      );
      final distanceB = Geolocator.distanceBetween(
        latitude,
        longitude,
        b.latitude,
        b.longitude,
      );
      return distanceA.compareTo(distanceB);
    });

    return contacts;
  }

  /// Získání zdravotnických zařízení podle lokace
  Future<List<HealthcareProvider>> getNearbyHealthcareProviders({
    required double latitude,
    required double longitude,
    double radiusKm = 25.0,
    String? type,
    bool emergencyOnly = false,
  }) async {
    await _ensureInitialized();

    var providers = _healthcareProviders.where((provider) {
      // Filtrování podle typu
      if (type != null && provider.type != type) return false;

      // Filtrování pouze pohotovosti
      if (emergencyOnly && !provider.isEmergency) return false;

      // Filtrování podle vzdálenosti
      final distance =
          Geolocator.distanceBetween(
            latitude,
            longitude,
            provider.latitude,
            provider.longitude,
          ) /
          1000;

      return distance <= radiusKm;
    }).toList();

    // Seřazení podle priority (pohotovost první) a vzdálenosti
    providers.sort((a, b) {
      if (a.isEmergency && !b.isEmergency) return -1;
      if (!a.isEmergency && b.isEmergency) return 1;

      final distanceA = Geolocator.distanceBetween(
        latitude,
        longitude,
        a.latitude,
        a.longitude,
      );
      final distanceB = Geolocator.distanceBetween(
        latitude,
        longitude,
        b.latitude,
        b.longitude,
      );
      return distanceA.compareTo(distanceB);
    });

    return providers;
  }

  /// Získání turistické asistence podle lokace
  Future<List<TouristAssistance>> getNearbyTouristAssistance({
    required double latitude,
    required double longitude,
    double radiusKm = 30.0,
    String? type,
  }) async {
    await _ensureInitialized();

    var assistance = _touristAssistance.where((assist) {
      // Filtrování podle typu
      if (type != null && assist.type != type) return false;

      // Filtrování podle vzdálenosti
      final distance =
          Geolocator.distanceBetween(
            latitude,
            longitude,
            assist.latitude,
            assist.longitude,
          ) /
          1000;

      return distance <= radiusKm;
    }).toList();

    // Seřazení podle oficiálnosti a vzdálenosti
    assistance.sort((a, b) {
      if (a.isOfficial && !b.isOfficial) return -1;
      if (!a.isOfficial && b.isOfficial) return 1;

      final distanceA = Geolocator.distanceBetween(
        latitude,
        longitude,
        a.latitude,
        a.longitude,
      );
      final distanceB = Geolocator.distanceBetween(
        latitude,
        longitude,
        b.latitude,
        b.longitude,
      );
      return distanceA.compareTo(distanceB);
    });

    return assistance;
  }

  /// Získání všech nouzových čísel pro Chorvatsko
  List<EmergencyContact> getEmergencyNumbers() {
    return [
      // Oficiální nouzová čísla Chorvatska
      EmergencyContact(
        id: 'hr_police',
        name: 'Policija',
        nameEn: 'Police',
        type: EmergencyType.police,
        phoneNumber: '192',
        address: 'Celostátní',
        addressEn: 'Nationwide',
        latitude: 45.1,
        longitude: 15.2,
        city: 'Zagreb',
        region: 'Celé Chorvatsko',
        availability: ServiceAvailability.available24h,
        languages: [LanguageSupport.croatian, LanguageSupport.english],
        description: 'Oficiální policejní linka',
        descriptionEn: 'Official police emergency line',
        isOfficial: true,
        isFree: true,
        lastUpdated: DateTime.now(),
      ),
      EmergencyContact(
        id: 'hr_fire',
        name: 'Vatrogasci',
        nameEn: 'Fire Department',
        type: EmergencyType.fire,
        phoneNumber: '193',
        address: 'Celostátní',
        addressEn: 'Nationwide',
        latitude: 45.1,
        longitude: 15.2,
        city: 'Zagreb',
        region: 'Celé Chorvatsko',
        availability: ServiceAvailability.available24h,
        languages: [LanguageSupport.croatian, LanguageSupport.english],
        description: 'Oficiální hasičská linka',
        descriptionEn: 'Official fire department emergency line',
        isOfficial: true,
        isFree: true,
        lastUpdated: DateTime.now(),
      ),
      EmergencyContact(
        id: 'hr_medical',
        name: 'Hitna medicinska pomoć',
        nameEn: 'Emergency Medical Services',
        type: EmergencyType.medical,
        phoneNumber: '194',
        address: 'Celostátní',
        addressEn: 'Nationwide',
        latitude: 45.1,
        longitude: 15.2,
        city: 'Zagreb',
        region: 'Celé Chorvatsko',
        availability: ServiceAvailability.available24h,
        languages: [LanguageSupport.croatian, LanguageSupport.english],
        description: 'Oficiální zdravotnická záchranná služba',
        descriptionEn: 'Official emergency medical services',
        isOfficial: true,
        isFree: true,
        lastUpdated: DateTime.now(),
      ),
      EmergencyContact(
        id: 'hr_sea_rescue',
        name: 'Pomorska i gorska služba spašavanja',
        nameEn: 'Sea and Mountain Rescue',
        type: EmergencyType.sea,
        phoneNumber: '195',
        address: 'Celostátní',
        addressEn: 'Nationwide',
        latitude: 45.1,
        longitude: 15.2,
        city: 'Zagreb',
        region: 'Celé Chorvatsko',
        availability: ServiceAvailability.available24h,
        languages: [LanguageSupport.croatian, LanguageSupport.english],
        description: 'Oficiální námořní a horská záchranná služba',
        descriptionEn: 'Official sea and mountain rescue service',
        isOfficial: true,
        isFree: true,
        lastUpdated: DateTime.now(),
      ),
    ];
  }

  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Načtení oficiálních nouzových kontaktů
  Future<void> _loadOfficialEmergencyContacts() async {
    // Zde by bylo volání na oficiální API
    // Pro demo používáme statická data z oficiálních zdrojů
    _emergencyContacts = getEmergencyNumbers();
  }

  /// Načtení oficiálních zdravotnických zařízení
  Future<void> _loadOfficialHealthcareProviders() async {
    // Zde by bylo volání na API Ministerstva zdravotnictví
    _healthcareProviders = [];
  }

  /// Načtení oficiální turistické asistence
  Future<void> _loadOfficialTouristAssistance() async {
    // Zde by bylo volání na API Chorvatské turistické centrály
    _touristAssistance = [];
  }

  /// Fallback data pro offline použití
  Future<void> _loadFallbackData() async {
    _emergencyContacts = getEmergencyNumbers();
    _healthcareProviders = [];
    _touristAssistance = [];
  }
}
