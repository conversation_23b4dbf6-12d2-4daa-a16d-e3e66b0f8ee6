import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:google_fonts/google_fonts.dart';

/// Služba pro správu témat a dark mode
class ThemeService extends ChangeNotifier {
  static final ThemeService _instance = ThemeService._internal();
  factory ThemeService() => _instance;
  ThemeService._internal();

  static const String _themeKey = 'theme_mode';
  static const String _accentColorKey = 'accent_color';
  static const String _fontSizeKey = 'font_size';
  static const String _animationsKey = 'animations_enabled';

  ThemeMode _themeMode = ThemeMode.system;
  Color _accentColor = const Color(0xFF006994);
  double _fontSizeScale = 1.0;
  bool _animationsEnabled = true;
  bool _isInitialized = false;

  // Gettery
  ThemeMode get themeMode => _themeMode;
  Color get accentColor => _accentColor;
  double get fontSizeScale => _fontSizeScale;
  bool get animationsEnabled => _animationsEnabled;
  bool get isInitialized => _isInitialized;
  bool get isDarkMode => _themeMode == ThemeMode.dark;
  bool get isLightMode => _themeMode == ThemeMode.light;
  bool get isSystemMode => _themeMode == ThemeMode.system;

  /// Chorvatské barevné palety
  static const Map<String, Color> croatianColors = {
    'primary': Color(0xFF006994), // Chorvatská modrá
    'secondary': Color(0xFFFF6B35), // Chorvatská oranžová
    'accent': Color(0xFF4CAF50), // Zelená
    'warning': Color(0xFFFF9800), // Oranžová
    'error': Color(0xFFE74C3C), // Červená
    'success': Color(0xFF2ECC71), // Zelená
    'info': Color(0xFF3498DB), // Modrá
    'purple': Color(0xFF9C27B0), // Fialová
  };

  /// Inicializuje theme službu
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      final prefs = await SharedPreferences.getInstance();

      // Načte uložené nastavení
      final themeModeIndex = prefs.getInt(_themeKey) ?? ThemeMode.system.index;
      _themeMode = ThemeMode.values[themeModeIndex];

      final accentColorValue =
          prefs.getInt(_accentColorKey) ?? croatianColors['primary']!.value;
      _accentColor = Color(accentColorValue);

      _fontSizeScale = prefs.getDouble(_fontSizeKey) ?? 1.0;
      _animationsEnabled = prefs.getBool(_animationsKey) ?? true;

      _isInitialized = true;
      notifyListeners();

      debugPrint(
        'Theme služba inicializována - Mode: $_themeMode, Accent: $_accentColor',
      );
    } catch (e) {
      debugPrint('Chyba při inicializaci theme služby: $e');
      _isInitialized = true;
    }
  }

  /// Změní theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode == mode) return;

    try {
      _themeMode = mode;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_themeKey, mode.index);

      // Aktualizuje system UI overlay
      _updateSystemUIOverlay();

      notifyListeners();
      debugPrint('Theme mode změněn na: $mode');
    } catch (e) {
      debugPrint('Chyba při změně theme mode: $e');
    }
  }

  /// Změní accent color
  Future<void> setAccentColor(Color color) async {
    if (_accentColor == color) return;

    try {
      _accentColor = color;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_accentColorKey, color.value);

      notifyListeners();
      debugPrint('Accent color změněna na: $color');
    } catch (e) {
      debugPrint('Chyba při změně accent color: $e');
    }
  }

  /// Změní velikost fontu
  Future<void> setFontSizeScale(double scale) async {
    if (_fontSizeScale == scale) return;

    try {
      _fontSizeScale = scale.clamp(0.8, 1.4);

      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(_fontSizeKey, _fontSizeScale);

      notifyListeners();
      debugPrint('Font size scale změněna na: $_fontSizeScale');
    } catch (e) {
      debugPrint('Chyba při změně font size: $e');
    }
  }

  /// Zapne/vypne animace
  Future<void> setAnimationsEnabled(bool enabled) async {
    if (_animationsEnabled == enabled) return;

    try {
      _animationsEnabled = enabled;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_animationsKey, enabled);

      notifyListeners();
      debugPrint('Animace ${enabled ? 'zapnuty' : 'vypnuty'}');
    } catch (e) {
      debugPrint('Chyba při změně animací: $e');
    }
  }

  /// Vytvoří light theme
  ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: ColorScheme.fromSeed(
        seedColor: _accentColor,
        brightness: Brightness.light,
      ),
      textTheme: _buildTextTheme(Brightness.light),
      appBarTheme: _buildAppBarTheme(Brightness.light),
      cardTheme: _buildCardTheme(Brightness.light),
      elevatedButtonTheme: _buildElevatedButtonTheme(Brightness.light),
      floatingActionButtonTheme: _buildFABTheme(Brightness.light),
      bottomNavigationBarTheme: _buildBottomNavTheme(Brightness.light),
      inputDecorationTheme: _buildInputTheme(Brightness.light),
      dividerTheme: _buildDividerTheme(Brightness.light),
      scaffoldBackgroundColor: const Color(0xFFF8F9FA),
    );
  }

  /// Vytvoří dark theme
  ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: ColorScheme.fromSeed(
        seedColor: _accentColor,
        brightness: Brightness.dark,
      ),
      textTheme: _buildTextTheme(Brightness.dark),
      appBarTheme: _buildAppBarTheme(Brightness.dark),
      cardTheme: _buildCardTheme(Brightness.dark),
      elevatedButtonTheme: _buildElevatedButtonTheme(Brightness.dark),
      floatingActionButtonTheme: _buildFABTheme(Brightness.dark),
      bottomNavigationBarTheme: _buildBottomNavTheme(Brightness.dark),
      inputDecorationTheme: _buildInputTheme(Brightness.dark),
      dividerTheme: _buildDividerTheme(Brightness.dark),
      scaffoldBackgroundColor: const Color(0xFF121212),
    );
  }

  /// Vytvoří text theme s Google Fonts
  TextTheme _buildTextTheme(Brightness brightness) {
    final baseTheme = brightness == Brightness.light
        ? ThemeData.light().textTheme
        : ThemeData.dark().textTheme;

    return GoogleFonts.interTextTheme(baseTheme).copyWith(
      displayLarge: GoogleFonts.inter(
        fontSize: 32 * _fontSizeScale,
        fontWeight: FontWeight.w700,
        color: brightness == Brightness.light
            ? const Color(0xFF2C3E50)
            : Colors.white,
      ),
      displayMedium: GoogleFonts.inter(
        fontSize: 28 * _fontSizeScale,
        fontWeight: FontWeight.w600,
        color: brightness == Brightness.light
            ? const Color(0xFF2C3E50)
            : Colors.white,
      ),
      displaySmall: GoogleFonts.inter(
        fontSize: 24 * _fontSizeScale,
        fontWeight: FontWeight.w600,
        color: brightness == Brightness.light
            ? const Color(0xFF2C3E50)
            : Colors.white,
      ),
      headlineLarge: GoogleFonts.inter(
        fontSize: 20 * _fontSizeScale,
        fontWeight: FontWeight.w600,
        color: brightness == Brightness.light
            ? const Color(0xFF2C3E50)
            : Colors.white,
      ),
      bodyLarge: GoogleFonts.inter(
        fontSize: 16 * _fontSizeScale,
        fontWeight: FontWeight.w400,
        color: brightness == Brightness.light
            ? const Color(0xFF2C3E50)
            : Colors.white70,
      ),
      bodyMedium: GoogleFonts.inter(
        fontSize: 14 * _fontSizeScale,
        fontWeight: FontWeight.w400,
        color: brightness == Brightness.light
            ? Colors.grey[600]
            : Colors.white60,
      ),
    );
  }

  /// Vytvoří AppBar theme
  AppBarTheme _buildAppBarTheme(Brightness brightness) {
    return AppBarTheme(
      elevation: 0,
      centerTitle: true,
      backgroundColor: brightness == Brightness.light
          ? Colors.white
          : const Color(0xFF1E1E1E),
      foregroundColor: brightness == Brightness.light
          ? const Color(0xFF2C3E50)
          : Colors.white,
      titleTextStyle: GoogleFonts.inter(
        fontSize: 20 * _fontSizeScale,
        fontWeight: FontWeight.w600,
        color: brightness == Brightness.light
            ? const Color(0xFF2C3E50)
            : Colors.white,
      ),
      iconTheme: IconThemeData(
        color: brightness == Brightness.light
            ? const Color(0xFF2C3E50)
            : Colors.white,
      ),
    );
  }

  /// Vytvoří Card theme
  CardThemeData _buildCardTheme(Brightness brightness) {
    return CardThemeData(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: brightness == Brightness.light
          ? Colors.white
          : const Color(0xFF2C2C2C),
    );
  }

  /// Vytvoří ElevatedButton theme
  ElevatedButtonThemeData _buildElevatedButtonTheme(Brightness brightness) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: _accentColor,
        foregroundColor: Colors.white,
        elevation: 2,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        textStyle: GoogleFonts.inter(
          fontSize: 14 * _fontSizeScale,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// Vytvoří FloatingActionButton theme
  FloatingActionButtonThemeData _buildFABTheme(Brightness brightness) {
    return FloatingActionButtonThemeData(
      backgroundColor: _accentColor,
      foregroundColor: Colors.white,
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
    );
  }

  /// Vytvoří BottomNavigationBar theme
  BottomNavigationBarThemeData _buildBottomNavTheme(Brightness brightness) {
    return BottomNavigationBarThemeData(
      backgroundColor: brightness == Brightness.light
          ? Colors.white
          : const Color(0xFF1E1E1E),
      selectedItemColor: _accentColor,
      unselectedItemColor: brightness == Brightness.light
          ? Colors.grey[600]
          : Colors.grey[400],
      elevation: 8,
      type: BottomNavigationBarType.fixed,
      selectedLabelStyle: GoogleFonts.inter(
        fontSize: 12 * _fontSizeScale,
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: GoogleFonts.inter(
        fontSize: 12 * _fontSizeScale,
        fontWeight: FontWeight.w400,
      ),
    );
  }

  /// Vytvoří InputDecoration theme
  InputDecorationTheme _buildInputTheme(Brightness brightness) {
    return InputDecorationTheme(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: Colors.grey[300]!),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: Colors.grey[300]!),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: _accentColor, width: 2),
      ),
      filled: true,
      fillColor: brightness == Brightness.light
          ? Colors.grey[50]
          : const Color(0xFF2C2C2C),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      labelStyle: GoogleFonts.inter(
        fontSize: 14 * _fontSizeScale,
        color: brightness == Brightness.light
            ? Colors.grey[600]
            : Colors.grey[400],
      ),
    );
  }

  /// Vytvoří Divider theme
  DividerThemeData _buildDividerTheme(Brightness brightness) {
    return DividerThemeData(
      color: brightness == Brightness.light
          ? Colors.grey[200]
          : Colors.grey[700],
      thickness: 1,
      space: 1,
    );
  }

  /// Aktualizuje system UI overlay
  void _updateSystemUIOverlay() {
    final isDark = _themeMode == ThemeMode.dark;

    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
        systemNavigationBarColor: isDark
            ? const Color(0xFF1E1E1E)
            : Colors.white,
        systemNavigationBarIconBrightness: isDark
            ? Brightness.light
            : Brightness.dark,
      ),
    );
  }

  /// Získá název theme mode v češtině
  String get themeModeDisplayName {
    switch (_themeMode) {
      case ThemeMode.light:
        return 'Světlý';
      case ThemeMode.dark:
        return 'Tmavý';
      case ThemeMode.system:
        return 'Systémový';
    }
  }

  /// Získá název font size v češtině
  String get fontSizeDisplayName {
    if (_fontSizeScale <= 0.8) return 'Malý';
    if (_fontSizeScale <= 0.9) return 'Menší';
    if (_fontSizeScale <= 1.1) return 'Normální';
    if (_fontSizeScale <= 1.2) return 'Větší';
    return 'Velký';
  }

  /// Resetuje na výchozí nastavení
  Future<void> resetToDefaults() async {
    try {
      await setThemeMode(ThemeMode.system);
      await setAccentColor(croatianColors['primary']!);
      await setFontSizeScale(1.0);
      await setAnimationsEnabled(true);

      debugPrint('Theme nastavení resetováno na výchozí');
    } catch (e) {
      debugPrint('Chyba při resetování theme: $e');
    }
  }
}
