class AudioGuideTrack {
  final String id;
  final String title;
  final String description;
  final String audioUrl;
  final Duration duration;
  final String language;
  final String narrator;
  final String? placeId;
  final AudioCategory category;
  final String transcript;
  final List<AudioKeyPoint> keyPoints;
  final bool isDownloaded;
  final DateTime? downloadedAt;
  final int? fileSize;

  const AudioGuideTrack({
    required this.id,
    required this.title,
    required this.description,
    required this.audioUrl,
    required this.duration,
    required this.language,
    required this.narrator,
    this.placeId,
    required this.category,
    required this.transcript,
    required this.keyPoints,
    this.isDownloaded = false,
    this.downloadedAt,
    this.fileSize,
  });

  AudioGuideTrack copyWith({
    String? id,
    String? title,
    String? description,
    String? audioUrl,
    Duration? duration,
    String? language,
    String? narrator,
    String? placeId,
    AudioCategory? category,
    String? transcript,
    List<AudioKeyPoint>? keyPoints,
    bool? isDownloaded,
    DateTime? downloadedAt,
    int? fileSize,
  }) {
    return AudioGuideTrack(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      audioUrl: audioUrl ?? this.audioUrl,
      duration: duration ?? this.duration,
      language: language ?? this.language,
      narrator: narrator ?? this.narrator,
      placeId: placeId ?? this.placeId,
      category: category ?? this.category,
      transcript: transcript ?? this.transcript,
      keyPoints: keyPoints ?? this.keyPoints,
      isDownloaded: isDownloaded ?? this.isDownloaded,
      downloadedAt: downloadedAt ?? this.downloadedAt,
      fileSize: fileSize ?? this.fileSize,
    );
  }
}

class AudioKeyPoint {
  final Duration timestamp;
  final String title;
  final String description;
  final String? imageUrl;
  final Map<String, dynamic>? additionalInfo;

  const AudioKeyPoint({
    required this.timestamp,
    required this.title,
    required this.description,
    this.imageUrl,
    this.additionalInfo,
  });
}

class AudioGuidePoint {
  final String id;
  final String name;
  final double latitude;
  final double longitude;
  final double radius;
  final String trackId;
  final bool autoPlay;
  final AudioPriority priority;
  final String? description;
  final String? imageUrl;

  const AudioGuidePoint({
    required this.id,
    required this.name,
    required this.latitude,
    required this.longitude,
    required this.radius,
    required this.trackId,
    required this.autoPlay,
    required this.priority,
    this.description,
    this.imageUrl,
  });
}

class AudioGuideEvent {
  final AudioEventType type;
  final AudioGuideTrack? track;
  final AudioGuidePoint? point;
  final AudioKeyPoint? keyPoint;
  final Duration? position;
  final Duration? duration;
  final double? playbackSpeed;
  final String? error;
  final DateTime timestamp;

  AudioGuideEvent({
    required this.type,
    this.track,
    this.point,
    this.keyPoint,
    this.position,
    this.duration,
    this.playbackSpeed,
    this.error,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();
}

class AudioGuideState {
  final bool isPlaying;
  final AudioGuideTrack? currentTrack;
  final Duration position;
  final Duration duration;
  final double playbackSpeed;
  final bool autoPlayEnabled;

  const AudioGuideState({
    required this.isPlaying,
    this.currentTrack,
    required this.position,
    required this.duration,
    required this.playbackSpeed,
    required this.autoPlayEnabled,
  });
}

class AudioPlaylist {
  final String id;
  final String name;
  final String description;
  final List<String> trackIds;
  final String? imageUrl;
  final Duration totalDuration;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isPublic;
  final String? authorId;

  const AudioPlaylist({
    required this.id,
    required this.name,
    required this.description,
    required this.trackIds,
    this.imageUrl,
    required this.totalDuration,
    required this.createdAt,
    required this.updatedAt,
    required this.isPublic,
    this.authorId,
  });
}

enum AudioCategory {
  introduction,
  detailedTour,
  history,
  architecture,
  legends,
  nature,
  culture,
  practical,
}

enum AudioPriority {
  low,
  medium,
  high,
  critical,
}

enum AudioEventType {
  playbackStarted,
  playbackPaused,
  playbackResumed,
  playbackStopped,
  playbackFinished,
  positionChanged,
  speedChanged,
  keyPointReached,
  autoPlayTriggered,
  downloadStarted,
  downloadCompleted,
  downloadFailed,
  error,
}

extension AudioCategoryExtension on AudioCategory {
  String get displayName {
    switch (this) {
      case AudioCategory.introduction:
        return 'Úvod';
      case AudioCategory.detailedTour:
        return 'Detailní prohlídka';
      case AudioCategory.history:
        return 'Historie';
      case AudioCategory.architecture:
        return 'Architektura';
      case AudioCategory.legends:
        return 'Legendy';
      case AudioCategory.nature:
        return 'Příroda';
      case AudioCategory.culture:
        return 'Kultura';
      case AudioCategory.practical:
        return 'Praktické info';
    }
  }

  String get icon {
    switch (this) {
      case AudioCategory.introduction:
        return '👋';
      case AudioCategory.detailedTour:
        return '🎯';
      case AudioCategory.history:
        return '📜';
      case AudioCategory.architecture:
        return '🏛️';
      case AudioCategory.legends:
        return '🐉';
      case AudioCategory.nature:
        return '🌿';
      case AudioCategory.culture:
        return '🎭';
      case AudioCategory.practical:
        return 'ℹ️';
    }
  }
}

// Předdefinované playlisty
class AudioPlaylists {
  static final List<AudioPlaylist> defaultPlaylists = [
    AudioPlaylist(
      id: 'unesco_tour',
      name: 'UNESCO místa Chorvatska',
      description: 'Audio průvodce všemi místy UNESCO v Chorvatsku',
      trackIds: [
        'dubrovnik_walls_intro',
        'diocletian_palace_tour',
        'plitvice_nature_guide',
        'euphrasian_basilica_tour',
      ],
      totalDuration: const Duration(hours: 2, minutes: 30),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isPublic: true,
    ),
    
    AudioPlaylist(
      id: 'roman_heritage',
      name: 'Římské dědictví',
      description: 'Objevte římskou historii Chorvatska',
      trackIds: [
        'diocletian_palace_tour',
        'pula_arena_guide',
        'salona_ruins_tour',
      ],
      totalDuration: const Duration(hours: 1, minutes: 45),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isPublic: true,
    ),
    
    AudioPlaylist(
      id: 'nature_sounds',
      name: 'Přírodní krásy',
      description: 'Audio průvodce národními parky',
      trackIds: [
        'plitvice_nature_guide',
        'krka_waterfalls_tour',
        'kornati_islands_guide',
      ],
      totalDuration: const Duration(hours: 2),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isPublic: true,
    ),
  ];
}
