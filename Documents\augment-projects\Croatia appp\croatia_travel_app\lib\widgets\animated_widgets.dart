import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:shimmer/shimmer.dart';
import 'package:google_fonts/google_fonts.dart';

/// Animovaný loading indikátor s chorvatskými barvami
class CroatianLoadingIndicator extends StatelessWidget {
  final double size;
  final String? message;

  const CroatianLoadingIndicator({super.key, this.size = 60, this.message});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: Stack(
            children: [
              // Vnějš<PERSON> kruh - chorvatská modrá
              Container(
                    width: size,
                    height: size,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: const Color(0xFF006994).withValues(alpha: 0.3),
                        width: 3,
                      ),
                    ),
                  )
                  .animate(onPlay: (controller) => controller.repeat())
                  .rotate(duration: 2000.ms),

              // Vnit<PERSON><PERSON><PERSON> kruh - chorvatsk<PERSON> oranžová
              Positioned(
                top: size * 0.15,
                left: size * 0.15,
                child:
                    Container(
                          width: size * 0.7,
                          height: size * 0.7,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: const Color(
                                0xFFFF6B35,
                              ).withValues(alpha: 0.6),
                              width: 2,
                            ),
                          ),
                        )
                        .animate(onPlay: (controller) => controller.repeat())
                        .rotate(duration: 1500.ms, begin: 1, end: 0),
              ),

              // Střední bod
              Positioned(
                top: size * 0.4,
                left: size * 0.4,
                child:
                    Container(
                          width: size * 0.2,
                          height: size * 0.2,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: Color(0xFF4CAF50),
                          ),
                        )
                        .animate(onPlay: (controller) => controller.repeat())
                        .scale(
                          duration: 1000.ms,
                          begin: const Offset(0.8, 0.8),
                          end: const Offset(1.2, 1.2),
                        )
                        .then()
                        .scale(
                          duration: 1000.ms,
                          begin: const Offset(1.2, 1.2),
                          end: const Offset(0.8, 0.8),
                        ),
              ),
            ],
          ),
        ),
        if (message != null) ...[
          const SizedBox(height: 16),
          Text(
            message!,
            style: GoogleFonts.inter(fontSize: 14, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ).animate().fadeIn(duration: 500.ms).slideY(begin: 0.3, end: 0),
        ],
      ],
    );
  }
}

/// Shimmer loading efekt pro karty
class ShimmerCard extends StatelessWidget {
  final double height;
  final double width;
  final BorderRadius? borderRadius;

  const ShimmerCard({
    super.key,
    this.height = 120,
    this.width = double.infinity,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: borderRadius ?? BorderRadius.circular(12),
        ),
      ),
    );
  }
}

/// Shimmer loading pro seznam
class ShimmerList extends StatelessWidget {
  final int itemCount;
  final double itemHeight;

  const ShimmerList({super.key, this.itemCount = 5, this.itemHeight = 80});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: itemCount,
      padding: const EdgeInsets.all(16),
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: ShimmerCard(height: itemHeight),
        );
      },
    );
  }
}

/// Animovaný success indikátor
class SuccessIndicator extends StatelessWidget {
  final String message;
  final VoidCallback? onComplete;

  const SuccessIndicator({super.key, required this.message, this.onComplete});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
              ),
              child: const Icon(
                Icons.check_circle,
                size: 48,
                color: Color(0xFF4CAF50),
              ),
            )
            .animate()
            .scale(duration: 300.ms, curve: Curves.elasticOut)
            .then()
            .shake(duration: 200.ms),

        const SizedBox(height: 16),

        Text(
              message,
              style: GoogleFonts.inter(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF2C3E50),
              ),
              textAlign: TextAlign.center,
            )
            .animate(delay: 200.ms)
            .fadeIn(duration: 300.ms)
            .slideY(begin: 0.3, end: 0),
      ],
    );
  }
}

/// Animovaný error indikátor
class ErrorIndicator extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;

  const ErrorIndicator({super.key, required this.message, this.onRetry});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: const Color(0xFFE74C3C).withValues(alpha: 0.1),
              ),
              child: const Icon(
                Icons.error_outline,
                size: 48,
                color: Color(0xFFE74C3C),
              ),
            )
            .animate()
            .scale(duration: 300.ms)
            .then()
            .shake(duration: 400.ms, hz: 4),

        const SizedBox(height: 16),

        Text(
              message,
              style: GoogleFonts.inter(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF2C3E50),
              ),
              textAlign: TextAlign.center,
            )
            .animate(delay: 200.ms)
            .fadeIn(duration: 300.ms)
            .slideY(begin: 0.3, end: 0),

        if (onRetry != null) ...[
          const SizedBox(height: 16),
          ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Zkusit znovu'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF006994),
                  foregroundColor: Colors.white,
                ),
              )
              .animate(delay: 400.ms)
              .fadeIn(duration: 300.ms)
              .scale(begin: const Offset(0.8, 0.8), end: const Offset(1, 1)),
        ],
      ],
    );
  }
}

/// Animovaná karta s hover efektem
class AnimatedCard extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final double elevation;

  const AnimatedCard({
    super.key,
    required this.child,
    this.onTap,
    this.padding,
    this.borderRadius,
    this.backgroundColor,
    this.elevation = 2,
  });

  @override
  State<AnimatedCard> createState() => _AnimatedCardState();
}

class _AnimatedCardState extends State<AnimatedCard> {
  bool _isHovered = false;
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      onTapDown: (_) => setState(() => _isPressed = true),
      onTapUp: (_) => setState(() => _isPressed = false),
      onTapCancel: () => setState(() => _isPressed = false),
      child: MouseRegion(
        onEnter: (_) => setState(() => _isHovered = true),
        onExit: (_) => setState(() => _isHovered = false),
        child:
            Card(
                  elevation: _isHovered
                      ? widget.elevation + 2
                      : widget.elevation,
                  color: widget.backgroundColor,
                  shape: RoundedRectangleBorder(
                    borderRadius:
                        widget.borderRadius ?? BorderRadius.circular(12),
                  ),
                  child: Container(
                    padding: widget.padding ?? const EdgeInsets.all(16),
                    child: widget.child,
                  ),
                )
                .animate(target: _isHovered ? 1 : 0)
                .scale(end: const Offset(1.02, 1.02), duration: 200.ms)
                .animate(target: _isPressed ? 1 : 0)
                .scale(end: const Offset(0.98, 0.98), duration: 100.ms),
      ),
    );
  }
}

/// Animovaný floating action button
class AnimatedFAB extends StatefulWidget {
  final VoidCallback? onPressed;
  final IconData icon;
  final String? tooltip;
  final Color? backgroundColor;

  const AnimatedFAB({
    super.key,
    required this.onPressed,
    required this.icon,
    this.tooltip,
    this.backgroundColor,
  });

  @override
  State<AnimatedFAB> createState() => _AnimatedFABState();
}

class _AnimatedFABState extends State<AnimatedFAB>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotateController;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _rotateController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
          onPressed: widget.onPressed != null
              ? () {
                  _rotateController.forward().then((_) {
                    _rotateController.reverse();
                  });
                  widget.onPressed!();
                }
              : null,
          tooltip: widget.tooltip,
          backgroundColor: widget.backgroundColor ?? const Color(0xFF006994),
          child: AnimatedBuilder(
            animation: _rotateController,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotateController.value * 0.5,
                child: Icon(widget.icon, color: Colors.white),
              );
            },
          ),
        )
        .animate()
        .fadeIn(duration: 500.ms)
        .scale(
          begin: const Offset(0, 0),
          end: const Offset(1, 1),
          curve: Curves.elasticOut,
        );
  }
}

/// Animovaný progress indikátor
class AnimatedProgressIndicator extends StatelessWidget {
  final double progress;
  final String? label;
  final Color? color;

  const AnimatedProgressIndicator({
    super.key,
    required this.progress,
    this.label,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (label != null) ...[
          Text(
            label!,
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 8),
        ],

        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(
              color ?? const Color(0xFF006994),
            ),
            minHeight: 8,
          ),
        ).animate().fadeIn(duration: 300.ms).slideX(begin: -1, end: 0),

        const SizedBox(height: 4),

        Text(
          '${(progress * 100).toInt()}%',
          style: GoogleFonts.inter(fontSize: 12, color: Colors.grey[600]),
        ).animate(delay: 200.ms).fadeIn(duration: 300.ms),
      ],
    );
  }
}

/// Animovaný badge
class AnimatedBadge extends StatelessWidget {
  final String text;
  final Color? backgroundColor;
  final Color? textColor;

  const AnimatedBadge({
    super.key,
    required this.text,
    this.backgroundColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: backgroundColor ?? const Color(0xFF006994),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            text,
            style: GoogleFonts.inter(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: textColor ?? Colors.white,
            ),
          ),
        )
        .animate()
        .fadeIn(duration: 300.ms)
        .scale(
          begin: const Offset(0, 0),
          end: const Offset(1, 1),
          curve: Curves.elasticOut,
        );
  }
}

/// Animovaný empty state
class AnimatedEmptyState extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final Widget? action;

  const AnimatedEmptyState({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
    this.action,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 80, color: Colors.grey[400])
                .animate()
                .fadeIn(duration: 500.ms)
                .scale(
                  begin: const Offset(0, 0),
                  end: const Offset(1, 1),
                  curve: Curves.elasticOut,
                ),

            const SizedBox(height: 24),

            Text(
                  title,
                  style: GoogleFonts.inter(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                )
                .animate(delay: 200.ms)
                .fadeIn(duration: 500.ms)
                .slideY(begin: 0.3, end: 0),

            const SizedBox(height: 8),

            Text(
                  subtitle,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                  textAlign: TextAlign.center,
                )
                .animate(delay: 400.ms)
                .fadeIn(duration: 500.ms)
                .slideY(begin: 0.3, end: 0),

            if (action != null) ...[
              const SizedBox(height: 24),
              action!
                  .animate(delay: 600.ms)
                  .fadeIn(duration: 500.ms)
                  .scale(
                    begin: const Offset(0.8, 0.8),
                    end: const Offset(1, 1),
                  ),
            ],
          ],
        ),
      ),
    );
  }
}
