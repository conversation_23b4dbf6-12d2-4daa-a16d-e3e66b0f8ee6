import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../services/personalization_service.dart';
import '../services/firebase_service.dart';
import '../models/user_models.dart';

/// Obrazovka pro personalizaci a doporučení
class PersonalizationScreen extends StatefulWidget {
  const PersonalizationScreen({super.key});

  @override
  State<PersonalizationScreen> createState() => _PersonalizationScreenState();
}

class _PersonalizationScreenState extends State<PersonalizationScreen> with TickerProviderStateMixin {
  final PersonalizationService _personalizationService = PersonalizationService();
  final FirebaseService _firebaseService = FirebaseService();
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeServices();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeServices() async {
    try {
      await _personalizationService.initialize();
      await _firebaseService.initialize();
      setState(() {});
    } catch (e) {
      debugPrint('Chyba při inicializaci služeb: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: Text(
          'Personalizace',
          style: GoogleFonts.inter(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF2C3E50),
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: Color(0xFF2C3E50)),
        bottom: TabBar(
          controller: _tabController,
          labelColor: const Color(0xFF006994),
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: const Color(0xFF006994),
          labelStyle: GoogleFonts.inter(fontWeight: FontWeight.w600),
          tabs: const [
            Tab(text: 'Profil'),
            Tab(text: 'Doporučení'),
            Tab(text: 'Itineráře'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildProfileTab(),
          _buildRecommendationsTab(),
          _buildItinerariesTab(),
        ],
      ),
    );
  }

  Widget _buildProfileTab() {
    return ListenableBuilder(
      listenable: _personalizationService,
      builder: (context, child) {
        final profile = _personalizationService.userProfile;
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildProfileHeader(profile),
              const SizedBox(height: 24),
              _buildTravelPreferences(profile),
              const SizedBox(height: 24),
              _buildPersonalInfo(profile),
              const SizedBox(height: 24),
              _buildWishlistSection(profile),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProfileHeader(UserProfile? profile) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            CircleAvatar(
              radius: 40,
              backgroundColor: const Color(0xFF006994).withValues(alpha: 0.1),
              backgroundImage: profile?.photoUrl != null 
                  ? NetworkImage(profile!.photoUrl!) 
                  : null,
              child: profile?.photoUrl == null
                  ? Icon(
                      Icons.person,
                      size: 40,
                      color: const Color(0xFF006994),
                    )
                  : null,
            ),
            const SizedBox(height: 16),
            Text(
              profile?.displayName ?? 'Cestovatel',
              style: GoogleFonts.inter(
                fontSize: 20,
                fontWeight: FontWeight.w700,
                color: const Color(0xFF2C3E50),
              ),
            ),
            if (profile?.email != null) ...[
              const SizedBox(height: 4),
              Text(
                profile!.email!,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildProfileStat(
                  'Navštíveno',
                  profile?.visitedPlaces.length.toString() ?? '0',
                  Icons.location_on,
                ),
                _buildProfileStat(
                  'Wishlist',
                  profile?.wishlist.length.toString() ?? '0',
                  Icons.favorite,
                ),
                _buildProfileStat(
                  'Itineráře',
                  _personalizationService.itineraries.length.toString(),
                  Icons.map,
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (profile == null || !profile.isComplete)
              ElevatedButton(
                onPressed: _showProfileSetup,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF006994),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                ),
                child: Text(
                  profile == null ? 'Vytvořit profil' : 'Dokončit profil',
                  style: GoogleFonts.inter(fontWeight: FontWeight.w600),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileStat(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: const Color(0xFF006994), size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.inter(
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: const Color(0xFF2C3E50),
          ),
        ),
        Text(
          label,
          style: GoogleFonts.inter(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildTravelPreferences(UserProfile? profile) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.favorite, color: const Color(0xFF006994)),
                const SizedBox(width: 8),
                Text(
                  'Cestovní preference',
                  style: GoogleFonts.inter(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF2C3E50),
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _editPreferences,
                  child: Text('Upravit'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (profile?.travelPreferences.isNotEmpty == true)
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: profile!.travelPreferences.map((type) {
                  return Chip(
                    label: Text(
                      type.czechName,
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                    backgroundColor: const Color(0xFF006994),
                    side: BorderSide.none,
                  );
                }).toList(),
              )
            else
              Text(
                'Žádné preference nastaveny',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPersonalInfo(UserProfile? profile) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: const Color(0xFF006994)),
                const SizedBox(width: 8),
                Text(
                  'Osobní informace',
                  style: GoogleFonts.inter(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF2C3E50),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Věková skupina', profile?.ageGroupName ?? 'Neuvedeno'),
            _buildInfoRow('Rozpočet', profile?.budgetCategoryName ?? 'Neuvedeno'),
            _buildInfoRow('Jazyky', profile?.languages.join(', ') ?? 'Čeština'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: GoogleFonts.inter(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF2C3E50),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWishlistSection(UserProfile? profile) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.bookmark, color: const Color(0xFF006994)),
                const SizedBox(width: 8),
                Text(
                  'Wishlist',
                  style: GoogleFonts.inter(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF2C3E50),
                  ),
                ),
                const Spacer(),
                Text(
                  '${profile?.wishlist.length ?? 0} míst',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (profile?.wishlist.isNotEmpty == true)
              Text(
                'Máte ${profile!.wishlist.length} míst v wishlistu',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              )
            else
              Text(
                'Žádná místa v wishlistu',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendationsTab() {
    return ListenableBuilder(
      listenable: _personalizationService,
      builder: (context, child) {
        final recommendations = _personalizationService.recommendations;
        
        if (recommendations.isEmpty) {
          return _buildEmptyRecommendations();
        }
        
        return ListView.builder(
          padding: const EdgeInsets.all(20),
          itemCount: recommendations.length,
          itemBuilder: (context, index) {
            final recommendation = recommendations[index];
            return _buildRecommendationCard(recommendation);
          },
        );
      },
    );
  }

  Widget _buildRecommendationCard(PersonalizedRecommendation recommendation) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: const Color(0xFF006994).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    recommendation.scorePercentage,
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF006994),
                    ),
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: Icon(
                    recommendation.isLiked ? Icons.favorite : Icons.favorite_border,
                    color: recommendation.isLiked ? Colors.red : Colors.grey,
                  ),
                  onPressed: () {
                    _personalizationService.toggleRecommendationLike(recommendation.id);
                  },
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              recommendation.title,
              style: GoogleFonts.inter(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF2C3E50),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              recommendation.description,
              style: GoogleFonts.inter(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 12),
            Text(
              recommendation.primaryReason,
              style: GoogleFonts.inter(
                fontSize: 12,
                fontStyle: FontStyle.italic,
                color: const Color(0xFF006994),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItinerariesTab() {
    return ListenableBuilder(
      listenable: _personalizationService,
      builder: (context, child) {
        final itineraries = _personalizationService.itineraries;
        
        if (itineraries.isEmpty) {
          return _buildEmptyItineraries();
        }
        
        return ListView.builder(
          padding: const EdgeInsets.all(20),
          itemCount: itineraries.length,
          itemBuilder: (context, index) {
            final itinerary = itineraries[index];
            return _buildItineraryCard(itinerary);
          },
        );
      },
    );
  }

  Widget _buildItineraryCard(TravelItinerary itinerary) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    itinerary.title,
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF2C3E50),
                    ),
                  ),
                ),
                if (itinerary.isGenerated)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: const Color(0xFF9C27B0).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'AI',
                      style: GoogleFonts.inter(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF9C27B0),
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              itinerary.description,
              style: GoogleFonts.inter(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  '${itinerary.dayCount} dní',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(width: 16),
                Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  itinerary.isCurrent ? 'Aktuální' : 
                  itinerary.isFuture ? 'Plánovaný' : 'Dokončený',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyRecommendations() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.lightbulb_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Žádná doporučení',
            style: GoogleFonts.inter(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Dokončete svůj profil pro personalizovaná doporučení',
            style: GoogleFonts.inter(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _showProfileSetup,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF006994),
              foregroundColor: Colors.white,
            ),
            child: Text('Nastavit profil'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyItineraries() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.map_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Žádné itineráře',
            style: GoogleFonts.inter(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Vytvořte svůj první cestovní itinerář',
            style: GoogleFonts.inter(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _createItinerary,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF006994),
              foregroundColor: Colors.white,
            ),
            child: Text('Vytvořit itinerář'),
          ),
        ],
      ),
    );
  }

  void _showProfileSetup() {
    // TODO: Implementovat setup profilu
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Nastavení profilu'),
        content: Text('Funkce nastavení profilu bude implementována.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  void _editPreferences() {
    // TODO: Implementovat editaci preferencí
  }

  void _createItinerary() {
    // TODO: Implementovat vytvoření itineráře
  }
}
