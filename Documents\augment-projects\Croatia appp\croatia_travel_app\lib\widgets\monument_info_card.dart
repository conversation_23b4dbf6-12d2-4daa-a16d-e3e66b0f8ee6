import 'package:flutter/material.dart';
import '../models/monument.dart';

class MonumentInfoCard extends StatelessWidget {
  final MonumentInfo monument;
  final double confidence;
  final VoidCallback onShowDetails;
  final VoidCallback onShowAR;

  const MonumentInfoCard({
    super.key,
    required this.monument,
    required this.confidence,
    required this.onShowDetails,
    required this.onShowAR,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 8,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Colors.blue.shade50, Colors.white],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(),
              const SizedBox(height: 12),
              _buildDescription(),
              const SizedBox(height: 12),
              _buildInfoRow(),
              const SizedBox(height: 16),
              _buildActionButtons(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _getCategoryColor().withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            monument.category.icon,
            style: const TextStyle(fontSize: 24),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                monument.name,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: monument.category == MonumentCategory.naturalSite
                          ? Colors.green
                          : monument.category == MonumentCategory.fortress
                          ? Colors.red
                          : Colors.blue,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      monument.category.displayName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  _buildConfidenceBadge(),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildConfidenceBadge() {
    final color = _getConfidenceColor();
    final text = '${(confidence * 100).round()}%';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            confidence > 0.8 ? Icons.check_circle : Icons.info,
            color: Colors.white,
            size: 12,
          ),
          const SizedBox(width: 2),
          Text(
            text,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescription() {
    return Text(
      monument.description,
      style: const TextStyle(fontSize: 14, color: Colors.grey, height: 1.4),
      maxLines: 3,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildInfoRow() {
    return Row(
      children: [
        Expanded(
          child: _buildInfoItem(
            icon: Icons.access_time,
            label: 'Otevřeno',
            value: _getOpeningStatus(),
            color: _getOpeningStatusColor(),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildInfoItem(
            icon: Icons.euro,
            label: 'Vstupné',
            value: monument.ticketPrice > 0
                ? '${monument.ticketPrice.round()} HRK'
                : 'Zdarma',
            color: monument.ticketPrice > 0 ? Colors.orange : Colors.green,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildInfoItem(
            icon: Icons.location_on,
            label: 'Region',
            value: _getRegionName(),
            color: Colors.blue,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 14, color: color),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: color,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: onShowDetails,
            icon: const Icon(Icons.info_outline, size: 16),
            label: const Text('Detail'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 8),
              side: BorderSide(color: Colors.blue.shade300),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: onShowAR,
            icon: const Icon(Icons.view_in_ar, size: 16),
            label: const Text('AR režim'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 8),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: IconButton(
            onPressed: () => _showShareOptions(context),
            icon: const Icon(Icons.share, size: 20),
            padding: const EdgeInsets.all(8),
            constraints: const BoxConstraints(minWidth: 36, minHeight: 36),
          ),
        ),
      ],
    );
  }

  Color _getCategoryColor() {
    switch (monument.category) {
      case MonumentCategory.fortress:
        return Colors.red;
      case MonumentCategory.palace:
        return Colors.purple;
      case MonumentCategory.church:
        return Colors.brown;
      case MonumentCategory.naturalSite:
        return Colors.green;
      case MonumentCategory.archaeological:
        return Colors.orange;
      default:
        return Colors.blue;
    }
  }

  Color _getConfidenceColor() {
    if (confidence > 0.8) return Colors.green;
    if (confidence > 0.6) return Colors.orange;
    return Colors.red;
  }

  String _getOpeningStatus() {
    final now = DateTime.now();
    final hour = now.hour;

    // Zjednodušená logika - v reálné aplikaci by se parsovaly skutečné hodiny
    if (monument.visitingHours.contains('24/7')) {
      return 'Vždy otevřeno';
    } else if (hour >= 8 && hour < 18) {
      return 'Otevřeno';
    } else {
      return 'Zavřeno';
    }
  }

  Color _getOpeningStatusColor() {
    final status = _getOpeningStatus();
    if (status == 'Otevřeno' || status == 'Vždy otevřeno') {
      return Colors.green;
    } else {
      return Colors.red;
    }
  }

  String _getRegionName() {
    switch (monument.region) {
      case 'dalmatia':
        return 'Dalmácie';
      case 'istria':
        return 'Istrie';
      case 'slavonia':
        return 'Slavonie';
      case 'lika':
        return 'Lika';
      case 'zagreb':
        return 'Zagreb';
      default:
        return monument.region;
    }
  }

  void _showShareOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Sdílet památku',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildShareOption(
                  icon: Icons.camera_alt,
                  label: 'Foto',
                  onTap: () {
                    Navigator.pop(context);
                    _sharePhoto(context);
                  },
                ),
                _buildShareOption(
                  icon: Icons.link,
                  label: 'Odkaz',
                  onTap: () {
                    Navigator.pop(context);
                    _shareLink(context);
                  },
                ),
                _buildShareOption(
                  icon: Icons.location_on,
                  label: 'Poloha',
                  onTap: () {
                    Navigator.pop(context);
                    _shareLocation(context);
                  },
                ),
                _buildShareOption(
                  icon: Icons.info,
                  label: 'Info',
                  onTap: () {
                    Navigator.pop(context);
                    _shareInfo(context);
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShareOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: Colors.blue),
          ),
          const SizedBox(height: 4),
          Text(label, style: const TextStyle(fontSize: 12)),
        ],
      ),
    );
  }

  void _sharePhoto(BuildContext context) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('📸 Sdílení fotky...')));
  }

  void _shareLink(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('🔗 Odkaz zkopírován do schránky')),
    );
  }

  void _shareLocation(BuildContext context) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('📍 Sdílení polohy...')));
  }

  void _shareInfo(BuildContext context) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('ℹ️ Sdílení informací...')));
  }
}
