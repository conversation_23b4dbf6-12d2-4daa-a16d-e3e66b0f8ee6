# 🤖 AI Web Scraping pro chorvatské dopravní systémy

Tento dokument popisuje implementaci AI-powered web scrapingu jako alternativu k oficiálním API pro získávání dopravních dat v Chorvatsku.

## 🎯 Proč AI Scraping?

### ✅ Výhody
- **Žádné API klíče** - Nepotřebujete oficiální povolení
- **Okamžitá dostupnost** - Funguje ihned bez čekání na schválení
- **Flexibilita** - Může získat data z jakéhokoli veřejného webu
- **Inteligentní parsing** - AI algoritmy se přizpůsobí změnám ve struktuře webu
- **Kombinovatelnost** - Lze kombinovat s oficiálními API

### ⚠️ Nevýhody a rizika
- **Právní rizika** - Může porušovat Terms of Service
- **Nestabilita** - Změny webu mohou rozbít scraping
- **Výkon** - Pomalejší než API
- **Etické <PERSON>** - Zatěžování serverů
- **Neúplná data** - Nemusí získat všechny informace

## 🏗️ Architektura systému

### Komponenty

1. **AITransportScraper** - Hlavní scraping engine
2. **HybridTransportService** - Kombinuje AI scraping s API
3. **ScrapingConfig** - Konfigurace pro jednotlivé weby
4. **AI Parsing algoritmy** - Inteligentní extrakce dat

### Tok dat

```
Web stránka → HTML parsing → AI extrakce → Validace → Cache → Aplikace
     ↓              ↓            ↓          ↓        ↓        ↓
   ZET.hr      BeautifulSoup   AI algo   Kontrola  Redis   UI Widget
```

## 🧠 AI Algoritmy

### Inteligentní hledání elementů

```dart
// AI heuristiky pro hledání zastávek
List<Element> _findStopElements(Document document, ScrapingConfig config) {
  // 1. Zkusíme konfigurovaný selektor
  elements.addAll(document.querySelectorAll(config.selectors['stops']));
  
  // 2. AI heuristiky - typické vzory
  elements.addAll(document.querySelectorAll('.stop, .stajaliste, .station'));
  
  // 3. Hledáme podle obsahu
  for (final div in allDivs) {
    if (div.text.contains('stajalište') || div.text.contains('zastávka')) {
      elements.add(div);
    }
  }
}
```

### Adaptivní parsing

AI systém se učí z různých struktur webů:

- **Pattern recognition** - Rozpoznává opakující se vzory
- **Context awareness** - Chápe kontext dat (čas, místo, linka)
- **Fallback strategies** - Má záložní strategie pro různé formáty
- **Self-healing** - Automaticky se přizpůsobuje změnám

## 🎯 Cílové weby

### Zagreb - ZET
```
URL: https://www.zet.hr
Zastávky: /stajalista
Jízdní řády: /vozni-red
Real-time: /info-promet
```

### Split - Promet Split
```
URL: https://www.promet-split.hr
Zastávky: /stajalista
Jízdní řády: /vozni-red
Real-time: /uzivo
```

### Rijeka - Autotrolej
```
URL: https://www.autotrolej.hr
Zastávky: /stajalista
Jízdní řády: /red-voznje
Real-time: /info
```

## 🔧 Implementace

### Základní scraping

```dart
final scraper = AITransportScraper();
await scraper.initialize();

// Získání zastávek
final stops = await scraper.getStopsForCity('zagreb');

// Real-time data
final arrivals = await scraper.getRealTimeArrivals('stop_001', 'zagreb');
```

### Hybridní přístup

```dart
final hybridService = HybridTransportService();
await hybridService.initialize();

// Automaticky vybere nejlepší zdroj (API nebo AI scraping)
final stops = await hybridService.getStopsForCity('zagreb');
```

### Konfigurace scrapingu

```dart
const ScrapingConfig zagrebConfig = ScrapingConfig(
  baseUrl: 'https://www.zet.hr',
  stopsUrl: 'https://www.zet.hr/stajalista',
  selectors: {
    'stops': '.stajaliste-item',
    'routes': '.linija-item',
    'times': '.vrijeme-item',
  },
);
```

## 🛡️ Bezpečnost a etika

### Rate limiting
```dart
// Omezení requestů
static const int maxRequestsPerMinute = 30;
static const Duration requestDelay = Duration(seconds: 2);

await Future.delayed(requestDelay); // Mezi requesty
```

### User-Agent rotation
```dart
final userAgents = [
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/91.0.4472.124',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Safari/537.36',
  'Mozilla/5.0 (X11; Linux x86_64) Firefox/89.0',
];

// Rotace User-Agent stringů
```

### Respektování robots.txt
```dart
// Kontrola robots.txt před scrapingem
final robotsUrl = '${config.baseUrl}/robots.txt';
final robotsResponse = await _dio.get(robotsUrl);
// Parsování a respektování pravidel
```

## 📊 Monitoring a debugging

### Debug widget
```dart
// Zobrazení AI scraping statistik
AiScrapingDebugWidget()
```

### Logování
```dart
debugPrint('🤖 AI Scraping: Nalezeno ${stops.length} zastávek');
debugPrint('⏱️ Scraping trval ${stopwatch.elapsedMilliseconds}ms');
debugPrint('📊 Cache hit ratio: ${cacheHits}/${totalRequests}');
```

### Metriky
- Úspěšnost scrapingu (%)
- Doba odezvy (ms)
- Počet nalezených elementů
- Cache hit ratio

## 🔄 Cache strategie

### Víceúrovňová cache
```dart
// L1: Memory cache (rychlá, malá)
final Map<String, dynamic> _memoryCache = {};

// L2: Disk cache (pomalejší, větší)
final SharedPreferences _diskCache = await SharedPreferences.getInstance();

// L3: Database cache (perzistentní)
final Database _dbCache = await openDatabase('transport_cache.db');
```

### TTL (Time To Live)
```dart
static const Duration staticDataTTL = Duration(hours: 24);
static const Duration realTimeTTL = Duration(seconds: 30);
static const Duration timetableTTL = Duration(hours: 6);
```

## 🧪 Testování

### Unit testy
```dart
test('AI scraping Zagreb zastávek', () async {
  final scraper = AITransportScraper();
  final stops = await scraper.getStopsForCity('zagreb');
  
  expect(stops, isNotEmpty);
  expect(stops.first.name, isNotEmpty);
  expect(stops.first.latitude, greaterThan(45.0));
});
```

### Mock servery
```dart
// Pro testování bez skutečných webů
class MockWebServer {
  static String zagrebStopsHtml = '''
    <div class="stajaliste-item" data-lat="45.815" data-lng="15.982">
      <h3>Trg bana Jelačića</h3>
    </div>
  ''';
}
```

## ⚖️ Právní aspekty

### Terms of Service
- **Vždy zkontrolujte** ToS cílových webů
- **Respektujte** robots.txt
- **Nepoužívejte** pro komerční účely bez povolení

### Fair Use
- **Rozumné množství** requestů
- **Nekonkurujte** původnímu webu
- **Přidejte hodnotu** pro uživatele

### GDPR Compliance
- **Neshromažďujte** osobní údaje
- **Informujte** uživatele o scrapingu
- **Umožněte** opt-out

## 🚀 Optimalizace výkonu

### Paralelní scraping
```dart
// Paralelní načítání z více zdrojů
final results = await Future.wait([
  scraper.getStopsForCity('zagreb'),
  scraper.getStopsForCity('split'),
  scraper.getStopsForCity('rijeka'),
]);
```

### Incremental updates
```dart
// Pouze změny od posledního scrapingu
final lastUpdate = await getLastUpdateTime();
final changes = await scraper.getChangesSince(lastUpdate);
```

### Compression
```dart
// Komprese cache dat
final compressed = gzip.encode(utf8.encode(jsonEncode(data)));
await storage.setBytes('cache_key', compressed);
```

## 🔮 Pokročilé techniky

### Machine Learning
```dart
// Použití ML pro lepší parsing
final model = await loadTensorFlowModel('transport_parser.tflite');
final prediction = model.predict(htmlFeatures);
```

### Computer Vision
```dart
// OCR pro extrakci času z obrázků
final ocrResult = await FlutterTesseract.extractText(imageBytes);
final times = parseTimeFromOCR(ocrResult);
```

### Natural Language Processing
```dart
// NLP pro parsing textových informací
final nlp = NaturalLanguageProcessor();
final entities = nlp.extractEntities(webText);
final stops = entities.where((e) => e.type == 'LOCATION');
```

## 📈 Budoucí vylepšení

### Plánované funkce
- [ ] **Automatické učení** - ML modely pro lepší parsing
- [ ] **Prediktivní cache** - Předpovídání potřebných dat
- [ ] **Distribuované scraping** - Více serverů pro rychlost
- [ ] **Real-time monitoring** - Sledování změn webů
- [ ] **API fallback** - Automatické přepnutí na API

### Technické vylepšení
- [ ] **GraphQL scraping** - Pro SPA aplikace
- [ ] **WebSocket monitoring** - Real-time změny
- [ ] **Blockchain verification** - Ověření integrity dat
- [ ] **Edge computing** - Scraping na edge serverech

## 🆘 Řešení problémů

### Časté problémy

**Web změnil strukturu:**
```
Řešení: AI algoritmy se automaticky přizpůsobí
Fallback: Aktualizace selektorů v konfiguraci
```

**Rate limiting:**
```
Řešení: Exponential backoff
Fallback: Použití proxy serverů
```

**Blokování IP:**
```
Řešení: Rotace IP adres
Fallback: Použití VPN/proxy
```

**Neúplná data:**
```
Řešení: Kombinace více zdrojů
Fallback: Použití oficiálního API
```

## 📞 Podpora

### Debugging
```dart
// Zapnutí debug režimu
const bool debugScraping = true;

if (debugScraping) {
  print('🔍 Scraping URL: $url');
  print('📄 HTML length: ${html.length}');
  print('🎯 Found elements: ${elements.length}');
}
```

### Kontakt
- **GitHub Issues**: Pro bug reporty
- **Discord**: Pro real-time podporu
- **Email**: pro citlivé dotazy

---

**⚠️ Důležité upozornění**: AI scraping používejte odpovědně a v souladu s právními předpisy. Vždy preferujte oficiální API, pokud jsou dostupná.

*Poslední aktualizace: Prosinec 2024*
