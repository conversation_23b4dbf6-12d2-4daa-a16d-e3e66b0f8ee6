import 'dart:async';
import 'package:flutter/foundation.dart';

import '../models/offline_data.dart';
import '../models/ai_orchestrator.dart';
import 'offline_storage_service.dart';

/// Služba pro offline AI odpovědi
class OfflineAIService extends ChangeNotifier {
  static final OfflineAIService _instance = OfflineAIService._internal();
  factory OfflineAIService() => _instance;
  OfflineAIService._internal();

  final OfflineStorageService _storageService = OfflineStorageService();
  List<OfflineAIResponse> _responses = [];
  bool _isInitialized = false;

  // Gettery
  List<OfflineAIResponse> get responses => _responses;
  bool get isInitialized => _isInitialized;

  /// Inicializuje offline AI službu
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _storageService.initialize();
      await _loadPredefinedResponses();
      await _loadCachedResponses();
      _isInitialized = true;
      debugPrint(
        'Offline AI služba inicializována s ${_responses.length} odpověďmi',
      );
    } catch (e) {
      debugPrint('Chyba při inicializaci offline AI: $e');
      throw Exception('Nepodařilo se inicializovat offline AI');
    }
  }

  /// Načte předdefinované odpovědi
  Future<void> _loadPredefinedResponses() async {
    final predefinedResponses = _createPredefinedResponses();

    // Uloží do databáze
    await _storageService.saveAIResponsesOffline(predefinedResponses);

    _responses.addAll(predefinedResponses);
  }

  /// Načte uložené odpovědi z databáze
  Future<void> _loadCachedResponses() async {
    try {
      final cachedResponses = await _storageService.getAIResponsesOffline();

      // Přidá pouze nové odpovědi (které nejsou v předdefinovaných)
      for (final cached in cachedResponses) {
        if (!_responses.any((r) => r.id == cached.id)) {
          _responses.add(cached);
        }
      }
    } catch (e) {
      debugPrint('Chyba při načítání cached AI odpovědí: $e');
    }
  }

  /// Vytvoří předdefinované odpovědi pro Chorvatsko
  List<OfflineAIResponse> _createPredefinedResponses() {
    final now = DateTime.now();

    return [
      // Obecné informace o Chorvatsku
      OfflineAIResponse(
        id: 'croatia_general_1',
        question: 'Co je Chorvatsko?',
        answer:
            'Chorvatsko je krásná země ve střední a jihovýchodní Evropě, známá svými úžasnými plážemi na Jaderském moři, historickými městy a národními parky. Hlavním městem je Záhřeb.',
        keywords: ['chorvatsko', 'země', 'evropa', 'záhřeb', 'hlavní město'],
        category: 'obecné',
        createdAt: now,
      ),

      // Doprava
      OfflineAIResponse(
        id: 'transport_1',
        question: 'Jak se dostanu do Chorvatska?',
        answer:
            'Do Chorvatska se můžete dostat letadlem (letiště v Záhřebu, Splitu, Dubrovníku), autem přes Slovinsko nebo Maďarsko, autobusem nebo vlakem. Nejrychlejší je letecká doprava.',
        keywords: ['doprava', 'letadlo', 'auto', 'autobus', 'vlak', 'cesta'],
        category: 'doprava',
        createdAt: now,
      ),

      OfflineAIResponse(
        id: 'transport_2',
        question: 'Jak se pohybovat po Chorvatsku?',
        answer:
            'Po Chorvatsku se můžete pohybovat pronajatým autem (nejflexibilnější), autobusy (dobré spojení mezi městy), trajekty mezi ostrovy, nebo vlakem (omezené spojení).',
        keywords: ['pohyb', 'auto', 'autobus', 'trajekt', 'vlak', 'doprava'],
        category: 'doprava',
        createdAt: now,
      ),

      // Ubytování
      OfflineAIResponse(
        id: 'accommodation_1',
        question: 'Kde se ubytovat v Chorvatsku?',
        answer:
            'V Chorvatsku najdete hotely, penziony, apartmány, kempy a soukromé ubytování. Nejpopulárnější jsou apartmány u moře a hotely v historických centrech měst.',
        keywords: ['ubytování', 'hotel', 'apartmán', 'penzion', 'kemp'],
        category: 'ubytování',
        createdAt: now,
      ),

      // Jídlo
      OfflineAIResponse(
        id: 'food_1',
        question: 'Co ochutnat v Chorvatsku?',
        answer:
            'Vyzkoušejte čevapčiči, pašticadu, crni rižot (černé rizoto), pršut (sušená šunka), sir iz mišine (sýr z měchu), a místní vína. Mořské plody jsou vynikající na pobřeží.',
        keywords: [
          'jídlo',
          'čevapčiči',
          'pašticada',
          'rizoto',
          'pršut',
          'víno',
        ],
        category: 'gastronomie',
        createdAt: now,
      ),

      OfflineAIResponse(
        id: 'food_2',
        question: 'Kde najdu nejlepší restaurace?',
        answer:
            'Nejlepší restaurace najdete v historických centrech měst, u přístavu v přímořských městech, a v malých rodinných konobách. Vyhněte se turistickým pastím u hlavních atrakcí.',
        keywords: ['restaurace', 'konoba', 'jídlo', 'centrum', 'přístav'],
        category: 'gastronomie',
        createdAt: now,
      ),

      // Památky
      OfflineAIResponse(
        id: 'sights_1',
        question: 'Jaké jsou hlavní památky Chorvatska?',
        answer:
            'Hlavní památky: Dubrovnické hradby, Diokleciánův palác ve Splitu, Plitvická jezera, Záhřebská katedrála, Hvarská pevnost, Trogir, Korčula, a národní park Krka.',
        keywords: [
          'památky',
          'dubrovník',
          'split',
          'plitvice',
          'záhřeb',
          'hvar',
        ],
        category: 'památky',
        createdAt: now,
      ),

      OfflineAIResponse(
        id: 'sights_2',
        question: 'Co vidět v Dubrovníku?',
        answer:
            'V Dubrovníku navštivte hradby starého města, Stradun (hlavní ulici), Knížecí palác, katedrálu, lanovku na Srđ, a užijte si západ slunce z hradeb.',
        keywords: [
          'dubrovník',
          'hradby',
          'stradun',
          'palác',
          'katedrála',
          'srđ',
        ],
        category: 'památky',
        createdAt: now,
      ),

      OfflineAIResponse(
        id: 'sights_3',
        question: 'Co vidět ve Splitu?',
        answer:
            'Ve Splitu prozkoumejte Diokleciánův palác, katedrálu sv. Duje, Riva (nábřeží), Marjan (park na kopci), a užijte si večerní procházku starým městem.',
        keywords: [
          'split',
          'diokleciánův palác',
          'katedrála',
          'riva',
          'marjan',
        ],
        category: 'památky',
        createdAt: now,
      ),

      // Pláže
      OfflineAIResponse(
        id: 'beaches_1',
        question: 'Jaké jsou nejlepší pláže v Chorvatsku?',
        answer:
            'Nejlepší pláže: Zlatni Rat (Brač), Sakarun (Dugi Otok), Stiniva (Vis), Banje (Dubrovník), Bacvice (Split), a Zrće (Pag) pro noční život.',
        keywords: [
          'pláže',
          'zlatni rat',
          'sakarun',
          'stiniva',
          'banje',
          'bacvice',
        ],
        category: 'pláže',
        createdAt: now,
      ),

      // Ostrovy
      OfflineAIResponse(
        id: 'islands_1',
        question: 'Které ostrovy navštívit?',
        answer:
            'Doporučené ostrovy: Hvar (levandule a noční život), Brač (Zlatni Rat), Korčula (rodný ostrov Marca Pola), Vis (autentický a klidný), Mljet (národní park).',
        keywords: ['ostrovy', 'hvar', 'brač', 'korčula', 'vis', 'mljet'],
        category: 'ostrovy',
        createdAt: now,
      ),

      // Počasí
      OfflineAIResponse(
        id: 'weather_1',
        question: 'Jaké je počasí v Chorvatsku?',
        answer:
            'Chorvatsko má středomořské klima na pobřeží (teplá léta, mírné zimy) a kontinentální klima ve vnitrozemí. Nejlepší čas pro návštěvu je květen-září.',
        keywords: ['počasí', 'klima', 'léto', 'zima', 'teplota', 'sezóna'],
        category: 'počasí',
        createdAt: now,
      ),

      // Peníze
      OfflineAIResponse(
        id: 'money_1',
        question: 'Jaká je měna v Chorvatsku?',
        answer:
            'Chorvatsko používá euro (EUR) od 1. ledna 2023. Dříve to byla chorvatská kuna. Karty jsou široce přijímány, ale mějte i hotovost pro menší obchody.',
        keywords: ['měna', 'euro', 'kuna', 'peníze', 'karta', 'hotovost'],
        category: 'praktické',
        createdAt: now,
      ),

      // Jazyk
      OfflineAIResponse(
        id: 'language_1',
        question: 'Jakým jazykem se mluví v Chorvatsku?',
        answer:
            'Oficiálním jazykem je chorvatština. Mnoho lidí, zejména v turistických oblastech, mluví anglicky, německy nebo italsky. Základní fráze v chorvatštině jsou vždy oceněny.',
        keywords: [
          'jazyk',
          'chorvatština',
          'angličtina',
          'němčina',
          'italština',
        ],
        category: 'praktické',
        createdAt: now,
      ),

      // Nouzové situace
      OfflineAIResponse(
        id: 'emergency_1',
        question: 'Nouzová čísla v Chorvatsku?',
        answer:
            'Nouzová čísla: 112 (evropské nouzové číslo), 192 (policie), 193 (hasiči), 194 (záchranná služba). Číslo 112 funguje i bez signálu a v angličtině.',
        keywords: [
          'nouzové',
          'čísla',
          '112',
          '192',
          '193',
          '194',
          'policie',
          'hasiči',
        ],
        category: 'nouzové',
        createdAt: now,
      ),

      // Tipy
      OfflineAIResponse(
        id: 'tips_1',
        question: 'Tipy pro cestování po Chorvatsku?',
        answer:
            'Tipy: Rezervujte ubytování předem v sezóně, mějte pohodlnou obuv na kamenité pláže, ochutnejte místní víno, respektujte přírodu, a naučte se základní chorvatské fráze.',
        keywords: ['tipy', 'rezervace', 'obuv', 'víno', 'příroda', 'fráze'],
        category: 'tipy',
        createdAt: now,
      ),

      // Vstupenky
      OfflineAIResponse(
        id: 'tickets_1',
        question: 'Kde koupit vstupenky na památky?',
        answer:
            'Vstupenky kupujte na oficiálních webech památek, v turistických informačních centrech, nebo přímo na místě. Online nákup často nabízí slevy a vyhne se frontám.',
        keywords: [
          'vstupenky',
          'památky',
          'online',
          'turistické centrum',
          'slevy',
        ],
        category: 'vstupenky',
        createdAt: now,
      ),
    ];
  }

  /// Najde odpověď na dotaz
  Future<String?> findAnswer(String query) async {
    await _ensureInitialized();

    if (query.trim().isEmpty) return null;

    // Najde nejlepší shodu
    final matches = _responses
        .where((response) => response.matchesQuery(query))
        .toList();

    if (matches.isEmpty) {
      return _getDefaultResponse(query);
    }

    // Seřadí podle relevance a použití
    matches.sort((a, b) {
      final scoreA = _calculateRelevanceScore(query, a);
      final scoreB = _calculateRelevanceScore(query, b);
      return scoreB.compareTo(scoreA);
    });

    final bestMatch = matches.first;

    // Aktualizuje počet použití
    await _updateUseCount(bestMatch);

    return bestMatch.answer;
  }

  /// Spočítá skóre relevance
  double _calculateRelevanceScore(String query, OfflineAIResponse response) {
    final lowerQuery = query.toLowerCase();
    double score = response.relevanceScore;

    // Bonus za přímou shodu v otázce
    if (response.question.toLowerCase().contains(lowerQuery)) {
      score += 2.0;
    }

    // Bonus za shodu klíčových slov
    for (final keyword in response.keywords) {
      if (lowerQuery.contains(keyword.toLowerCase()) ||
          keyword.toLowerCase().contains(lowerQuery)) {
        score += 1.0;
      }
    }

    // Bonus za častější použití
    score += response.useCount * 0.1;

    return score;
  }

  /// Aktualizuje počet použití odpovědi
  Future<void> _updateUseCount(OfflineAIResponse response) async {
    final updatedResponse = response.copyWith(useCount: response.useCount + 1);

    // Aktualizuje v seznamu
    final index = _responses.indexWhere((r) => r.id == response.id);
    if (index != -1) {
      _responses[index] = updatedResponse;
    }

    // Uloží do databáze
    await _storageService.saveAIResponsesOffline([updatedResponse]);
  }

  /// Vrátí výchozí odpověď pro neznámé dotazy
  String _getDefaultResponse(String query) {
    final lowerQuery = query.toLowerCase();

    if (lowerQuery.contains('kde') || lowerQuery.contains('where')) {
      return 'Pro informace o místech v Chorvatsku použijte mapu nebo sekci míst v aplikaci. Mohu vám pomoci s obecnými informacemi o cestování.';
    }

    if (lowerQuery.contains('jak') || lowerQuery.contains('how')) {
      return 'Pro podrobné návody doporučuji kontaktovat místní turistické informační centrum nebo se podívat do sekce praktických informací.';
    }

    if (lowerQuery.contains('kolik') ||
        lowerQuery.contains('cena') ||
        lowerQuery.contains('price')) {
      return 'Aktuální ceny najdete v sekci vstupenek nebo na oficiálních webech jednotlivých míst. Ceny se mohou měnit podle sezóny.';
    }

    return 'Omlouváme se, ale na tento dotaz nemám offline odpověď. Zkuste přeformulovat otázku nebo se připojte k internetu pro kompletnější odpovědi.';
  }

  /// Přidá novou odpověď
  Future<void> addResponse(OfflineAIResponse response) async {
    await _ensureInitialized();

    _responses.add(response);
    await _storageService.saveAIResponsesOffline([response]);
    notifyListeners();
  }

  /// Získá statistiky odpovědí
  Map<String, int> getResponseStats() {
    final stats = <String, int>{};

    for (final response in _responses) {
      stats[response.category] = (stats[response.category] ?? 0) + 1;
    }

    return stats;
  }

  /// Získá nejpoužívanější odpovědi
  List<OfflineAIResponse> getMostUsedResponses({int limit = 10}) {
    final sorted = List<OfflineAIResponse>.from(_responses);
    sorted.sort((a, b) => b.useCount.compareTo(a.useCount));
    return sorted.take(limit).toList();
  }

  /// Generuje odpověď pro orchestrátor
  Future<AIResponse> generateResponse(String query, AIContext context) async {
    try {
      final answer = await findAnswer(query);

      return AIResponse(
        content: answer ?? 'Omlouvám se, na tento dotaz nemám offline odpověď.',
        type: AIResponseType.text,
        confidence: answer != null ? 0.8 : 0.3,
        source: AIResponseSource.local,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      debugPrint('❌ Chyba při generování offline odpovědi: $e');
      return AIResponse(
        content: 'Došlo k chybě při zpracování dotazu.',
        type: AIResponseType.error,
        confidence: 0.0,
        source: AIResponseSource.fallback,
        timestamp: DateTime.now(),
      );
    }
  }

  /// Zajistí, že je služba inicializována
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }
}
