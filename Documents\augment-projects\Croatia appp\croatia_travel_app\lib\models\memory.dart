import 'dart:io';

class TravelMemory {
  final String id;
  final String title;
  final String description;
  final List<String> photos;
  final List<String> visitedPlaces;
  final DateTime createdAt;
  final DateTime date;
  final MemoryTemplate template;
  final MemoryMetadata metadata;
  final List<String>? tags;
  final double? rating;
  final String? weather;
  final List<String>? companions;

  const TravelMemory({
    required this.id,
    required this.title,
    required this.description,
    required this.photos,
    required this.visitedPlaces,
    required this.createdAt,
    required this.date,
    required this.template,
    required this.metadata,
    this.tags,
    this.rating,
    this.weather,
    this.companions,
  });
}

class MemoryMetadata {
  final int totalPhotos;
  final int selectedPhotos;
  final DateTime processingTime;
  final bool autoGenerated;
  final double? averageQuality;
  final List<String>? detectedLandmarks;
  final int? faceCount;

  const MemoryMetadata({
    required this.totalPhotos,
    required this.selectedPhotos,
    required this.processingTime,
    required this.autoGenerated,
    this.averageQuality,
    this.detectedLandmarks,
    this.faceCount,
  });

  Map<String, dynamic> toJson() {
    return {
      'totalPhotos': totalPhotos,
      'selectedPhotos': selectedPhotos,
      'processingTime': processingTime.toIso8601String(),
      'autoGenerated': autoGenerated,
      'averageQuality': averageQuality,
      'detectedLandmarks': detectedLandmarks,
      'faceCount': faceCount,
    };
  }

  factory MemoryMetadata.fromJson(Map<String, dynamic> json) {
    return MemoryMetadata(
      totalPhotos: json['totalPhotos'],
      selectedPhotos: json['selectedPhotos'],
      processingTime: DateTime.parse(json['processingTime']),
      autoGenerated: json['autoGenerated'],
      averageQuality: json['averageQuality'],
      detectedLandmarks: json['detectedLandmarks'] != null 
          ? List<String>.from(json['detectedLandmarks'])
          : null,
      faceCount: json['faceCount'],
    );
  }
}

class VideoMemory {
  final String id;
  final String memoryId;
  final String title;
  final String videoPath;
  final VideoTemplate template;
  final Duration duration;
  final DateTime createdAt;
  final String? thumbnailPath;
  final VideoMetadata metadata;
  final String? musicUrl;

  const VideoMemory({
    required this.id,
    required this.memoryId,
    required this.title,
    required this.videoPath,
    required this.template,
    required this.duration,
    required this.createdAt,
    this.thumbnailPath,
    required this.metadata,
    this.musicUrl,
  });
}

class VideoMetadata {
  final VideoResolution resolution;
  final int frameRate;
  final int bitrate;
  final int fileSize;
  final String? codec;

  const VideoMetadata({
    required this.resolution,
    required this.frameRate,
    required this.bitrate,
    required this.fileSize,
    this.codec,
  });

  Map<String, dynamic> toJson() {
    return {
      'resolution': resolution.name,
      'frameRate': frameRate,
      'bitrate': bitrate,
      'fileSize': fileSize,
      'codec': codec,
    };
  }

  factory VideoMetadata.fromJson(Map<String, dynamic> json) {
    return VideoMetadata(
      resolution: VideoResolution.values.firstWhere((e) => e.name == json['resolution']),
      frameRate: json['frameRate'],
      bitrate: json['bitrate'],
      fileSize: json['fileSize'],
      codec: json['codec'],
    );
  }
}

class AnalyzedPhoto {
  final File file;
  final int index;
  final double qualityScore;
  final CompositionAnalysis composition;
  final LightingAnalysis lighting;
  final double sharpness;
  final ColorAnalysis colors;
  final List<FaceDetection> faces;
  final List<LandmarkDetection> landmarks;
  final DateTime? timestamp;

  const AnalyzedPhoto({
    required this.file,
    required this.index,
    required this.qualityScore,
    required this.composition,
    required this.lighting,
    required this.sharpness,
    required this.colors,
    required this.faces,
    required this.landmarks,
    this.timestamp,
  });
}

class PhotoAnalysis {
  final double qualityScore;
  final CompositionAnalysis composition;
  final LightingAnalysis lighting;
  final double sharpness;
  final ColorAnalysis colors;
  final List<FaceDetection> faces;
  final List<LandmarkDetection> landmarks;
  final DateTime timestamp;

  const PhotoAnalysis({
    required this.qualityScore,
    required this.composition,
    required this.lighting,
    required this.sharpness,
    required this.colors,
    required this.faces,
    required this.landmarks,
    required this.timestamp,
  });
}

class CompositionAnalysis {
  final double ruleOfThirds;
  final double symmetry;
  final bool leadingLines;
  final bool framing;

  const CompositionAnalysis({
    required this.ruleOfThirds,
    required this.symmetry,
    required this.leadingLines,
    required this.framing,
  });
}

class LightingAnalysis {
  final double brightness;
  final double contrast;
  final ExposureLevel exposure;
  final bool goldenHour;

  const LightingAnalysis({
    required this.brightness,
    required this.contrast,
    required this.exposure,
    required this.goldenHour,
  });
}

class ColorAnalysis {
  final List<String> dominantColors;
  final double colorfulness;
  final double warmth;
  final double saturation;

  const ColorAnalysis({
    required this.dominantColors,
    required this.colorfulness,
    required this.warmth,
    required this.saturation,
  });
}

class FaceDetection {
  final double confidence;
  final Rect boundingBox;
  final Emotion emotion;

  const FaceDetection({
    required this.confidence,
    required this.boundingBox,
    required this.emotion,
  });
}

class LandmarkDetection {
  final String name;
  final double confidence;
  final String category;

  const LandmarkDetection({
    required this.name,
    required this.confidence,
    required this.category,
  });
}

class VideoSequence {
  final List<VideoClip> clips;
  final VideoTemplate template;
  final Duration totalDuration;
  String? musicUrl;

  VideoSequence({
    required this.clips,
    required this.template,
    required this.totalDuration,
    this.musicUrl,
  });
}

class VideoClip {
  final String imagePath;
  final Duration duration;
  final VideoTransition transition;
  final List<VideoEffect> effects;

  const VideoClip({
    required this.imagePath,
    required this.duration,
    required this.transition,
    required this.effects,
  });
}

class Rect {
  final double left;
  final double top;
  final double width;
  final double height;

  const Rect({
    required this.left,
    required this.top,
    required this.width,
    required this.height,
  });
}

// Enums
enum MemoryTemplate {
  classic,
  modern,
  vintage,
  collage,
  timeline,
}

enum VideoTemplate {
  classic,
  modern,
  vintage,
  dynamic,
}

enum VideoResolution {
  sd480,
  hd720,
  hd1080,
  uhd4k,
}

enum VideoTransition {
  none,
  fade,
  slide,
  zoom,
  dissolve,
  wipe,
}

enum VideoEffect {
  none,
  sepia,
  vignette,
  parallax,
  colorGrading,
  zoom,
  rotation,
}

enum ExposureLevel {
  underexposed,
  good,
  overexposed,
}

enum Emotion {
  happy,
  sad,
  neutral,
  surprised,
  angry,
}

enum ShareFormat {
  images,
  pdf,
  video,
  link,
}
