import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/ai_orchestrator.dart';
import 'ai_assistant_service.dart';
import 'cloud_ai_service.dart';
import 'contextual_ai_service.dart';
import 'ai_learning_service.dart';
import 'offline_ai_service.dart';

/// Zjednodušený AI orchestrátor
class AIOrchestrator {
  static final AIOrchestrator _instance = AIOrchestrator._internal();
  factory AIOrchestrator() => _instance;
  AIOrchestrator._internal();

  // AI služby
  final AIAssistantService _assistant = AIAssistantService();
  final CloudAIService _cloudAI = CloudAIService();
  final ContextualAIService _contextualAI = ContextualAIService();
  final AILearningService _learning = AILearningService();
  final OfflineAIService _offlineAI = OfflineAIService();

  // Stav
  bool _isInitialized = false;
  AIContext? _currentContext;

  /// Inicializace AI orchestrátoru
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🤖 Inicializuji AI Orchestrator...');

      // Paralelní inicializace služeb
      await Future.wait([
        _assistant.initialize(),
        _cloudAI.initialize(),
        _contextualAI.initialize(),
        _learning.initialize(),
      ]);

      // Vytvoření výchozího kontextu
      _currentContext = AIContext(
        userId: 'current_user',
        timestamp: DateTime.now(),
        memory: ConversationMemory(lastUpdated: DateTime.now()),
      );

      _isInitialized = true;
      debugPrint('✅ AI Orchestrator inicializován úspěšně');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci AI Orchestrator: $e');
      _isInitialized = true;
    }
  }

  /// Hlavní metoda pro zpracování dotazů
  Future<AIResponse> processQuery(
    String query, {
    AIContext? context,
    bool useVoice = false,
    bool forceOffline = false,
  }) async {
    try {
      // Aktualizace kontextu
      final currentContext = context ?? _getCurrentContext();

      // Výběr AI služby
      final aiService = _selectBestAIService(forceOffline);

      // Generování odpovědi
      final response = await _generateResponse(
        query,
        currentContext,
        aiService,
      );

      // Zaznamenání interakce
      await _recordInteraction(query, response);

      return response;
    } catch (e) {
      debugPrint('❌ Chyba při zpracování dotazu: $e');
      return _createErrorResponse();
    }
  }

  /// Rozpoznání památky z obrázku
  Future<AIResponse> recognizeMonument(String imagePath) async {
    try {
      // Zjednodušená implementace
      return AIResponse(
        content: 'Rozpoznávání památek bude implementováno v budoucí verzi.',
        type: AIResponseType.text,
        confidence: 0.5,
        source: AIResponseSource.local,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      debugPrint('❌ Chyba při rozpoznávání památky: $e');
      return _createErrorResponse();
    }
  }

  /// Personalizované doporučení
  Future<List<AIRecommendation>> getPersonalizedRecommendations({
    LocationData? location,
    int limit = 10,
  }) async {
    try {
      // Zjednodušená implementace
      return [
        AIRecommendation(
          id: 'rec_1',
          title: 'Dubrovník - Stradun',
          description: 'Hlavní ulice Starého města Dubrovníku',
          type: RecommendationType.attraction,
          score: 0.95,
        ),
        AIRecommendation(
          id: 'rec_2',
          title: 'Split - Diokleciánův palác',
          description: 'Historický palác v centru Splitu',
          type: RecommendationType.attraction,
          score: 0.92,
        ),
      ];
    } catch (e) {
      debugPrint('❌ Chyba při získávání doporučení: $e');
      return [];
    }
  }

  /// Výběr nejlepší AI služby
  AIServiceType _selectBestAIService(bool forceOffline) {
    if (forceOffline) return AIServiceType.offline;

    // Zjednodušená logika - vždy použije lokální službu
    return AIServiceType.local;
  }

  /// Generování odpovědi
  Future<AIResponse> _generateResponse(
    String query,
    AIContext context,
    AIServiceType serviceType,
  ) async {
    switch (serviceType) {
      case AIServiceType.cloud:
        try {
          return await _cloudAI.generateResponse(query, context);
        } catch (e) {
          debugPrint('Cloud AI nedostupné, fallback na lokální: $e');
          return await _generateLocalResponse(query);
        }
      case AIServiceType.contextual:
        try {
          return await _contextualAI.generateResponse(query, context);
        } catch (e) {
          debugPrint('Contextual AI nedostupné, fallback na lokální: $e');
          return await _generateLocalResponse(query);
        }
      case AIServiceType.offline:
      case AIServiceType.local:
      default:
        return await _generateLocalResponse(query);
    }
  }

  /// Generování lokální odpovědi
  Future<AIResponse> _generateLocalResponse(String query) async {
    try {
      // Použití existující logiky z AI Assistant
      final message = await _assistant.processTextMessage(query);

      return AIResponse(
        content: message.content,
        type: AIResponseType.text,
        confidence: 0.8,
        source: AIResponseSource.local,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      debugPrint('❌ Chyba při generování lokální odpovědi: $e');
      return _createErrorResponse();
    }
  }

  /// Zaznamenání interakce
  Future<void> _recordInteraction(String query, AIResponse response) async {
    try {
      final interaction = UserInteraction(
        query: query,
        response: response.content,
        wasHelpful: true,
        confidence: response.confidence,
        timestamp: DateTime.now(),
      );

      await _learning.recordInteraction(interaction);
    } catch (e) {
      debugPrint('❌ Chyba při zaznamenávání interakce: $e');
    }
  }

  /// Získání aktuálního kontextu
  AIContext _getCurrentContext() {
    return _currentContext ??
        AIContext(
          userId: 'anonymous',
          timestamp: DateTime.now(),
          memory: ConversationMemory(lastUpdated: DateTime.now()),
        );
  }

  /// Vytvoření chybové odpovědi
  AIResponse _createErrorResponse() {
    return AIResponse(
      content: 'Omlouvám se, došlo k chybě. Zkuste to prosím znovu.',
      type: AIResponseType.error,
      confidence: 0.0,
      source: AIResponseSource.fallback,
      timestamp: DateTime.now(),
    );
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  AIContext? get currentContext => _currentContext;
}

/// Typ AI služby
enum AIServiceType { local, cloud, contextual, offline }

/// Analýza dotazu
class QueryAnalysis {
  final QueryIntent intent;
  final double complexity;
  final List<String> entities;
  final Map<String, dynamic> metadata;

  const QueryAnalysis({
    required this.intent,
    required this.complexity,
    this.entities = const [],
    this.metadata = const {},
  });
}

/// Záměr dotazu
enum QueryIntent {
  simple,
  complex,
  factual,
  creative,
  navigation,
  recommendation,
}
