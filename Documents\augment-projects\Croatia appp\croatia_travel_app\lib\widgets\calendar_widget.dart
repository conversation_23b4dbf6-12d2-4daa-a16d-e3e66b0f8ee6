import 'package:flutter/material.dart';
import '../models/event.dart';

class CalendarWidget extends StatefulWidget {
  final List<Event> events;
  final Function(Event) onEventTap;

  const CalendarWidget({
    super.key,
    required this.events,
    required this.onEventTap,
  });

  @override
  State<CalendarWidget> createState() => _CalendarWidgetState();
}

class _CalendarWidgetState extends State<CalendarWidget> {
  DateTime _selectedDate = DateTime.now();
  DateTime _focusedDate = DateTime.now();
  // ignore: prefer_final_fields
  Map<DateTime, List<Event>> _eventsByDate = {};

  @override
  void initState() {
    super.initState();
    _organizeEventsByDate();
  }

  @override
  void didUpdateWidget(CalendarWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.events != widget.events) {
      _organizeEventsByDate();
    }
  }

  void _organizeEventsByDate() {
    _eventsByDate.clear();

    for (final event in widget.events) {
      final startDate = DateTime(
        event.startDate.year,
        event.startDate.month,
        event.startDate.day,
      );

      // Pokud událost trvá více dní, přidáme ji do všech dní
      final endDate = DateTime(
        event.endDate.year,
        event.endDate.month,
        event.endDate.day,
      );

      DateTime currentDate = startDate;
      while (currentDate.isBefore(endDate.add(const Duration(days: 1)))) {
        if (_eventsByDate[currentDate] == null) {
          _eventsByDate[currentDate] = [];
        }
        _eventsByDate[currentDate]!.add(event);
        currentDate = currentDate.add(const Duration(days: 1));
      }
    }

    setState(() {});
  }

  List<Event> _getEventsForDay(DateTime day) {
    final normalizedDay = DateTime(day.year, day.month, day.day);
    return _eventsByDate[normalizedDay] ?? [];
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Kalendář
        Container(
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // Header s navigací
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () {
                        setState(() {
                          _focusedDate = DateTime(
                            _focusedDate.year,
                            _focusedDate.month - 1,
                          );
                        });
                      },
                      icon: const Icon(Icons.chevron_left, color: Colors.white),
                    ),
                    Expanded(
                      child: Text(
                        _getMonthYearString(_focusedDate),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        setState(() {
                          _focusedDate = DateTime(
                            _focusedDate.year,
                            _focusedDate.month + 1,
                          );
                        });
                      },
                      icon: const Icon(
                        Icons.chevron_right,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),

              // Dny v týdnu
              Container(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  children: ['Po', 'Út', 'St', 'Čt', 'Pá', 'So', 'Ne']
                      .map(
                        (day) => Expanded(
                          child: Text(
                            day,
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[600],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      )
                      .toList(),
                ),
              ),

              // Kalendářová mřížka
              _buildCalendarGrid(),
            ],
          ),
        ),

        // Seznam událostí pro vybraný den
        Expanded(child: _buildEventsList()),
      ],
    );
  }

  Widget _buildCalendarGrid() {
    final firstDayOfMonth = DateTime(_focusedDate.year, _focusedDate.month, 1);
    final lastDayOfMonth = DateTime(
      _focusedDate.year,
      _focusedDate.month + 1,
      0,
    );
    final firstDayOfWeek = firstDayOfMonth.weekday;

    final daysInMonth = lastDayOfMonth.day;
    final totalCells = ((daysInMonth + firstDayOfWeek - 1) / 7).ceil() * 7;

    return Container(
      padding: const EdgeInsets.all(8),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 7,
          childAspectRatio: 1,
        ),
        itemCount: totalCells,
        itemBuilder: (context, index) {
          final dayNumber = index - firstDayOfWeek + 2;

          if (dayNumber <= 0 || dayNumber > daysInMonth) {
            return const SizedBox();
          }

          final date = DateTime(
            _focusedDate.year,
            _focusedDate.month,
            dayNumber,
          );
          final events = _getEventsForDay(date);
          final isSelected = _isSameDay(date, _selectedDate);
          final isToday = _isSameDay(date, DateTime.now());

          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedDate = date;
              });
            },
            child: Container(
              margin: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : isToday
                    ? Theme.of(
                        context,
                      ).colorScheme.primary.withValues(alpha: 0.3)
                    : null,
                borderRadius: BorderRadius.circular(8),
                border: events.isNotEmpty
                    ? Border.all(
                        color: Theme.of(context).colorScheme.secondary,
                        width: 2,
                      )
                    : null,
              ),
              child: Stack(
                children: [
                  Center(
                    child: Text(
                      dayNumber.toString(),
                      style: TextStyle(
                        color: isSelected
                            ? Colors.white
                            : isToday
                            ? Theme.of(context).colorScheme.primary
                            : Colors.black,
                        fontWeight: isToday || isSelected
                            ? FontWeight.bold
                            : FontWeight.normal,
                      ),
                    ),
                  ),
                  if (events.isNotEmpty)
                    Positioned(
                      bottom: 2,
                      right: 2,
                      child: Container(
                        width: 6,
                        height: 6,
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Colors.white
                              : Theme.of(context).colorScheme.secondary,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEventsList() {
    final events = _getEventsForDay(_selectedDate);

    if (events.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.event_busy, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Žádné události',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'Pro ${_formatDate(_selectedDate)} nejsou naplánované žádné události',
              style: TextStyle(color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: events.length,
      itemBuilder: (context, index) {
        final event = events[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: Card(
            child: ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _getEventTypeColor(event.type),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getEventTypeIcon(event.type),
                  color: Colors.white,
                  size: 20,
                ),
              ),
              title: Text(
                event.name,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(event.location),
                  const SizedBox(height: 4),
                  Text(
                    _formatEventTime(event),
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                ],
              ),
              trailing: event.isFree
                  ? Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green.shade100,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        'Zdarma',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.green.shade700,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    )
                  : Text(
                      '${event.price?.toStringAsFixed(0) ?? '?'}€',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
              onTap: () => widget.onEventTap(event),
            ),
          ),
        );
      },
    );
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  String _getMonthYearString(DateTime date) {
    const months = [
      'Leden',
      'Únor',
      'Březen',
      'Duben',
      'Květen',
      'Červen',
      'Červenec',
      'Srpen',
      'Září',
      'Říjen',
      'Listopad',
      'Prosinec',
    ];
    return '${months[date.month - 1]} ${date.year}';
  }

  String _formatDate(DateTime date) {
    return '${date.day}.${date.month}.${date.year}';
  }

  String _formatEventTime(Event event) {
    if (_isSameDay(event.startDate, event.endDate)) {
      return '${event.startDate.hour}:${event.startDate.minute.toString().padLeft(2, '0')} - ${event.endDate.hour}:${event.endDate.minute.toString().padLeft(2, '0')}';
    } else {
      return 'Vícedenní událost';
    }
  }

  Color _getEventTypeColor(EventType type) {
    switch (type) {
      case EventType.festival:
        return Colors.purple;
      case EventType.concert:
        return Colors.red;
      case EventType.exhibition:
        return Colors.indigo;
      case EventType.cultural:
        return Colors.teal;
      case EventType.sports:
        return Colors.orange;
      case EventType.food:
        return Colors.green;
      case EventType.religious:
        return Colors.brown;
      case EventType.traditional:
        return Colors.amber;
      case EventType.other:
        return Colors.blueGrey;
    }
  }

  IconData _getEventTypeIcon(EventType type) {
    switch (type) {
      case EventType.festival:
        return Icons.celebration;
      case EventType.concert:
        return Icons.music_note;
      case EventType.exhibition:
        return Icons.palette;
      case EventType.cultural:
        return Icons.theater_comedy;
      case EventType.sports:
        return Icons.sports;
      case EventType.food:
        return Icons.restaurant;
      case EventType.religious:
        return Icons.church;
      case EventType.traditional:
        return Icons.flag;
      case EventType.other:
        return Icons.event;
    }
  }
}
