// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'city_services.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GovernmentOffice _$GovernmentOfficeFromJson(Map<String, dynamic> json) =>
    GovernmentOffice(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      address: json['address'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      phoneNumber: json['phoneNumber'] as String,
      email: json['email'] as String,
      website: json['website'] as String?,
      openingHours: (json['openingHours'] as List<dynamic>)
          .map((e) => OfficeHours.fromJson(e as Map<String, dynamic>))
          .toList(),
      services:
          (json['services'] as List<dynamic>).map((e) => e as String).toList(),
      languages: (json['languages'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const ['hr'],
      hasDisabledAccess: json['hasDisabledAccess'] as bool? ?? false,
      hasParking: json['hasParking'] as bool? ?? false,
      description: json['description'] as String?,
      requiredDocuments: (json['requiredDocuments'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$GovernmentOfficeToJson(GovernmentOffice instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'address': instance.address,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'phoneNumber': instance.phoneNumber,
      'email': instance.email,
      'website': instance.website,
      'openingHours': instance.openingHours,
      'services': instance.services,
      'languages': instance.languages,
      'hasDisabledAccess': instance.hasDisabledAccess,
      'hasParking': instance.hasParking,
      'description': instance.description,
      'requiredDocuments': instance.requiredDocuments,
    };

OfficeHours _$OfficeHoursFromJson(Map<String, dynamic> json) => OfficeHours(
      dayOfWeek: json['dayOfWeek'] as String,
      openTime: json['openTime'] as String,
      closeTime: json['closeTime'] as String,
      isClosed: json['isClosed'] as bool? ?? false,
    );

Map<String, dynamic> _$OfficeHoursToJson(OfficeHours instance) =>
    <String, dynamic>{
      'dayOfWeek': instance.dayOfWeek,
      'openTime': instance.openTime,
      'closeTime': instance.closeTime,
      'isClosed': instance.isClosed,
    };

OnlineForm _$OnlineFormFromJson(Map<String, dynamic> json) => OnlineForm(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      category: $enumDecode(_$FormCategoryEnumMap, json['category']),
      fields: (json['fields'] as List<dynamic>)
          .map((e) => FormField.fromJson(e as Map<String, dynamic>))
          .toList(),
      requiredDocuments: (json['requiredDocuments'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      submitUrl: json['submitUrl'] as String,
      estimatedTime:
          Duration(microseconds: (json['estimatedTime'] as num).toInt()),
      fee: (json['fee'] as num?)?.toDouble(),
      currency: json['currency'] as String?,
      status: $enumDecodeNullable(_$FormStatusEnumMap, json['status']) ??
          FormStatus.active,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      requiresAuthentication: json['requiresAuthentication'] as bool? ?? true,
      supportedLanguages: (json['supportedLanguages'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const ['hr', 'en'],
    );

Map<String, dynamic> _$OnlineFormToJson(OnlineForm instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'category': _$FormCategoryEnumMap[instance.category]!,
      'fields': instance.fields,
      'requiredDocuments': instance.requiredDocuments,
      'submitUrl': instance.submitUrl,
      'estimatedTime': instance.estimatedTime.inMicroseconds,
      'fee': instance.fee,
      'currency': instance.currency,
      'status': _$FormStatusEnumMap[instance.status]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'requiresAuthentication': instance.requiresAuthentication,
      'supportedLanguages': instance.supportedLanguages,
    };

const _$FormCategoryEnumMap = {
  FormCategory.residence: 'residence',
  FormCategory.business: 'business',
  FormCategory.construction: 'construction',
  FormCategory.social: 'social',
  FormCategory.tax: 'tax',
  FormCategory.other: 'other',
};

const _$FormStatusEnumMap = {
  FormStatus.active: 'active',
  FormStatus.inactive: 'inactive',
  FormStatus.archived: 'archived',
};

FormField _$FormFieldFromJson(Map<String, dynamic> json) => FormField(
      id: json['id'] as String,
      label: json['label'] as String,
      placeholder: json['placeholder'] as String?,
      type: $enumDecode(_$FieldTypeEnumMap, json['type']),
      isRequired: json['isRequired'] as bool? ?? false,
      options:
          (json['options'] as List<dynamic>?)?.map((e) => e as String).toList(),
      validationPattern: json['validationPattern'] as String?,
      helpText: json['helpText'] as String?,
    );

Map<String, dynamic> _$FormFieldToJson(FormField instance) => <String, dynamic>{
      'id': instance.id,
      'label': instance.label,
      'placeholder': instance.placeholder,
      'type': _$FieldTypeEnumMap[instance.type]!,
      'isRequired': instance.isRequired,
      'options': instance.options,
      'validationPattern': instance.validationPattern,
      'helpText': instance.helpText,
    };

const _$FieldTypeEnumMap = {
  FieldType.text: 'text',
  FieldType.email: 'email',
  FieldType.phone: 'phone',
  FieldType.number: 'number',
  FieldType.date: 'date',
  FieldType.select: 'select',
  FieldType.multiSelect: 'multiSelect',
  FieldType.checkbox: 'checkbox',
  FieldType.file: 'file',
  FieldType.textarea: 'textarea',
};

FormSubmission _$FormSubmissionFromJson(Map<String, dynamic> json) =>
    FormSubmission(
      id: json['id'] as String,
      formId: json['formId'] as String,
      userId: json['userId'] as String,
      data: json['data'] as Map<String, dynamic>,
      attachments: (json['attachments'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      status: $enumDecodeNullable(_$SubmissionStatusEnumMap, json['status']) ??
          SubmissionStatus.submitted,
      submittedAt: DateTime.parse(json['submittedAt'] as String),
      processedAt: json['processedAt'] == null
          ? null
          : DateTime.parse(json['processedAt'] as String),
      referenceNumber: json['referenceNumber'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$FormSubmissionToJson(FormSubmission instance) =>
    <String, dynamic>{
      'id': instance.id,
      'formId': instance.formId,
      'userId': instance.userId,
      'data': instance.data,
      'attachments': instance.attachments,
      'status': _$SubmissionStatusEnumMap[instance.status]!,
      'submittedAt': instance.submittedAt.toIso8601String(),
      'processedAt': instance.processedAt?.toIso8601String(),
      'referenceNumber': instance.referenceNumber,
      'notes': instance.notes,
    };

const _$SubmissionStatusEnumMap = {
  SubmissionStatus.submitted: 'submitted',
  SubmissionStatus.inReview: 'inReview',
  SubmissionStatus.approved: 'approved',
  SubmissionStatus.rejected: 'rejected',
  SubmissionStatus.completed: 'completed',
};

CityPayment _$CityPaymentFromJson(Map<String, dynamic> json) => CityPayment(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      category: $enumDecode(_$PaymentCategoryEnumMap, json['category']),
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      dueDate: DateTime.parse(json['dueDate'] as String),
      status: $enumDecodeNullable(_$PaymentStatusEnumMap, json['status']) ??
          PaymentStatus.pending,
      userId: json['userId'] as String?,
      referenceNumber: json['referenceNumber'] as String?,
      acceptedMethods: (json['acceptedMethods'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$PaymentMethodEnumMap, e))
              .toList() ??
          const [],
      isRecurring: json['isRecurring'] as bool? ?? false,
      recurringPeriod: json['recurringPeriod'] == null
          ? null
          : Duration(microseconds: (json['recurringPeriod'] as num).toInt()),
    );

Map<String, dynamic> _$CityPaymentToJson(CityPayment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'category': _$PaymentCategoryEnumMap[instance.category]!,
      'amount': instance.amount,
      'currency': instance.currency,
      'dueDate': instance.dueDate.toIso8601String(),
      'status': _$PaymentStatusEnumMap[instance.status]!,
      'userId': instance.userId,
      'referenceNumber': instance.referenceNumber,
      'acceptedMethods': instance.acceptedMethods
          .map((e) => _$PaymentMethodEnumMap[e]!)
          .toList(),
      'isRecurring': instance.isRecurring,
      'recurringPeriod': instance.recurringPeriod?.inMicroseconds,
    };

const _$PaymentCategoryEnumMap = {
  PaymentCategory.communalWaste: 'communalWaste',
  PaymentCategory.propertyTax: 'propertyTax',
  PaymentCategory.businessLicense: 'businessLicense',
  PaymentCategory.parking: 'parking',
  PaymentCategory.utilities: 'utilities',
  PaymentCategory.fines: 'fines',
  PaymentCategory.other: 'other',
};

const _$PaymentStatusEnumMap = {
  PaymentStatus.pending: 'pending',
  PaymentStatus.paid: 'paid',
  PaymentStatus.overdue: 'overdue',
  PaymentStatus.cancelled: 'cancelled',
  PaymentStatus.refunded: 'refunded',
};

const _$PaymentMethodEnumMap = {
  PaymentMethod.card: 'card',
  PaymentMethod.bankTransfer: 'bankTransfer',
  PaymentMethod.cash: 'cash',
  PaymentMethod.mobilePay: 'mobilePay',
  PaymentMethod.crypto: 'crypto',
};

IssueReport _$IssueReportFromJson(Map<String, dynamic> json) => IssueReport(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      category: $enumDecode(_$IssueCategoryEnumMap, json['category']),
      priority: $enumDecodeNullable(_$IssuePriorityEnumMap, json['priority']) ??
          IssuePriority.medium,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      address: json['address'] as String,
      photos: (json['photos'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      reporterName: json['reporterName'] as String?,
      reporterEmail: json['reporterEmail'] as String?,
      reporterPhone: json['reporterPhone'] as String?,
      status: $enumDecodeNullable(_$IssueStatusEnumMap, json['status']) ??
          IssueStatus.reported,
      reportedAt: DateTime.parse(json['reportedAt'] as String),
      assignedAt: json['assignedAt'] == null
          ? null
          : DateTime.parse(json['assignedAt'] as String),
      resolvedAt: json['resolvedAt'] == null
          ? null
          : DateTime.parse(json['resolvedAt'] as String),
      assignedTo: json['assignedTo'] as String?,
      updates: (json['updates'] as List<dynamic>?)
              ?.map((e) => IssueUpdate.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$IssueReportToJson(IssueReport instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'category': _$IssueCategoryEnumMap[instance.category]!,
      'priority': _$IssuePriorityEnumMap[instance.priority]!,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'address': instance.address,
      'photos': instance.photos,
      'reporterName': instance.reporterName,
      'reporterEmail': instance.reporterEmail,
      'reporterPhone': instance.reporterPhone,
      'status': _$IssueStatusEnumMap[instance.status]!,
      'reportedAt': instance.reportedAt.toIso8601String(),
      'assignedAt': instance.assignedAt?.toIso8601String(),
      'resolvedAt': instance.resolvedAt?.toIso8601String(),
      'assignedTo': instance.assignedTo,
      'updates': instance.updates,
    };

const _$IssueCategoryEnumMap = {
  IssueCategory.roads: 'roads',
  IssueCategory.lighting: 'lighting',
  IssueCategory.waste: 'waste',
  IssueCategory.water: 'water',
  IssueCategory.parks: 'parks',
  IssueCategory.noise: 'noise',
  IssueCategory.safety: 'safety',
  IssueCategory.other: 'other',
};

const _$IssuePriorityEnumMap = {
  IssuePriority.low: 'low',
  IssuePriority.medium: 'medium',
  IssuePriority.high: 'high',
  IssuePriority.urgent: 'urgent',
};

const _$IssueStatusEnumMap = {
  IssueStatus.reported: 'reported',
  IssueStatus.assigned: 'assigned',
  IssueStatus.inProgress: 'inProgress',
  IssueStatus.resolved: 'resolved',
  IssueStatus.closed: 'closed',
};

IssueUpdate _$IssueUpdateFromJson(Map<String, dynamic> json) => IssueUpdate(
      id: json['id'] as String,
      message: json['message'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      updatedBy: json['updatedBy'] as String?,
      newStatus: $enumDecodeNullable(_$IssueStatusEnumMap, json['newStatus']),
    );

Map<String, dynamic> _$IssueUpdateToJson(IssueUpdate instance) =>
    <String, dynamic>{
      'id': instance.id,
      'message': instance.message,
      'timestamp': instance.timestamp.toIso8601String(),
      'updatedBy': instance.updatedBy,
      'newStatus': _$IssueStatusEnumMap[instance.newStatus],
    };

PublicWifi _$PublicWifiFromJson(Map<String, dynamic> json) => PublicWifi(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      address: json['address'] as String,
      type: $enumDecode(_$WifiTypeEnumMap, json['type']),
      requiresPassword: json['requiresPassword'] as bool? ?? false,
      requiresRegistration: json['requiresRegistration'] as bool? ?? false,
      password: json['password'] as String?,
      speedMbps: (json['speedMbps'] as num?)?.toDouble(),
      userLimit: (json['userLimit'] as num?)?.toInt(),
      timeLimit: json['timeLimit'] == null
          ? null
          : Duration(microseconds: (json['timeLimit'] as num).toInt()),
      isActive: json['isActive'] as bool? ?? true,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$PublicWifiToJson(PublicWifi instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'address': instance.address,
      'type': _$WifiTypeEnumMap[instance.type]!,
      'requiresPassword': instance.requiresPassword,
      'requiresRegistration': instance.requiresRegistration,
      'password': instance.password,
      'speedMbps': instance.speedMbps,
      'userLimit': instance.userLimit,
      'timeLimit': instance.timeLimit?.inMicroseconds,
      'isActive': instance.isActive,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };

const _$WifiTypeEnumMap = {
  WifiType.municipal: 'municipal',
  WifiType.library: 'library',
  WifiType.park: 'park',
  WifiType.transport: 'transport',
  WifiType.tourist: 'tourist',
  WifiType.emergency: 'emergency',
};

FormAssistance _$FormAssistanceFromJson(Map<String, dynamic> json) =>
    FormAssistance(
      formId: json['formId'] as String,
      suggestions: json['suggestions'] as Map<String, dynamic>,
      missingFields: (json['missingFields'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      validationErrors: (json['validationErrors'] as List<dynamic>)
          .map((e) => FormValidationError.fromJson(e as Map<String, dynamic>))
          .toList(),
      autoFillRecommendations:
          (json['autoFillRecommendations'] as List<dynamic>)
              .map((e) => e as String)
              .toList(),
      completionPercentage: (json['completionPercentage'] as num).toDouble(),
      estimatedTimeToComplete: Duration(
          microseconds: (json['estimatedTimeToComplete'] as num).toInt()),
    );

Map<String, dynamic> _$FormAssistanceToJson(FormAssistance instance) =>
    <String, dynamic>{
      'formId': instance.formId,
      'suggestions': instance.suggestions,
      'missingFields': instance.missingFields,
      'validationErrors': instance.validationErrors,
      'autoFillRecommendations': instance.autoFillRecommendations,
      'completionPercentage': instance.completionPercentage,
      'estimatedTimeToComplete':
          instance.estimatedTimeToComplete.inMicroseconds,
    };

FormValidationError _$FormValidationErrorFromJson(Map<String, dynamic> json) =>
    FormValidationError(
      fieldId: json['fieldId'] as String,
      errorType: json['errorType'] as String,
      message: json['message'] as String,
      suggestion: json['suggestion'] as String?,
    );

Map<String, dynamic> _$FormValidationErrorToJson(
        FormValidationError instance) =>
    <String, dynamic>{
      'fieldId': instance.fieldId,
      'errorType': instance.errorType,
      'message': instance.message,
      'suggestion': instance.suggestion,
    };

ServiceRecommendation _$ServiceRecommendationFromJson(
        Map<String, dynamic> json) =>
    ServiceRecommendation(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      category: $enumDecode(_$ServiceCategoryEnumMap, json['category']),
      relevanceScore: (json['relevanceScore'] as num).toDouble(),
      reasons:
          (json['reasons'] as List<dynamic>).map((e) => e as String).toList(),
      deadline: DateTime.parse(json['deadline'] as String),
      priority: $enumDecode(_$ServicePriorityEnumMap, json['priority']),
      actionData: json['actionData'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$ServiceRecommendationToJson(
        ServiceRecommendation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'category': _$ServiceCategoryEnumMap[instance.category]!,
      'relevanceScore': instance.relevanceScore,
      'reasons': instance.reasons,
      'deadline': instance.deadline.toIso8601String(),
      'priority': _$ServicePriorityEnumMap[instance.priority]!,
      'actionData': instance.actionData,
    };

const _$ServiceCategoryEnumMap = {
  ServiceCategory.government: 'government',
  ServiceCategory.utilities: 'utilities',
  ServiceCategory.transport: 'transport',
  ServiceCategory.health: 'health',
  ServiceCategory.education: 'education',
  ServiceCategory.emergency: 'emergency',
  ServiceCategory.other: 'other',
};

const _$ServicePriorityEnumMap = {
  ServicePriority.low: 'low',
  ServicePriority.medium: 'medium',
  ServicePriority.high: 'high',
  ServicePriority.urgent: 'urgent',
};

PredictiveAlert _$PredictiveAlertFromJson(Map<String, dynamic> json) =>
    PredictiveAlert(
      id: json['id'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      type: $enumDecode(_$AlertTypeEnumMap, json['type']),
      priority: $enumDecode(_$AlertPriorityEnumMap, json['priority']),
      triggerTime: DateTime.parse(json['triggerTime'] as String),
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
      metadata: json['metadata'] as Map<String, dynamic>,
      affectedServices: (json['affectedServices'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      isActive: json['isActive'] as bool? ?? true,
    );

Map<String, dynamic> _$PredictiveAlertToJson(PredictiveAlert instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'message': instance.message,
      'type': _$AlertTypeEnumMap[instance.type]!,
      'priority': _$AlertPriorityEnumMap[instance.priority]!,
      'triggerTime': instance.triggerTime.toIso8601String(),
      'expiresAt': instance.expiresAt?.toIso8601String(),
      'metadata': instance.metadata,
      'affectedServices': instance.affectedServices,
      'isActive': instance.isActive,
    };

const _$AlertTypeEnumMap = {
  AlertType.info: 'info',
  AlertType.warning: 'warning',
  AlertType.emergency: 'emergency',
  AlertType.maintenance: 'maintenance',
  AlertType.event: 'event',
};

const _$AlertPriorityEnumMap = {
  AlertPriority.low: 'low',
  AlertPriority.medium: 'medium',
  AlertPriority.high: 'high',
};

DocumentVerification _$DocumentVerificationFromJson(
        Map<String, dynamic> json) =>
    DocumentVerification(
      id: json['id'] as String,
      userId: json['userId'] as String,
      documentType: $enumDecode(_$DocumentTypeEnumMap, json['documentType']),
      status: $enumDecode(_$VerificationStatusEnumMap, json['status']),
      checks: (json['checks'] as List<dynamic>)
          .map((e) => VerificationCheck.fromJson(e as Map<String, dynamic>))
          .toList(),
      submittedAt: DateTime.parse(json['submittedAt'] as String),
      verifiedAt: json['verifiedAt'] == null
          ? null
          : DateTime.parse(json['verifiedAt'] as String),
      rejectionReason: json['rejectionReason'] as String?,
    );

Map<String, dynamic> _$DocumentVerificationToJson(
        DocumentVerification instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'documentType': _$DocumentTypeEnumMap[instance.documentType]!,
      'status': _$VerificationStatusEnumMap[instance.status]!,
      'checks': instance.checks,
      'submittedAt': instance.submittedAt.toIso8601String(),
      'verifiedAt': instance.verifiedAt?.toIso8601String(),
      'rejectionReason': instance.rejectionReason,
    };

const _$DocumentTypeEnumMap = {
  DocumentType.passport: 'passport',
  DocumentType.id: 'id',
  DocumentType.drivingLicense: 'drivingLicense',
  DocumentType.birthCertificate: 'birthCertificate',
  DocumentType.marriageCertificate: 'marriageCertificate',
  DocumentType.residencePermit: 'residencePermit',
  DocumentType.other: 'other',
};

const _$VerificationStatusEnumMap = {
  VerificationStatus.pending: 'pending',
  VerificationStatus.verified: 'verified',
  VerificationStatus.rejected: 'rejected',
  VerificationStatus.expired: 'expired',
};

VerificationCheck _$VerificationCheckFromJson(Map<String, dynamic> json) =>
    VerificationCheck(
      name: json['name'] as String,
      passed: json['passed'] as bool,
      details: json['details'] as String?,
    );

Map<String, dynamic> _$VerificationCheckToJson(VerificationCheck instance) =>
    <String, dynamic>{
      'name': instance.name,
      'passed': instance.passed,
      'details': instance.details,
    };

ChatbotResponse _$ChatbotResponseFromJson(Map<String, dynamic> json) =>
    ChatbotResponse(
      id: json['id'] as String,
      query: json['query'] as String,
      response: json['response'] as String,
      confidence: (json['confidence'] as num).toDouble(),
      suggestedActions: (json['suggestedActions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      context: json['context'] as Map<String, dynamic>,
      timestamp: DateTime.parse(json['timestamp'] as String),
      sessionId: json['sessionId'] as String?,
      relatedServices: (json['relatedServices'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      sentiment: json['sentiment'] == null
          ? null
          : SentimentAnalysis.fromJson(
              json['sentiment'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ChatbotResponseToJson(ChatbotResponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'query': instance.query,
      'response': instance.response,
      'confidence': instance.confidence,
      'suggestedActions': instance.suggestedActions,
      'context': instance.context,
      'timestamp': instance.timestamp.toIso8601String(),
      'sessionId': instance.sessionId,
      'relatedServices': instance.relatedServices,
      'sentiment': instance.sentiment,
    };

SentimentAnalysis _$SentimentAnalysisFromJson(Map<String, dynamic> json) =>
    SentimentAnalysis(
      type: $enumDecode(_$SentimentTypeEnumMap, json['type']),
      confidence: (json['confidence'] as num).toDouble(),
      scores: (json['scores'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
    );

Map<String, dynamic> _$SentimentAnalysisToJson(SentimentAnalysis instance) =>
    <String, dynamic>{
      'type': _$SentimentTypeEnumMap[instance.type]!,
      'confidence': instance.confidence,
      'scores': instance.scores,
    };

const _$SentimentTypeEnumMap = {
  SentimentType.positive: 'positive',
  SentimentType.negative: 'negative',
  SentimentType.neutral: 'neutral',
  SentimentType.mixed: 'mixed',
};
