import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Služba pro lokalizaci a více jazyků
class LocalizationService extends ChangeNotifier {
  static final LocalizationService _instance = LocalizationService._internal();
  factory LocalizationService() => _instance;
  LocalizationService._internal();

  static const String _localeKey = 'selected_locale';
  
  Locale _currentLocale = const Locale('cs', 'CZ');
  bool _isInitialized = false;

  // Gettery
  Locale get currentLocale => _currentLocale;
  bool get isInitialized => _isInitialized;
  String get currentLanguageCode => _currentLocale.languageCode;
  String get currentCountryCode => _currentLocale.countryCode ?? '';

  /// Podporované jazyky
  static const List<Locale> supportedLocales = [
    Locale('cs', 'CZ'), // Čeština
    Locale('en', 'US'), // Angličtina
    Locale('de', 'DE'), // Němčina
    Locale('it', 'IT'), // Italština
    Locale('hr', 'HR'), // Chorvatština
  ];

  /// Mapování jazyků na názvy
  static const Map<String, String> languageNames = {
    'cs': 'Čeština',
    'en': 'English',
    'de': 'Deutsch',
    'it': 'Italiano',
    'hr': 'Hrvatski',
  };

  /// Mapování jazyků na vlajky (emoji)
  static const Map<String, String> languageFlags = {
    'cs': '🇨🇿',
    'en': '🇺🇸',
    'de': '🇩🇪',
    'it': '🇮🇹',
    'hr': '🇭🇷',
  };

  /// Inicializuje localization službu
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Načte uložený jazyk
      final savedLanguage = prefs.getString(_localeKey);
      if (savedLanguage != null) {
        final parts = savedLanguage.split('_');
        if (parts.length == 2) {
          final locale = Locale(parts[0], parts[1]);
          if (supportedLocales.contains(locale)) {
            _currentLocale = locale;
          }
        }
      }
      
      _isInitialized = true;
      notifyListeners();
      
      debugPrint('Localization služba inicializována - jazyk: $_currentLocale');
    } catch (e) {
      debugPrint('Chyba při inicializaci localization služby: $e');
      _isInitialized = true;
    }
  }

  /// Změní jazyk aplikace
  Future<void> setLocale(Locale locale) async {
    if (!supportedLocales.contains(locale) || _currentLocale == locale) {
      return;
    }

    try {
      _currentLocale = locale;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_localeKey, '${locale.languageCode}_${locale.countryCode}');
      
      notifyListeners();
      debugPrint('Jazyk změněn na: $locale');
    } catch (e) {
      debugPrint('Chyba při změně jazyka: $e');
    }
  }

  /// Získá název aktuálního jazyka
  String getCurrentLanguageName() {
    return languageNames[_currentLocale.languageCode] ?? 'Neznámý';
  }

  /// Získá vlajku aktuálního jazyka
  String getCurrentLanguageFlag() {
    return languageFlags[_currentLocale.languageCode] ?? '🌐';
  }

  /// Získá název jazyka podle kódu
  String getLanguageName(String languageCode) {
    return languageNames[languageCode] ?? languageCode.toUpperCase();
  }

  /// Získá vlajku jazyka podle kódu
  String getLanguageFlag(String languageCode) {
    return languageFlags[languageCode] ?? '🌐';
  }

  /// Zkontroluje, zda je jazyk podporován
  bool isLanguageSupported(String languageCode) {
    return supportedLocales.any((locale) => locale.languageCode == languageCode);
  }

  /// Získá locale podle jazyka
  Locale? getLocaleByLanguageCode(String languageCode) {
    try {
      return supportedLocales.firstWhere(
        (locale) => locale.languageCode == languageCode,
      );
    } catch (e) {
      return null;
    }
  }

  /// Detekuje systémový jazyk
  Locale detectSystemLocale() {
    final systemLocales = WidgetsBinding.instance.platformDispatcher.locales;
    
    for (final systemLocale in systemLocales) {
      // Hledá přesnou shodu
      if (supportedLocales.contains(systemLocale)) {
        return systemLocale;
      }
      
      // Hledá shodu podle jazyka
      final matchingLocale = supportedLocales.firstWhere(
        (locale) => locale.languageCode == systemLocale.languageCode,
        orElse: () => const Locale('cs', 'CZ'),
      );
      
      if (matchingLocale.languageCode == systemLocale.languageCode) {
        return matchingLocale;
      }
    }
    
    // Výchozí čeština
    return const Locale('cs', 'CZ');
  }

  /// Nastaví systémový jazyk
  Future<void> setSystemLocale() async {
    final systemLocale = detectSystemLocale();
    await setLocale(systemLocale);
  }

  /// Získá směr textu pro aktuální jazyk
  TextDirection getTextDirection() {
    // Všechny podporované jazyky používají LTR
    return TextDirection.ltr;
  }

  /// Získá formát data pro aktuální jazyk
  String getDateFormat() {
    switch (_currentLocale.languageCode) {
      case 'cs':
        return 'd.M.yyyy';
      case 'en':
        return 'M/d/yyyy';
      case 'de':
        return 'd.M.yyyy';
      case 'it':
        return 'd/M/yyyy';
      case 'hr':
        return 'd.M.yyyy.';
      default:
        return 'd.M.yyyy';
    }
  }

  /// Získá formát času pro aktuální jazyk
  String getTimeFormat() {
    switch (_currentLocale.languageCode) {
      case 'en':
        return 'h:mm a'; // 12-hodinový formát
      default:
        return 'HH:mm'; // 24-hodinový formát
    }
  }

  /// Získá symbol měny pro aktuální jazyk/region
  String getCurrencySymbol() {
    switch (_currentLocale.countryCode) {
      case 'CZ':
        return 'Kč';
      case 'HR':
        return '€';
      case 'DE':
        return '€';
      case 'IT':
        return '€';
      case 'US':
        return '\$';
      default:
        return '€';
    }
  }

  /// Získá formát čísla pro aktuální jazyk
  String getNumberFormat() {
    switch (_currentLocale.languageCode) {
      case 'cs':
      case 'de':
      case 'hr':
        return '#,##0.00'; // Evropský formát
      case 'en':
        return '#,##0.00'; // Americký formát
      case 'it':
        return '#.##0,00'; // Italský formát
      default:
        return '#,##0.00';
    }
  }

  /// Získá první den týdne pro aktuální jazyk
  int getFirstDayOfWeek() {
    switch (_currentLocale.languageCode) {
      case 'en':
        return 0; // Neděle
      default:
        return 1; // Pondělí
    }
  }

  /// Získá lokalizované názvy měsíců
  List<String> getMonthNames() {
    switch (_currentLocale.languageCode) {
      case 'cs':
        return [
          'Leden', 'Únor', 'Březen', 'Duben', 'Květen', 'Červen',
          'Červenec', 'Srpen', 'Září', 'Říjen', 'Listopad', 'Prosinec'
        ];
      case 'en':
        return [
          'January', 'February', 'March', 'April', 'May', 'June',
          'July', 'August', 'September', 'October', 'November', 'December'
        ];
      case 'de':
        return [
          'Januar', 'Februar', 'März', 'April', 'Mai', 'Juni',
          'Juli', 'August', 'September', 'Oktober', 'November', 'Dezember'
        ];
      case 'it':
        return [
          'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',
          'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'
        ];
      case 'hr':
        return [
          'Siječanj', 'Veljača', 'Ožujak', 'Travanj', 'Svibanj', 'Lipanj',
          'Srpanj', 'Kolovoz', 'Rujan', 'Listopad', 'Studeni', 'Prosinac'
        ];
      default:
        return getMonthNames(); // Fallback na čestinu
    }
  }

  /// Získá lokalizované názvy dnů v týdnu
  List<String> getDayNames() {
    switch (_currentLocale.languageCode) {
      case 'cs':
        return ['Pondělí', 'Úterý', 'Středa', 'Čtvrtek', 'Pátek', 'Sobota', 'Neděle'];
      case 'en':
        return ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
      case 'de':
        return ['Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag', 'Samstag', 'Sonntag'];
      case 'it':
        return ['Lunedì', 'Martedì', 'Mercoledì', 'Giovedì', 'Venerdì', 'Sabato', 'Domenica'];
      case 'hr':
        return ['Ponedjeljak', 'Utorak', 'Srijeda', 'Četvrtak', 'Petak', 'Subota', 'Nedjelja'];
      default:
        return getDayNames(); // Fallback na čestinu
    }
  }

  /// Získá lokalizované zkrácené názvy dnů
  List<String> getShortDayNames() {
    switch (_currentLocale.languageCode) {
      case 'cs':
        return ['Po', 'Út', 'St', 'Čt', 'Pá', 'So', 'Ne'];
      case 'en':
        return ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      case 'de':
        return ['Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa', 'So'];
      case 'it':
        return ['Lun', 'Mar', 'Mer', 'Gio', 'Ven', 'Sab', 'Dom'];
      case 'hr':
        return ['Pon', 'Uto', 'Sri', 'Čet', 'Pet', 'Sub', 'Ned'];
      default:
        return getShortDayNames(); // Fallback na čestinu
    }
  }

  /// Formátuje číslo podle aktuálního locale
  String formatNumber(double number, {int decimals = 2}) {
    switch (_currentLocale.languageCode) {
      case 'it':
        return number.toStringAsFixed(decimals).replaceAll('.', ',');
      default:
        return number.toStringAsFixed(decimals);
    }
  }

  /// Formátuje měnu podle aktuálního locale
  String formatCurrency(double amount, {int decimals = 2}) {
    final formattedAmount = formatNumber(amount, decimals: decimals);
    final symbol = getCurrencySymbol();
    
    switch (_currentLocale.languageCode) {
      case 'cs':
        return '$formattedAmount $symbol';
      case 'en':
        return '$symbol$formattedAmount';
      case 'de':
      case 'it':
        return '$formattedAmount $symbol';
      case 'hr':
        return '$formattedAmount $symbol';
      default:
        return '$formattedAmount $symbol';
    }
  }

  /// Resetuje na výchozí jazyk
  Future<void> resetToDefault() async {
    await setLocale(const Locale('cs', 'CZ'));
  }
}
