// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'diary_entry.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DiaryEntry _$DiaryEntryFromJson(Map<String, dynamic> json) => DiaryEntry(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      date: DateTime.parse(json['date'] as String),
      location: json['location'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      photos: (json['photos'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      voiceNotes: (json['voiceNotes'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      videos: (json['videos'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      mood: $enumDecodeNullable(_$DiaryMoodEnumMap, json['mood']),
      weather: json['weather'] as String?,
      rating: (json['rating'] as num?)?.toDouble(),
      isPrivate: json['isPrivate'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$DiaryEntryToJson(DiaryEntry instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'content': instance.content,
      'date': instance.date.toIso8601String(),
      'location': instance.location,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'photos': instance.photos,
      'voiceNotes': instance.voiceNotes,
      'videos': instance.videos,
      'tags': instance.tags,
      'mood': _$DiaryMoodEnumMap[instance.mood],
      'weather': instance.weather,
      'rating': instance.rating,
      'isPrivate': instance.isPrivate,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$DiaryMoodEnumMap = {
  DiaryMood.veryHappy: 'veryHappy',
  DiaryMood.happy: 'happy',
  DiaryMood.neutral: 'neutral',
  DiaryMood.sad: 'sad',
  DiaryMood.verySad: 'verySad',
  DiaryMood.excited: 'excited',
  DiaryMood.relaxed: 'relaxed',
  DiaryMood.tired: 'tired',
  DiaryMood.adventurous: 'adventurous',
  DiaryMood.nostalgic: 'nostalgic',
};
