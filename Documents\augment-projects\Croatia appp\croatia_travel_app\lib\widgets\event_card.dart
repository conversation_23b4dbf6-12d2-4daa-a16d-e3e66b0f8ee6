import 'package:flutter/material.dart';
import '../models/event.dart';

class EventCard extends StatelessWidget {
  final Event event;
  final VoidCallback? onTap;
  final VoidCallback? onBookmarkToggle;

  const EventCard({
    super.key,
    required this.event,
    this.onTap,
    this.onBookmarkToggle,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header s datem a bookmark
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _getEventTypeColor(event.type).withValues(alpha: 0.1),
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getEventTypeColor(event.type),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getEventTypeIcon(event.type),
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _formatDateRange(event.startDate, event.endDate),
                          style: TextStyle(
                            fontSize: 12,
                            color: _getEventTypeColor(event.type),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          event.location,
                          style: TextStyle(
                            fontSize: 11,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (onBookmarkToggle != null)
                    InkWell(
                      onTap: onBookmarkToggle,
                      borderRadius: BorderRadius.circular(20),
                      child: Padding(
                        padding: const EdgeInsets.all(4),
                        child: Icon(
                          event.isBookmarked
                              ? Icons.bookmark
                              : Icons.bookmark_border,
                          color: event.isBookmarked
                              ? _getEventTypeColor(event.type)
                              : Colors.grey,
                          size: 24,
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // Obsah události
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    event.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 6),

                  Text(
                    event.description,
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 12),

                  // Tagy a informace
                  Row(
                    children: [
                      // Typ události
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _getEventTypeColor(
                            event.type,
                          ).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _getEventTypeLabel(event.type),
                          style: TextStyle(
                            fontSize: 10,
                            color: _getEventTypeColor(event.type),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),

                      const SizedBox(width: 8),

                      // Cena
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: event.isFree
                              ? Colors.green.shade100
                              : Colors.blue.shade100,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          event.isFree
                              ? 'Zdarma'
                              : '${event.price?.toStringAsFixed(0) ?? '?'}€',
                          style: TextStyle(
                            fontSize: 10,
                            color: event.isFree
                                ? Colors.green.shade700
                                : Colors.blue.shade700,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),

                      const Spacer(),

                      // Čas do začátku
                      Text(
                        _getTimeUntilEvent(event.startDate),
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey[500],
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ),

                  // Website link
                  if (event.website != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Row(
                        children: [
                          Icon(Icons.link, size: 14, color: Colors.blue[600]),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              'Více informací',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.blue[600],
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getEventTypeColor(EventType type) {
    switch (type) {
      case EventType.festival:
        return Colors.purple;
      case EventType.concert:
        return Colors.red;
      case EventType.exhibition:
        return Colors.indigo;
      case EventType.cultural:
        return Colors.teal;
      case EventType.sports:
        return Colors.orange;
      case EventType.food:
        return Colors.green;
      case EventType.religious:
        return Colors.brown;
      case EventType.traditional:
        return Colors.amber;
      case EventType.other:
        return Colors.blueGrey;
    }
  }

  IconData _getEventTypeIcon(EventType type) {
    switch (type) {
      case EventType.festival:
        return Icons.celebration;
      case EventType.concert:
        return Icons.music_note;
      case EventType.exhibition:
        return Icons.palette;
      case EventType.cultural:
        return Icons.theater_comedy;
      case EventType.sports:
        return Icons.sports;
      case EventType.food:
        return Icons.restaurant;
      case EventType.religious:
        return Icons.church;
      case EventType.traditional:
        return Icons.flag;
      case EventType.other:
        return Icons.event;
    }
  }

  String _getEventTypeLabel(EventType type) {
    switch (type) {
      case EventType.festival:
        return 'Festival';
      case EventType.concert:
        return 'Koncert';
      case EventType.exhibition:
        return 'Výstava';
      case EventType.cultural:
        return 'Kultura';
      case EventType.sports:
        return 'Sport';
      case EventType.food:
        return 'Gastronomie';
      case EventType.religious:
        return 'Náboženské';
      case EventType.traditional:
        return 'Tradiční';
      case EventType.other:
        return 'Ostatní';
    }
  }

  String _formatDateRange(DateTime start, DateTime end) {
    if (start.year == end.year &&
        start.month == end.month &&
        start.day == end.day) {
      // Stejný den
      return '${start.day}.${start.month}.${start.year}';
    } else if (start.year == end.year && start.month == end.month) {
      // Stejný měsíc
      return '${start.day}-${end.day}.${start.month}.${start.year}';
    } else if (start.year == end.year) {
      // Stejný rok
      return '${start.day}.${start.month} - ${end.day}.${end.month}.${start.year}';
    } else {
      // Různé roky
      return '${start.day}.${start.month}.${start.year} - ${end.day}.${end.month}.${end.year}';
    }
  }

  String _getTimeUntilEvent(DateTime eventDate) {
    final now = DateTime.now();
    final difference = eventDate.difference(now);

    if (difference.isNegative) {
      return 'Probíhá';
    } else if (difference.inDays == 0) {
      return 'Dnes';
    } else if (difference.inDays == 1) {
      return 'Zítra';
    } else if (difference.inDays < 7) {
      return 'Za ${difference.inDays} dní';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).round();
      return 'Za $weeks týdnů';
    } else {
      final months = (difference.inDays / 30).round();
      return 'Za $months měsíců';
    }
  }
}
