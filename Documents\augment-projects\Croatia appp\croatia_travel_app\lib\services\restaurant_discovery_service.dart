import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/restaurant.dart';

/// Služba pro objevování restaurací v Chorvatsku
/// Právně bezpečné řešení bez API závislostí
class RestaurantDiscoveryService {
  static final RestaurantDiscoveryService _instance = RestaurantDiscoveryService._internal();
  factory RestaurantDiscoveryService() => _instance;
  RestaurantDiscoveryService._internal();

  // Cache pro restaurace
  List<Restaurant>? _cachedRestaurants;
  DateTime? _lastCacheUpdate;
  final Duration _cacheValidDuration = const Duration(hours: 6);

  /// Získá všechny restaurace
  Future<List<Restaurant>> getAllRestaurants() async {
    if (_isCacheValid()) {
      return _cachedRestaurants!;
    }

    try {
      // Simulace načítání z lokální databáze
      await Future.delayed(const Duration(milliseconds: 500));
      
      _cachedRestaurants = _generateSampleRestaurants();
      _lastCacheUpdate = DateTime.now();
      
      return _cachedRestaurants!;
    } catch (e) {
      debugPrint('Chyba při načítání restaurací: $e');
      return [];
    }
  }

  /// Vyhledá restaurace podle kritérií
  Future<List<Restaurant>> searchRestaurants({
    String? query,
    String? region,
    String? cuisineType,
    String? priceRange,
    double? latitude,
    double? longitude,
    double? maxDistance,
    bool? isOpenNow,
    double? minRating,
  }) async {
    final allRestaurants = await getAllRestaurants();
    
    return allRestaurants.where((restaurant) {
      // Text search
      if (query != null && query.isNotEmpty) {
        final searchLower = query.toLowerCase();
        if (!restaurant.name.toLowerCase().contains(searchLower) &&
            !restaurant.description.toLowerCase().contains(searchLower) &&
            !restaurant.specialties.any((s) => s.toLowerCase().contains(searchLower))) {
          return false;
        }
      }

      // Region filter
      if (region != null && region != 'all' && restaurant.region != region) {
        return false;
      }

      // Cuisine type filter
      if (cuisineType != null && cuisineType != 'all' && restaurant.cuisineType != cuisineType) {
        return false;
      }

      // Price range filter
      if (priceRange != null && priceRange != 'all' && restaurant.priceRange != priceRange) {
        return false;
      }

      // Distance filter
      if (latitude != null && longitude != null && maxDistance != null) {
        final distance = restaurant.distanceFrom(latitude, longitude);
        if (distance > maxDistance) {
          return false;
        }
      }

      // Open now filter
      if (isOpenNow == true && !restaurant.isOpenNow) {
        return false;
      }

      // Rating filter
      if (minRating != null && restaurant.rating < minRating) {
        return false;
      }

      return true;
    }).toList();
  }

  /// Získá doporučené restaurace
  Future<List<Restaurant>> getRecommendedRestaurants({
    String? userRegion,
    List<String>? userPreferences,
    int limit = 10,
  }) async {
    final allRestaurants = await getAllRestaurants();
    
    // Seřadí podle hodnocení a popularity
    final recommended = allRestaurants.where((r) => r.rating >= 4.0).toList();
    recommended.sort((a, b) {
      // Priorita: hodnocení * počet recenzí
      final scoreA = a.rating * (a.reviewCount / 100);
      final scoreB = b.rating * (b.reviewCount / 100);
      return scoreB.compareTo(scoreA);
    });

    return recommended.take(limit).toList();
  }

  /// Získá top hodnocené restaurace
  Future<List<Restaurant>> getTopRatedRestaurants({int limit = 20}) async {
    final allRestaurants = await getAllRestaurants();
    
    final topRated = allRestaurants.where((r) => r.rating >= 4.5).toList();
    topRated.sort((a, b) => b.rating.compareTo(a.rating));
    
    return topRated.take(limit).toList();
  }

  /// Získá tradiční chorvatské restaurace
  Future<List<Restaurant>> getTraditionalRestaurants({int limit = 20}) async {
    final allRestaurants = await getAllRestaurants();
    
    final traditional = allRestaurants.where((r) => r.cuisineType == 'traditional').toList();
    traditional.sort((a, b) => b.rating.compareTo(a.rating));
    
    return traditional.take(limit).toList();
  }

  /// Získá restaurace v blízkosti
  Future<List<Restaurant>> getNearbyRestaurants(
    double latitude,
    double longitude, {
    double maxDistance = 10.0, // km
    int limit = 20,
  }) async {
    final allRestaurants = await getAllRestaurants();
    
    final nearby = allRestaurants.map((restaurant) {
      final distance = restaurant.distanceFrom(latitude, longitude);
      return MapEntry(restaurant, distance);
    }).where((entry) => entry.value <= maxDistance).toList();

    nearby.sort((a, b) => a.value.compareTo(b.value));
    
    return nearby.take(limit).map((entry) => entry.key).toList();
  }

  /// Získá statistiky restaurací
  Future<RestaurantStatistics> getRestaurantStatistics() async {
    final allRestaurants = await getAllRestaurants();
    
    return RestaurantStatistics(
      totalRestaurants: allRestaurants.length,
      openNow: allRestaurants.where((r) => r.isOpenNow).length,
      topRated: allRestaurants.where((r) => r.rating >= 4.5).length,
      traditionalCroatian: allRestaurants.where((r) => r.cuisineType == 'traditional').length,
      byRegion: _getRestaurantsByRegion(allRestaurants),
      byCuisineType: _getRestaurantsByCuisineType(allRestaurants),
      byPriceRange: _getRestaurantsByPriceRange(allRestaurants),
    );
  }

  /// Kontroluje platnost cache
  bool _isCacheValid() {
    return _cachedRestaurants != null &&
           _lastCacheUpdate != null &&
           DateTime.now().difference(_lastCacheUpdate!) < _cacheValidDuration;
  }

  /// Generuje ukázková data restaurací
  List<Restaurant> _generateSampleRestaurants() {
    final random = Random();
    final restaurants = <Restaurant>[];

    // Dalmatian restaurants
    restaurants.addAll([
      _createRestaurant(
        'Villa Dalmacija',
        'Luxusní restaurace s výhledem na moře a tradiční dalmatskou kuchyní',
        'Obala Hrvatskog narodnog preporoda 12, Split',
        43.5081, 16.4402,
        'dalmatia', 'traditional', 'upscale',
        4.8, 156, ['Peka', 'Crni rižot', 'Pašticada'],
        '+385 21 123 456',
      ),
      _createRestaurant(
        'Konoba Matejuška',
        'Autentická konoba v srdci Splitu s čerstvými mořskými plody',
        'Matejuška bb, Split',
        43.5069, 16.4389,
        'dalmatia', 'seafood', 'mid',
        4.6, 203, ['Buzara', 'Gregada', 'Brudet'],
        '+385 21 234 567',
      ),
      _createRestaurant(
        'Dubrovnik Palace',
        'Elegantní restaurace v historickém centru Dubrovníku',
        'Stradun 15, Dubrovnik',
        42.6405, 18.1094,
        'dalmatia', 'mediterranean', 'fine',
        4.9, 89, ['Lamb peka', 'Oysters', 'Truffle pasta'],
        '+385 20 345 678',
      ),
    ]);

    // Istrian restaurants
    restaurants.addAll([
      _createRestaurant(
        'Taverna Istria',
        'Rodinná taverna s istrskými specialitami a domácím vínem',
        'Istarska 25, Pula',
        44.8666, 13.8496,
        'istria', 'traditional', 'mid',
        4.5, 134, ['Fuži s tartufima', 'Istarski pršut', 'Maneštra'],
        '+385 52 456 789',
      ),
      _createRestaurant(
        'Konoba Batelina',
        'Proslulá konoba s nejlepšími mořskými plody v Istrii',
        'Čimulje 25, Banjole',
        44.8234, 13.8567,
        'istria', 'seafood', 'upscale',
        4.7, 178, ['Scampi na buzaru', 'Tuna carpaccio', 'Sea bass'],
        '+385 52 567 890',
      ),
    ]);

    // Zagreb restaurants
    restaurants.addAll([
      _createRestaurant(
        'Dubravkin Put',
        'Moderní chorvatská kuchyně v elegantním prostředí',
        'Dubravkin put 2, Zagreb',
        45.8150, 15.9819,
        'zagreb', 'international', 'fine',
        4.6, 145, ['Duck breast', 'Truffle risotto', 'Chocolate soufflé'],
        '+385 1 678 901',
      ),
      _createRestaurant(
        'Vinodol',
        'Tradiční zagrebská restaurace s dlouhou historií',
        'Nikole Tesle 10, Zagreb',
        45.8131, 15.9775,
        'zagreb', 'traditional', 'mid',
        4.4, 267, ['Zagrebački odrezak', 'Štrukli', 'Kremšnita'],
        '+385 1 789 012',
      ),
    ]);

    // Slavonian restaurants
    restaurants.addAll([
      _createRestaurant(
        'Slavonska kuća',
        'Autentická slavonská kuchyně s domácími specialitami',
        'Trg Ante Starčevića 5, Osijek',
        45.5550, 18.6955,
        'slavonia', 'traditional', 'budget',
        4.3, 98, ['Čobanac', 'Kulen', 'Fiš paprikaš'],
        '+385 31 890 123',
      ),
    ]);

    // Lika restaurants
    restaurants.addAll([
      _createRestaurant(
        'Lička kuća',
        'Horská restaurace s tradičními specialitami z Liky',
        'Josipa Jelačića 12, Gospić',
        44.5467, 15.3750,
        'lika', 'traditional', 'budget',
        4.2, 76, ['Janjetina ispod peke', 'Lički krumpir', 'Škripavac'],
        '+385 53 901 234',
      ),
    ]);

    // Přidá náhodné variace
    for (int i = 0; i < 15; i++) {
      restaurants.add(_createRandomRestaurant(random));
    }

    return restaurants;
  }

  Restaurant _createRestaurant(
    String name,
    String description,
    String address,
    double latitude,
    double longitude,
    String region,
    String cuisineType,
    String priceRange,
    double rating,
    int reviewCount,
    List<String> specialties,
    String phone,
  ) {
    return Restaurant(
      id: 'rest_${name.toLowerCase().replaceAll(' ', '_')}',
      name: name,
      description: description,
      address: address,
      latitude: latitude,
      longitude: longitude,
      region: region,
      cuisineType: cuisineType,
      priceRange: priceRange,
      rating: rating,
      reviewCount: reviewCount,
      specialties: specialties,
      phone: phone,
      website: 'https://www.${name.toLowerCase().replaceAll(' ', '')}.hr',
      workingHours: _generateWorkingHours(),
      features: _generateFeatures(),
      isActive: true,
      lastUpdated: DateTime.now(),
    );
  }

  Restaurant _createRandomRestaurant(Random random) {
    final regions = ['dalmatia', 'istria', 'zagreb', 'slavonia', 'lika'];
    final cuisineTypes = ['traditional', 'seafood', 'mediterranean', 'international'];
    final priceRanges = ['budget', 'mid', 'upscale', 'fine'];
    
    final names = [
      'Konoba Adriatic', 'Villa Marina', 'Taverna Sunset', 'Restaurant Panorama',
      'Konoba Dalmacija', 'Bistro Central', 'Grill House', 'Seafood Palace',
      'Traditional Corner', 'Modern Kitchen', 'Coastal View', 'Mountain Taste'
    ];

    final region = regions[random.nextInt(regions.length)];
    final name = names[random.nextInt(names.length)] + ' ${random.nextInt(100)}';
    
    return Restaurant(
      id: 'rest_random_${random.nextInt(10000)}',
      name: name,
      description: 'Skvělá restaurace s výbornou kuchyní a příjemnou atmosférou',
      address: 'Náhodná adresa ${random.nextInt(100)}, Chorvatsko',
      latitude: 42.0 + random.nextDouble() * 4.0,
      longitude: 13.0 + random.nextDouble() * 6.0,
      region: region,
      cuisineType: cuisineTypes[random.nextInt(cuisineTypes.length)],
      priceRange: priceRanges[random.nextInt(priceRanges.length)],
      rating: 3.5 + random.nextDouble() * 1.5,
      reviewCount: 20 + random.nextInt(200),
      specialties: ['Specialita 1', 'Specialita 2', 'Specialita 3'],
      phone: '+385 ${random.nextInt(99)} ${random.nextInt(999)} ${random.nextInt(999)}',
      website: 'https://www.${name.toLowerCase().replaceAll(' ', '')}.hr',
      workingHours: _generateWorkingHours(),
      features: _generateFeatures(),
      isActive: true,
      lastUpdated: DateTime.now(),
    );
  }

  List<WorkingHours> _generateWorkingHours() {
    return [
      // Pondělí - Pátek
      for (int day = 1; day <= 5; day++)
        WorkingHours(
          dayOfWeek: day,
          openHour: 11,
          openMinute: 0,
          closeHour: 23,
          closeMinute: 0,
        ),
      // Sobota - Neděle
      for (int day = 6; day <= 7; day++)
        WorkingHours(
          dayOfWeek: day,
          openHour: 12,
          openMinute: 0,
          closeHour: 24,
          closeMinute: 0,
        ),
    ];
  }

  List<String> _generateFeatures() {
    return ['terrace', 'wifi', 'creditCards', 'parking'];
  }

  Map<String, int> _getRestaurantsByRegion(List<Restaurant> restaurants) {
    final Map<String, int> result = {};
    for (final restaurant in restaurants) {
      result[restaurant.region] = (result[restaurant.region] ?? 0) + 1;
    }
    return result;
  }

  Map<String, int> _getRestaurantsByCuisineType(List<Restaurant> restaurants) {
    final Map<String, int> result = {};
    for (final restaurant in restaurants) {
      result[restaurant.cuisineType] = (result[restaurant.cuisineType] ?? 0) + 1;
    }
    return result;
  }

  Map<String, int> _getRestaurantsByPriceRange(List<Restaurant> restaurants) {
    final Map<String, int> result = {};
    for (final restaurant in restaurants) {
      result[restaurant.priceRange] = (result[restaurant.priceRange] ?? 0) + 1;
    }
    return result;
  }
}

/// Statistiky restaurací
class RestaurantStatistics {
  final int totalRestaurants;
  final int openNow;
  final int topRated;
  final int traditionalCroatian;
  final Map<String, int> byRegion;
  final Map<String, int> byCuisineType;
  final Map<String, int> byPriceRange;

  RestaurantStatistics({
    required this.totalRestaurants,
    required this.openNow,
    required this.topRated,
    required this.traditionalCroatian,
    required this.byRegion,
    required this.byCuisineType,
    required this.byPriceRange,
  });
}
