import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/ai_orchestrator.dart';
import 'ai_assistant_service.dart';
import 'cloud_ai_service.dart';
import 'contextual_ai_service.dart';
import 'ai_learning_service.dart';
import 'offline_ai_service.dart';
import 'monument_recognition_service.dart';
import 'location_service.dart';
import 'personalization_service.dart';

/// Centrální orchestrátor pro všechny AI služby
class AIOrchestrator {
  static final AIOrchestrator _instance = AIOrchestrator._internal();
  factory AIOrchestrator() => _instance;
  AIOrchestrator._internal();

  // AI služby
  final AIAssistantService _assistant = AIAssistantService();
  final CloudAIService _cloudAI = CloudAIService();
  final ContextualAIService _contextualAI = ContextualAIService();
  final AILearningService _learning = AILearningService();
  final OfflineAIService _offlineAI = OfflineAIService();
  final MonumentRecognitionService _monumentRecognition =
      MonumentRecognitionService();
  final LocationService _locationService = LocationService();
  final PersonalizationService _personalization = PersonalizationService();

  // Stav
  bool _isInitialized = false;
  AIContext? _currentContext;
  final Connectivity _connectivity = Connectivity();

  /// Inicializace AI orchestrátoru
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🤖 Inicializuji AI Orchestrator...');

      // Paralelní inicializace všech služeb
      await Future.wait([
        _assistant.initialize(),
        _cloudAI.initialize(),
        _contextualAI.initialize(),
        _learning.initialize(),
        _offlineAI.initialize(),
        _monumentRecognition.initialize(),
        _locationService.initialize(),
        _personalization.initialize(),
      ]);

      // Načtení kontextu
      await _loadContext();

      _isInitialized = true;
      debugPrint('✅ AI Orchestrator inicializován úspěšně');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci AI Orchestrator: $e');
      _isInitialized = true; // Pokračujeme i s chybou
    }
  }

  /// Hlavní metoda pro zpracování dotazů
  Future<AIResponse> processQuery(
    String query, {
    AIContext? context,
    bool useVoice = false,
    bool forceOffline = false,
  }) async {
    try {
      // Aktualizace kontextu
      final currentContext = context ?? await _getCurrentContext();

      // Analýza dotazu
      final queryAnalysis = await _analyzeQuery(query, currentContext);

      // Výběr nejlepší AI služby
      final aiService = await _selectBestAIService(queryAnalysis, forceOffline);

      // Generování odpovědi
      final response = await _generateResponse(
        query,
        currentContext,
        aiService,
      );

      // Učení z interakce
      await _learning.recordInteraction(
        UserInteraction(
          query: query,
          response: response.content,
          wasHelpful: true, // Bude aktualizováno na základě feedback
          confidence: response.confidence,
          timestamp: DateTime.now(),
        ),
      );

      // Aktualizace kontextu
      await _updateContext(query, response);

      return response;
    } catch (e) {
      debugPrint('❌ Chyba při zpracování dotazu: $e');
      return _createErrorResponse();
    }
  }

  /// Zpracování hlasového dotazu
  Future<AIResponse> processVoiceQuery(String audioPath) async {
    try {
      // Převod řeči na text
      final text = await _assistant.convertSpeechToText(audioPath);

      // Zpracování jako textový dotaz
      final response = await processQuery(text, useVoice: true);

      // Převod odpovědi na řeč
      await _assistant.speak(response.content);

      return response;
    } catch (e) {
      debugPrint('❌ Chyba při zpracování hlasového dotazu: $e');
      return _createErrorResponse();
    }
  }

  /// Rozpoznání památky z obrázku
  Future<AIResponse> recognizeMonument(String imagePath) async {
    try {
      final monument = await _monumentRecognition.recognizeFromImage(imagePath);

      if (monument != null) {
        final response = await _contextualAI.generateMonumentResponse(monument);
        return AIResponse(
          content: response,
          type: AIResponseType.recommendation,
          confidence: 0.9,
          source: AIResponseSource.local,
          timestamp: DateTime.now(),
          recommendations: [
            AIRecommendation(
              id: monument.id,
              title: monument.name,
              description: monument.description,
              type: RecommendationType.attraction,
              score: 0.95,
              data: monument.toJson(),
            ),
          ],
        );
      } else {
        return AIResponse(
          content:
              'Nepodařilo se mi rozpoznat tuto památku. Můžete mi říct více informací?',
          type: AIResponseType.clarification,
          confidence: 0.3,
          source: AIResponseSource.local,
          timestamp: DateTime.now(),
        );
      }
    } catch (e) {
      debugPrint('❌ Chyba při rozpoznávání památky: $e');
      return _createErrorResponse();
    }
  }

  /// Personalizované doporučení
  Future<List<AIRecommendation>> getPersonalizedRecommendations({
    LocationData? location,
    int limit = 10,
  }) async {
    try {
      final context = await _getCurrentContext();
      final userLocation = location ?? context.currentLocation;

      if (userLocation == null) {
        return [];
      }

      return await _contextualAI.getPersonalizedRecommendations(
        context.userProfile!,
        userLocation,
        limit: limit,
      );
    } catch (e) {
      debugPrint('❌ Chyba při získávání doporučení: $e');
      return [];
    }
  }

  /// Analýza dotazu
  Future<QueryAnalysis> _analyzeQuery(String query, AIContext context) async {
    return await _contextualAI.analyzeQuery(query, context);
  }

  /// Výběr nejlepší AI služby
  Future<AIServiceType> _selectBestAIService(
    QueryAnalysis analysis,
    bool forceOffline,
  ) async {
    if (forceOffline) return AIServiceType.offline;

    // Kontrola připojení
    final connectivityResults = await _connectivity.checkConnectivity();
    final isOnline = !connectivityResults.contains(ConnectivityResult.none);

    if (!isOnline) return AIServiceType.offline;

    // Výběr na základě typu dotazu
    switch (analysis.intent) {
      case QueryIntent.complex:
      case QueryIntent.creative:
        return AIServiceType.cloud;
      case QueryIntent.factual:
      case QueryIntent.navigation:
        return AIServiceType.contextual;
      case QueryIntent.simple:
      default:
        return AIServiceType.local;
    }
  }

  /// Generování odpovědi
  Future<AIResponse> _generateResponse(
    String query,
    AIContext context,
    AIServiceType serviceType,
  ) async {
    switch (serviceType) {
      case AIServiceType.cloud:
        return await _cloudAI.generateResponse(query, context);
      case AIServiceType.contextual:
        return await _contextualAI.generateResponse(query, context);
      case AIServiceType.offline:
        return await _offlineAI.generateResponse(query, context);
      case AIServiceType.local:
      default:
        return await _assistant.generateLocalResponse(query, context);
    }
  }

  /// Načtení kontextu
  Future<void> _loadContext() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final contextJson = prefs.getString('ai_context');

      if (contextJson != null) {
        final contextData = jsonDecode(contextJson);
        _currentContext = AIContext.fromJson(contextData);
      } else {
        _currentContext = await _createDefaultContext();
      }
    } catch (e) {
      debugPrint('❌ Chyba při načítání kontextu: $e');
      _currentContext = await _createDefaultContext();
    }
  }

  /// Vytvoření výchozího kontextu
  Future<AIContext> _createDefaultContext() async {
    final userProfileData = await _personalization.getUserProfile();

    LocationData? locationData;
    try {
      final location = await _locationService.getCurrentLocation();
      locationData = LocationData(
        latitude: location.latitude,
        longitude: location.longitude,
        accuracy: location.accuracy,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      debugPrint('Nepodařilo se získat polohu: $e');
    }

    // Konverze UserProfile z user_models na UserProfile z ai_orchestrator
    UserProfile? userProfile;
    if (userProfileData != null) {
      userProfile = UserProfile(
        id: userProfileData.id,
        name: userProfileData.displayName ?? 'Uživatel',
        interests: [], // TODO: mapovat z travelPreferences
        preferences: {},
        visitedPlaces: userProfileData.visitedPlaces,
        preferredLanguage: userProfileData.languages.isNotEmpty
            ? userProfileData.languages.first
            : 'cs',
        travelStyle: TravelStyle.balanced, // TODO: mapovat z travelPreferences
        budget: Budget.medium, // TODO: mapovat z budgetCategory
      );
    }

    return AIContext(
      userId: userProfileData?.id ?? 'anonymous',
      currentLocation: locationData,
      userProfile: userProfile,
      timestamp: DateTime.now(),
      memory: ConversationMemory(lastUpdated: DateTime.now()),
    );
  }

  /// Aktualizace kontextu
  Future<void> _updateContext(String query, AIResponse response) async {
    if (_currentContext == null) return;

    try {
      // Aktualizace paměti
      final newMemory = _currentContext!.memory?.addInteraction(
        UserInteraction(
          query: query,
          response: response.content,
          wasHelpful: true,
          confidence: response.confidence,
          timestamp: DateTime.now(),
        ),
      );

      // Aktualizace kontextu
      _currentContext = _currentContext!.copyWith(
        recentQueries: [
          ..._currentContext!.recentQueries,
          query,
        ].take(10).toList(),
        timestamp: DateTime.now(),
        memory: newMemory,
      );

      // Uložení
      await _saveContext();
    } catch (e) {
      debugPrint('❌ Chyba při aktualizaci kontextu: $e');
    }
  }

  /// Uložení kontextu
  Future<void> _saveContext() async {
    if (_currentContext == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'ai_context',
        jsonEncode(_currentContext!.toJson()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání kontextu: $e');
    }
  }

  /// Získání aktuálního kontextu
  Future<AIContext> _getCurrentContext() async {
    if (_currentContext == null) {
      await _loadContext();
    }
    return _currentContext!;
  }

  /// Vytvoření chybové odpovědi
  AIResponse _createErrorResponse() {
    return AIResponse(
      content: 'Omlouvám se, došlo k chybě. Zkuste to prosím znovu.',
      type: AIResponseType.error,
      confidence: 0.0,
      source: AIResponseSource.fallback,
      timestamp: DateTime.now(),
    );
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  AIContext? get currentContext => _currentContext;
}

/// Typ AI služby
enum AIServiceType { local, cloud, contextual, offline }

/// Analýza dotazu
class QueryAnalysis {
  final QueryIntent intent;
  final double complexity;
  final List<String> entities;
  final Map<String, dynamic> metadata;

  const QueryAnalysis({
    required this.intent,
    required this.complexity,
    this.entities = const [],
    this.metadata = const {},
  });
}

/// Záměr dotazu
enum QueryIntent {
  simple,
  complex,
  factual,
  creative,
  navigation,
  recommendation,
}
