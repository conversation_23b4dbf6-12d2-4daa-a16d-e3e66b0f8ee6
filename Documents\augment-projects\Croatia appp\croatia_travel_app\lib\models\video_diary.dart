import 'package:json_annotation/json_annotation.dart';

part 'video_diary.g.dart';

@JsonSerializable()
class VideoDiary {
  final String id;
  final String title;
  final String? description;
  final String filePath;
  final String? thumbnailPath;
  final Duration duration;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final int fileSize; // v bytech
  final List<String> tags;
  final String? location;
  final double? latitude;
  final double? longitude;
  final VideoQuality quality;
  final VideoOrientation orientation;
  final int width;
  final int height;
  final double frameRate;
  final bool hasAudio;
  final bool isFavorite;
  final bool isProcessed;
  final List<VideoEdit> edits;
  final String? musicTrack;
  final List<VideoFilter> filters;

  VideoDiary({
    required this.id,
    required this.title,
    this.description,
    required this.filePath,
    this.thumbnailPath,
    required this.duration,
    required this.createdAt,
    this.updatedAt,
    required this.fileSize,
    this.tags = const [],
    this.location,
    this.latitude,
    this.longitude,
    this.quality = VideoQuality.medium,
    this.orientation = VideoOrientation.landscape,
    required this.width,
    required this.height,
    this.frameRate = 30.0,
    this.hasAudio = true,
    this.isFavorite = false,
    this.isProcessed = false,
    this.edits = const [],
    this.musicTrack,
    this.filters = const [],
  });

  factory VideoDiary.fromJson(Map<String, dynamic> json) => _$VideoDiaryFromJson(json);
  Map<String, dynamic> toJson() => _$VideoDiaryToJson(this);

  VideoDiary copyWith({
    String? id,
    String? title,
    String? description,
    String? filePath,
    String? thumbnailPath,
    Duration? duration,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? fileSize,
    List<String>? tags,
    String? location,
    double? latitude,
    double? longitude,
    VideoQuality? quality,
    VideoOrientation? orientation,
    int? width,
    int? height,
    double? frameRate,
    bool? hasAudio,
    bool? isFavorite,
    bool? isProcessed,
    List<VideoEdit>? edits,
    String? musicTrack,
    List<VideoFilter>? filters,
  }) {
    return VideoDiary(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      filePath: filePath ?? this.filePath,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      duration: duration ?? this.duration,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      fileSize: fileSize ?? this.fileSize,
      tags: tags ?? this.tags,
      location: location ?? this.location,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      quality: quality ?? this.quality,
      orientation: orientation ?? this.orientation,
      width: width ?? this.width,
      height: height ?? this.height,
      frameRate: frameRate ?? this.frameRate,
      hasAudio: hasAudio ?? this.hasAudio,
      isFavorite: isFavorite ?? this.isFavorite,
      isProcessed: isProcessed ?? this.isProcessed,
      edits: edits ?? this.edits,
      musicTrack: musicTrack ?? this.musicTrack,
      filters: filters ?? this.filters,
    );
  }

  /// Formátovaná doba trvání
  String get formattedDuration {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// Formátovaná velikost souboru
  String get formattedFileSize {
    if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    } else if (fileSize < 1024 * 1024 * 1024) {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// Rozlišení videa
  String get resolution => '${width}x$height';

  /// Kontrola existence souboru
  bool get hasValidFile => filePath.isNotEmpty;

  /// Kontrola lokace
  bool get hasLocation => latitude != null && longitude != null;

  /// Kontrola thumbnail
  bool get hasThumbnail => thumbnailPath != null && thumbnailPath!.isNotEmpty;

  /// Poměr stran
  double get aspectRatio => width / height;

  /// Je video editované?
  bool get isEdited => edits.isNotEmpty || filters.isNotEmpty || musicTrack != null;
}

@JsonSerializable()
class VideoEdit {
  final String id;
  final VideoEditType type;
  final Duration startTime;
  final Duration endTime;
  final Map<String, dynamic> parameters;
  final DateTime createdAt;

  VideoEdit({
    required this.id,
    required this.type,
    required this.startTime,
    required this.endTime,
    required this.parameters,
    required this.createdAt,
  });

  factory VideoEdit.fromJson(Map<String, dynamic> json) => _$VideoEditFromJson(json);
  Map<String, dynamic> toJson() => _$VideoEditToJson(this);
}

@JsonSerializable()
class VideoFilter {
  final String id;
  final String name;
  final VideoFilterType type;
  final double intensity;
  final Map<String, dynamic> settings;
  final bool isEnabled;

  VideoFilter({
    required this.id,
    required this.name,
    required this.type,
    this.intensity = 1.0,
    this.settings = const {},
    this.isEnabled = true,
  });

  factory VideoFilter.fromJson(Map<String, dynamic> json) => _$VideoFilterFromJson(json);
  Map<String, dynamic> toJson() => _$VideoFilterToJson(this);
}

enum VideoQuality {
  @JsonValue('low')
  low,
  @JsonValue('medium')
  medium,
  @JsonValue('high')
  high,
  @JsonValue('ultra')
  ultra,
}

enum VideoOrientation {
  @JsonValue('portrait')
  portrait,
  @JsonValue('landscape')
  landscape,
  @JsonValue('square')
  square,
}

enum VideoEditType {
  @JsonValue('trim')
  trim,
  @JsonValue('cut')
  cut,
  @JsonValue('merge')
  merge,
  @JsonValue('speed')
  speed,
  @JsonValue('transition')
  transition,
  @JsonValue('text_overlay')
  textOverlay,
  @JsonValue('audio_overlay')
  audioOverlay,
}

enum VideoFilterType {
  @JsonValue('color_correction')
  colorCorrection,
  @JsonValue('vintage')
  vintage,
  @JsonValue('black_white')
  blackWhite,
  @JsonValue('sepia')
  sepia,
  @JsonValue('blur')
  blur,
  @JsonValue('sharpen')
  sharpen,
  @JsonValue('croatia_sunset')
  croatiaSunset,
  @JsonValue('adriatic_blue')
  adriaticBlue,
  @JsonValue('mediterranean')
  mediterranean,
}

extension VideoQualityExtension on VideoQuality {
  String get displayName {
    switch (this) {
      case VideoQuality.low:
        return 'Nízká (480p)';
      case VideoQuality.medium:
        return 'Střední (720p)';
      case VideoQuality.high:
        return 'Vysoká (1080p)';
      case VideoQuality.ultra:
        return 'Ultra (4K)';
    }
  }

  int get height {
    switch (this) {
      case VideoQuality.low:
        return 480;
      case VideoQuality.medium:
        return 720;
      case VideoQuality.high:
        return 1080;
      case VideoQuality.ultra:
        return 2160;
    }
  }

  int get bitrate {
    switch (this) {
      case VideoQuality.low:
        return 1000000; // 1 Mbps
      case VideoQuality.medium:
        return 3000000; // 3 Mbps
      case VideoQuality.high:
        return 8000000; // 8 Mbps
      case VideoQuality.ultra:
        return 25000000; // 25 Mbps
    }
  }
}

extension VideoFilterTypeExtension on VideoFilterType {
  String get displayName {
    switch (this) {
      case VideoFilterType.colorCorrection:
        return 'Korekce barev';
      case VideoFilterType.vintage:
        return 'Vintage';
      case VideoFilterType.blackWhite:
        return 'Černobílý';
      case VideoFilterType.sepia:
        return 'Sepia';
      case VideoFilterType.blur:
        return 'Rozmazání';
      case VideoFilterType.sharpen:
        return 'Zaostření';
      case VideoFilterType.croatiaSunset:
        return 'Chorvatský západ slunce';
      case VideoFilterType.adriaticBlue:
        return 'Jaderská modrá';
      case VideoFilterType.mediterranean:
        return 'Středomořský';
    }
  }

  String get description {
    switch (this) {
      case VideoFilterType.colorCorrection:
        return 'Základní úprava barev a kontrastu';
      case VideoFilterType.vintage:
        return 'Retro vzhled s teplými tóny';
      case VideoFilterType.blackWhite:
        return 'Klasický černobílý efekt';
      case VideoFilterType.sepia:
        return 'Nostalgický sepia tón';
      case VideoFilterType.blur:
        return 'Jemné rozmazání pozadí';
      case VideoFilterType.sharpen:
        return 'Zvýšení ostrosti detailů';
      case VideoFilterType.croatiaSunset:
        return 'Teplé oranžové tóny chorvatských západů slunce';
      case VideoFilterType.adriaticBlue:
        return 'Hluboké modré tóny Jaderského moře';
      case VideoFilterType.mediterranean:
        return 'Živé barvy středomořské krajiny';
    }
  }
}
