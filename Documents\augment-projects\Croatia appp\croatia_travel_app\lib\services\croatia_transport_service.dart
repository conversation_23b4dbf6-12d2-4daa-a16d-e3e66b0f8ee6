import '../models/transport_connection.dart';

/// 🚌 SLUŽBA PRO VYHLEDÁVÁNÍ DOPRAVNÍCH SPOJENÍ V CHORVATSKU
class CroatiaTransportService {
  
  /// 🔍 Vyhledání spojení mezi městy
  Future<List<TransportConnection>> searchConnections({
    required String from,
    required String to,
    required DateTime date,
    required List<TransportType> transportTypes,
  }) async {
    // Simulace API volání
    await Future.delayed(const Duration(seconds: 2));
    
    // Vrátit demo data podle zadání
    return _getDemoConnections(from, to, transportTypes);
  }

  /// 📊 Demo data pro spojení
  List<TransportConnection> _getDemoConnections(
    String from, 
    String to, 
    List<TransportType> types
  ) {
    final connections = <TransportConnection>[];
    
    // Příklad: Rijeka → Pag
    if (from.toLowerCase().contains('rijeka') && to.toLowerCase().contains('pag')) {
      if (types.contains(TransportType.bus)) {
        connections.add(TransportConnection(
          id: 'rijeka_pag_bus_1',
          fromCity: 'Rijeka',
          toCity: 'Pag',
          fromStation: 'Autobusni kolodvor Rijeka',
          toStation: 'Pag - centar',
          type: TransportType.bus,
          operatorName: 'Autotrans',
          operatorWebsite: 'https://www.autotrans.hr',
          operatorPhone: '+385 21 338 347',
          duration: const Duration(hours: 3, minutes: 30),
          price: 85.0,
          currency: 'HRK',
          departureTimes: ['06:30', '10:15', '14:45', '18:20'],
          arrivalTimes: ['10:00', '13:45', '18:15', '21:50'],
          operatingDays: {
            'monday': true,
            'tuesday': true,
            'wednesday': true,
            'thursday': true,
            'friday': true,
            'saturday': true,
            'sunday': true,
          },
          isDirectConnection: false,
          intermediateStops: ['Senj', 'Karlobag', 'Starigrad'],
          notes: 'Přestup v Senju. Rezervace doporučena v letní sezóně.',
          bookingRequired: true,
          distance: 180.0,
        ));
      }

      if (types.contains(TransportType.ferry)) {
        connections.add(TransportConnection(
          id: 'rijeka_pag_ferry_1',
          fromCity: 'Rijeka',
          toCity: 'Pag',
          fromStation: 'Luka Rijeka',
          toStation: 'Luka Novalja',
          type: TransportType.ferry,
          operatorName: 'Jadrolinija',
          operatorWebsite: 'https://www.jadrolinija.hr',
          operatorPhone: '+385 51 666 111',
          duration: const Duration(hours: 2, minutes: 45),
          price: 120.0,
          currency: 'HRK',
          departureTimes: ['08:00', '15:30'],
          arrivalTimes: ['10:45', '18:15'],
          operatingDays: {
            'monday': true,
            'tuesday': true,
            'wednesday': true,
            'thursday': true,
            'friday': true,
            'saturday': true,
            'sunday': false,
          },
          isDirectConnection: true,
          notes: 'Sezónní linka (květen-září). Rezervace povinná pro vozidla.',
          bookingRequired: true,
          distance: 95.0,
        ));
      }
    }

    // Zagreb → Split
    if (from.toLowerCase().contains('zagreb') && to.toLowerCase().contains('split')) {
      if (types.contains(TransportType.bus)) {
        connections.add(TransportConnection(
          id: 'zagreb_split_bus_1',
          fromCity: 'Zagreb',
          toCity: 'Split',
          fromStation: 'Autobusni kolodvor Zagreb',
          toStation: 'Autobusni kolodvor Split',
          type: TransportType.bus,
          operatorName: 'Arriva Croatia',
          operatorWebsite: 'https://arriva.com.hr',
          operatorPhone: '+385 72 660 660',
          duration: const Duration(hours: 5, minutes: 30),
          price: 150.0,
          currency: 'HRK',
          departureTimes: ['06:00', '08:30', '11:00', '14:30', '17:00', '20:30'],
          arrivalTimes: ['11:30', '14:00', '16:30', '20:00', '22:30', '02:00'],
          operatingDays: {
            'monday': true,
            'tuesday': true,
            'wednesday': true,
            'thursday': true,
            'friday': true,
            'saturday': true,
            'sunday': true,
          },
          isDirectConnection: true,
          notes: 'Nejrychlejší spojení. Online rezervace dostupná.',
          bookingRequired: false,
          distance: 380.0,
        ));
      }

      if (types.contains(TransportType.train)) {
        connections.add(TransportConnection(
          id: 'zagreb_split_train_1',
          fromCity: 'Zagreb',
          toCity: 'Split',
          fromStation: 'Zagreb Glavni kolodvor',
          toStation: 'Split železnička stanica',
          type: TransportType.train,
          operatorName: 'Hrvatske željeznice (HŽ)',
          operatorWebsite: 'https://www.hzpp.hr',
          operatorPhone: '+385 1 3782 583',
          duration: const Duration(hours: 6, minutes: 45),
          price: 120.0,
          currency: 'HRK',
          departureTimes: ['07:05', '22:05'],
          arrivalTimes: ['13:50', '04:50'],
          operatingDays: {
            'monday': true,
            'tuesday': true,
            'wednesday': true,
            'thursday': true,
            'friday': true,
            'saturday': true,
            'sunday': true,
          },
          isDirectConnection: true,
          notes: 'Noční vlak s lůžkovými vozy. Krásné výhledy na krajinu.',
          bookingRequired: true,
          distance: 410.0,
        ));
      }

      if (types.contains(TransportType.plane)) {
        connections.add(TransportConnection(
          id: 'zagreb_split_plane_1',
          fromCity: 'Zagreb',
          toCity: 'Split',
          fromStation: 'Zagreb Airport (ZAG)',
          toStation: 'Split Airport (SPU)',
          type: TransportType.plane,
          operatorName: 'Croatia Airlines',
          operatorWebsite: 'https://www.croatiaairlines.com',
          operatorPhone: '+385 1 6676 555',
          duration: const Duration(hours: 1, minutes: 15),
          price: 450.0,
          currency: 'HRK',
          departureTimes: ['07:30', '12:45', '18:20'],
          arrivalTimes: ['08:45', '14:00', '19:35'],
          operatingDays: {
            'monday': true,
            'tuesday': true,
            'wednesday': true,
            'thursday': true,
            'friday': true,
            'saturday': true,
            'sunday': true,
          },
          isDirectConnection: true,
          notes: 'Nejrychlejší způsob dopravy. Včetně transferu na letiště.',
          bookingRequired: true,
          distance: 320.0,
        ));
      }
    }

    // Split → Dubrovnik
    if (from.toLowerCase().contains('split') && to.toLowerCase().contains('dubrovnik')) {
      if (types.contains(TransportType.bus)) {
        connections.add(TransportConnection(
          id: 'split_dubrovnik_bus_1',
          fromCity: 'Split',
          toCity: 'Dubrovnik',
          fromStation: 'Autobusni kolodvor Split',
          toStation: 'Autobusni kolodvor Dubrovnik',
          type: TransportType.bus,
          operatorName: 'Autotrans',
          operatorWebsite: 'https://www.autotrans.hr',
          operatorPhone: '+385 21 338 347',
          duration: const Duration(hours: 4, minutes: 30),
          price: 120.0,
          currency: 'HRK',
          departureTimes: ['06:30', '09:15', '12:00', '15:30', '18:45'],
          arrivalTimes: ['11:00', '13:45', '16:30', '20:00', '23:15'],
          operatingDays: {
            'monday': true,
            'tuesday': true,
            'wednesday': true,
            'thursday': true,
            'friday': true,
            'saturday': true,
            'sunday': true,
          },
          isDirectConnection: true,
          notes: 'Krásná cesta podél pobřeží. Klimatizované autobusy.',
          bookingRequired: false,
          distance: 230.0,
        ));
      }

      if (types.contains(TransportType.catamaran)) {
        connections.add(TransportConnection(
          id: 'split_dubrovnik_catamaran_1',
          fromCity: 'Split',
          toCity: 'Dubrovnik',
          fromStation: 'Luka Split',
          toStation: 'Luka Gruž Dubrovnik',
          type: TransportType.catamaran,
          operatorName: 'Jadrolinija',
          operatorWebsite: 'https://www.jadrolinija.hr',
          operatorPhone: '+385 51 666 111',
          duration: const Duration(hours: 4, minutes: 15),
          price: 180.0,
          currency: 'HRK',
          departureTimes: ['09:00', '16:30'],
          arrivalTimes: ['13:15', '20:45'],
          operatingDays: {
            'monday': true,
            'tuesday': true,
            'wednesday': true,
            'thursday': true,
            'friday': true,
            'saturday': true,
            'sunday': false,
          },
          isDirectConnection: false,
          intermediateStops: ['Korčula', 'Mljet'],
          notes: 'Sezónní linka (červen-září). Nádherné výhledy na ostrovy.',
          bookingRequired: true,
          distance: 160.0,
        ));
      }
    }

    // Obecné spojení pro ostatní města
    if (connections.isEmpty) {
      connections.add(TransportConnection(
        id: 'general_connection_1',
        fromCity: from,
        toCity: to,
        fromStation: 'Autobusni kolodvor $from',
        toStation: 'Autobusni kolodvor $to',
        type: TransportType.bus,
        operatorName: 'Arriva Croatia',
        operatorWebsite: 'https://arriva.com.hr',
        operatorPhone: '+385 72 660 660',
        duration: const Duration(hours: 2, minutes: 30),
        price: 80.0,
        currency: 'HRK',
        departureTimes: ['08:00', '12:00', '16:00', '20:00'],
        arrivalTimes: ['10:30', '14:30', '18:30', '22:30'],
        operatingDays: {
          'monday': true,
          'tuesday': true,
          'wednesday': true,
          'thursday': true,
          'friday': true,
          'saturday': true,
          'sunday': true,
        },
        isDirectConnection: true,
        notes: 'Standardní autobusové spojení. Rezervace online dostupná.',
        bookingRequired: false,
        distance: 150.0,
      ));
    }

    return connections;
  }

  /// 📋 Získání všech dopravců
  List<Map<String, dynamic>> getAllOperators() {
    return [
      {
        'name': 'Arriva Croatia',
        'website': 'https://arriva.com.hr',
        'phone': '+385 72 660 660',
        'types': ['🚌 Autobusy'],
        'regions': ['Zagreb', 'Varaždin', 'Koprivnica', 'Čakovec'],
        'logo': '🚌',
        'description': 'Největší autobusový dopravce v severním Chorvatsku',
      },
      {
        'name': 'Autotrans',
        'website': 'https://www.autotrans.hr',
        'phone': '+385 21 338 347',
        'types': ['🚌 Autobusy'],
        'regions': ['Split', 'Makarska', 'Dubrovnik', 'Dalmácie'],
        'logo': '🚌',
        'description': 'Hlavní dopravce v Dalmácii a jižním Chorvatsku',
      },
      {
        'name': 'Jadrolinija',
        'website': 'https://www.jadrolinija.hr',
        'phone': '+385 51 666 111',
        'types': ['⛴️ Trajekty', '🚤 Katamarany'],
        'regions': ['Všechny chorvatské ostrovy', 'Jadranské pobřeží'],
        'logo': '⛴️',
        'description': 'Národní námořní dopravce - spojení na všechny ostrovy',
      },
      {
        'name': 'Hrvatske željeznice (HŽ)',
        'website': 'https://www.hzpp.hr',
        'phone': '+385 1 3782 583',
        'types': ['🚂 Vlaky'],
        'regions': ['Celé Chorvatsko'],
        'logo': '🚂',
        'description': 'Národní železniční dopravce',
      },
      {
        'name': 'Croatia Airlines',
        'website': 'https://www.croatiaairlines.com',
        'phone': '+385 1 6676 555',
        'types': ['✈️ Letadla'],
        'regions': ['Domácí lety mezi hlavními městy'],
        'logo': '✈️',
        'description': 'Národní letecká společnost',
      },
      {
        'name': 'Libertas Dubrovnik',
        'website': 'https://www.libertasdubrovnik.hr',
        'phone': '+385 20 357 020',
        'types': ['🚌 Autobusy'],
        'regions': ['Dubrovnik', 'Dubrovačko-neretvanska župa'],
        'logo': '🚌',
        'description': 'Regionální dopravce v oblasti Dubrovníku',
      },
      {
        'name': 'Samoborček',
        'website': 'https://www.samoborecek.hr',
        'phone': '+385 1 3362 789',
        'types': ['🚌 Autobusy'],
        'regions': ['Zagreb', 'Samobor', 'Zagrebačka župa'],
        'logo': '🚌',
        'description': 'Regionální dopravce v oblasti Záhřebu',
      },
    ];
  }
}
