// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'voice_note.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VoiceNote _$VoiceNoteFromJson(Map<String, dynamic> json) => VoiceNote(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      filePath: json['filePath'] as String,
      duration: Duration(microseconds: (json['duration'] as num).toInt()),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      fileSize: (json['fileSize'] as num).toInt(),
      transcription: json['transcription'] as String?,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      location: json['location'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      type: $enumDecodeNullable(_$VoiceNoteTypeEnumMap, json['type']) ??
          VoiceNoteType.personal,
      isTranscribed: json['isTranscribed'] as bool? ?? false,
      isFavorite: json['isFavorite'] as bool? ?? false,
      quality: $enumDecodeNullable(_$VoiceQualityEnumMap, json['quality']) ??
          VoiceQuality.medium,
    );

Map<String, dynamic> _$VoiceNoteToJson(VoiceNote instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'filePath': instance.filePath,
      'duration': instance.duration.inMicroseconds,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'fileSize': instance.fileSize,
      'transcription': instance.transcription,
      'tags': instance.tags,
      'location': instance.location,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'type': _$VoiceNoteTypeEnumMap[instance.type]!,
      'isTranscribed': instance.isTranscribed,
      'isFavorite': instance.isFavorite,
      'quality': _$VoiceQualityEnumMap[instance.quality]!,
    };

const _$VoiceNoteTypeEnumMap = {
  VoiceNoteType.personal: 'personal',
  VoiceNoteType.diary: 'diary',
  VoiceNoteType.reminder: 'reminder',
  VoiceNoteType.review: 'review',
  VoiceNoteType.guide: 'guide',
  VoiceNoteType.interview: 'interview',
  VoiceNoteType.memo: 'memo',
};

const _$VoiceQualityEnumMap = {
  VoiceQuality.low: 'low',
  VoiceQuality.medium: 'medium',
  VoiceQuality.high: 'high',
  VoiceQuality.lossless: 'lossless',
};

VoiceRecordingEvent _$VoiceRecordingEventFromJson(Map<String, dynamic> json) =>
    VoiceRecordingEvent(
      type: $enumDecode(_$VoiceEventTypeEnumMap, json['type']),
      error: json['error'] as String?,
      status: json['status'] as String?,
      filePath: json['filePath'] as String?,
      duration: json['duration'] == null
          ? null
          : Duration(microseconds: (json['duration'] as num).toInt()),
      voiceNote: json['voiceNote'] == null
          ? null
          : VoiceNote.fromJson(json['voiceNote'] as Map<String, dynamic>),
      transcription: json['transcription'] as String?,
      isPartial: json['isPartial'] as bool?,
    );

Map<String, dynamic> _$VoiceRecordingEventToJson(
        VoiceRecordingEvent instance) =>
    <String, dynamic>{
      'type': _$VoiceEventTypeEnumMap[instance.type]!,
      'error': instance.error,
      'status': instance.status,
      'filePath': instance.filePath,
      'duration': instance.duration?.inMicroseconds,
      'voiceNote': instance.voiceNote,
      'transcription': instance.transcription,
      'isPartial': instance.isPartial,
    };

const _$VoiceEventTypeEnumMap = {
  VoiceEventType.recordingStarted: 'recording_started',
  VoiceEventType.recordingStopped: 'recording_stopped',
  VoiceEventType.recordingProgress: 'recording_progress',
  VoiceEventType.playbackStarted: 'playback_started',
  VoiceEventType.playbackStopped: 'playback_stopped',
  VoiceEventType.playbackCompleted: 'playback_completed',
  VoiceEventType.listeningStarted: 'listening_started',
  VoiceEventType.listeningStopped: 'listening_stopped',
  VoiceEventType.transcriptionUpdated: 'transcription_updated',
  VoiceEventType.ttsStarted: 'tts_started',
  VoiceEventType.ttsCompleted: 'tts_completed',
  VoiceEventType.voiceNoteDeleted: 'voice_note_deleted',
  VoiceEventType.speechStatusChanged: 'speech_status_changed',
  VoiceEventType.error: 'error',
};

VoiceNoteStatistics _$VoiceNoteStatisticsFromJson(Map<String, dynamic> json) =>
    VoiceNoteStatistics(
      totalNotes: (json['totalNotes'] as num).toInt(),
      totalDuration:
          Duration(microseconds: (json['totalDuration'] as num).toInt()),
      totalFileSize: (json['totalFileSize'] as num).toInt(),
      notesByType: (json['notesByType'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(
            $enumDecode(_$VoiceNoteTypeEnumMap, k), (e as num).toInt()),
      ),
      transcribedNotes: (json['transcribedNotes'] as num).toInt(),
      favoriteNotes: (json['favoriteNotes'] as num).toInt(),
      oldestNote: json['oldestNote'] == null
          ? null
          : DateTime.parse(json['oldestNote'] as String),
      newestNote: json['newestNote'] == null
          ? null
          : DateTime.parse(json['newestNote'] as String),
      averageDuration: (json['averageDuration'] as num).toDouble(),
    );

Map<String, dynamic> _$VoiceNoteStatisticsToJson(
        VoiceNoteStatistics instance) =>
    <String, dynamic>{
      'totalNotes': instance.totalNotes,
      'totalDuration': instance.totalDuration.inMicroseconds,
      'totalFileSize': instance.totalFileSize,
      'notesByType': instance.notesByType
          .map((k, e) => MapEntry(_$VoiceNoteTypeEnumMap[k]!, e)),
      'transcribedNotes': instance.transcribedNotes,
      'favoriteNotes': instance.favoriteNotes,
      'oldestNote': instance.oldestNote?.toIso8601String(),
      'newestNote': instance.newestNote?.toIso8601String(),
      'averageDuration': instance.averageDuration,
    };

VoiceNoteFilter _$VoiceNoteFilterFromJson(Map<String, dynamic> json) =>
    VoiceNoteFilter(
      type: $enumDecodeNullable(_$VoiceNoteTypeEnumMap, json['type']),
      isFavorite: json['isFavorite'] as bool?,
      isTranscribed: json['isTranscribed'] as bool?,
      dateFrom: json['dateFrom'] == null
          ? null
          : DateTime.parse(json['dateFrom'] as String),
      dateTo: json['dateTo'] == null
          ? null
          : DateTime.parse(json['dateTo'] as String),
      minDuration: json['minDuration'] == null
          ? null
          : Duration(microseconds: (json['minDuration'] as num).toInt()),
      maxDuration: json['maxDuration'] == null
          ? null
          : Duration(microseconds: (json['maxDuration'] as num).toInt()),
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      searchQuery: json['searchQuery'] as String?,
    );

Map<String, dynamic> _$VoiceNoteFilterToJson(VoiceNoteFilter instance) =>
    <String, dynamic>{
      'type': _$VoiceNoteTypeEnumMap[instance.type],
      'isFavorite': instance.isFavorite,
      'isTranscribed': instance.isTranscribed,
      'dateFrom': instance.dateFrom?.toIso8601String(),
      'dateTo': instance.dateTo?.toIso8601String(),
      'minDuration': instance.minDuration?.inMicroseconds,
      'maxDuration': instance.maxDuration?.inMicroseconds,
      'tags': instance.tags,
      'searchQuery': instance.searchQuery,
    };
