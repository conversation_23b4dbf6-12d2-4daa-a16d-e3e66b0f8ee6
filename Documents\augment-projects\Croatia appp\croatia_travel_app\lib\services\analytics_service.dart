import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_performance/firebase_performance.dart';

/// Služba pro analytics, crashlytics a performance monitoring
class AnalyticsService {
  static final AnalyticsService _instance = AnalyticsService._internal();
  factory AnalyticsService() => _instance;
  AnalyticsService._internal();

  late FirebaseAnalytics _analytics;
  late FirebaseCrashlytics _crashlytics;
  late FirebasePerformance _performance;
  bool _isInitialized = false;

  // Gettery
  FirebaseAnalytics get analytics => _analytics;
  FirebaseCrashlytics get crashlytics => _crashlytics;
  FirebasePerformance get performance => _performance;
  bool get isInitialized => _isInitialized;

  /// Inicializuje analytics službu
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _analytics = FirebaseAnalytics.instance;
      _crashlytics = FirebaseCrashlytics.instance;
      _performance = FirebasePerformance.instance;

      // Nastaví crashlytics
      await _setupCrashlytics();

      // Nastaví performance monitoring
      await _setupPerformance();

      _isInitialized = true;
      debugPrint('Analytics služba inicializována');

      // Zaloguje inicializaci
      await logEvent('app_initialized', {
        'platform': defaultTargetPlatform.name,
        'debug_mode': kDebugMode,
      });
    } catch (e) {
      debugPrint('Chyba při inicializaci analytics: $e');
      _isInitialized = true; // Pokračuje i při chybě
    }
  }

  /// Nastaví Firebase Crashlytics
  Future<void> _setupCrashlytics() async {
    try {
      // Zapne crashlytics pouze v release mode
      await _crashlytics.setCrashlyticsCollectionEnabled(!kDebugMode);

      // Nastaví custom keys
      await _crashlytics.setCustomKey('app_version', '1.0.0');
      await _crashlytics.setCustomKey(
        'build_mode',
        kDebugMode ? 'debug' : 'release',
      );

      debugPrint('Crashlytics nastaven');
    } catch (e) {
      debugPrint('Chyba při nastavování crashlytics: $e');
    }
  }

  /// Nastaví Firebase Performance
  Future<void> _setupPerformance() async {
    try {
      // Zapne performance monitoring
      await _performance.setPerformanceCollectionEnabled(!kDebugMode);

      debugPrint('Performance monitoring nastaven');
    } catch (e) {
      debugPrint('Chyba při nastavování performance: $e');
    }
  }

  /// Zaloguje custom event
  Future<void> logEvent(String name, [Map<String, dynamic>? parameters]) async {
    if (!_isInitialized) return;

    try {
      await _analytics.logEvent(
        name: name,
        parameters: parameters?.cast<String, Object>(),
      );
      debugPrint('Event zalogován: $name');
    } catch (e) {
      debugPrint('Chyba při logování eventu: $e');
    }
  }

  /// Zaloguje screen view
  Future<void> logScreenView(String screenName, [String? screenClass]) async {
    if (!_isInitialized) return;

    try {
      await _analytics.logScreenView(
        screenName: screenName,
        screenClass: screenClass ?? screenName,
      );
      debugPrint('Screen view zalogován: $screenName');
    } catch (e) {
      debugPrint('Chyba při logování screen view: $e');
    }
  }

  /// Nastaví user properties
  Future<void> setUserProperties({
    String? userId,
    String? userType,
    Map<String, String>? customProperties,
  }) async {
    if (!_isInitialized) return;

    try {
      if (userId != null) {
        await _analytics.setUserId(id: userId);
        await _crashlytics.setUserIdentifier(userId);
      }

      if (userType != null) {
        await _analytics.setUserProperty(name: 'user_type', value: userType);
      }

      if (customProperties != null) {
        for (final entry in customProperties.entries) {
          await _analytics.setUserProperty(name: entry.key, value: entry.value);
        }
      }

      debugPrint('User properties nastaveny');
    } catch (e) {
      debugPrint('Chyba při nastavování user properties: $e');
    }
  }

  /// Zaloguje chybu do crashlytics
  Future<void> logError(
    dynamic exception,
    StackTrace? stackTrace, {
    String? reason,
    Map<String, dynamic>? customData,
    bool fatal = false,
  }) async {
    if (!_isInitialized) return;

    try {
      // Nastaví custom data
      if (customData != null) {
        for (final entry in customData.entries) {
          await _crashlytics.setCustomKey(entry.key, entry.value);
        }
      }

      // Zaloguje chybu
      await _crashlytics.recordError(
        exception,
        stackTrace,
        reason: reason,
        fatal: fatal,
      );

      debugPrint('Chyba zalogována do crashlytics: $exception');
    } catch (e) {
      debugPrint('Chyba při logování do crashlytics: $e');
    }
  }

  /// Vytvoří performance trace
  Trace createTrace(String name) {
    return _performance.newTrace(name);
  }

  /// Zaloguje AI assistant interakci
  Future<void> logAIInteraction({
    required String query,
    required String response,
    required bool isOffline,
    required Duration responseTime,
  }) async {
    await logEvent('ai_interaction', {
      'query_length': query.length,
      'response_length': response.length,
      'is_offline': isOffline,
      'response_time_ms': responseTime.inMilliseconds,
    });
  }

  /// Zaloguje ticket booking
  Future<void> logTicketBooking({
    required String ticketId,
    required String venue,
    required double price,
    required String paymentMethod,
  }) async {
    await logEvent('ticket_booking', {
      'ticket_id': ticketId,
      'venue': venue,
      'price': price,
      'payment_method': paymentMethod,
    });
  }

  /// Zaloguje map interaction
  Future<void> logMapInteraction({
    required String action,
    required String placeType,
    double? latitude,
    double? longitude,
  }) async {
    await logEvent('map_interaction', {
      'action': action,
      'place_type': placeType,
      'has_location': latitude != null && longitude != null,
    });
  }

  /// Zaloguje offline data download
  Future<void> logOfflineDownload({
    required String packageType,
    required int sizeBytes,
    required Duration downloadTime,
    required bool success,
  }) async {
    await logEvent('offline_download', {
      'package_type': packageType,
      'size_mb': (sizeBytes / (1024 * 1024)).round(),
      'download_time_s': downloadTime.inSeconds,
      'success': success,
    });
  }

  /// Zaloguje camera usage
  Future<void> logCameraUsage({
    required String action,
    required bool hasGPS,
    String? qrCodeType,
  }) async {
    await logEvent('camera_usage', {
      'action': action,
      'has_gps': hasGPS,
      'qr_code_type': qrCodeType,
    });
  }

  /// Zaloguje personalization interaction
  Future<void> logPersonalizationInteraction({
    required String action,
    required String contentType,
    double? relevanceScore,
  }) async {
    await logEvent('personalization_interaction', {
      'action': action,
      'content_type': contentType,
      'relevance_score': relevanceScore,
    });
  }

  /// Zaloguje emergency service usage
  Future<void> logEmergencyServiceUsage({
    required String serviceType,
    required String region,
    required bool hasLocation,
  }) async {
    await logEvent('emergency_service_usage', {
      'service_type': serviceType,
      'region': region,
      'has_location': hasLocation,
    });
  }

  /// Zaloguje search query
  Future<void> logSearch({
    required String query,
    required String category,
    required int resultsCount,
  }) async {
    await logEvent('search', {
      'query_length': query.length,
      'category': category,
      'results_count': resultsCount,
    });
  }

  /// Zaloguje app performance metrics
  Future<void> logPerformanceMetrics({
    required String screenName,
    required Duration loadTime,
    required int memoryUsageMB,
  }) async {
    await logEvent('performance_metrics', {
      'screen_name': screenName,
      'load_time_ms': loadTime.inMilliseconds,
      'memory_usage_mb': memoryUsageMB,
    });
  }

  /// Zaloguje user engagement
  Future<void> logUserEngagement({
    required Duration sessionDuration,
    required int screensVisited,
    required int actionsPerformed,
  }) async {
    await logEvent('user_engagement', {
      'session_duration_s': sessionDuration.inSeconds,
      'screens_visited': screensVisited,
      'actions_performed': actionsPerformed,
    });
  }

  /// Zaloguje conversion event
  Future<void> logConversion({
    required String conversionType,
    required String source,
    double? value,
  }) async {
    await logEvent('conversion', {
      'conversion_type': conversionType,
      'source': source,
      'value': value,
    });
  }

  /// Zaloguje feature usage
  Future<void> logFeatureUsage({
    required String featureName,
    required String action,
    Map<String, dynamic>? additionalData,
  }) async {
    final parameters = <String, dynamic>{
      'feature_name': featureName,
      'action': action,
    };

    if (additionalData != null) {
      parameters.addAll(additionalData);
    }

    await logEvent('feature_usage', parameters);
  }

  /// Zaloguje app crash
  Future<void> logAppCrash({
    required String crashType,
    required String stackTrace,
    Map<String, dynamic>? context,
  }) async {
    await logError(
      Exception('App Crash: $crashType'),
      StackTrace.fromString(stackTrace),
      reason: 'Application crashed',
      customData: context,
      fatal: true,
    );
  }

  /// Získá analytics observer pro navigaci
  FirebaseAnalyticsObserver getAnalyticsObserver() {
    return FirebaseAnalyticsObserver(analytics: _analytics);
  }

  /// Flush všech pending events
  Future<void> flush() async {
    if (!_isInitialized) return;

    try {
      // Firebase Analytics automaticky flush-uje events
      debugPrint('Analytics events flushed');
    } catch (e) {
      debugPrint('Chyba při flush analytics: $e');
    }
  }
}
