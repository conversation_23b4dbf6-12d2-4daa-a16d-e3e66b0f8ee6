import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../models/map_place.dart';
import '../services/map_service.dart';

class InteractiveMapScreen extends StatefulWidget {
  const InteractiveMapScreen({super.key});

  @override
  State<InteractiveMapScreen> createState() => _InteractiveMapScreenState();
}

class _InteractiveMapScreenState extends State<InteractiveMapScreen>
    with TickerProviderStateMixin {
  final MapService _mapService = MapService();
  final TextEditingController _searchController = TextEditingController();

  Set<Marker> _markers = {};
  final Set<Circle> _circles = {};

  bool _isLoading = true;
  bool _showFilters = false;
  MapPlaceFilter _currentFilter = const MapPlaceFilter();

  late AnimationController _filterController;
  late AnimationController _fabController;

  // Chorvatské souřadnice pro výchozí pozici
  static const LatLng _croatiaCenter = LatLng(45.1, 15.2);
  static const CameraPosition _initialPosition = CameraPosition(
    target: _croatiaCenter,
    zoom: 7.0,
  );

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeMap();
  }

  void _initializeAnimations() {
    _filterController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fabController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _fabController.forward();
  }

  Future<void> _initializeMap() async {
    setState(() => _isLoading = true);

    try {
      await _mapService.initialize();
      _updateMarkers();
    } catch (e) {
      debugPrint('Chyba při inicializaci mapy: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _updateMarkers() {
    final markers = <Marker>{};

    for (final place in _mapService.filteredPlaces) {
      markers.add(
        Marker(
          markerId: MarkerId(place.id),
          position: place.position,
          icon: BitmapDescriptor.defaultMarkerWithHue(
            _getHueFromColor(place.color),
          ),
          onTap: () => _showPlaceBottomSheet(place),
          infoWindow: InfoWindow(title: place.name, snippet: place.typeName),
        ),
      );
    }

    setState(() {
      _markers = markers;
    });
  }

  MapPlaceType _getDominantType(List<MapPlace> places) {
    final typeCount = <MapPlaceType, int>{};
    for (final place in places) {
      typeCount[place.type] = (typeCount[place.type] ?? 0) + 1;
    }

    return typeCount.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  Color _getTypeColor(MapPlaceType type) {
    final place = MapPlace(
      id: '',
      name: '',
      description: '',
      position: const LatLng(0, 0),
      type: type,
      category: MapPlaceCategory.services,
      address: '',
    );
    return place.color;
  }

  double _getHueFromColor(Color color) {
    final hsl = HSLColor.fromColor(color);
    return hsl.hue;
  }

  String _getClusterDescription(List<MapPlace> places) {
    final typeCount = <MapPlaceType, int>{};
    for (final place in places) {
      typeCount[place.type] = (typeCount[place.type] ?? 0) + 1;
    }

    final sortedTypes = typeCount.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    if (sortedTypes.length >= 2) {
      final first = sortedTypes[0];
      final second = sortedTypes[1];
      return '${first.value}x ${_getTypeShortName(first.key)}, ${second.value}x ${_getTypeShortName(second.key)}';
    } else if (sortedTypes.isNotEmpty) {
      final first = sortedTypes[0];
      return '${first.value}x ${_getTypeShortName(first.key)}';
    }

    return 'Různá místa';
  }

  String _getTypeShortName(MapPlaceType type) {
    switch (type) {
      case MapPlaceType.restaurant:
        return 'restaurace';
      case MapPlaceType.accommodation:
        return 'ubytování';
      case MapPlaceType.culturalSite:
        return 'kultura';
      case MapPlaceType.beach:
        return 'pláže';
      case MapPlaceType.ticket:
        return 'vstupenky';
      case MapPlaceType.emergency:
        return 'nouzové';
      case MapPlaceType.transport:
        return 'doprava';
      case MapPlaceType.entertainment:
        return 'zábava';
      case MapPlaceType.shopping:
        return 'nákupy';
      case MapPlaceType.nature:
        return 'příroda';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: CustomPaint(
        painter: WatercolorMapBackgroundPainter(),
        child: Stack(
          children: [
            // Google Maps
            _buildGoogleMap(),

            // Top UI overlay
            _buildTopOverlay(),

            // Filter panel
            if (_showFilters) _buildFilterPanel(),

            // Floating action buttons
            _buildFloatingButtons(),

            // Loading overlay
            if (_isLoading) _buildLoadingOverlay(),
          ],
        ),
      ),
    );
  }

  Widget _buildGoogleMap() {
    return GoogleMap(
      initialCameraPosition: _initialPosition,
      markers: _markers,
      circles: _circles,
      onMapCreated: (GoogleMapController controller) {
        _mapService.setMapController(controller);
      },
      myLocationEnabled: true,
      myLocationButtonEnabled: false,
      zoomControlsEnabled: false,
      mapToolbarEnabled: false,
      compassEnabled: true,
      style: _getMapStyle(),
    );
  }

  String? _getMapStyle() {
    // Jemný watercolor styl pro mapu
    return '''
    [
      {
        "featureType": "water",
        "elementType": "geometry",
        "stylers": [
          {
            "color": "#006994"
          },
          {
            "lightness": 20
          }
        ]
      },
      {
        "featureType": "landscape",
        "elementType": "geometry",
        "stylers": [
          {
            "color": "#f5f5f2"
          },
          {
            "lightness": 20
          }
        ]
      }
    ]
    ''';
  }

  Widget _buildTopOverlay() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.black.withValues(alpha: 0.3), Colors.transparent],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Header
                Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                      style: IconButton.styleFrom(
                        backgroundColor: Colors.black.withValues(alpha: 0.3),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        'Interaktivní mapa',
                        style: GoogleFonts.inter(
                          fontSize: 20,
                          fontWeight: FontWeight.w700,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: _toggleFilters,
                      icon: Icon(
                        _showFilters ? Icons.close : Icons.tune,
                        color: Colors.white,
                      ),
                      style: IconButton.styleFrom(
                        backgroundColor: _showFilters
                            ? const Color(0xFF006994)
                            : Colors.black.withValues(alpha: 0.3),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Vyhledávací pole
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Hledat místa na mapě...',
                      hintStyle: GoogleFonts.inter(
                        color: const Color(0xFF999999),
                      ),
                      prefixIcon: const Icon(
                        Icons.search,
                        color: Color(0xFF006994),
                      ),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                              onPressed: _clearSearch,
                              icon: const Icon(
                                Icons.clear,
                                color: Color(0xFF999999),
                              ),
                            )
                          : null,
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.all(16),
                    ),
                    onChanged: _onSearchChanged,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceListItem(MapPlace place) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: place.color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(place.icon, color: place.color, size: 20),
        ),
        title: Text(
          place.name,
          style: GoogleFonts.inter(fontSize: 14, fontWeight: FontWeight.w600),
        ),
        subtitle: Text(
          place.address,
          style: GoogleFonts.inter(fontSize: 12, color: Colors.grey[600]),
        ),
        trailing: place.rating != null
            ? Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.star, color: Colors.amber, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    place.rating!.toStringAsFixed(1),
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              )
            : null,
        onTap: () {
          Navigator.pop(context);
          _mapService.moveToPosition(place.position);
          _showPlaceBottomSheet(place);
        },
      ),
    );
  }

  Widget _buildPlaceDetails(MapPlace place) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header s ikonou a názvem
        Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: place.color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(place.icon, color: place.color, size: 30),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    place.name,
                    style: GoogleFonts.inter(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    place.typeName,
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: place.color,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 20),

        // Popis
        Text(
          place.description,
          style: GoogleFonts.inter(
            fontSize: 14,
            color: Colors.grey[700],
            height: 1.5,
          ),
        ),

        const SizedBox(height: 20),

        // Informace
        _buildInfoRow(Icons.location_on, 'Adresa', place.address),

        if (place.phoneNumber != null)
          _buildInfoRow(Icons.phone, 'Telefon', place.phoneNumber!),

        if (place.website != null)
          _buildInfoRow(Icons.language, 'Web', place.website!),

        if (place.rating != null)
          _buildInfoRow(
            Icons.star,
            'Hodnocení',
            '${place.rating!.toStringAsFixed(1)}/5.0${place.reviewCount != null ? ' (${place.reviewCount} recenzí)' : ''}',
          ),

        if (place.price != null)
          _buildInfoRow(
            Icons.euro,
            'Cena',
            place.priceRange ?? '${place.price!.toStringAsFixed(0)} €',
          ),

        if (place.openingHours != null)
          _buildInfoRow(
            Icons.access_time,
            'Otevírací doba',
            place.openingHours!,
          ),

        const SizedBox(height: 20),

        // Tagy
        if (place.tags.isNotEmpty) ...[
          Text(
            'Tagy',
            style: GoogleFonts.inter(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: place.tags
                .map(
                  (tag) => Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: place.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: place.color.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      tag,
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        color: place.color,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                )
                .toList(),
          ),
          const SizedBox(height: 20),
        ],

        // Akční tlačítka
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _showDirections(place),
                icon: const Icon(Icons.directions),
                label: const Text('Navigace'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: place.color,
                  side: BorderSide(color: place.color),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _openPlaceWebsite(place),
                icon: const Icon(Icons.open_in_new),
                label: const Text('Otevřít'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: place.color,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Event handlers (pokračování)
  void _toggleFilters() {
    setState(() {
      _showFilters = !_showFilters;
    });

    if (_showFilters) {
      _filterController.forward();
    } else {
      _filterController.reverse();
    }
  }

  void _onSearchChanged(String query) {
    _mapService.searchPlaces(query);
    _updateMarkers();
  }

  void _clearSearch() {
    _searchController.clear();
    _mapService.searchPlaces('');
    _updateMarkers();
  }

  void _toggleTypeFilter(MapPlaceType type) {
    final currentTypes = Set<MapPlaceType>.from(_currentFilter.types);

    if (currentTypes.contains(type)) {
      currentTypes.remove(type);
    } else {
      currentTypes.add(type);
    }

    setState(() {
      _currentFilter = _currentFilter.copyWith(types: currentTypes);
    });
  }

  void _clearAllFilters() {
    setState(() {
      _currentFilter = const MapPlaceFilter();
    });
  }

  void _applyFilters() {
    _mapService.setFilter(_currentFilter);
    _updateMarkers();
    _toggleFilters();
  }

  void _moveToCurrentLocation() {
    _mapService.moveToCurrentLocation();
  }

  void _moveToCroatia() {
    _mapService.moveToCroatia();
  }

  void _showNearbyPlaces() {
    if (_mapService.currentPosition != null) {
      final currentPos = LatLng(
        _mapService.currentPosition!.latitude,
        _mapService.currentPosition!.longitude,
      );
      final nearbyPlaces = _mapService.findNearbyPlaces(currentPos);
      _showNearbyBottomSheet(nearbyPlaces);
    }
  }

  void _showDirections(MapPlace place) {
    // TODO: Implementovat navigaci
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Navigace k ${place.name}'),
        backgroundColor: place.color,
      ),
    );
  }

  void _openPlaceWebsite(MapPlace place) {
    if (place.website != null) {
      // TODO: Otevřít web v prohlížeči
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Otevírání ${place.website}'),
          backgroundColor: place.color,
        ),
      );
    }
  }

  void _showPlaceBottomSheet(MapPlace place) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: _buildPlaceDetails(place),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showNearbyBottomSheet(List<MapPlace> places) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Text(
                'Místa v okolí (${places.length})',
                style: GoogleFonts.inter(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                itemCount: places.length,
                itemBuilder: (context, index) {
                  final place = places[index];
                  return _buildPlaceListItem(place);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterPanel() {
    return AnimatedBuilder(
      animation: _filterController,
      builder: (context, child) {
        return Positioned(
          top: 160,
          left: 16,
          right: 16,
          child: SlideTransition(
            position:
                Tween<Offset>(
                  begin: const Offset(0, -1),
                  end: Offset.zero,
                ).animate(
                  CurvedAnimation(
                    parent: _filterController,
                    curve: Curves.easeOut,
                  ),
                ),
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Filtry',
                    style: GoogleFonts.inter(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF2C2C2C),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Typ místa',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF666666),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: MapPlaceType.values.map((type) {
                      final isSelected = _currentFilter.types.contains(type);
                      final place = MapPlace(
                        id: '',
                        name: '',
                        description: '',
                        position: const LatLng(0, 0),
                        type: type,
                        category: MapPlaceCategory.services,
                        address: '',
                      );

                      return FilterChip(
                        label: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              place.icon,
                              size: 16,
                              color: isSelected ? Colors.white : place.color,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              place.typeName,
                              style: GoogleFonts.inter(
                                fontSize: 12,
                                color: isSelected ? Colors.white : place.color,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        selected: isSelected,
                        onSelected: (selected) => _toggleTypeFilter(type),
                        backgroundColor: Colors.white,
                        selectedColor: place.color,
                        checkmarkColor: Colors.white,
                        elevation: 2,
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: _clearAllFilters,
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(color: Color(0xFF006994)),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(
                            'Vymazat',
                            style: GoogleFonts.inter(
                              color: const Color(0xFF006994),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _applyFilters,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF006994),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(
                            'Použít',
                            style: GoogleFonts.inter(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFloatingButtons() {
    return Positioned(
      right: 16,
      bottom: 100,
      child: AnimatedBuilder(
        animation: _fabController,
        builder: (context, child) {
          return ScaleTransition(
            scale: _fabController,
            child: Column(
              children: [
                FloatingActionButton(
                  mini: true,
                  backgroundColor: Colors.white,
                  onPressed: _moveToCurrentLocation,
                  heroTag: "location",
                  child: const Icon(
                    Icons.my_location,
                    color: Color(0xFF006994),
                  ),
                ),
                const SizedBox(height: 8),
                FloatingActionButton(
                  mini: true,
                  backgroundColor: Colors.white,
                  onPressed: _moveToCroatia,
                  heroTag: "croatia",
                  child: const Icon(Icons.map, color: Color(0xFF006994)),
                ),
                const SizedBox(height: 8),
                FloatingActionButton(
                  mini: true,
                  backgroundColor: Colors.white,
                  onPressed: _showNearbyPlaces,
                  heroTag: "nearby",
                  child: const Icon(Icons.near_me, color: Color(0xFF006994)),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.black.withValues(alpha: 0.3),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF006994)),
              ),
              const SizedBox(height: 16),
              Text(
                'Načítání mapových dat...',
                style: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF2C2C2C),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _filterController.dispose();
    _fabController.dispose();
    super.dispose();
  }
}

// Watercolor painter pro pozadí mapy
class WatercolorMapBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Jemný watercolor efekt v chorvatských barvách
    final path1 = Path();
    path1.moveTo(0, size.height * 0.2);
    path1.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.1,
      size.width * 0.6,
      size.height * 0.25,
    );
    path1.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.35,
      size.width,
      size.height * 0.3,
    );
    path1.lineTo(size.width, 0);
    path1.lineTo(0, 0);
    path1.close();

    paint.color = const Color(0xFF006994).withValues(alpha: 0.05);
    canvas.drawPath(path1, paint);

    // Druhá vrstva
    final path2 = Path();
    path2.moveTo(size.width, size.height * 0.7);
    path2.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.6,
      size.width * 0.4,
      size.height * 0.75,
    );
    path2.quadraticBezierTo(
      size.width * 0.2,
      size.height * 0.85,
      0,
      size.height * 0.8,
    );
    path2.lineTo(0, size.height);
    path2.lineTo(size.width, size.height);
    path2.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.03);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
