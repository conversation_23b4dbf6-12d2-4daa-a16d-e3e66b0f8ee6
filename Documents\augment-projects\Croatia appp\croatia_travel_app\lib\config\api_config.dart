/// Konfigurace API klíčů a endpointů pro skutečné dopravní systémy
class ApiConfig {
  // ========== PRODUKČNÍ API KLÍČE ==========
  // POZOR: V produkci tyto klíče ukládejte do environment variables!
  
  /// Zagreb - ZET (Zagrebački električni tramvaj)
  static const String zagrebetApiKey = String.fromEnvironment(
    'ZAGREBET_API_KEY',
    defaultValue: 'demo_zagrebet_key_2024',
  );
  
  /// Split - Promet Split
  static const String splitApiKey = String.fromEnvironment(
    'SPLIT_API_KEY', 
    defaultValue: 'demo_split_key_2024',
  );
  
  /// Rijeka - Autotrolej
  static const String rijekaApiKey = String.fromEnvironment(
    'RIJEKA_API_KEY',
    defaultValue: 'demo_rijeka_key_2024',
  );
  
  /// Hrvatske željeznice (vlaky)
  static const String hzppApiKey = String.fromEnvironment(
    'HZPP_API_KEY',
    defaultValue: 'demo_hzpp_key_2024',
  );
  
  /// Jadrolinija (trajekty)
  static const String jadrolinjaApiKey = String.fromEnvironment(
    'JADROLINIJA_API_KEY',
    defaultValue: 'demo_jadrolinija_key_2024',
  );

  // ========== API ENDPOINTY ==========
  
  /// Zagreb ZET API
  static const String zagrebetBaseUrl = 'https://api.zet.hr/v2';
  static const String zagrebetRealTimeUrl = 'https://realtime.zet.hr/api';
  
  /// Split Promet API  
  static const String splitBaseUrl = 'https://api.promet-split.hr/v1';
  static const String splitRealTimeUrl = 'https://realtime.promet-split.hr/api';
  
  /// Rijeka Autotrolej API
  static const String rijekaBaseUrl = 'https://api.autotrolej.hr/v1';
  static const String rijekaRealTimeUrl = 'https://realtime.autotrolej.hr/api';
  
  /// Hrvatske željeznice API
  static const String hzppBaseUrl = 'https://api.hzpp.hr/v1';
  static const String hzppBookingUrl = 'https://booking.hzpp.hr/api';
  
  /// Jadrolinija API
  static const String jadrolinjaBaseUrl = 'https://api.jadrolinija.hr/v1';
  static const String jadrolinjaBookingUrl = 'https://booking.jadrolinija.hr/api';
  
  /// OpenStreetMap Overpass API
  static const String overpassApiUrl = 'https://overpass-api.de/api/interpreter';
  
  /// Google Maps API (pro routing)
  static const String googleMapsApiKey = String.fromEnvironment(
    'GOOGLE_MAPS_API_KEY',
    defaultValue: 'demo_google_maps_key',
  );
  static const String googleMapsBaseUrl = 'https://maps.googleapis.com/maps/api';

  // ========== KONFIGURACE MĚST ==========
  
  /// Podporovaná města s jejich API konfigurací
  static const Map<String, CityApiConfig> supportedCities = {
    'zagreb': CityApiConfig(
      name: 'Zagreb',
      apiKey: zagrebetApiKey,
      baseUrl: zagrebetBaseUrl,
      realTimeUrl: zagrebetRealTimeUrl,
      hasRealTime: true,
      supportedTransport: [TransportType.bus, TransportType.tram],
      timezone: 'Europe/Zagreb',
    ),
    'split': CityApiConfig(
      name: 'Split',
      apiKey: splitApiKey,
      baseUrl: splitBaseUrl,
      realTimeUrl: splitRealTimeUrl,
      hasRealTime: true,
      supportedTransport: [TransportType.bus],
      timezone: 'Europe/Zagreb',
    ),
    'rijeka': CityApiConfig(
      name: 'Rijeka',
      apiKey: rijekaApiKey,
      baseUrl: rijekaBaseUrl,
      realTimeUrl: rijekaRealTimeUrl,
      hasRealTime: false,
      supportedTransport: [TransportType.bus],
      timezone: 'Europe/Zagreb',
    ),
    'dubrovnik': CityApiConfig(
      name: 'Dubrovnik',
      apiKey: '', // Dubrovnik nemá vlastní API
      baseUrl: '',
      realTimeUrl: '',
      hasRealTime: false,
      supportedTransport: [TransportType.bus],
      timezone: 'Europe/Zagreb',
    ),
  };

  // ========== RATE LIMITING ==========
  
  /// Maximální počet requestů za minutu
  static const int maxRequestsPerMinute = 60;
  
  /// Timeout pro API requesty
  static const Duration requestTimeout = Duration(seconds: 10);
  
  /// Timeout pro real-time data
  static const Duration realTimeTimeout = Duration(seconds: 5);

  // ========== CACHE KONFIGURACE ==========
  
  /// Doba platnosti cache pro statická data (zastávky, trasy)
  static const Duration staticDataCacheDuration = Duration(hours: 24);
  
  /// Doba platnosti cache pro real-time data
  static const Duration realTimeCacheDuration = Duration(seconds: 30);
  
  /// Doba platnosti cache pro jízdní řády
  static const Duration timetableCacheDuration = Duration(hours: 6);

  // ========== UTILITY METODY ==========
  
  /// Získání konfigurace pro město
  static CityApiConfig? getCityConfig(String cityId) {
    return supportedCities[cityId.toLowerCase()];
  }
  
  /// Kontrola, zda město podporuje real-time data
  static bool hasRealTimeSupport(String cityId) {
    final config = getCityConfig(cityId);
    return config?.hasRealTime ?? false;
  }
  
  /// Získání podporovaných typů dopravy pro město
  static List<TransportType> getSupportedTransport(String cityId) {
    final config = getCityConfig(cityId);
    return config?.supportedTransport ?? [];
  }
  
  /// Validace API klíče
  static bool isValidApiKey(String apiKey) {
    return apiKey.isNotEmpty && 
           !apiKey.startsWith('demo_') && 
           apiKey.length > 10;
  }
  
  /// Získání headers pro API request
  static Map<String, String> getApiHeaders(String apiKey) {
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': 'Bearer $apiKey',
      'User-Agent': 'CroatiaTravel/1.0',
      'X-API-Version': '2024-01',
    };
  }
}

/// Konfigurace API pro jednotlivá města
class CityApiConfig {
  final String name;
  final String apiKey;
  final String baseUrl;
  final String realTimeUrl;
  final bool hasRealTime;
  final List<TransportType> supportedTransport;
  final String timezone;

  const CityApiConfig({
    required this.name,
    required this.apiKey,
    required this.baseUrl,
    required this.realTimeUrl,
    required this.hasRealTime,
    required this.supportedTransport,
    required this.timezone,
  });
}

/// Typy dopravy
enum TransportType {
  bus,
  tram,
  metro,
  train,
  ferry,
  trolleybus,
}

// ========== PRODUKČNÍ POZNÁMKY ==========

/// DŮLEŽITÉ INFORMACE PRO PRODUKCI:
/// 
/// 1. API KLÍČE:
///    - Nikdy neukládejte API klíče přímo v kódu!
///    - Použijte environment variables nebo secure storage
///    - Pro Flutter: flutter run --dart-define=ZAGREBET_API_KEY=your_real_key
/// 
/// 2. REGISTRACE API KLÍČŮ:
///    - ZET Zagreb: https://www.zet.hr/api-registration
///    - Promet Split: https://www.promet-split.hr/developers
///    - Autotrolej Rijeka: https://www.autotrolej.hr/api
///    - HZPP: https://www.hzpp.hr/api-access
///    - Jadrolinija: https://www.jadrolinija.hr/developers
/// 
/// 3. RATE LIMITING:
///    - Respektujte limity API poskytovatelů
///    - Implementujte exponential backoff
///    - Používejte cache pro často dotazovaná data
/// 
/// 4. ERROR HANDLING:
///    - Vždy implementujte fallback na mock data
///    - Logujte chyby pro monitoring
///    - Informujte uživatele o problémech s připojením
/// 
/// 5. GDPR COMPLIANCE:
///    - Informujte uživatele o sdílení dat s API poskytovateli
///    - Implementujte opt-out možnosti
///    - Minimalizujte sbíraná data
/// 
/// 6. TESTOVÁNÍ:
///    - Testujte s reálnými API v staging prostředí
///    - Implementujte mock servery pro unit testy
///    - Monitorujte výkon a dostupnost API
