import 'package:json_annotation/json_annotation.dart';

part 'route_plan.g.dart';

@JsonSerializable()
class RoutePlan {
  final String id;
  final String name;
  final String? description;
  final List<RoutePoint> points;
  final DateTime startDate;
  final DateTime endDate;
  final RouteType type;
  final double totalDistance;
  final Duration estimatedDuration;
  final String? notes;
  final bool isPublic;
  final DateTime createdAt;
  final DateTime updatedAt;

  const RoutePlan({
    required this.id,
    required this.name,
    this.description,
    required this.points,
    required this.startDate,
    required this.endDate,
    required this.type,
    required this.totalDistance,
    required this.estimatedDuration,
    this.notes,
    this.isPublic = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory RoutePlan.fromJson(Map<String, dynamic> json) =>
      _$RoutePlanFromJson(json);
  Map<String, dynamic> toJson() => _$RoutePlanToJson(this);

  RoutePlan copyWith({
    String? id,
    String? name,
    String? description,
    List<RoutePoint>? points,
    DateTime? startDate,
    DateTime? endDate,
    RouteType? type,
    double? totalDistance,
    Duration? estimatedDuration,
    String? notes,
    bool? isPublic,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RoutePlan(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      points: points ?? this.points,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      type: type ?? this.type,
      totalDistance: totalDistance ?? this.totalDistance,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      notes: notes ?? this.notes,
      isPublic: isPublic ?? this.isPublic,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  int get totalPoints => points.length;

  Duration get totalStayDuration =>
      points.fold(Duration.zero, (sum, point) => sum + point.stayDuration);

  double get averageStayHours =>
      totalPoints > 0 ? totalStayDuration.inMinutes / totalPoints / 60.0 : 0.0;
}

@JsonSerializable()
class RoutePoint {
  final String id;
  final String placeId;
  final String name;
  final double latitude;
  final double longitude;
  final int order;
  final DateTime? arrivalTime;
  final DateTime? departureTime;
  final Duration stayDuration;
  final String? notes;
  final List<String> activities;
  final RoutePointType type;
  final bool isVisited;

  const RoutePoint({
    required this.id,
    required this.placeId,
    required this.name,
    required this.latitude,
    required this.longitude,
    required this.order,
    this.arrivalTime,
    this.departureTime,
    required this.stayDuration,
    this.notes,
    this.activities = const [],
    required this.type,
    this.isVisited = false,
  });

  factory RoutePoint.fromJson(Map<String, dynamic> json) =>
      _$RoutePointFromJson(json);
  Map<String, dynamic> toJson() => _$RoutePointToJson(this);

  RoutePoint copyWith({
    String? id,
    String? placeId,
    String? name,
    double? latitude,
    double? longitude,
    int? order,
    DateTime? arrivalTime,
    DateTime? departureTime,
    Duration? stayDuration,
    String? notes,
    List<String>? activities,
    RoutePointType? type,
    bool? isVisited,
  }) {
    return RoutePoint(
      id: id ?? this.id,
      placeId: placeId ?? this.placeId,
      name: name ?? this.name,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      order: order ?? this.order,
      arrivalTime: arrivalTime ?? this.arrivalTime,
      departureTime: departureTime ?? this.departureTime,
      stayDuration: stayDuration ?? this.stayDuration,
      notes: notes ?? this.notes,
      activities: activities ?? this.activities,
      type: type ?? this.type,
      isVisited: isVisited ?? this.isVisited,
    );
  }

  bool get hasScheduledTime => arrivalTime != null && departureTime != null;

  bool get isOverdue =>
      departureTime != null &&
      DateTime.now().isAfter(departureTime!) &&
      !isVisited;
}

enum RouteType { walking, cycling, driving, publicTransport, mixed }

enum RoutePointType {
  attraction,
  restaurant,
  accommodation,
  transport,
  shopping,
  viewpoint,
  beach,
  park,
  museum,
  other,
}

extension RouteTypeExtension on RouteType {
  String get displayName {
    switch (this) {
      case RouteType.walking:
        return 'Pěší';
      case RouteType.cycling:
        return 'Cyklistická';
      case RouteType.driving:
        return 'Autem';
      case RouteType.publicTransport:
        return 'Veřejná doprava';
      case RouteType.mixed:
        return 'Smíšená';
    }
  }

  String get icon {
    switch (this) {
      case RouteType.walking:
        return '🚶';
      case RouteType.cycling:
        return '🚴';
      case RouteType.driving:
        return '🚗';
      case RouteType.publicTransport:
        return '🚌';
      case RouteType.mixed:
        return '🔄';
    }
  }

  double get averageSpeed {
    switch (this) {
      case RouteType.walking:
        return 5.0; // km/h
      case RouteType.cycling:
        return 15.0; // km/h
      case RouteType.driving:
        return 50.0; // km/h
      case RouteType.publicTransport:
        return 30.0; // km/h
      case RouteType.mixed:
        return 25.0; // km/h
    }
  }
}

extension RoutePointTypeExtension on RoutePointType {
  String get displayName {
    switch (this) {
      case RoutePointType.attraction:
        return 'Atrakce';
      case RoutePointType.restaurant:
        return 'Restaurace';
      case RoutePointType.accommodation:
        return 'Ubytování';
      case RoutePointType.transport:
        return 'Doprava';
      case RoutePointType.shopping:
        return 'Nákupy';
      case RoutePointType.viewpoint:
        return 'Vyhlídka';
      case RoutePointType.beach:
        return 'Pláž';
      case RoutePointType.park:
        return 'Park';
      case RoutePointType.museum:
        return 'Muzeum';
      case RoutePointType.other:
        return 'Ostatní';
    }
  }

  String get icon {
    switch (this) {
      case RoutePointType.attraction:
        return '🎯';
      case RoutePointType.restaurant:
        return '🍽️';
      case RoutePointType.accommodation:
        return '🏨';
      case RoutePointType.transport:
        return '🚌';
      case RoutePointType.shopping:
        return '🛍️';
      case RoutePointType.viewpoint:
        return '👁️';
      case RoutePointType.beach:
        return '🏖️';
      case RoutePointType.park:
        return '🌳';
      case RoutePointType.museum:
        return '🏛️';
      case RoutePointType.other:
        return '📍';
    }
  }

  Duration get recommendedStayDuration {
    switch (this) {
      case RoutePointType.attraction:
        return const Duration(hours: 2);
      case RoutePointType.restaurant:
        return const Duration(hours: 1, minutes: 30);
      case RoutePointType.accommodation:
        return const Duration(hours: 8);
      case RoutePointType.transport:
        return const Duration(minutes: 30);
      case RoutePointType.shopping:
        return const Duration(hours: 1);
      case RoutePointType.viewpoint:
        return const Duration(minutes: 30);
      case RoutePointType.beach:
        return const Duration(hours: 3);
      case RoutePointType.park:
        return const Duration(hours: 2);
      case RoutePointType.museum:
        return const Duration(hours: 2, minutes: 30);
      case RoutePointType.other:
        return const Duration(hours: 1);
    }
  }
}

// Šablony tras
class RouteTemplates {
  static const List<Map<String, dynamic>> templates = [
    {
      'name': 'Dubrovník - 1 den',
      'description': 'Jednodení prohlídka Dubrovníku',
      'type': RouteType.walking,
      'estimatedDuration': Duration(hours: 8),
      'points': [
        'dubrovnik_old_town',
        'dubrovnik_walls',
        'rector_palace',
        'dubrovnik_cathedral',
      ],
    },
    {
      'name': 'Split - víkend',
      'description': 'Víkendový výlet do Splitu',
      'type': RouteType.mixed,
      'estimatedDuration': Duration(days: 2),
      'points': [
        'diocletian_palace',
        'split_riva',
        'marjan_hill',
        'bacvice_beach',
      ],
    },
    {
      'name': 'Plitvická jezera',
      'description': 'Celodenní návštěva národního parku',
      'type': RouteType.walking,
      'estimatedDuration': Duration(hours: 6),
      'points': [
        'plitvice_entrance1',
        'lower_lakes',
        'upper_lakes',
        'veliki_slap',
      ],
    },
  ];
}
