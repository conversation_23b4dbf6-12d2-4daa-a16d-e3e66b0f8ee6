import 'package:flutter/material.dart';
import '../models/place.dart';

class FilterWidget extends StatelessWidget {
  final String selectedRegion;
  final PlaceType? selectedType;
  final bool showVisitedOnly;
  final bool sortByDistance;
  final Function(String) onRegionChanged;
  final Function(PlaceType?) onTypeChanged;
  final Function(bool) onVisitedOnlyChanged;
  final Function(bool) onSortByDistanceChanged;

  const FilterWidget({
    super.key,
    required this.selectedRegion,
    required this.selectedType,
    required this.showVisitedOnly,
    required this.sortByDistance,
    required this.onRegionChanged,
    required this.onTypeChanged,
    required this.onVisitedOnlyChanged,
    required this.onSortByDistanceChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Nadpis
          Row(
            children: [
              const Text(
                'Filtry',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Region
          const Text(
            'Region',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          DropdownButtonFormField<String>(
            value: selectedRegion,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            items: const [
              DropdownMenuItem(value: 'all', child: Text('Všechny regiony')),
              DropdownMenuItem(value: 'istria', child: Text('Istrie')),
              DropdownMenuItem(value: 'dalmatia', child: Text('Dalmácie')),
              DropdownMenuItem(value: 'slavonia', child: Text('Slavonie')),
              DropdownMenuItem(value: 'lika', child: Text('Lika')),
              DropdownMenuItem(value: 'zagreb', child: Text('Zagreb a okolí')),
            ],
            onChanged: (value) {
              if (value != null) {
                onRegionChanged(value);
              }
            },
          ),
          
          const SizedBox(height: 16),
          
          // Typ místa
          const Text(
            'Typ místa',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          DropdownButtonFormField<PlaceType?>(
            value: selectedType,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            items: const [
              DropdownMenuItem(value: null, child: Text('Všechny typy')),
              DropdownMenuItem(value: PlaceType.monument, child: Text('Památky')),
              DropdownMenuItem(value: PlaceType.beach, child: Text('Pláže')),
              DropdownMenuItem(value: PlaceType.restaurant, child: Text('Restaurace')),
              DropdownMenuItem(value: PlaceType.hotel, child: Text('Hotely')),
              DropdownMenuItem(value: PlaceType.museum, child: Text('Muzea')),
              DropdownMenuItem(value: PlaceType.park, child: Text('Parky')),
              DropdownMenuItem(value: PlaceType.church, child: Text('Kostely')),
              DropdownMenuItem(value: PlaceType.castle, child: Text('Hrady')),
              DropdownMenuItem(value: PlaceType.viewpoint, child: Text('Vyhlídky')),
              DropdownMenuItem(value: PlaceType.other, child: Text('Ostatní')),
            ],
            onChanged: onTypeChanged,
          ),
          
          const SizedBox(height: 16),
          
          // Přepínače
          const Text(
            'Možnosti',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          
          SwitchListTile(
            title: const Text('Pouze navštívená místa'),
            subtitle: const Text('Zobrazit jen místa, která jste již navštívili'),
            value: showVisitedOnly,
            onChanged: onVisitedOnlyChanged,
            contentPadding: EdgeInsets.zero,
          ),
          
          SwitchListTile(
            title: const Text('Řadit podle vzdálenosti'),
            subtitle: const Text('Nejbližší místa budou zobrazena první'),
            value: sortByDistance,
            onChanged: onSortByDistanceChanged,
            contentPadding: EdgeInsets.zero,
          ),
          
          const SizedBox(height: 16),
          
          // Tlačítka
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    // Reset filtrů
                    onRegionChanged('all');
                    onTypeChanged(null);
                    onVisitedOnlyChanged(false);
                    onSortByDistanceChanged(false);
                  },
                  child: const Text('Resetovat'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Použít'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
