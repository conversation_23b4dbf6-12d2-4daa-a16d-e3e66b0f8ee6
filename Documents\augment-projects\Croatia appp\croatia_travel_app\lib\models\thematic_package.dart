class ThematicPackage {
  final String id;
  final String name;
  final String description;
  final String icon;
  final PackageCategory category;
  final PackageDifficulty difficulty;
  final Duration estimatedDuration;
  final List<String> places;
  final List<String> cuisineItems;
  final List<String> events;
  final List<PackageActivity> activities;
  final List<PackageReward> rewards;
  final List<PackageRequirement> requirements;
  final List<String> tips;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? imageUrl;
  final double? price;
  final bool isPublic;

  const ThematicPackage({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.category,
    required this.difficulty,
    required this.estimatedDuration,
    required this.places,
    required this.cuisineItems,
    required this.events,
    required this.activities,
    required this.rewards,
    required this.requirements,
    required this.tips,
    required this.createdAt,
    required this.updatedAt,
    this.imageUrl,
    this.price,
    this.isPublic = true,
  });
}

class PackageActivity {
  final String id;
  final String name;
  final String description;
  final ActivityType type;
  final Duration duration;
  final String location;
  final double price;
  final ActivityDifficulty difficulty;
  final List<String>? requirements;
  final String? bookingUrl;
  final String? contactInfo;

  const PackageActivity({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.duration,
    required this.location,
    required this.price,
    required this.difficulty,
    this.requirements,
    this.bookingUrl,
    this.contactInfo,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'duration': duration.inMilliseconds,
      'location': location,
      'price': price,
      'difficulty': difficulty.name,
      'requirements': requirements,
      'bookingUrl': bookingUrl,
      'contactInfo': contactInfo,
    };
  }

  factory PackageActivity.fromJson(Map<String, dynamic> json) {
    return PackageActivity(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      type: ActivityType.values.firstWhere((e) => e.name == json['type']),
      duration: Duration(milliseconds: json['duration']),
      location: json['location'],
      price: json['price'],
      difficulty: ActivityDifficulty.values.firstWhere((e) => e.name == json['difficulty']),
      requirements: json['requirements'] != null ? List<String>.from(json['requirements']) : null,
      bookingUrl: json['bookingUrl'],
      contactInfo: json['contactInfo'],
    );
  }
}

class PackageReward {
  final String id;
  final String name;
  final String description;
  final String icon;
  final RewardType type;
  final String? contentUrl;
  final Map<String, dynamic>? metadata;

  const PackageReward({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.type,
    this.contentUrl,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon,
      'type': type.name,
      'contentUrl': contentUrl,
      'metadata': metadata,
    };
  }

  factory PackageReward.fromJson(Map<String, dynamic> json) {
    return PackageReward(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      icon: json['icon'],
      type: RewardType.values.firstWhere((e) => e.name == json['type']),
      contentUrl: json['contentUrl'],
      metadata: json['metadata'],
    );
  }
}

class PackageRequirement {
  final RequirementType type;
  final int target;
  final String description;
  final List<String>? specificItems;

  const PackageRequirement({
    required this.type,
    required this.target,
    required this.description,
    this.specificItems,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'target': target,
      'description': description,
      'specificItems': specificItems,
    };
  }

  factory PackageRequirement.fromJson(Map<String, dynamic> json) {
    return PackageRequirement(
      type: RequirementType.values.firstWhere((e) => e.name == json['type']),
      target: json['target'],
      description: json['description'],
      specificItems: json['specificItems'] != null ? List<String>.from(json['specificItems']) : null,
    );
  }
}

class PackageProgress {
  final String packageId;
  final List<String> completedItems;
  final int totalItems;
  final double completionPercentage;
  final bool isStarted;
  final bool isCompleted;
  final List<PackageRequirement> completedRequirements;
  final DateTime? startedAt;
  final DateTime? completedAt;

  const PackageProgress({
    required this.packageId,
    required this.completedItems,
    required this.totalItems,
    required this.completionPercentage,
    required this.isStarted,
    required this.isCompleted,
    required this.completedRequirements,
    this.startedAt,
    this.completedAt,
  });
}

class PackageRecommendation {
  final String packageId;
  final double relevanceScore;
  final List<String> reasons;
  final String? personalizedMessage;

  const PackageRecommendation({
    required this.packageId,
    required this.relevanceScore,
    required this.reasons,
    this.personalizedMessage,
  });
}

class PackageStatistics {
  final String packageId;
  final int totalUsers;
  final int completedUsers;
  final double averageRating;
  final Duration averageCompletionTime;
  final Map<String, int> popularItems;

  const PackageStatistics({
    required this.packageId,
    required this.totalUsers,
    required this.completedUsers,
    required this.averageRating,
    required this.averageCompletionTime,
    required this.popularItems,
  });
}

// Enums
enum PackageCategory {
  gastronomy,
  history,
  nature,
  culture,
  adventure,
  relaxation,
  family,
  romantic,
  custom,
}

enum PackageDifficulty {
  easy,
  medium,
  hard,
  expert,
}

enum ActivityType {
  tour,
  workshop,
  experience,
  tasting,
  sport,
  cruise,
  hiking,
  cultural,
}

enum ActivityDifficulty {
  easy,
  beginner,
  intermediate,
  advanced,
  expert,
}

enum RewardType {
  badge,
  content,
  discount,
  access,
  certificate,
}

enum RequirementType {
  visitPlaces,
  tryCuisine,
  attendEvent,
  completeActivity,
  completeQuiz,
  spendTime,
  takePhotos,
}

// Extensions pro lepší UX
extension PackageCategoryExtension on PackageCategory {
  String get displayName {
    switch (this) {
      case PackageCategory.gastronomy:
        return 'Gastronomie';
      case PackageCategory.history:
        return 'Historie';
      case PackageCategory.nature:
        return 'Příroda';
      case PackageCategory.culture:
        return 'Kultura';
      case PackageCategory.adventure:
        return 'Dobrodružství';
      case PackageCategory.relaxation:
        return 'Relaxace';
      case PackageCategory.family:
        return 'Rodinný';
      case PackageCategory.romantic:
        return 'Romantický';
      case PackageCategory.custom:
        return 'Vlastní';
    }
  }

  String get icon {
    switch (this) {
      case PackageCategory.gastronomy:
        return '🍽️';
      case PackageCategory.history:
        return '🏛️';
      case PackageCategory.nature:
        return '🌿';
      case PackageCategory.culture:
        return '🎭';
      case PackageCategory.adventure:
        return '🏔️';
      case PackageCategory.relaxation:
        return '🧘';
      case PackageCategory.family:
        return '👨‍👩‍👧‍👦';
      case PackageCategory.romantic:
        return '💕';
      case PackageCategory.custom:
        return '📦';
    }
  }
}

extension PackageDifficultyExtension on PackageDifficulty {
  String get displayName {
    switch (this) {
      case PackageDifficulty.easy:
        return 'Snadný';
      case PackageDifficulty.medium:
        return 'Střední';
      case PackageDifficulty.hard:
        return 'Těžký';
      case PackageDifficulty.expert:
        return 'Expert';
    }
  }

  String get description {
    switch (this) {
      case PackageDifficulty.easy:
        return 'Vhodný pro začátečníky, minimální fyzická náročnost';
      case PackageDifficulty.medium:
        return 'Střední náročnost, vyžaduje základní kondici';
      case PackageDifficulty.hard:
        return 'Náročný balíček, vyžaduje dobrou kondici';
      case PackageDifficulty.expert:
        return 'Pouze pro zkušené cestovatele';
    }
  }
}

extension ActivityTypeExtension on ActivityType {
  String get displayName {
    switch (this) {
      case ActivityType.tour:
        return 'Prohlídka';
      case ActivityType.workshop:
        return 'Workshop';
      case ActivityType.experience:
        return 'Zážitek';
      case ActivityType.tasting:
        return 'Degustace';
      case ActivityType.sport:
        return 'Sport';
      case ActivityType.cruise:
        return 'Plavba';
      case ActivityType.hiking:
        return 'Pěší turistika';
      case ActivityType.cultural:
        return 'Kulturní';
    }
  }
}
