import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SeasonalThemeService {
  static final SeasonalThemeService _instance =
      SeasonalThemeService._internal();
  factory SeasonalThemeService() => _instance;
  SeasonalThemeService._internal();

  StreamController<SeasonalTheme>? _themeController;
  Timer? _timeUpdateTimer;

  SeasonalTheme _currentTheme = SeasonalTheme.spring;
  TimeOfDay _currentTimeOfDay = TimeOfDay.day;

  /// Stream změn tématu
  Stream<SeasonalTheme> get themeStream {
    _themeController ??= StreamController<SeasonalTheme>.broadcast();
    return _themeController!.stream;
  }

  /// Aktuální téma
  SeasonalTheme get currentTheme => _currentTheme;

  /// Aktuální doba dne
  TimeOfDay get currentTimeOfDay => _currentTimeOfDay;

  /// Inicializace služby
  Future<void> initialize() async {
    await _loadSavedTheme();
    _updateTheme();
    _startTimeUpdateTimer();
  }

  /// Načtení uloženého tématu
  Future<void> _loadSavedTheme() async {
    final prefs = await SharedPreferences.getInstance();
    final savedTheme = prefs.getString('seasonal_theme');
    if (savedTheme != null) {
      _currentTheme = SeasonalTheme.values.firstWhere(
        (theme) => theme.name == savedTheme,
        orElse: () => _getSeasonFromDate(),
      );
    } else {
      _currentTheme = _getSeasonFromDate();
    }
  }

  /// Uložení tématu
  Future<void> _saveTheme() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('seasonal_theme', _currentTheme.name);
  }

  /// Spuštění časovače pro aktualizaci času
  void _startTimeUpdateTimer() {
    _timeUpdateTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _updateTheme();
    });
  }

  /// Aktualizace tématu podle času a sezóny
  void _updateTheme() {
    final now = DateTime.now();
    final newSeason = _getSeasonFromDate();
    final newTimeOfDay = _getTimeOfDayFromHour(now.hour);

    bool shouldUpdate = false;

    if (_currentTheme != newSeason) {
      _currentTheme = newSeason;
      shouldUpdate = true;
    }

    if (_currentTimeOfDay != newTimeOfDay) {
      _currentTimeOfDay = newTimeOfDay;
      shouldUpdate = true;
    }

    if (shouldUpdate) {
      _themeController?.add(_currentTheme);
      _saveTheme();
    }
  }

  /// Určení sezóny podle data
  SeasonalTheme _getSeasonFromDate() {
    final now = DateTime.now();
    final month = now.month;

    if (month >= 3 && month <= 5) {
      return SeasonalTheme.spring;
    } else if (month >= 6 && month <= 8) {
      return SeasonalTheme.summer;
    } else if (month >= 9 && month <= 11) {
      return SeasonalTheme.autumn;
    } else {
      return SeasonalTheme.winter;
    }
  }

  /// Určení doby dne podle hodiny
  TimeOfDay _getTimeOfDayFromHour(int hour) {
    if (hour >= 5 && hour < 12) {
      return TimeOfDay.morning;
    } else if (hour >= 12 && hour < 17) {
      return TimeOfDay.day;
    } else if (hour >= 17 && hour < 21) {
      return TimeOfDay.evening;
    } else {
      return TimeOfDay.night;
    }
  }

  /// Manuální nastavení tématu
  Future<void> setTheme(SeasonalTheme theme) async {
    _currentTheme = theme;
    _themeController?.add(_currentTheme);
    await _saveTheme();
  }

  /// Získání barevného schématu pro aktuální téma a dobu dne
  SeasonalColorScheme getColorScheme() {
    return SeasonalColorScheme.fromThemeAndTime(
      _currentTheme,
      _currentTimeOfDay,
    );
  }

  /// Získání gradientu pro pozadí
  LinearGradient getBackgroundGradient() {
    final colorScheme = getColorScheme();
    return LinearGradient(
      colors: [colorScheme.primary, colorScheme.secondary],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    );
  }

  /// Získání doporučení obsahu podle sezóny
  List<String> getSeasonalRecommendations() {
    switch (_currentTheme) {
      case SeasonalTheme.spring:
        return [
          'Návštěva národních parků v květnu',
          'Pěší turistika v Gorském kotaru',
          'Fotografování kvetoucích mandloní v Istrii',
          'Cykloturistika podél pobřeží',
        ];
      case SeasonalTheme.summer:
        return [
          'Koupání na nejkrásnějších plážích',
          'Ostrovní hopping v Dalmácii',
          'Letní festivaly v Dubrovníku',
          'Vodní sporty na Jadranu',
          'Večerní procházky historickými centry',
        ];
      case SeasonalTheme.autumn:
        return [
          'Degustace vín v Istrii',
          'Sběr oliv a návštěva olejarů',
          'Podzimní barvy v kontinentálním Chorvatsku',
          'Termální lázně v Istrii',
        ];
      case SeasonalTheme.winter:
        return [
          'Vánoční trhy v Záhřebu',
          'Zimní procházky po Dubrovníku',
          'Návštěva muzeí a galerií',
          'Degustace zimních specialit',
          'Wellness pobyty u moře',
        ];
    }
  }

  /// Získání sezónních tagů pro fotografie
  List<String> getSeasonalPhotoTags() {
    switch (_currentTheme) {
      case SeasonalTheme.spring:
        return ['jaro', 'květiny', 'příroda', 'zelená', 'svěží'];
      case SeasonalTheme.summer:
        return ['léto', 'moře', 'pláž', 'slunce', 'dovolená'];
      case SeasonalTheme.autumn:
        return ['podzim', 'víno', 'barvy', 'sklizeň', 'zlatá'];
      case SeasonalTheme.winter:
        return ['zima', 'vánoce', 'klid', 'útulno', 'teplo'];
    }
  }

  void dispose() {
    _timeUpdateTimer?.cancel();
    _themeController?.close();
  }
}

enum SeasonalTheme { spring, summer, autumn, winter }

enum TimeOfDay { morning, day, evening, night }

class SeasonalColorScheme {
  final Color primary;
  final Color secondary;
  final Color accent;
  final Color background;
  final Color surface;
  final Color text;
  final Color textSecondary;

  const SeasonalColorScheme({
    required this.primary,
    required this.secondary,
    required this.accent,
    required this.background,
    required this.surface,
    required this.text,
    required this.textSecondary,
  });

  factory SeasonalColorScheme.fromThemeAndTime(
    SeasonalTheme theme,
    TimeOfDay timeOfDay,
  ) {
    switch (theme) {
      case SeasonalTheme.spring:
        return _getSpringColors(timeOfDay);
      case SeasonalTheme.summer:
        return _getSummerColors(timeOfDay);
      case SeasonalTheme.autumn:
        return _getAutumnColors(timeOfDay);
      case SeasonalTheme.winter:
        return _getWinterColors(timeOfDay);
    }
  }

  static SeasonalColorScheme _getSpringColors(TimeOfDay timeOfDay) {
    switch (timeOfDay) {
      case TimeOfDay.morning:
        return const SeasonalColorScheme(
          primary: Color(0xFF4CAF50), // Svěží zelená
          secondary: Color(0xFF81C784), // Světle zelená
          accent: Color(0xFFFFEB3B), // Žlutá
          background: Color(0xFFF1F8E9), // Velmi světle zelená
          surface: Color(0xFFFFFFFF),
          text: Color(0xFF2E7D32),
          textSecondary: Color(0xFF4CAF50),
        );
      case TimeOfDay.day:
        return const SeasonalColorScheme(
          primary: Color(0xFF4CAF50),
          secondary: Color(0xFF8BC34A),
          accent: Color(0xFFFFEB3B),
          background: Color(0xFFF8FFF8),
          surface: Color(0xFFFFFFFF),
          text: Color(0xFF1B5E20),
          textSecondary: Color(0xFF388E3C),
        );
      case TimeOfDay.evening:
        return const SeasonalColorScheme(
          primary: Color(0xFF388E3C),
          secondary: Color(0xFF66BB6A),
          accent: Color(0xFFFFC107),
          background: Color(0xFFE8F5E8),
          surface: Color(0xFFF5F5F5),
          text: Color(0xFF1B5E20),
          textSecondary: Color(0xFF2E7D32),
        );
      case TimeOfDay.night:
        return const SeasonalColorScheme(
          primary: Color(0xFF2E7D32),
          secondary: Color(0xFF4CAF50),
          accent: Color(0xFFFF9800),
          background: Color(0xFF1B5E20),
          surface: Color(0xFF2E7D32),
          text: Color(0xFFE8F5E8),
          textSecondary: Color(0xFFC8E6C9),
        );
    }
  }

  static SeasonalColorScheme _getSummerColors(TimeOfDay timeOfDay) {
    switch (timeOfDay) {
      case TimeOfDay.morning:
        return const SeasonalColorScheme(
          primary: Color(0xFF006994), // Jaderská modrá
          secondary: Color(0xFF2E8B8B), // Tyrkysová
          accent: Color(0xFFFFEB3B), // Slunečná žlutá
          background: Color(0xFFF8F6F0), // Krémová
          surface: Color(0xFFFFFFFF),
          text: Color(0xFF2C2C2C),
          textSecondary: Color(0xFF006994),
        );
      case TimeOfDay.day:
        return const SeasonalColorScheme(
          primary: Color(0xFF006994),
          secondary: Color(0xFF00BCD4), // Jasná tyrkysová
          accent: Color(0xFFFFEB3B),
          background: Color(0xFFE3F2FD),
          surface: Color(0xFFFFFFFF),
          text: Color(0xFF0D47A1),
          textSecondary: Color(0xFF1976D2),
        );
      case TimeOfDay.evening:
        return const SeasonalColorScheme(
          primary: Color(0xFFFF6B35), // Západ slunce
          secondary: Color(0xFFFF9800), // Oranžová
          accent: Color(0xFFFFEB3B),
          background: Color(0xFFFFF3E0),
          surface: Color(0xFFFFF8E1),
          text: Color(0xFFE65100),
          textSecondary: Color(0xFFFF6F00),
        );
      case TimeOfDay.night:
        return const SeasonalColorScheme(
          primary: Color(0xFF1A237E), // Tmavě modrá
          secondary: Color(0xFF3F51B5), // Indigo
          accent: Color(0xFFFFEB3B),
          background: Color(0xFF0D47A1),
          surface: Color(0xFF1565C0),
          text: Color(0xFFE3F2FD),
          textSecondary: Color(0xFFBBDEFB),
        );
    }
  }

  static SeasonalColorScheme _getAutumnColors(TimeOfDay timeOfDay) {
    switch (timeOfDay) {
      case TimeOfDay.morning:
        return const SeasonalColorScheme(
          primary: Color(0xFFFF8F00), // Jantarová
          secondary: Color(0xFFFFB74D), // Světle oranžová
          accent: Color(0xFFD32F2F), // Červená
          background: Color(0xFFFFF8E1),
          surface: Color(0xFFFFFFFF),
          text: Color(0xFFE65100),
          textSecondary: Color(0xFFFF8F00),
        );
      case TimeOfDay.day:
        return const SeasonalColorScheme(
          primary: Color(0xFFFF6F00),
          secondary: Color(0xFFFFB74D),
          accent: Color(0xFFD32F2F),
          background: Color(0xFFFFF3E0),
          surface: Color(0xFFFFFFFF),
          text: Color(0xFFBF360C),
          textSecondary: Color(0xFFE65100),
        );
      case TimeOfDay.evening:
        return const SeasonalColorScheme(
          primary: Color(0xFFD32F2F),
          secondary: Color(0xFFFF5722),
          accent: Color(0xFFFF9800),
          background: Color(0xFFFFEBEE),
          surface: Color(0xFFFFF5F5),
          text: Color(0xFFB71C1C),
          textSecondary: Color(0xFFD32F2F),
        );
      case TimeOfDay.night:
        return const SeasonalColorScheme(
          primary: Color(0xFF8D6E63), // Hnědá
          secondary: Color(0xFFA1887F),
          accent: Color(0xFFFF9800),
          background: Color(0xFF3E2723),
          surface: Color(0xFF5D4037),
          text: Color(0xFFEFEBE9),
          textSecondary: Color(0xFFD7CCC8),
        );
    }
  }

  static SeasonalColorScheme _getWinterColors(TimeOfDay timeOfDay) {
    switch (timeOfDay) {
      case TimeOfDay.morning:
        return const SeasonalColorScheme(
          primary: Color(0xFF607D8B), // Šedá
          secondary: Color(0xFF90A4AE), // Světle šedá
          accent: Color(0xFF2196F3), // Modrá
          background: Color(0xFFF5F5F5),
          surface: Color(0xFFFFFFFF),
          text: Color(0xFF263238),
          textSecondary: Color(0xFF455A64),
        );
      case TimeOfDay.day:
        return const SeasonalColorScheme(
          primary: Color(0xFF2196F3),
          secondary: Color(0xFF64B5F6),
          accent: Color(0xFFFFFFFF),
          background: Color(0xFFE3F2FD),
          surface: Color(0xFFFFFFFF),
          text: Color(0xFF0D47A1),
          textSecondary: Color(0xFF1976D2),
        );
      case TimeOfDay.evening:
        return const SeasonalColorScheme(
          primary: Color(0xFF3F51B5),
          secondary: Color(0xFF7986CB),
          accent: Color(0xFFFFEB3B),
          background: Color(0xFFE8EAF6),
          surface: Color(0xFFF3F4F6),
          text: Color(0xFF1A237E),
          textSecondary: Color(0xFF303F9F),
        );
      case TimeOfDay.night:
        return const SeasonalColorScheme(
          primary: Color(0xFF263238),
          secondary: Color(0xFF37474F),
          accent: Color(0xFF64B5F6),
          background: Color(0xFF1C1C1C),
          surface: Color(0xFF2C2C2C),
          text: Color(0xFFECEFF1),
          textSecondary: Color(0xFFB0BEC5),
        );
    }
  }
}

extension SeasonalThemeExtension on SeasonalTheme {
  String get displayName {
    switch (this) {
      case SeasonalTheme.spring:
        return 'Jaro';
      case SeasonalTheme.summer:
        return 'Léto';
      case SeasonalTheme.autumn:
        return 'Podzim';
      case SeasonalTheme.winter:
        return 'Zima';
    }
  }

  String get description {
    switch (this) {
      case SeasonalTheme.spring:
        return 'Svěží zelené tóny probouzející se přírody';
      case SeasonalTheme.summer:
        return 'Jasné modré a tyrkysové barvy Jaderského moře';
      case SeasonalTheme.autumn:
        return 'Teplé oranžové a červené tóny podzimu';
      case SeasonalTheme.winter:
        return 'Klidné modré a šedé tóny zimního klidu';
    }
  }

  IconData get icon {
    switch (this) {
      case SeasonalTheme.spring:
        return Icons.local_florist;
      case SeasonalTheme.summer:
        return Icons.wb_sunny;
      case SeasonalTheme.autumn:
        return Icons.park;
      case SeasonalTheme.winter:
        return Icons.ac_unit;
    }
  }
}

extension TimeOfDayExtension on TimeOfDay {
  String get displayName {
    switch (this) {
      case TimeOfDay.morning:
        return 'Ráno';
      case TimeOfDay.day:
        return 'Den';
      case TimeOfDay.evening:
        return 'Večer';
      case TimeOfDay.night:
        return 'Noc';
    }
  }
}
