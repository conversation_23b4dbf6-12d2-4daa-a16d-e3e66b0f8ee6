import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/privacy_policy.dart';

/// 🔒 PRIVACY POLICY SERVICE - GDPR Compliance
class PrivacyPolicyService {
  static final PrivacyPolicyService _instance = PrivacyPolicyService._internal();
  factory PrivacyPolicyService() => _instance;
  PrivacyPolicyService._internal();

  bool _isInitialized = false;
  UserConsent? _currentConsent;
  final StreamController<ConsentEvent> _eventController = StreamController.broadcast();

  /// Stream consent událostí
  Stream<ConsentEvent> get consentEvents => _eventController.stream;

  /// Inicializace služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🔒 Inicializuji Privacy Policy Service...');
      
      await _loadUserConsent();
      
      _isInitialized = true;
      debugPrint('✅ Privacy Policy Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Privacy Policy: $e');
      _isInitialized = true;
    }
  }

  /// Získání aktuální privacy policy
  PrivacyPolicy getCurrentPrivacyPolicy() {
    return PrivacyPolicy(
      version: '1.0.0',
      effectiveDate: DateTime(2024, 1, 1),
      lastUpdated: DateTime.now(),
      language: 'cs',
      sections: [
        _getDataCollectionSection(),
        _getDataUsageSection(),
        _getDataSharingSection(),
        _getUserRightsSection(),
        _getSecuritySection(),
        _getCookiesSection(),
        _getContactSection(),
      ],
    );
  }

  /// Získání Terms of Service
  TermsOfService getTermsOfService() {
    return TermsOfService(
      version: '1.0.0',
      effectiveDate: DateTime(2024, 1, 1),
      lastUpdated: DateTime.now(),
      language: 'cs',
      sections: [
        _getServiceDescriptionSection(),
        _getUserObligationsSection(),
        _getIntellectualPropertySection(),
        _getPaymentTermsSection(),
        _getLiabilitySection(),
        _getTerminationSection(),
        _getGoverningLawSection(),
      ],
    );
  }

  /// Záznam souhlasu uživatele
  Future<bool> recordConsent({
    required ConsentType type,
    required bool granted,
    String? specificPurpose,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final consent = ConsentRecord(
        id: 'consent_${DateTime.now().millisecondsSinceEpoch}',
        userId: 'current_user', // V produkci by se získalo z auth
        type: type,
        granted: granted,
        timestamp: DateTime.now(),
        ipAddress: await _getCurrentIpAddress(),
        userAgent: await _getUserAgent(),
        specificPurpose: specificPurpose,
        metadata: metadata ?? {},
      );

      // Aktualizace current consent
      if (_currentConsent == null) {
        _currentConsent = UserConsent(
          userId: 'current_user',
          consents: [consent],
          lastUpdated: DateTime.now(),
        );
      } else {
        _currentConsent = _currentConsent!.copyWith(
          consents: [..._currentConsent!.consents, consent],
          lastUpdated: DateTime.now(),
        );
      }

      await _saveUserConsent();

      _eventController.add(ConsentEvent(
        type: ConsentEventType.consentRecorded,
        consentType: type,
        granted: granted,
        timestamp: DateTime.now(),
      ));

      debugPrint('✅ Consent zaznamenán: $type = $granted');
      return true;
    } catch (e) {
      debugPrint('❌ Chyba při zaznamenávání consent: $e');
      return false;
    }
  }

  /// Kontrola, zda má uživatel platný souhlas
  bool hasValidConsent(ConsentType type) {
    if (_currentConsent == null) return false;

    final relevantConsents = _currentConsent!.consents
        .where((c) => c.type == type)
        .toList();

    if (relevantConsents.isEmpty) return false;

    // Najít nejnovější consent
    relevantConsents.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    final latestConsent = relevantConsents.first;

    // Kontrola platnosti (consent je platný 2 roky)
    final isValid = DateTime.now().difference(latestConsent.timestamp).inDays < 730;
    
    return latestConsent.granted && isValid;
  }

  /// Odvolání souhlasu
  Future<bool> revokeConsent(ConsentType type) async {
    return await recordConsent(
      type: type,
      granted: false,
      specificPurpose: 'User revoked consent',
    );
  }

  /// Export uživatelských dat (GDPR Article 20)
  Future<UserDataExport> exportUserData() async {
    try {
      // Simulace sběru všech uživatelských dat
      final export = UserDataExport(
        userId: 'current_user',
        exportDate: DateTime.now(),
        format: ExportFormat.json,
        data: {
          'profile': await _exportProfileData(),
          'diary_entries': await _exportDiaryData(),
          'photos': await _exportPhotoData(),
          'analytics': await _exportAnalyticsData(),
          'consents': await _exportConsentData(),
          'settings': await _exportSettingsData(),
        },
        metadata: {
          'export_version': '1.0.0',
          'total_entries': 150,
          'total_photos': 89,
          'account_created': '2024-01-15',
        },
      );

      _eventController.add(ConsentEvent(
        type: ConsentEventType.dataExported,
        timestamp: DateTime.now(),
      ));

      debugPrint('📦 Data export vytvořen');
      return export;
    } catch (e) {
      debugPrint('❌ Chyba při exportu dat: $e');
      rethrow;
    }
  }

  /// Smazání uživatelských dat (GDPR Article 17 - Right to be forgotten)
  Future<bool> deleteUserData({
    required String userId,
    required String reason,
    bool keepLegallyRequired = true,
  }) async {
    try {
      debugPrint('🗑️ Zahajuji smazání uživatelských dat...');

      // Smazání různých typů dat
      await _deleteDiaryEntries(userId, keepLegallyRequired);
      await _deletePhotos(userId, keepLegallyRequired);
      await _deleteAnalyticsData(userId, keepLegallyRequired);
      await _deleteProfileData(userId, keepLegallyRequired);
      
      // Záznam o smazání
      await recordConsent(
        type: ConsentType.dataProcessing,
        granted: false,
        specificPurpose: 'Data deletion requested: $reason',
        metadata: {
          'deletion_date': DateTime.now().toIso8601String(),
          'reason': reason,
          'kept_legal_data': keepLegallyRequired,
        },
      );

      _eventController.add(ConsentEvent(
        type: ConsentEventType.dataDeleted,
        timestamp: DateTime.now(),
        metadata: {'reason': reason},
      ));

      debugPrint('✅ Uživatelská data smazána');
      return true;
    } catch (e) {
      debugPrint('❌ Chyba při mazání dat: $e');
      return false;
    }
  }

  /// Kontrola, zda je potřeba aktualizovat consent
  bool needsConsentUpdate() {
    if (_currentConsent == null) return true;

    // Kontrola, zda je consent starší než 2 roky
    final daysSinceLastUpdate = DateTime.now()
        .difference(_currentConsent!.lastUpdated)
        .inDays;

    return daysSinceLastUpdate > 730; // 2 roky
  }

  /// Získání consent historie
  List<ConsentRecord> getConsentHistory(ConsentType? type) {
    if (_currentConsent == null) return [];

    var consents = _currentConsent!.consents;
    
    if (type != null) {
      consents = consents.where((c) => c.type == type).toList();
    }

    // Seřazení podle data (nejnovější první)
    consents.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    
    return consents;
  }

  /// Privátní metody pro sekce Privacy Policy
  PrivacyPolicySection _getDataCollectionSection() {
    return PrivacyPolicySection(
      title: 'Jaká data sbíráme',
      content: '''
Sbíráme následující typy osobních údajů:

• **Údaje o účtu**: Jméno, e-mail, heslo (hashované)
• **Obsah deníku**: Texty, fotografie, audio nahrávky, lokace
• **Analytická data**: Statistiky používání, preference, nálady
• **Technická data**: IP adresa, typ zařízení, verze aplikace
• **Cookies**: Pro zlepšení uživatelského zážitku

Všechna data sbíráme pouze s vaším výslovným souhlasem.
      ''',
      isRequired: true,
    );
  }

  PrivacyPolicySection _getDataUsageSection() {
    return PrivacyPolicySection(
      title: 'Jak data používáme',
      content: '''
Vaše data používáme pro:

• **Poskytování služby**: Ukládání a zobrazování vašich vzpomínek
• **Personalizaci**: Přizpůsobení obsahu a doporučení
• **Analytiku**: Generování osobních statistik a insights
• **Zlepšování služby**: Analýza používání pro vylepšení aplikace
• **Komunikaci**: Zasílání důležitých informací o službě

Nikdy neprodáváme vaše data třetím stranám.
      ''',
      isRequired: true,
    );
  }

  PrivacyPolicySection _getDataSharingSection() {
    return PrivacyPolicySection(
      title: 'Sdílení dat',
      content: '''
Vaše data sdílíme pouze v těchto případech:

• **S vaším souhlasem**: Když explicitně povolíte sdílení
• **Rodinné kruhy**: Data sdílená v rámci rodinných skupin
• **Právní požadavky**: Pokud to vyžaduje zákon
• **Poskytovatelé služeb**: Zabezpečení cloud služby (AWS, Google Cloud)

Všichni naši partneři jsou vázáni přísnou mlčenlivostí.
      ''',
      isRequired: true,
    );
  }

  PrivacyPolicySection _getUserRightsSection() {
    return PrivacyPolicySection(
      title: 'Vaše práva',
      content: '''
Podle GDPR máte následující práva:

• **Právo na přístup**: Můžete požádat o kopii svých dat
• **Právo na opravu**: Můžete opravit nesprávné údaje
• **Právo na výmaz**: Můžete požádat o smazání svých dat
• **Právo na přenositelnost**: Můžete exportovat svá data
• **Právo na omezení**: Můžete omezit zpracování
• **Právo vznést námitku**: Můžete nesouhlasit se zpracováním

Pro uplatnění práv nás <NAME_EMAIL>
      ''',
      isRequired: true,
    );
  }

  PrivacyPolicySection _getSecuritySection() {
    return PrivacyPolicySection(
      title: 'Zabezpečení dat',
      content: '''
Vaše data chráníme pomocí:

• **Šifrování**: AES-256 šifrování pro všechna citlivá data
• **Zabezpečené přenosy**: HTTPS/TLS pro všechnu komunikaci
• **Přístupové kontroly**: Striktní omezení přístupu k datům
• **Pravidelné audity**: Bezpečnostní kontroly a penetrační testy
• **Zálohování**: Pravidelné zálohy s šifrováním

Při podezření na narušení bezpečnosti vás informujeme do 72 hodin.
      ''',
      isRequired: true,
    );
  }

  PrivacyPolicySection _getCookiesSection() {
    return PrivacyPolicySection(
      title: 'Cookies a sledování',
      content: '''
Používáme následující typy cookies:

• **Nezbytné cookies**: Pro základní funkčnost aplikace
• **Analytické cookies**: Pro měření výkonu (Google Analytics)
• **Funkční cookies**: Pro zapamatování preferencí
• **Marketingové cookies**: Pro personalizované reklamy (pouze s souhlasem)

Můžete spravovat cookies v nastavení aplikace nebo prohlížeče.
      ''',
      isRequired: false,
    );
  }

  PrivacyPolicySection _getContactSection() {
    return PrivacyPolicySection(
      title: 'Kontakt',
      content: '''
Pro otázky ohledně ochrany osobních údajů nás kontaktujte:

**E-mail**: <EMAIL>
**Telefon**: +420 123 456 789
**Adresa**: Croatia Travel App s.r.o., Praha 1, Česká republika

**Pověřenec pro ochranu osobních údajů (DPO)**:
<EMAIL>

Odpovíme vám do 30 dnů od obdržení dotazu.
      ''',
      isRequired: true,
    );
  }

  /// Privátní metody pro Terms of Service sekce
  TermsOfServiceSection _getServiceDescriptionSection() {
    return TermsOfServiceSection(
      title: 'Popis služby',
      content: '''
Croatia Travel App je digitální cestovní deník umožňující:

• Vytváření a ukládání cestovních vzpomínek
• Sdílení obsahu s rodinou a přáteli
• Analýzu cestovních vzorců a statistik
• Přístup k informacím o Chorvatsku
• Premium funkce pro pokročilé uživatele

Služba je poskytována "tak jak je" s možností změn a vylepšení.
      ''',
    );
  }

  TermsOfServiceSection _getUserObligationsSection() {
    return TermsOfServiceSection(
      title: 'Povinnosti uživatele',
      content: '''
Jako uživatel se zavazujete:

• Poskytovat pravdivé a aktuální informace
• Neporušovat autorská práva třetích stran
• Nesdílet nevhodný nebo nezákonný obsah
• Nepoužívat službu k škodlivým účelům
• Chránit své přihlašovací údaje
• Dodržovat všechny platné zákony

Porušení těchto podmínek může vést k ukončení účtu.
      ''',
    );
  }

  TermsOfServiceSection _getIntellectualPropertySection() {
    return TermsOfServiceSection(
      title: 'Duševní vlastnictví',
      content: '''
• **Váš obsah**: Zůstává vaším vlastnictvím
• **Naše aplikace**: Chráněna autorskými právy
• **Licence k obsahu**: Udělujete nám licenci k zobrazování vašeho obsahu
• **Ochranné známky**: Croatia Travel App je naše ochranná známka
• **Třetí strany**: Respektujeme práva třetích stran

Nepoužívejte obsah chráněný autorskými právy bez povolení.
      ''',
    );
  }

  TermsOfServiceSection _getPaymentTermsSection() {
    return TermsOfServiceSection(
      title: 'Platební podmínky',
      content: '''
• **Premium předplatné**: 12.99€/měsíc nebo 129.99€/rok
• **Platební metody**: Kreditní karty, PayPal, Apple Pay, Google Pay
• **Automatické obnovení**: Pokud není zrušeno 24h před koncem období
• **Refundace**: 14 dní na vrácení peněz bez udání důvodu
• **Změny cen**: S 30denním předstihem

Všechny ceny jsou uvedeny včetně DPH.
      ''',
    );
  }

  TermsOfServiceSection _getLiabilitySection() {
    return TermsOfServiceSection(
      title: 'Omezení odpovědnosti',
      content: '''
• **Dostupnost služby**: Nezaručujeme 100% dostupnost
• **Ztráta dat**: Doporučujeme pravidelné zálohy
• **Nepřímé škody**: Neneseme odpovědnost za nepřímé škody
• **Maximální odpovědnost**: Omezena na výši ročního předplatného
• **Vyšší moc**: Neneseme odpovědnost za události vyšší moci

Používáte službu na vlastní riziko.
      ''',
    );
  }

  TermsOfServiceSection _getTerminationSection() {
    return TermsOfServiceSection(
      title: 'Ukončení služby',
      content: '''
• **Vámi**: Můžete kdykoli zrušit účet v nastavení
• **Námi**: Můžeme ukončit při porušení podmínek
• **Výpovědní lhůta**: 30 dní pro ukončení z naší strany
• **Zachování dat**: 90 dní po ukončení pro možnost obnovení
• **Smazání dat**: Automatické po 90 dnech nebo na požádání

Po ukončení ztratíte přístup ke všem datům.
      ''',
    );
  }

  TermsOfServiceSection _getGoverningLawSection() {
    return TermsOfServiceSection(
      title: 'Rozhodné právo',
      content: '''
• **Rozhodné právo**: České právo
• **Jurisdikce**: České soudy
• **Řešení sporů**: Přednostně mimosoudní řešení
• **Evropské právo**: GDPR a další EU nařízení
• **Mezinárodní právo**: Pokud je relevantní

Tyto podmínky se řídí českým právem.
      ''',
    );
  }

  /// Pomocné metody
  Future<void> _loadUserConsent() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final consentJson = prefs.getString('user_consent');
      
      if (consentJson != null) {
        // V produkci by se deserializovalo z JSON
        debugPrint('📋 User consent načten');
      }
    } catch (e) {
      debugPrint('❌ Chyba při načítání consent: $e');
    }
  }

  Future<void> _saveUserConsent() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      // V produkci by se serializovalo do JSON
      await prefs.setString('user_consent', 'consent_data');
      debugPrint('💾 User consent uložen');
    } catch (e) {
      debugPrint('❌ Chyba při ukládání consent: $e');
    }
  }

  Future<String> _getCurrentIpAddress() async {
    // V produkci by se získala skutečná IP
    return '***********';
  }

  Future<String> _getUserAgent() async {
    // V produkci by se získal skutečný user agent
    return 'Croatia Travel App/1.0.0';
  }

  Future<Map<String, dynamic>> _exportProfileData() async {
    return {
      'name': 'Jan Novák',
      'email': '<EMAIL>',
      'created_at': '2024-01-15T10:30:00Z',
      'preferences': {'language': 'cs', 'theme': 'light'},
    };
  }

  Future<Map<String, dynamic>> _exportDiaryData() async {
    return {
      'total_entries': 150,
      'entries': [], // V produkci by obsahovalo skutečná data
    };
  }

  Future<Map<String, dynamic>> _exportPhotoData() async {
    return {
      'total_photos': 89,
      'photos': [], // V produkci by obsahovalo metadata fotek
    };
  }

  Future<Map<String, dynamic>> _exportAnalyticsData() async {
    return {
      'writing_statistics': {},
      'mood_analysis': {},
      'location_insights': {},
    };
  }

  Future<Map<String, dynamic>> _exportConsentData() async {
    return {
      'consents': _currentConsent?.consents.map((c) => {
        'type': c.type.name,
        'granted': c.granted,
        'timestamp': c.timestamp.toIso8601String(),
      }).toList() ?? [],
    };
  }

  Future<Map<String, dynamic>> _exportSettingsData() async {
    return {
      'notifications': true,
      'privacy_level': 'medium',
      'data_sharing': false,
    };
  }

  Future<void> _deleteDiaryEntries(String userId, bool keepLegal) async {
    debugPrint('🗑️ Mazání diary entries...');
    // V produkci by smazalo skutečná data
  }

  Future<void> _deletePhotos(String userId, bool keepLegal) async {
    debugPrint('🗑️ Mazání photos...');
    // V produkci by smazalo skutečná data
  }

  Future<void> _deleteAnalyticsData(String userId, bool keepLegal) async {
    debugPrint('🗑️ Mazání analytics data...');
    // V produkci by smazalo skutečná data
  }

  Future<void> _deleteProfileData(String userId, bool keepLegal) async {
    debugPrint('🗑️ Mazání profile data...');
    // V produkci by smazalo skutečná data
  }

  @override
  void dispose() {
    _eventController.close();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  UserConsent? get currentConsent => _currentConsent;
}
