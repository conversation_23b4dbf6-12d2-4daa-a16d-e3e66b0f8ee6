// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'beach.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Beach _$BeachFromJson(Map<String, dynamic> json) => Beach(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      location: json['location'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      region: json['region'] as String,
      beachType: $enumDecode(_$BeachTypeEnumMap, json['beachType']),
      features:
          (json['features'] as List<dynamic>).map((e) => e as String).toList(),
      activities: (json['activities'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      rating: (json['rating'] as num).toDouble(),
      reviewCount: (json['reviewCount'] as num).toInt(),
      accessType: json['accessType'] as String,
      hasLifeguard: json['hasLifeguard'] as bool,
      hasParking: json['hasParking'] as bool,
      hasRestaurant: json['hasRestaurant'] as bool,
      hasShower: json['hasShower'] as bool,
      hasToilet: json['hasToilet'] as bool,
      hasUmbrella: json['hasUmbrella'] as bool,
      hasChair: json['hasChair'] as bool,
      isPetFriendly: json['isPetFriendly'] as bool,
      isNudist: json['isNudist'] as bool,
      isAccessible: json['isAccessible'] as bool,
      photos:
          (json['photos'] as List<dynamic>).map((e) => e as String).toList(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$BeachToJson(Beach instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'location': instance.location,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'region': instance.region,
      'beachType': _$BeachTypeEnumMap[instance.beachType]!,
      'features': instance.features,
      'activities': instance.activities,
      'rating': instance.rating,
      'reviewCount': instance.reviewCount,
      'accessType': instance.accessType,
      'hasLifeguard': instance.hasLifeguard,
      'hasParking': instance.hasParking,
      'hasRestaurant': instance.hasRestaurant,
      'hasShower': instance.hasShower,
      'hasToilet': instance.hasToilet,
      'hasUmbrella': instance.hasUmbrella,
      'hasChair': instance.hasChair,
      'isPetFriendly': instance.isPetFriendly,
      'isNudist': instance.isNudist,
      'isAccessible': instance.isAccessible,
      'photos': instance.photos,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };

const _$BeachTypeEnumMap = {
  BeachType.sandy: 'sandy',
  BeachType.pebble: 'pebble',
  BeachType.rocky: 'rocky',
  BeachType.concrete: 'concrete',
  BeachType.mixed: 'mixed',
};

BeachConditions _$BeachConditionsFromJson(Map<String, dynamic> json) =>
    BeachConditions(
      beachId: json['beachId'] as String,
      seaTemperature: (json['seaTemperature'] as num).toDouble(),
      airTemperature: (json['airTemperature'] as num).toDouble(),
      waveHeight: (json['waveHeight'] as num).toInt(),
      windSpeed: (json['windSpeed'] as num).toInt(),
      windDirection: json['windDirection'] as String,
      uvIndex: (json['uvIndex'] as num).toInt(),
      weatherCondition: json['weatherCondition'] as String,
      visibility: (json['visibility'] as num).toInt(),
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$BeachConditionsToJson(BeachConditions instance) =>
    <String, dynamic>{
      'beachId': instance.beachId,
      'seaTemperature': instance.seaTemperature,
      'airTemperature': instance.airTemperature,
      'waveHeight': instance.waveHeight,
      'windSpeed': instance.windSpeed,
      'windDirection': instance.windDirection,
      'uvIndex': instance.uvIndex,
      'weatherCondition': instance.weatherCondition,
      'visibility': instance.visibility,
      'timestamp': instance.timestamp.toIso8601String(),
    };

BeachReview _$BeachReviewFromJson(Map<String, dynamic> json) => BeachReview(
      id: json['id'] as String,
      beachId: json['beachId'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      rating: (json['rating'] as num).toDouble(),
      comment: json['comment'] as String,
      photos:
          (json['photos'] as List<dynamic>).map((e) => e as String).toList(),
      visitDate: DateTime.parse(json['visitDate'] as String),
      activities: (json['activities'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      conditions: json['conditions'] == null
          ? null
          : BeachConditions.fromJson(
              json['conditions'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$BeachReviewToJson(BeachReview instance) =>
    <String, dynamic>{
      'id': instance.id,
      'beachId': instance.beachId,
      'userId': instance.userId,
      'userName': instance.userName,
      'rating': instance.rating,
      'comment': instance.comment,
      'photos': instance.photos,
      'visitDate': instance.visitDate.toIso8601String(),
      'activities': instance.activities,
      'conditions': instance.conditions,
      'createdAt': instance.createdAt.toIso8601String(),
    };

FavoriteBeach _$FavoriteBeachFromJson(Map<String, dynamic> json) =>
    FavoriteBeach(
      id: json['id'] as String,
      userId: json['userId'] as String,
      beachId: json['beachId'] as String,
      addedAt: DateTime.parse(json['addedAt'] as String),
      notes: json['notes'] as String?,
      preferredActivities: (json['preferredActivities'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$FavoriteBeachToJson(FavoriteBeach instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'beachId': instance.beachId,
      'addedAt': instance.addedAt.toIso8601String(),
      'notes': instance.notes,
      'preferredActivities': instance.preferredActivities,
    };

BeachVisit _$BeachVisitFromJson(Map<String, dynamic> json) => BeachVisit(
      id: json['id'] as String,
      userId: json['userId'] as String,
      beachId: json['beachId'] as String,
      visitDate: DateTime.parse(json['visitDate'] as String),
      userRating: (json['userRating'] as num?)?.toDouble(),
      notes: json['notes'] as String?,
      photos:
          (json['photos'] as List<dynamic>).map((e) => e as String).toList(),
      activities: (json['activities'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      conditions: json['conditions'] == null
          ? null
          : BeachConditions.fromJson(
              json['conditions'] as Map<String, dynamic>),
      stayDuration: json['stayDuration'] == null
          ? null
          : Duration(microseconds: (json['stayDuration'] as num).toInt()),
    );

Map<String, dynamic> _$BeachVisitToJson(BeachVisit instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'beachId': instance.beachId,
      'visitDate': instance.visitDate.toIso8601String(),
      'userRating': instance.userRating,
      'notes': instance.notes,
      'photos': instance.photos,
      'activities': instance.activities,
      'conditions': instance.conditions,
      'stayDuration': instance.stayDuration?.inMicroseconds,
    };

WaterSport _$WaterSportFromJson(Map<String, dynamic> json) => WaterSport(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: $enumDecode(_$WaterActivityEnumMap, json['type']),
      difficulty: json['difficulty'] as String,
      equipment:
          (json['equipment'] as List<dynamic>).map((e) => e as String).toList(),
      suitableBeaches: (json['suitableBeaches'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      season: json['season'] as String,
      averagePrice: (json['averagePrice'] as num).toDouble(),
      priceUnit: json['priceUnit'] as String,
      requiresLicense: json['requiresLicense'] as bool,
      minAge: (json['minAge'] as num).toInt(),
      providers:
          (json['providers'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$WaterSportToJson(WaterSport instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': _$WaterActivityEnumMap[instance.type]!,
      'difficulty': instance.difficulty,
      'equipment': instance.equipment,
      'suitableBeaches': instance.suitableBeaches,
      'season': instance.season,
      'averagePrice': instance.averagePrice,
      'priceUnit': instance.priceUnit,
      'requiresLicense': instance.requiresLicense,
      'minAge': instance.minAge,
      'providers': instance.providers,
    };

const _$WaterActivityEnumMap = {
  WaterActivity.swimming: 'swimming',
  WaterActivity.snorkeling: 'snorkeling',
  WaterActivity.diving: 'diving',
  WaterActivity.windsurfing: 'windsurfing',
  WaterActivity.kitesurfing: 'kitesurfing',
  WaterActivity.sailing: 'sailing',
  WaterActivity.jetski: 'jetski',
  WaterActivity.parasailing: 'parasailing',
  WaterActivity.fishing: 'fishing',
  WaterActivity.boating: 'boating',
  WaterActivity.kayaking: 'kayaking',
  WaterActivity.paddleboard: 'paddleboard',
  WaterActivity.volleyball: 'volleyball',
  WaterActivity.waterski: 'waterski',
};
