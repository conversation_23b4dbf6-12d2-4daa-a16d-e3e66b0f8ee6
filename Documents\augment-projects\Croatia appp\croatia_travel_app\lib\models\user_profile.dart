import 'package:json_annotation/json_annotation.dart';

part 'user_profile.g.dart';

@JsonSerializable()
class UserProfile {
  final String id;
  final String username;
  final String email;
  final String? displayName;
  final String? avatarUrl;
  final String? bio;
  final String? location;
  final DateTime? birthDate;
  final List<String> interests;
  final List<String> languages;
  final UserPreferences preferences;
  final UserStats stats;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isPublic;
  final bool isVerified;

  UserProfile({
    required this.id,
    required this.username,
    required this.email,
    this.displayName,
    this.avatarUrl,
    this.bio,
    this.location,
    this.birthDate,
    this.interests = const [],
    this.languages = const [],
    required this.preferences,
    required this.stats,
    required this.createdAt,
    required this.updatedAt,
    this.isPublic = false,
    this.isVerified = false,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) => 
      _$UserProfileFromJson(json);
  Map<String, dynamic> toJson() => _$UserProfileToJson(this);

  UserProfile copyWith({
    String? id,
    String? username,
    String? email,
    String? displayName,
    String? avatarUrl,
    String? bio,
    String? location,
    DateTime? birthDate,
    List<String>? interests,
    List<String>? languages,
    UserPreferences? preferences,
    UserStats? stats,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isPublic,
    bool? isVerified,
  }) {
    return UserProfile(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      bio: bio ?? this.bio,
      location: location ?? this.location,
      birthDate: birthDate ?? this.birthDate,
      interests: interests ?? this.interests,
      languages: languages ?? this.languages,
      preferences: preferences ?? this.preferences,
      stats: stats ?? this.stats,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isPublic: isPublic ?? this.isPublic,
      isVerified: isVerified ?? this.isVerified,
    );
  }

  String get effectiveDisplayName => displayName ?? username;
  
  bool get hasAvatar => avatarUrl != null && avatarUrl!.isNotEmpty;
  
  bool get hasBio => bio != null && bio!.isNotEmpty;
  
  int? get age {
    if (birthDate == null) return null;
    final now = DateTime.now();
    int age = now.year - birthDate!.year;
    if (now.month < birthDate!.month || 
        (now.month == birthDate!.month && now.day < birthDate!.day)) {
      age--;
    }
    return age;
  }
}

@JsonSerializable()
class UserPreferences {
  final String language;
  final String currency;
  final String dateFormat;
  final String timeFormat;
  final bool notificationsEnabled;
  final bool locationSharingEnabled;
  final bool publicProfile;
  final bool autoSync;
  final bool batterySaverMode;
  final VoiceSettings voiceSettings;
  final PrivacySettings privacySettings;

  UserPreferences({
    this.language = 'cs',
    this.currency = 'EUR',
    this.dateFormat = 'dd.MM.yyyy',
    this.timeFormat = '24h',
    this.notificationsEnabled = true,
    this.locationSharingEnabled = false,
    this.publicProfile = false,
    this.autoSync = true,
    this.batterySaverMode = false,
    required this.voiceSettings,
    required this.privacySettings,
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) => 
      _$UserPreferencesFromJson(json);
  Map<String, dynamic> toJson() => _$UserPreferencesToJson(this);

  UserPreferences copyWith({
    String? language,
    String? currency,
    String? dateFormat,
    String? timeFormat,
    bool? notificationsEnabled,
    bool? locationSharingEnabled,
    bool? publicProfile,
    bool? autoSync,
    bool? batterySaverMode,
    VoiceSettings? voiceSettings,
    PrivacySettings? privacySettings,
  }) {
    return UserPreferences(
      language: language ?? this.language,
      currency: currency ?? this.currency,
      dateFormat: dateFormat ?? this.dateFormat,
      timeFormat: timeFormat ?? this.timeFormat,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      locationSharingEnabled: locationSharingEnabled ?? this.locationSharingEnabled,
      publicProfile: publicProfile ?? this.publicProfile,
      autoSync: autoSync ?? this.autoSync,
      batterySaverMode: batterySaverMode ?? this.batterySaverMode,
      voiceSettings: voiceSettings ?? this.voiceSettings,
      privacySettings: privacySettings ?? this.privacySettings,
    );
  }
}

@JsonSerializable()
class VoiceSettings {
  final bool voiceNotesEnabled;
  final bool speechToTextEnabled;
  final bool textToSpeechEnabled;
  final String preferredLanguage;
  final double speechRate;
  final double volume;
  final double pitch;
  final bool autoTranscription;

  VoiceSettings({
    this.voiceNotesEnabled = true,
    this.speechToTextEnabled = true,
    this.textToSpeechEnabled = true,
    this.preferredLanguage = 'cs-CZ',
    this.speechRate = 0.8,
    this.volume = 1.0,
    this.pitch = 1.0,
    this.autoTranscription = false,
  });

  factory VoiceSettings.fromJson(Map<String, dynamic> json) => 
      _$VoiceSettingsFromJson(json);
  Map<String, dynamic> toJson() => _$VoiceSettingsToJson(this);

  VoiceSettings copyWith({
    bool? voiceNotesEnabled,
    bool? speechToTextEnabled,
    bool? textToSpeechEnabled,
    String? preferredLanguage,
    double? speechRate,
    double? volume,
    double? pitch,
    bool? autoTranscription,
  }) {
    return VoiceSettings(
      voiceNotesEnabled: voiceNotesEnabled ?? this.voiceNotesEnabled,
      speechToTextEnabled: speechToTextEnabled ?? this.speechToTextEnabled,
      textToSpeechEnabled: textToSpeechEnabled ?? this.textToSpeechEnabled,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      speechRate: speechRate ?? this.speechRate,
      volume: volume ?? this.volume,
      pitch: pitch ?? this.pitch,
      autoTranscription: autoTranscription ?? this.autoTranscription,
    );
  }
}

@JsonSerializable()
class PrivacySettings {
  final bool shareLocation;
  final bool shareTrips;
  final bool sharePhotos;
  final bool shareVoiceNotes;
  final bool allowInvitations;
  final bool showOnlineStatus;
  final List<String> blockedUsers;

  PrivacySettings({
    this.shareLocation = false,
    this.shareTrips = true,
    this.sharePhotos = true,
    this.shareVoiceNotes = false,
    this.allowInvitations = true,
    this.showOnlineStatus = true,
    this.blockedUsers = const [],
  });

  factory PrivacySettings.fromJson(Map<String, dynamic> json) => 
      _$PrivacySettingsFromJson(json);
  Map<String, dynamic> toJson() => _$PrivacySettingsToJson(this);

  PrivacySettings copyWith({
    bool? shareLocation,
    bool? shareTrips,
    bool? sharePhotos,
    bool? shareVoiceNotes,
    bool? allowInvitations,
    bool? showOnlineStatus,
    List<String>? blockedUsers,
  }) {
    return PrivacySettings(
      shareLocation: shareLocation ?? this.shareLocation,
      shareTrips: shareTrips ?? this.shareTrips,
      sharePhotos: sharePhotos ?? this.sharePhotos,
      shareVoiceNotes: shareVoiceNotes ?? this.shareVoiceNotes,
      allowInvitations: allowInvitations ?? this.allowInvitations,
      showOnlineStatus: showOnlineStatus ?? this.showOnlineStatus,
      blockedUsers: blockedUsers ?? this.blockedUsers,
    );
  }
}

@JsonSerializable()
class UserStats {
  final int totalTrips;
  final int placesVisited;
  final int diaryEntries;
  final int voiceNotes;
  final int photosShared;
  final int collaborativeTrips;
  final int friendsCount;
  final Duration totalTravelTime;
  final double totalDistance;
  final DateTime? lastActivity;

  UserStats({
    this.totalTrips = 0,
    this.placesVisited = 0,
    this.diaryEntries = 0,
    this.voiceNotes = 0,
    this.photosShared = 0,
    this.collaborativeTrips = 0,
    this.friendsCount = 0,
    this.totalTravelTime = Duration.zero,
    this.totalDistance = 0.0,
    this.lastActivity,
  });

  factory UserStats.fromJson(Map<String, dynamic> json) => 
      _$UserStatsFromJson(json);
  Map<String, dynamic> toJson() => _$UserStatsToJson(this);

  UserStats copyWith({
    int? totalTrips,
    int? placesVisited,
    int? diaryEntries,
    int? voiceNotes,
    int? photosShared,
    int? collaborativeTrips,
    int? friendsCount,
    Duration? totalTravelTime,
    double? totalDistance,
    DateTime? lastActivity,
  }) {
    return UserStats(
      totalTrips: totalTrips ?? this.totalTrips,
      placesVisited: placesVisited ?? this.placesVisited,
      diaryEntries: diaryEntries ?? this.diaryEntries,
      voiceNotes: voiceNotes ?? this.voiceNotes,
      photosShared: photosShared ?? this.photosShared,
      collaborativeTrips: collaborativeTrips ?? this.collaborativeTrips,
      friendsCount: friendsCount ?? this.friendsCount,
      totalTravelTime: totalTravelTime ?? this.totalTravelTime,
      totalDistance: totalDistance ?? this.totalDistance,
      lastActivity: lastActivity ?? this.lastActivity,
    );
  }

  String get formattedTotalDistance {
    if (totalDistance < 1000) {
      return '${totalDistance.toStringAsFixed(0)} m';
    } else {
      return '${(totalDistance / 1000).toStringAsFixed(1)} km';
    }
  }

  String get formattedTotalTravelTime {
    final days = totalTravelTime.inDays;
    final hours = totalTravelTime.inHours % 24;
    
    if (days > 0) {
      return '$days dní, $hours hodin';
    } else if (hours > 0) {
      return '$hours hodin';
    } else {
      return '${totalTravelTime.inMinutes} minut';
    }
  }
}
