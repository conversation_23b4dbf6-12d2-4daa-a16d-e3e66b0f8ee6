// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'emergency_services.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EmergencyContact _$EmergencyContactFromJson(Map<String, dynamic> json) =>
    EmergencyContact(
      id: json['id'] as String,
      name: json['name'] as String,
      nameEn: json['nameEn'] as String,
      type: $enumDecode(_$EmergencyTypeEnumMap, json['type']),
      phoneNumber: json['phoneNumber'] as String,
      alternativePhone: json['alternativePhone'] as String?,
      email: json['email'] as String?,
      website: json['website'] as String?,
      address: json['address'] as String,
      addressEn: json['addressEn'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      city: json['city'] as String,
      region: json['region'] as String,
      availability:
          $enumDecode(_$ServiceAvailabilityEnumMap, json['availability']),
      languages: (json['languages'] as List<dynamic>)
          .map((e) => $enumDecode(_$LanguageSupportEnumMap, e))
          .toList(),
      description: json['description'] as String,
      descriptionEn: json['descriptionEn'] as String,
      isOfficial: json['isOfficial'] as bool,
      isFree: json['isFree'] as bool,
      specialInstructions: json['specialInstructions'] as String?,
      specialInstructionsEn: json['specialInstructionsEn'] as String?,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$EmergencyContactToJson(EmergencyContact instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'nameEn': instance.nameEn,
      'type': _$EmergencyTypeEnumMap[instance.type]!,
      'phoneNumber': instance.phoneNumber,
      'alternativePhone': instance.alternativePhone,
      'email': instance.email,
      'website': instance.website,
      'address': instance.address,
      'addressEn': instance.addressEn,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'city': instance.city,
      'region': instance.region,
      'availability': _$ServiceAvailabilityEnumMap[instance.availability]!,
      'languages':
          instance.languages.map((e) => _$LanguageSupportEnumMap[e]!).toList(),
      'description': instance.description,
      'descriptionEn': instance.descriptionEn,
      'isOfficial': instance.isOfficial,
      'isFree': instance.isFree,
      'specialInstructions': instance.specialInstructions,
      'specialInstructionsEn': instance.specialInstructionsEn,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };

const _$EmergencyTypeEnumMap = {
  EmergencyType.police: 'police',
  EmergencyType.fire: 'fire',
  EmergencyType.medical: 'medical',
  EmergencyType.mountain: 'mountain',
  EmergencyType.sea: 'sea',
  EmergencyType.tourist: 'tourist',
  EmergencyType.consulate: 'consulate',
  EmergencyType.roadside: 'roadside',
};

const _$ServiceAvailabilityEnumMap = {
  ServiceAvailability.available24h: 'available24h',
  ServiceAvailability.businessHours: 'businessHours',
  ServiceAvailability.seasonal: 'seasonal',
  ServiceAvailability.emergency: 'emergency',
  ServiceAvailability.unavailable: 'unavailable',
};

const _$LanguageSupportEnumMap = {
  LanguageSupport.croatian: 'croatian',
  LanguageSupport.english: 'english',
  LanguageSupport.german: 'german',
  LanguageSupport.italian: 'italian',
  LanguageSupport.french: 'french',
  LanguageSupport.czech: 'czech',
  LanguageSupport.slovak: 'slovak',
};

HealthcareProvider _$HealthcareProviderFromJson(Map<String, dynamic> json) =>
    HealthcareProvider(
      id: json['id'] as String,
      name: json['name'] as String,
      nameEn: json['nameEn'] as String,
      type: json['type'] as String,
      phoneNumber: json['phoneNumber'] as String,
      emergencyPhone: json['emergencyPhone'] as String?,
      email: json['email'] as String?,
      website: json['website'] as String?,
      address: json['address'] as String,
      addressEn: json['addressEn'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      city: json['city'] as String,
      region: json['region'] as String,
      availability:
          $enumDecode(_$ServiceAvailabilityEnumMap, json['availability']),
      languages: (json['languages'] as List<dynamic>)
          .map((e) => $enumDecode(_$LanguageSupportEnumMap, e))
          .toList(),
      services:
          (json['services'] as List<dynamic>).map((e) => e as String).toList(),
      servicesEn: (json['servicesEn'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      acceptsInsurance: json['acceptsInsurance'] as bool,
      acceptsTourists: json['acceptsTourists'] as bool,
      isEmergency: json['isEmergency'] as bool,
      specialties: json['specialties'] as String?,
      specialtiesEn: json['specialtiesEn'] as String?,
      rating: (json['rating'] as num?)?.toDouble(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$HealthcareProviderToJson(HealthcareProvider instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'nameEn': instance.nameEn,
      'type': instance.type,
      'phoneNumber': instance.phoneNumber,
      'emergencyPhone': instance.emergencyPhone,
      'email': instance.email,
      'website': instance.website,
      'address': instance.address,
      'addressEn': instance.addressEn,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'city': instance.city,
      'region': instance.region,
      'availability': _$ServiceAvailabilityEnumMap[instance.availability]!,
      'languages':
          instance.languages.map((e) => _$LanguageSupportEnumMap[e]!).toList(),
      'services': instance.services,
      'servicesEn': instance.servicesEn,
      'acceptsInsurance': instance.acceptsInsurance,
      'acceptsTourists': instance.acceptsTourists,
      'isEmergency': instance.isEmergency,
      'specialties': instance.specialties,
      'specialtiesEn': instance.specialtiesEn,
      'rating': instance.rating,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };

TouristAssistance _$TouristAssistanceFromJson(Map<String, dynamic> json) =>
    TouristAssistance(
      id: json['id'] as String,
      name: json['name'] as String,
      nameEn: json['nameEn'] as String,
      type: json['type'] as String,
      phoneNumber: json['phoneNumber'] as String,
      email: json['email'] as String?,
      website: json['website'] as String?,
      address: json['address'] as String,
      addressEn: json['addressEn'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      city: json['city'] as String,
      region: json['region'] as String,
      availability:
          $enumDecode(_$ServiceAvailabilityEnumMap, json['availability']),
      languages: (json['languages'] as List<dynamic>)
          .map((e) => $enumDecode(_$LanguageSupportEnumMap, e))
          .toList(),
      services:
          (json['services'] as List<dynamic>).map((e) => e as String).toList(),
      servicesEn: (json['servicesEn'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      isFree: json['isFree'] as bool,
      isOfficial: json['isOfficial'] as bool,
      priceInfo: json['priceInfo'] as String?,
      priceInfoEn: json['priceInfoEn'] as String?,
      rating: (json['rating'] as num?)?.toDouble(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$TouristAssistanceToJson(TouristAssistance instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'nameEn': instance.nameEn,
      'type': instance.type,
      'phoneNumber': instance.phoneNumber,
      'email': instance.email,
      'website': instance.website,
      'address': instance.address,
      'addressEn': instance.addressEn,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'city': instance.city,
      'region': instance.region,
      'availability': _$ServiceAvailabilityEnumMap[instance.availability]!,
      'languages':
          instance.languages.map((e) => _$LanguageSupportEnumMap[e]!).toList(),
      'services': instance.services,
      'servicesEn': instance.servicesEn,
      'isFree': instance.isFree,
      'isOfficial': instance.isOfficial,
      'priceInfo': instance.priceInfo,
      'priceInfoEn': instance.priceInfoEn,
      'rating': instance.rating,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };
