import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:flutter/material.dart';

/// Typ místa na mapě
enum MapPlaceType {
  restaurant,
  accommodation,
  culturalSite,
  beach,
  ticket,
  emergency,
  transport,
  entertainment,
  shopping,
  nature,
}

/// Kategorie místa pro filtrování
enum MapPlaceCategory {
  food,
  stay,
  culture,
  nature,
  entertainment,
  services,
  transport,
  emergency,
}

/// Model pro místo na mapě
class MapPlace {
  final String id;
  final String name;
  final String description;
  final LatLng position;
  final MapPlaceType type;
  final MapPlaceCategory category;
  final String address;
  final String? phoneNumber;
  final String? website;
  final String? imageUrl;
  final double? rating;
  final int? reviewCount;
  final double? price;
  final String? priceRange;
  final List<String> tags;
  final Map<String, dynamic> additionalData;
  final bool isVerified;
  final bool isOpen;
  final String? openingHours;

  const MapPlace({
    required this.id,
    required this.name,
    required this.description,
    required this.position,
    required this.type,
    required this.category,
    required this.address,
    this.phoneNumber,
    this.website,
    this.imageUrl,
    this.rating,
    this.reviewCount,
    this.price,
    this.priceRange,
    this.tags = const [],
    this.additionalData = const {},
    this.isVerified = false,
    this.isOpen = true,
    this.openingHours,
  });

  /// Získá ikonu podle typu místa
  IconData get icon {
    switch (type) {
      case MapPlaceType.restaurant:
        return Icons.restaurant;
      case MapPlaceType.accommodation:
        return Icons.hotel;
      case MapPlaceType.culturalSite:
        return Icons.account_balance;
      case MapPlaceType.beach:
        return Icons.beach_access;
      case MapPlaceType.ticket:
        return Icons.confirmation_number;
      case MapPlaceType.emergency:
        return Icons.local_hospital;
      case MapPlaceType.transport:
        return Icons.directions_bus;
      case MapPlaceType.entertainment:
        return Icons.local_activity;
      case MapPlaceType.shopping:
        return Icons.shopping_bag;
      case MapPlaceType.nature:
        return Icons.park;
    }
  }

  /// Získá barvu podle typu místa (chorvatská paleta)
  Color get color {
    switch (type) {
      case MapPlaceType.restaurant:
        return const Color(0xFFFF6B35); // Chorvatská oranžová
      case MapPlaceType.accommodation:
        return const Color(0xFF006994); // Jaderská modrá
      case MapPlaceType.culturalSite:
        return const Color(0xFF8E24AA); // Fialová pro kulturu
      case MapPlaceType.beach:
        return const Color(0xFF00BCD4); // Azurová moře
      case MapPlaceType.ticket:
        return const Color(0xFF4CAF50); // Zelená pro vstupenky
      case MapPlaceType.emergency:
        return const Color(0xFFE53935); // Červená pro nouzové služby
      case MapPlaceType.transport:
        return const Color(0xFF607D8B); // Šedá pro dopravu
      case MapPlaceType.entertainment:
        return const Color(0xFF9C27B0); // Purpurová pro zábavu
      case MapPlaceType.shopping:
        return const Color(0xFFFF9800); // Oranžová pro nákupy
      case MapPlaceType.nature:
        return const Color(0xFF4CAF50); // Zelená pro přírodu
    }
  }

  /// Získá název kategorie v češtině
  String get categoryName {
    switch (category) {
      case MapPlaceCategory.food:
        return 'Jídlo a pití';
      case MapPlaceCategory.stay:
        return 'Ubytování';
      case MapPlaceCategory.culture:
        return 'Kultura';
      case MapPlaceCategory.nature:
        return 'Příroda';
      case MapPlaceCategory.entertainment:
        return 'Zábava';
      case MapPlaceCategory.services:
        return 'Služby';
      case MapPlaceCategory.transport:
        return 'Doprava';
      case MapPlaceCategory.emergency:
        return 'Nouzové služby';
    }
  }

  /// Získá název typu v češtině
  String get typeName {
    switch (type) {
      case MapPlaceType.restaurant:
        return 'Restaurace';
      case MapPlaceType.accommodation:
        return 'Ubytování';
      case MapPlaceType.culturalSite:
        return 'Kulturní místo';
      case MapPlaceType.beach:
        return 'Pláž';
      case MapPlaceType.ticket:
        return 'Vstupenka';
      case MapPlaceType.emergency:
        return 'Nouzová služba';
      case MapPlaceType.transport:
        return 'Doprava';
      case MapPlaceType.entertainment:
        return 'Zábava';
      case MapPlaceType.shopping:
        return 'Nákupy';
      case MapPlaceType.nature:
        return 'Příroda';
    }
  }

  /// Zkopíruje místo s novými hodnotami
  MapPlace copyWith({
    String? id,
    String? name,
    String? description,
    LatLng? position,
    MapPlaceType? type,
    MapPlaceCategory? category,
    String? address,
    String? phoneNumber,
    String? website,
    String? imageUrl,
    double? rating,
    int? reviewCount,
    double? price,
    String? priceRange,
    List<String>? tags,
    Map<String, dynamic>? additionalData,
    bool? isVerified,
    bool? isOpen,
    String? openingHours,
  }) {
    return MapPlace(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      position: position ?? this.position,
      type: type ?? this.type,
      category: category ?? this.category,
      address: address ?? this.address,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      website: website ?? this.website,
      imageUrl: imageUrl ?? this.imageUrl,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      price: price ?? this.price,
      priceRange: priceRange ?? this.priceRange,
      tags: tags ?? this.tags,
      additionalData: additionalData ?? this.additionalData,
      isVerified: isVerified ?? this.isVerified,
      isOpen: isOpen ?? this.isOpen,
      openingHours: openingHours ?? this.openingHours,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MapPlace && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MapPlace(id: $id, name: $name, type: $type, position: $position)';
  }
}

/// Cluster pro seskupování míst na mapě
class MapPlaceCluster {
  final String id;
  final LatLng position;
  final List<MapPlace> places;
  final int count;

  const MapPlaceCluster({
    required this.id,
    required this.position,
    required this.places,
    required this.count,
  });

  /// Získá dominantní typ v clusteru
  MapPlaceType get dominantType {
    final typeCount = <MapPlaceType, int>{};
    for (final place in places) {
      typeCount[place.type] = (typeCount[place.type] ?? 0) + 1;
    }
    
    return typeCount.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  /// Získá barvu clusteru podle dominantního typu
  Color get color {
    final place = MapPlace(
      id: '',
      name: '',
      description: '',
      position: position,
      type: dominantType,
      category: MapPlaceCategory.services,
      address: '',
    );
    return place.color;
  }
}

/// Filtr pro mapová místa
class MapPlaceFilter {
  final Set<MapPlaceType> types;
  final Set<MapPlaceCategory> categories;
  final double? minRating;
  final double? maxPrice;
  final bool? isVerified;
  final bool? isOpen;
  final String? searchQuery;

  const MapPlaceFilter({
    this.types = const {},
    this.categories = const {},
    this.minRating,
    this.maxPrice,
    this.isVerified,
    this.isOpen,
    this.searchQuery,
  });

  /// Zkontroluje, zda místo odpovídá filtru
  bool matches(MapPlace place) {
    // Filtr podle typu
    if (types.isNotEmpty && !types.contains(place.type)) {
      return false;
    }

    // Filtr podle kategorie
    if (categories.isNotEmpty && !categories.contains(place.category)) {
      return false;
    }

    // Filtr podle hodnocení
    if (minRating != null && (place.rating == null || place.rating! < minRating!)) {
      return false;
    }

    // Filtr podle ceny
    if (maxPrice != null && place.price != null && place.price! > maxPrice!) {
      return false;
    }

    // Filtr podle ověření
    if (isVerified != null && place.isVerified != isVerified!) {
      return false;
    }

    // Filtr podle otevřenosti
    if (isOpen != null && place.isOpen != isOpen!) {
      return false;
    }

    // Filtr podle vyhledávání
    if (searchQuery != null && searchQuery!.isNotEmpty) {
      final query = searchQuery!.toLowerCase();
      if (!place.name.toLowerCase().contains(query) &&
          !place.description.toLowerCase().contains(query) &&
          !place.address.toLowerCase().contains(query) &&
          !place.tags.any((tag) => tag.toLowerCase().contains(query))) {
        return false;
      }
    }

    return true;
  }

  /// Zkopíruje filtr s novými hodnotami
  MapPlaceFilter copyWith({
    Set<MapPlaceType>? types,
    Set<MapPlaceCategory>? categories,
    double? minRating,
    double? maxPrice,
    bool? isVerified,
    bool? isOpen,
    String? searchQuery,
  }) {
    return MapPlaceFilter(
      types: types ?? this.types,
      categories: categories ?? this.categories,
      minRating: minRating ?? this.minRating,
      maxPrice: maxPrice ?? this.maxPrice,
      isVerified: isVerified ?? this.isVerified,
      isOpen: isOpen ?? this.isOpen,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}
