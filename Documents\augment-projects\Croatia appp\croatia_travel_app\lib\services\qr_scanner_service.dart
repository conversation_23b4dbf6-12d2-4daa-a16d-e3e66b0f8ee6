import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import 'package:geolocator/geolocator.dart';

import '../models/camera_models.dart';

/// Služba pro skenování QR kódů
class QRScannerService extends ChangeNotifier {
  static final QRScannerService _instance = QRScannerService._internal();
  factory QRScannerService() => _instance;
  QRScannerService._internal();

  QRViewController? _controller;
  List<ScannedQRCode> _scannedCodes = [];
  bool _isScanning = false;
  bool _isInitialized = false;

  // Gettery
  QRViewController? get controller => _controller;
  List<ScannedQRCode> get scannedCodes => _scannedCodes;
  bool get isScanning => _isScanning;
  bool get isInitialized => _isInitialized;

  /// Inicializuje QR scanner službu
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadScannedCodes();
      _isInitialized = true;
      debugPrint('QR Scanner služba inicializována');
    } catch (e) {
      debugPrint('Chyba při inicializaci QR scanner služby: $e');
      throw Exception('Nepodařilo se inicializovat QR scanner');
    }
  }

  /// Nastaví QR controller
  void setController(QRViewController controller) {
    _controller = controller;
    _controller!.scannedDataStream.listen(_onQRCodeScanned);
  }

  /// Zpracuje naskenovaný QR kód
  void _onQRCodeScanned(Barcode scanData) async {
    if (!_isScanning || scanData.code == null) return;

    try {
      _isScanning = false;
      await _controller?.pauseCamera();

      final qrCode = await _processQRCode(scanData.code!);
      if (qrCode != null) {
        _scannedCodes.insert(0, qrCode);
        await _saveScannedCodes();
        notifyListeners();
      }

      // Krátká pauza před dalším skenováním
      await Future.delayed(const Duration(seconds: 2));
      _isScanning = true;
      await _controller?.resumeCamera();
    } catch (e) {
      debugPrint('Chyba při zpracování QR kódu: $e');
      _isScanning = true;
      await _controller?.resumeCamera();
    }
  }

  /// Zpracuje obsah QR kódu a vytvoří ScannedQRCode objekt
  Future<ScannedQRCode?> _processQRCode(String content) async {
    try {
      // Získá GPS pozici
      Position? position;
      try {
        position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high,
          timeLimit: const Duration(seconds: 3),
        );
      } catch (e) {
        debugPrint('Nepodařilo se získat GPS pozici: $e');
      }

      // Analyzuje typ QR kódu
      final qrType = _analyzeQRType(content);
      final parsedData = _parseQRContent(content, qrType);

      return ScannedQRCode(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: content,
        type: qrType,
        title: parsedData['title'],
        description: parsedData['description'],
        parsedData: parsedData,
        scannedAt: DateTime.now(),
        latitude: position?.latitude,
        longitude: position?.longitude,
      );
    } catch (e) {
      debugPrint('Chyba při zpracování QR kódu: $e');
      return null;
    }
  }

  /// Analyzuje typ QR kódu podle obsahu
  QRCodeType _analyzeQRType(String content) {
    final lowerContent = content.toLowerCase();

    // URL adresy
    if (content.startsWith('http://') || content.startsWith('https://')) {
      if (lowerContent.contains('ticket') || lowerContent.contains('vstupenka')) {
        return QRCodeType.ticket;
      }
      if (lowerContent.contains('menu') || lowerContent.contains('jidlo')) {
        return QRCodeType.menu;
      }
      return QRCodeType.website;
    }

    // WiFi konfigurace
    if (content.startsWith('WIFI:')) {
      return QRCodeType.wifi;
    }

    // Kontaktní informace
    if (content.startsWith('BEGIN:VCARD') || content.startsWith('MECARD:')) {
      return QRCodeType.contact;
    }

    // GPS souřadnice
    if (content.startsWith('geo:') || 
        (content.contains('lat') && content.contains('lng'))) {
      return QRCodeType.location;
    }

    // Vstupenky (specifické formáty)
    if (lowerContent.contains('ticket') || 
        lowerContent.contains('vstup') ||
        lowerContent.contains('muzeum') ||
        lowerContent.contains('galerie')) {
      return QRCodeType.ticket;
    }

    // Menu restaurace
    if (lowerContent.contains('menu') || 
        lowerContent.contains('restaurace') ||
        lowerContent.contains('jidlo')) {
      return QRCodeType.menu;
    }

    return QRCodeType.other;
  }

  /// Parsuje obsah QR kódu podle typu
  Map<String, dynamic> _parseQRContent(String content, QRCodeType type) {
    final Map<String, dynamic> result = {};

    switch (type) {
      case QRCodeType.website:
        result['url'] = content;
        result['title'] = _extractDomainFromUrl(content);
        result['description'] = 'Webová stránka';
        break;

      case QRCodeType.wifi:
        final wifiData = _parseWiFiQR(content);
        result.addAll(wifiData);
        result['title'] = 'WiFi: ${wifiData['ssid'] ?? 'Neznámá síť'}';
        result['description'] = 'Konfigurace WiFi sítě';
        break;

      case QRCodeType.contact:
        final contactData = _parseContactQR(content);
        result.addAll(contactData);
        result['title'] = contactData['name'] ?? 'Kontakt';
        result['description'] = 'Kontaktní informace';
        break;

      case QRCodeType.location:
        final locationData = _parseLocationQR(content);
        result.addAll(locationData);
        result['title'] = 'GPS Lokace';
        result['description'] = 'Geografické souřadnice';
        break;

      case QRCodeType.ticket:
        result['title'] = 'Vstupenka';
        result['description'] = 'Elektronická vstupenka';
        result['ticket_data'] = content;
        break;

      case QRCodeType.menu:
        result['title'] = 'Menu restaurace';
        result['description'] = 'Digitální menu';
        result['menu_url'] = content;
        break;

      default:
        result['title'] = 'QR Kód';
        result['description'] = 'Naskenovaný obsah';
        result['raw_content'] = content;
    }

    return result;
  }

  /// Extrahuje doménu z URL
  String _extractDomainFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.host;
    } catch (e) {
      return 'Webová stránka';
    }
  }

  /// Parsuje WiFi QR kód
  Map<String, dynamic> _parseWiFiQR(String content) {
    final Map<String, dynamic> result = {};
    
    // Formát: WIFI:T:WPA;S:ssid;P:password;H:hidden;;
    final regex = RegExp(r'WIFI:T:([^;]*);S:([^;]*);P:([^;]*);H:([^;]*);');
    final match = regex.firstMatch(content);
    
    if (match != null) {
      result['security'] = match.group(1);
      result['ssid'] = match.group(2);
      result['password'] = match.group(3);
      result['hidden'] = match.group(4) == 'true';
    }
    
    return result;
  }

  /// Parsuje kontaktní QR kód
  Map<String, dynamic> _parseContactQR(String content) {
    final Map<String, dynamic> result = {};
    
    if (content.startsWith('BEGIN:VCARD')) {
      // vCard formát
      final lines = content.split('\n');
      for (final line in lines) {
        if (line.startsWith('FN:')) {
          result['name'] = line.substring(3);
        } else if (line.startsWith('TEL:')) {
          result['phone'] = line.substring(4);
        } else if (line.startsWith('EMAIL:')) {
          result['email'] = line.substring(6);
        }
      }
    } else if (content.startsWith('MECARD:')) {
      // MECARD formát
      final regex = RegExp(r'N:([^;]*);TEL:([^;]*);EMAIL:([^;]*);');
      final match = regex.firstMatch(content);
      if (match != null) {
        result['name'] = match.group(1);
        result['phone'] = match.group(2);
        result['email'] = match.group(3);
      }
    }
    
    return result;
  }

  /// Parsuje lokační QR kód
  Map<String, dynamic> _parseLocationQR(String content) {
    final Map<String, dynamic> result = {};
    
    if (content.startsWith('geo:')) {
      // Formát: geo:lat,lng
      final coords = content.substring(4).split(',');
      if (coords.length >= 2) {
        result['latitude'] = double.tryParse(coords[0]);
        result['longitude'] = double.tryParse(coords[1]);
      }
    }
    
    return result;
  }

  /// Spustí skenování
  Future<void> startScanning() async {
    if (_controller == null) return;
    
    _isScanning = true;
    await _controller!.resumeCamera();
    notifyListeners();
  }

  /// Zastaví skenování
  Future<void> stopScanning() async {
    if (_controller == null) return;
    
    _isScanning = false;
    await _controller!.pauseCamera();
    notifyListeners();
  }

  /// Smaže naskenovaný QR kód
  Future<void> deleteScannedCode(String codeId) async {
    try {
      _scannedCodes.removeWhere((code) => code.id == codeId);
      await _saveScannedCodes();
      notifyListeners();
    } catch (e) {
      debugPrint('Chyba při mazání QR kódu: $e');
    }
  }

  /// Vymaže všechny naskenované QR kódy
  Future<void> clearAllScannedCodes() async {
    try {
      _scannedCodes.clear();
      await _saveScannedCodes();
      notifyListeners();
    } catch (e) {
      debugPrint('Chyba při mazání všech QR kódů: $e');
    }
  }

  /// Získá QR kódy podle typu
  List<ScannedQRCode> getCodesByType(QRCodeType type) {
    return _scannedCodes.where((code) => code.type == type).toList();
  }

  /// Získá nejnovější QR kódy
  List<ScannedQRCode> getRecentCodes({int limit = 10}) {
    final sorted = List<ScannedQRCode>.from(_scannedCodes);
    sorted.sort((a, b) => b.scannedAt.compareTo(a.scannedAt));
    return sorted.take(limit).toList();
  }

  /// Načte naskenované QR kódy z úložiště
  Future<void> _loadScannedCodes() async {
    try {
      // TODO: Implementovat načítání z SQLite databáze
      _scannedCodes = [];
    } catch (e) {
      debugPrint('Chyba při načítání QR kódů: $e');
    }
  }

  /// Uloží naskenované QR kódy do úložiště
  Future<void> _saveScannedCodes() async {
    try {
      // TODO: Implementovat ukládání do SQLite databáze
    } catch (e) {
      debugPrint('Chyba při ukládání QR kódů: $e');
    }
  }

  /// Získá statistiky QR kódů
  Map<String, int> getQRStats() {
    final stats = <String, int>{};
    
    for (final code in _scannedCodes) {
      final typeName = code.typeName;
      stats[typeName] = (stats[typeName] ?? 0) + 1;
    }
    
    return stats;
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }
}
