// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'restaurant.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Restaurant _$RestaurantFromJson(Map<String, dynamic> json) => Restaurant(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      address: json['address'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      region: json['region'] as String,
      cuisineType: json['cuisineType'] as String,
      priceRange: json['priceRange'] as String,
      rating: (json['rating'] as num).toDouble(),
      reviewCount: (json['reviewCount'] as num).toInt(),
      specialties: (json['specialties'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      phone: json['phone'] as String,
      website: json['website'] as String?,
      email: json['email'] as String?,
      workingHours: (json['workingHours'] as List<dynamic>)
          .map((e) => WorkingHours.fromJson(e as Map<String, dynamic>))
          .toList(),
      features:
          (json['features'] as List<dynamic>).map((e) => e as String).toList(),
      isActive: json['isActive'] as bool,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$RestaurantToJson(Restaurant instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'address': instance.address,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'region': instance.region,
      'cuisineType': instance.cuisineType,
      'priceRange': instance.priceRange,
      'rating': instance.rating,
      'reviewCount': instance.reviewCount,
      'specialties': instance.specialties,
      'phone': instance.phone,
      'website': instance.website,
      'email': instance.email,
      'workingHours': instance.workingHours,
      'features': instance.features,
      'isActive': instance.isActive,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };

WorkingHours _$WorkingHoursFromJson(Map<String, dynamic> json) => WorkingHours(
      dayOfWeek: (json['dayOfWeek'] as num).toInt(),
      openHour: (json['openHour'] as num).toInt(),
      openMinute: (json['openMinute'] as num).toInt(),
      closeHour: (json['closeHour'] as num).toInt(),
      closeMinute: (json['closeMinute'] as num).toInt(),
    );

Map<String, dynamic> _$WorkingHoursToJson(WorkingHours instance) =>
    <String, dynamic>{
      'dayOfWeek': instance.dayOfWeek,
      'openHour': instance.openHour,
      'openMinute': instance.openMinute,
      'closeHour': instance.closeHour,
      'closeMinute': instance.closeMinute,
    };

RestaurantReview _$RestaurantReviewFromJson(Map<String, dynamic> json) =>
    RestaurantReview(
      id: json['id'] as String,
      restaurantId: json['restaurantId'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      rating: (json['rating'] as num).toDouble(),
      comment: json['comment'] as String,
      photos:
          (json['photos'] as List<dynamic>).map((e) => e as String).toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      isVerified: json['isVerified'] as bool,
    );

Map<String, dynamic> _$RestaurantReviewToJson(RestaurantReview instance) =>
    <String, dynamic>{
      'id': instance.id,
      'restaurantId': instance.restaurantId,
      'userId': instance.userId,
      'userName': instance.userName,
      'rating': instance.rating,
      'comment': instance.comment,
      'photos': instance.photos,
      'createdAt': instance.createdAt.toIso8601String(),
      'isVerified': instance.isVerified,
    };

FavoriteRestaurant _$FavoriteRestaurantFromJson(Map<String, dynamic> json) =>
    FavoriteRestaurant(
      id: json['id'] as String,
      userId: json['userId'] as String,
      restaurantId: json['restaurantId'] as String,
      addedAt: DateTime.parse(json['addedAt'] as String),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$FavoriteRestaurantToJson(FavoriteRestaurant instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'restaurantId': instance.restaurantId,
      'addedAt': instance.addedAt.toIso8601String(),
      'notes': instance.notes,
    };

RestaurantVisit _$RestaurantVisitFromJson(Map<String, dynamic> json) =>
    RestaurantVisit(
      id: json['id'] as String,
      userId: json['userId'] as String,
      restaurantId: json['restaurantId'] as String,
      visitDate: DateTime.parse(json['visitDate'] as String),
      userRating: (json['userRating'] as num?)?.toDouble(),
      notes: json['notes'] as String?,
      photos:
          (json['photos'] as List<dynamic>).map((e) => e as String).toList(),
      totalSpent: (json['totalSpent'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$RestaurantVisitToJson(RestaurantVisit instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'restaurantId': instance.restaurantId,
      'visitDate': instance.visitDate.toIso8601String(),
      'userRating': instance.userRating,
      'notes': instance.notes,
      'photos': instance.photos,
      'totalSpent': instance.totalSpent,
    };
